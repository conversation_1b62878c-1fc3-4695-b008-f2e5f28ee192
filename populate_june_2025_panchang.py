#!/usr/bin/env python3
"""
Populate Supabase with Tamil Panchang data for June 2025
This will pre-load all days so the app doesn't need to hit the API every time
"""

import requests
import json
from datetime import datetime, date, timedelta
import uuid
import time
import sys

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

# ProKerala API credentials
PROKERALA_CLIENT_ID = "6df0ec16-722b-4acd-a574-bfd546c0c270"
PROKERALA_CLIENT_SECRET = "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"

# Chennai coordinates (default location)
CHENNAI_LAT = 13.0827
CHENNAI_LON = 80.2707

class PanchangPopulator:
    def __init__(self):
        self.supabase_headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=representation"
        }
        self.prokerala_token = None
        self.token_expires_at = None
        
    def get_prokerala_token(self):
        """Get or refresh ProKerala API token"""
        if self.prokerala_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.prokerala_token
            
        print("🔑 Getting ProKerala API token...")
        token_url = "https://api.prokerala.com/token"
        token_data = {
            "grant_type": "client_credentials",
            "client_id": PROKERALA_CLIENT_ID,
            "client_secret": PROKERALA_CLIENT_SECRET
        }
        
        response = requests.post(token_url, data=token_data)
        if response.status_code != 200:
            raise Exception(f"Failed to get token: {response.status_code} - {response.text}")
        
        token_info = response.json()
        self.prokerala_token = token_info["access_token"]
        # Token expires in 1 hour, refresh 5 minutes early
        self.token_expires_at = datetime.now() + timedelta(seconds=token_info["expires_in"] - 300)
        
        print("✅ ProKerala token obtained successfully")
        return self.prokerala_token
    
    def fetch_panchang_for_date(self, target_date):
        """Fetch panchang data from ProKerala API for a specific date"""
        token = self.get_prokerala_token()
        
        # Format date for ProKerala API (6 AM IST)
        datetime_str = f"{target_date.strftime('%Y-%m-%d')}T06:00:00+05:30"
        
        # Build API URL with proper encoding
        base_url = "https://api.prokerala.com/v2/astrology/panchang"
        params = {
            "ayanamsa": 1,  # Lahiri
            "coordinates": f"{CHENNAI_LAT},{CHENNAI_LON}",
            "datetime": datetime_str,
            "la": "en"
        }
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"📡 Fetching panchang for {target_date.strftime('%Y-%m-%d')}...")
        
        response = requests.get(base_url, headers=headers, params=params)
        
        if response.status_code != 200:
            raise Exception(f"API Error {response.status_code}: {response.text}")
        
        return response.json()
    
    def convert_to_supabase_format(self, api_data, target_date):
        """Convert ProKerala API response to Supabase format"""
        data = api_data.get("data", {})
        
        # Extract basic info
        vaara = data.get("vaara", "Unknown")
        
        # Extract and format arrays
        tithi_array = data.get("tithi", [])
        nakshatra_array = data.get("nakshatra", [])
        yoga_array = data.get("yoga", [])
        karana_array = data.get("karana", [])
        
        # Create Supabase record
        record = {
            "id": str(uuid.uuid4()),
            "date": target_date.strftime("%Y-%m-%d"),
            "location_info": json.dumps({
                "latitude": CHENNAI_LAT,
                "longitude": CHENNAI_LON,
                "city": "Chennai",
                "country": "India"
            }),
            "tamil_date": json.dumps({
                "tamil_year": target_date.year + 1000,  # Approximate Tamil year
                "tamil_month": self.get_tamil_month(target_date.month),
                "tamil_day": target_date.day
            }),
            "sun_times": json.dumps({
                "sunrise": self.extract_time_from_iso(data.get("sunrise")),
                "sunset": self.extract_time_from_iso(data.get("sunset"))
            }),
            "moon_times": json.dumps({
                "moonrise": self.extract_time_from_iso(data.get("moonrise")),
                "moonset": self.extract_time_from_iso(data.get("moonset"))
            }),
            "tithi": json.dumps(tithi_array),
            "nakshatra": json.dumps(nakshatra_array),
            "yoga": json.dumps(yoga_array),
            "karana": json.dumps(karana_array),
            "weekday_info": json.dumps({
                "weekday_number": target_date.weekday() + 1,
                "weekday_name": target_date.strftime("%A"),
                "vedic_weekday_number": target_date.weekday() + 1,
                "vedic_weekday_name": vaara
            }),
            "lunar_month": json.dumps({
                "name": self.get_tamil_month(target_date.month),
                "number": target_date.month
            }),
            "season": json.dumps({
                "name": self.get_season(target_date.month),
                "tamil_name": self.get_tamil_season(target_date.month)
            }),
            "year_info": json.dumps({
                "gregorian_year": target_date.year,
                "tamil_year": target_date.year + 1000,
                "year_name": "Vilambi"  # Placeholder
            }),
            "significance": f"Panchang for {target_date.strftime('%B %d, %Y')}",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        return record
    
    def extract_time_from_iso(self, iso_string):
        """Extract time from ISO string"""
        if not iso_string:
            return "Unknown"
        
        try:
            dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
            return dt.strftime("%H:%M")
        except:
            return "Unknown"
    
    def get_tamil_month(self, month):
        """Get Tamil month name"""
        tamil_months = {
            1: "Thai", 2: "Maasi", 3: "Panguni", 4: "Chittirai",
            5: "Vaikasi", 6: "Aani", 7: "Aadi", 8: "Aavani",
            9: "Purattasi", 10: "Aippasi", 11: "Karthikai", 12: "Margazhi"
        }
        return tamil_months.get(month, "Unknown")
    
    def get_season(self, month):
        """Get season name"""
        if month in [12, 1, 2]:
            return "Winter"
        elif month in [3, 4, 5]:
            return "Spring"
        elif month in [6, 7, 8]:
            return "Summer"
        else:
            return "Autumn"
    
    def get_tamil_season(self, month):
        """Get Tamil season name"""
        if month in [12, 1, 2]:
            return "Kaar"
        elif month in [3, 4, 5]:
            return "Koothir"
        elif month in [6, 7, 8]:
            return "Munpani"
        else:
            return "Pinpani"
    
    def save_to_supabase(self, record):
        """Save panchang record to Supabase"""
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
        
        response = requests.post(url, json=record, headers=self.supabase_headers)
        
        if response.status_code in [200, 201]:
            print(f"✅ Saved panchang for {record['date']}")
            return True
        elif response.status_code == 409:
            print(f"⚠️ Record already exists for {record['date']}")
            return True
        else:
            print(f"❌ Failed to save {record['date']}: {response.status_code} - {response.text}")
            return False
    
    def populate_june_2025(self):
        """Populate all days of June 2025"""
        print("🚀 Starting June 2025 Panchang Population")
        print("=" * 50)
        
        # Generate all dates in June 2025
        start_date = date(2025, 6, 1)
        end_date = date(2025, 6, 30)
        
        current_date = start_date
        success_count = 0
        total_days = (end_date - start_date).days + 1
        
        while current_date <= end_date:
            try:
                # Fetch from API
                api_data = self.fetch_panchang_for_date(current_date)
                
                # Convert to Supabase format
                record = self.convert_to_supabase_format(api_data, current_date)
                
                # Save to Supabase
                if self.save_to_supabase(record):
                    success_count += 1
                
                # Rate limiting - wait 15 seconds between requests (5 requests per 60 seconds)
                print(f"⏳ Waiting 15 seconds for rate limiting...")
                time.sleep(15)
                
            except Exception as e:
                print(f"❌ Error processing {current_date}: {e}")
            
            current_date += timedelta(days=1)
        
        print("\n" + "=" * 50)
        print("📊 POPULATION SUMMARY")
        print("=" * 50)
        print(f"📅 Total Days: {total_days}")
        print(f"✅ Successfully Populated: {success_count}")
        print(f"❌ Failed: {total_days - success_count}")
        print(f"📈 Success Rate: {(success_count/total_days)*100:.1f}%")
        
        if success_count == total_days:
            print("\n🎉 ALL JUNE 2025 PANCHANG DATA SUCCESSFULLY POPULATED!")
            print("🚀 Your app can now work offline for the entire month!")
        else:
            print(f"\n⚠️ Some records failed. You may want to retry the failed dates.")

def main():
    populator = PanchangPopulator()
    populator.populate_june_2025()

if __name__ == "__main__":
    main()
