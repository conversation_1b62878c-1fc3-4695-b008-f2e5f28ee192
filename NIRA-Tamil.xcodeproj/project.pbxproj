// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		398AB9FD2E046BA9007D5E2D /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = 398AB9FC2E046BA9007D5E2D /* Auth */; };
		398AB9FF2E046BAB007D5E2D /* Functions in Frameworks */ = {isa = PBXBuildFile; productRef = 398AB9FE2E046BAB007D5E2D /* Functions */; };
		398ABA012E046BAE007D5E2D /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = 398ABA002E046BAE007D5E2D /* PostgREST */; };
		398ABA032E046BB1007D5E2D /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = 398ABA022E046BB1007D5E2D /* Realtime */; };
		398ABA052E046BB3007D5E2D /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = 398ABA042E046BB3007D5E2D /* Storage */; };
		398ABA072E046BB6007D5E2D /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = 398ABA062E046BB6007D5E2D /* Supabase */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		3945C86B2E024F2F00FB230E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3945C8542E024F2E00FB230E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3945C85B2E024F2E00FB230E;
			remoteInfo = "NIRA-Tamil";
		};
		3945C8752E024F2F00FB230E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3945C8542E024F2E00FB230E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3945C85B2E024F2E00FB230E;
			remoteInfo = "NIRA-Tamil";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		3945C85C2E024F2E00FB230E /* NIRA-Tamil.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "NIRA-Tamil.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		3945C86A2E024F2F00FB230E /* NIRA-TamilTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "NIRA-TamilTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		3945C8742E024F2F00FB230E /* NIRA-TamilUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "NIRA-TamilUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3945C85E2E024F2E00FB230E /* NIRA-Tamil */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "NIRA-Tamil";
			sourceTree = "<group>";
		};
		3945C86D2E024F2F00FB230E /* NIRA-TamilTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "NIRA-TamilTests";
			sourceTree = "<group>";
		};
		3945C8772E024F2F00FB230E /* NIRA-TamilUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "NIRA-TamilUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		3945C8592E024F2E00FB230E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				398ABA012E046BAE007D5E2D /* PostgREST in Frameworks */,
				398AB9FF2E046BAB007D5E2D /* Functions in Frameworks */,
				398ABA072E046BB6007D5E2D /* Supabase in Frameworks */,
				398AB9FD2E046BA9007D5E2D /* Auth in Frameworks */,
				398ABA052E046BB3007D5E2D /* Storage in Frameworks */,
				398ABA032E046BB1007D5E2D /* Realtime in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3945C8672E024F2F00FB230E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3945C8712E024F2F00FB230E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3945C8532E024F2E00FB230E = {
			isa = PBXGroup;
			children = (
				3945C85E2E024F2E00FB230E /* NIRA-Tamil */,
				3945C86D2E024F2F00FB230E /* NIRA-TamilTests */,
				3945C8772E024F2F00FB230E /* NIRA-TamilUITests */,
				398AB9FB2E046BA9007D5E2D /* Frameworks */,
				3945C85D2E024F2E00FB230E /* Products */,
			);
			sourceTree = "<group>";
		};
		3945C85D2E024F2E00FB230E /* Products */ = {
			isa = PBXGroup;
			children = (
				3945C85C2E024F2E00FB230E /* NIRA-Tamil.app */,
				3945C86A2E024F2F00FB230E /* NIRA-TamilTests.xctest */,
				3945C8742E024F2F00FB230E /* NIRA-TamilUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		398AB9FB2E046BA9007D5E2D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3945C85B2E024F2E00FB230E /* NIRA-Tamil */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3945C87E2E024F2F00FB230E /* Build configuration list for PBXNativeTarget "NIRA-Tamil" */;
			buildPhases = (
				3945C8582E024F2E00FB230E /* Sources */,
				3945C8592E024F2E00FB230E /* Frameworks */,
				3945C85A2E024F2E00FB230E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				3945C85E2E024F2E00FB230E /* NIRA-Tamil */,
			);
			name = "NIRA-Tamil";
			packageProductDependencies = (
				398AB9FC2E046BA9007D5E2D /* Auth */,
				398AB9FE2E046BAB007D5E2D /* Functions */,
				398ABA002E046BAE007D5E2D /* PostgREST */,
				398ABA022E046BB1007D5E2D /* Realtime */,
				398ABA042E046BB3007D5E2D /* Storage */,
				398ABA062E046BB6007D5E2D /* Supabase */,
			);
			productName = "NIRA-Tamil";
			productReference = 3945C85C2E024F2E00FB230E /* NIRA-Tamil.app */;
			productType = "com.apple.product-type.application";
		};
		3945C8692E024F2F00FB230E /* NIRA-TamilTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3945C8812E024F2F00FB230E /* Build configuration list for PBXNativeTarget "NIRA-TamilTests" */;
			buildPhases = (
				3945C8662E024F2F00FB230E /* Sources */,
				3945C8672E024F2F00FB230E /* Frameworks */,
				3945C8682E024F2F00FB230E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3945C86C2E024F2F00FB230E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3945C86D2E024F2F00FB230E /* NIRA-TamilTests */,
			);
			name = "NIRA-TamilTests";
			packageProductDependencies = (
			);
			productName = "NIRA-TamilTests";
			productReference = 3945C86A2E024F2F00FB230E /* NIRA-TamilTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		3945C8732E024F2F00FB230E /* NIRA-TamilUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3945C8842E024F2F00FB230E /* Build configuration list for PBXNativeTarget "NIRA-TamilUITests" */;
			buildPhases = (
				3945C8702E024F2F00FB230E /* Sources */,
				3945C8712E024F2F00FB230E /* Frameworks */,
				3945C8722E024F2F00FB230E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3945C8762E024F2F00FB230E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3945C8772E024F2F00FB230E /* NIRA-TamilUITests */,
			);
			name = "NIRA-TamilUITests";
			packageProductDependencies = (
			);
			productName = "NIRA-TamilUITests";
			productReference = 3945C8742E024F2F00FB230E /* NIRA-TamilUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3945C8542E024F2E00FB230E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					3945C85B2E024F2E00FB230E = {
						CreatedOnToolsVersion = 16.4;
					};
					3945C8692E024F2F00FB230E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 3945C85B2E024F2E00FB230E;
					};
					3945C8732E024F2F00FB230E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 3945C85B2E024F2E00FB230E;
					};
				};
			};
			buildConfigurationList = 3945C8572E024F2E00FB230E /* Build configuration list for PBXProject "NIRA-Tamil" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 3945C8532E024F2E00FB230E;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 3945C85D2E024F2E00FB230E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3945C85B2E024F2E00FB230E /* NIRA-Tamil */,
				3945C8692E024F2F00FB230E /* NIRA-TamilTests */,
				3945C8732E024F2F00FB230E /* NIRA-TamilUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3945C85A2E024F2E00FB230E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3945C8682E024F2F00FB230E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3945C8722E024F2F00FB230E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		3945C8582E024F2E00FB230E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3945C8662E024F2F00FB230E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3945C8702E024F2F00FB230E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		3945C86C2E024F2F00FB230E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3945C85B2E024F2E00FB230E /* NIRA-Tamil */;
			targetProxy = 3945C86B2E024F2F00FB230E /* PBXContainerItemProxy */;
		};
		3945C8762E024F2F00FB230E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3945C85B2E024F2E00FB230E /* NIRA-Tamil */;
			targetProxy = 3945C8752E024F2F00FB230E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		3945C87C2E024F2F00FB230E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		3945C87D2E024F2F00FB230E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		3945C87F2E024F2F00FB230E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = "NIRA-Tamil/NIRA_Tamil.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "NIRA Tamil needs access to your microphone to record your voice for pronunciation practice and voice-based learning exercises. This helps you improve your Tamil speaking skills with real-time feedback.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "NIRA Tamil uses speech recognition to help you practice Tamil pronunciation and provide voice-based learning interactions. This helps improve your speaking skills and provides personalized feedback on your pronunciation.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.securight.NIRA-Tamil.NIRA-Tamil";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		3945C8802E024F2F00FB230E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = "NIRA-Tamil/NIRA_Tamil.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "NIRA Tamil needs access to your microphone to record your voice for pronunciation practice and voice-based learning exercises. This helps you improve your Tamil speaking skills with real-time feedback.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "NIRA Tamil uses speech recognition to help you practice Tamil pronunciation and provide voice-based learning interactions. This helps improve your speaking skills and provides personalized feedback on your pronunciation.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.securight.NIRA-Tamil.NIRA-Tamil";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		3945C8822E024F2F00FB230E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.securight.NIRA-Tamil.NIRA-TamilTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIRA-Tamil.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIRA-Tamil";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		3945C8832E024F2F00FB230E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.securight.NIRA-Tamil.NIRA-TamilTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIRA-Tamil.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIRA-Tamil";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		3945C8852E024F2F00FB230E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.securight.NIRA-Tamil.NIRA-TamilUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "NIRA-Tamil";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		3945C8862E024F2F00FB230E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.securight.NIRA-Tamil.NIRA-TamilUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "NIRA-Tamil";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3945C8572E024F2E00FB230E /* Build configuration list for PBXProject "NIRA-Tamil" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3945C87C2E024F2F00FB230E /* Debug */,
				3945C87D2E024F2F00FB230E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3945C87E2E024F2F00FB230E /* Build configuration list for PBXNativeTarget "NIRA-Tamil" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3945C87F2E024F2F00FB230E /* Debug */,
				3945C8802E024F2F00FB230E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3945C8812E024F2F00FB230E /* Build configuration list for PBXNativeTarget "NIRA-TamilTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3945C8822E024F2F00FB230E /* Debug */,
				3945C8832E024F2F00FB230E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3945C8842E024F2F00FB230E /* Build configuration list for PBXNativeTarget "NIRA-TamilUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3945C8852E024F2F00FB230E /* Debug */,
				3945C8862E024F2F00FB230E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		398AB9FC2E046BA9007D5E2D /* Auth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Auth;
		};
		398AB9FE2E046BAB007D5E2D /* Functions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Functions;
		};
		398ABA002E046BAE007D5E2D /* PostgREST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = PostgREST;
		};
		398ABA022E046BB1007D5E2D /* Realtime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Realtime;
		};
		398ABA042E046BB3007D5E2D /* Storage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Storage;
		};
		398ABA062E046BB6007D5E2D /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 398AB9FA2E046B92007D5E2D /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 3945C8542E024F2E00FB230E /* Project object */;
}
