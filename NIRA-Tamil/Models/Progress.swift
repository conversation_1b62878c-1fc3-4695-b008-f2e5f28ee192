import Foundation
import SwiftData

@Model
final class Progress {
    var id: UUID
    var userID: UUID
    var lessonID: UUID
    var language: Language
    var category: LessonCategory
    var difficulty: Difficulty
    var score: Int
    var maxScore: Int
    var completedAt: Date?
    var timeSpent: Int // in seconds
    var exerciseResults: [ExerciseResult]
    var isCompleted: Bool
    var attempts: Int
    var bestScore: Int
    var streakContribution: Bool // Whether this lesson contributed to current streak
    var difficultyAtCompletion: Difficulty?
    var feedback: String?
    var notes: String?
    var createdDate: Date
    var updatedDate: Date
    
    init(
        userID: UUID,
        lessonID: UUID,
        language: Language = .french,
        category: LessonCategory = .vocabulary,
        difficulty: Difficulty = .beginner,
        score: Int = 0,
        maxScore: Int = 100,
        completedAt: Date? = nil,
        timeSpent: Int = 0,
        exerciseResults: [ExerciseResult] = [],
        isCompleted: Bool = false,
        attempts: Int = 0,
        bestScore: Int = 0,
        streakContribution: Bool = false,
        difficultyAtCompletion: Difficulty? = nil,
        feedback: String? = nil,
        notes: String? = nil,
        createdDate: Date = Date(),
        updatedDate: Date = Date()
    ) {
        self.id = UUID()
        self.userID = userID
        self.lessonID = lessonID
        self.language = language
        self.category = category
        self.difficulty = difficulty
        self.score = score
        self.maxScore = maxScore
        self.completedAt = completedAt
        self.timeSpent = timeSpent
        self.exerciseResults = exerciseResults
        self.isCompleted = isCompleted
        self.attempts = attempts
        self.bestScore = bestScore
        self.streakContribution = streakContribution
        self.difficultyAtCompletion = difficultyAtCompletion
        self.feedback = feedback
        self.notes = notes
        self.createdDate = createdDate
        self.updatedDate = updatedDate
    }
    
    var scorePercentage: Double {
        guard maxScore > 0 else { return 0 }
        return Double(score) / Double(maxScore) * 100
    }
    
    var bestScorePercentage: Double {
        guard maxScore > 0 else { return 0 }
        return Double(bestScore) / Double(maxScore) * 100
    }
    
    var isPassed: Bool {
        return scorePercentage >= 70.0 // 70% pass threshold
    }
    
    var isPerfect: Bool {
        return score == maxScore
    }
    
    var averageExerciseScore: Double {
        guard !exerciseResults.isEmpty else { return 0 }
        let totalScore = exerciseResults.reduce(0) { $0 + $1.score }
        let maxTotalScore = exerciseResults.reduce(0) { $0 + $1.maxScore }
        guard maxTotalScore > 0 else { return 0 }
        return Double(totalScore) / Double(maxTotalScore) * 100
    }
    
    var timeSpentFormatted: String {
        let minutes = timeSpent / 60
        let seconds = timeSpent % 60
        if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
    
    func updateScore(_ newScore: Int) {
        self.score = newScore
        self.bestScore = max(bestScore, newScore)
        self.updatedDate = Date()
    }
    
    func markCompleted() {
        self.isCompleted = true
        self.completedAt = Date()
        self.updatedDate = Date()
    }
}

@Model
final class ExerciseResult {
    var id: UUID
    var exerciseID: UUID
    var exerciseType: ExerciseType
    var userAnswer: String?
    var userAnswers: [String] // For multiple answers
    var correctAnswer: String?
    var correctAnswers: [String]
    var isCorrect: Bool
    var score: Int
    var maxScore: Int
    var timeSpent: Int // in seconds
    var hintsUsed: Int
    var attempts: Int
    var pronunciationScore: Double?
    var pronunciationFeedback: String?
    var culturalContextUnderstood: Bool?
    var difficulty: Difficulty
    var feedback: String?
    var createdDate: Date
    
    init(
        id: UUID = UUID(),
        exerciseID: UUID,
        exerciseType: ExerciseType,
        userAnswer: String? = nil,
        userAnswers: [String] = [],
        correctAnswer: String? = nil,
        correctAnswers: [String] = [],
        isCorrect: Bool = false,
        score: Int = 0,
        maxScore: Int = 10,
        timeSpent: Int = 0,
        hintsUsed: Int = 0,
        attempts: Int = 1,
        pronunciationScore: Double? = nil,
        pronunciationFeedback: String? = nil,
        culturalContextUnderstood: Bool? = nil,
        difficulty: Difficulty = .beginner,
        feedback: String? = nil,
        createdDate: Date = Date()
    ) {
        self.id = id
        self.exerciseID = exerciseID
        self.exerciseType = exerciseType
        self.userAnswer = userAnswer
        self.userAnswers = userAnswers
        self.correctAnswer = correctAnswer
        self.correctAnswers = correctAnswers
        self.isCorrect = isCorrect
        self.score = score
        self.maxScore = maxScore
        self.timeSpent = timeSpent
        self.hintsUsed = hintsUsed
        self.attempts = attempts
        self.pronunciationScore = pronunciationScore
        self.pronunciationFeedback = pronunciationFeedback
        self.culturalContextUnderstood = culturalContextUnderstood
        self.difficulty = difficulty
        self.feedback = feedback
        self.createdDate = createdDate
    }
    
    var scorePercentage: Double {
        guard maxScore > 0 else { return 0 }
        return Double(score) / Double(maxScore) * 100
    }
    
    var isPronunciationExercise: Bool {
        return exerciseType == .pronunciation
    }
    
    var hasPronunciationFeedback: Bool {
        return pronunciationScore != nil && pronunciationFeedback != nil
    }
    
    var timeSpentFormatted: String {
        if timeSpent < 60 {
            return "\(timeSpent)s"
        } else {
            let minutes = timeSpent / 60
            let seconds = timeSpent % 60
            return "\(minutes)m \(seconds)s"
        }
    }
    
    var performanceLevel: PerformanceLevel {
        let percentage = scorePercentage
        switch percentage {
        case 90...100: return .excellent
        case 80..<90: return .good
        case 70..<80: return .satisfactory
        case 60..<70: return .needsImprovement
        default: return .poor
        }
    }
}

enum PerformanceLevel: String, CaseIterable, Codable {
    case excellent = "excellent"
    case good = "good"
    case satisfactory = "satisfactory"
    case needsImprovement = "needs_improvement"
    case poor = "poor"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .satisfactory: return "Satisfactory"
        case .needsImprovement: return "Needs Improvement"
        case .poor: return "Poor"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .satisfactory: return "orange"
        case .needsImprovement: return "yellow"
        case .poor: return "red"
        }
    }
    
    var icon: String {
        switch self {
        case .excellent: return "star.fill"
        case .good: return "checkmark.circle.fill"
        case .satisfactory: return "checkmark.circle"
        case .needsImprovement: return "exclamationmark.triangle.fill"
        case .poor: return "xmark.circle.fill"
        }
    }
    
    var emoji: String {
        switch self {
        case .excellent: return "🌟"
        case .good: return "😊"
        case .satisfactory: return "👍"
        case .needsImprovement: return "🤔"
        case .poor: return "😔"
        }
    }
}

@Model
final class LearningSession {
    var id: UUID
    var userID: UUID
    var startTime: Date
    var endTime: Date?
    var totalTimeSpent: Int // in seconds
    var lessonsCompleted: [UUID] // Lesson IDs
    var exercisesAttempted: Int
    var exercisesCompleted: Int
    var totalScore: Int
    var maxPossibleScore: Int
    var streakDay: Int?
    var language: Language?
    var sessionType: SessionType
    var interruptions: Int
    var notes: String?
    var mood: MoodLevel?
    var energyLevel: EnergyLevel?
    var environment: StudyEnvironment?
    var achievements: [AchievementType] = []
    var createdDate: Date
    
    init(
        id: UUID = UUID(),
        userID: UUID,
        startTime: Date = Date(),
        endTime: Date? = nil,
        totalTimeSpent: Int = 0,
        lessonsCompleted: [UUID] = [],
        exercisesAttempted: Int = 0,
        exercisesCompleted: Int = 0,
        totalScore: Int = 0,
        maxPossibleScore: Int = 0,
        streakDay: Int? = nil,
        language: Language? = nil,
        sessionType: SessionType = .regular,
        interruptions: Int = 0,
        notes: String? = nil,
        mood: MoodLevel? = nil,
        energyLevel: EnergyLevel? = nil,
        environment: StudyEnvironment? = nil,
        achievements: [AchievementType] = [],
        createdDate: Date = Date()
    ) {
        self.id = id
        self.userID = userID
        self.startTime = startTime
        self.endTime = endTime
        self.totalTimeSpent = totalTimeSpent
        self.lessonsCompleted = lessonsCompleted
        self.exercisesAttempted = exercisesAttempted
        self.exercisesCompleted = exercisesCompleted
        self.totalScore = totalScore
        self.maxPossibleScore = maxPossibleScore
        self.streakDay = streakDay
        self.language = language
        self.sessionType = sessionType
        self.interruptions = interruptions
        self.notes = notes
        self.mood = mood
        self.energyLevel = energyLevel
        self.environment = environment
        self.achievements = achievements
        self.createdDate = createdDate
    }
    
    var isActive: Bool {
        return endTime == nil
    }
    
    var duration: Int {
        if let endTime = endTime {
            return Int(endTime.timeIntervalSince(startTime))
        } else {
            return Int(Date().timeIntervalSince(startTime))
        }
    }
    
    var durationFormatted: String {
        let totalSeconds = duration
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
    
    var averageScore: Double {
        guard maxPossibleScore > 0 else { return 0 }
        return Double(totalScore) / Double(maxPossibleScore) * 100
    }
    
    var completionRate: Double {
        guard exercisesAttempted > 0 else { return 0 }
        return Double(exercisesCompleted) / Double(exercisesAttempted) * 100
    }
    
    var productivityScore: Double {
        let scoreWeight = 0.4
        let completionWeight = 0.3
        let timeWeight = 0.2
        let focusWeight = 0.1
        
        let normalizedScore = min(averageScore / 100.0, 1.0)
        let normalizedCompletion = min(completionRate / 100.0, 1.0)
        let normalizedTime = min(Double(totalTimeSpent) / 3600.0, 1.0) // Normalized to 1 hour
        let normalizedFocus = interruptions == 0 ? 1.0 : max(0, 1.0 - Double(interruptions) * 0.1)
        
        return (normalizedScore * scoreWeight +
                normalizedCompletion * completionWeight +
                normalizedTime * timeWeight +
                normalizedFocus * focusWeight) * 100
    }
    
    func endSession() {
        self.endTime = Date()
        self.totalTimeSpent = duration
    }
}

enum SessionType: String, CaseIterable, Codable {
    case regular = "regular"
    case intensive = "intensive"
    case review = "review"
    case challenge = "challenge"
    case cultural = "cultural"
    case pronunciation = "pronunciation"
    case grammar = "grammar"
    case vocabulary = "vocabulary"
    
    var displayName: String {
        switch self {
        case .regular: return "Regular Session"
        case .intensive: return "Intensive Session"
        case .review: return "Review Session"
        case .challenge: return "Challenge Session"
        case .cultural: return "Cultural Session"
        case .pronunciation: return "Pronunciation Focus"
        case .grammar: return "Grammar Focus"
        case .vocabulary: return "Vocabulary Focus"
        }
    }
    
    var icon: String {
        switch self {
        case .regular: return "book.fill"
        case .intensive: return "flame.fill"
        case .review: return "arrow.clockwise"
        case .challenge: return "target"
        case .cultural: return "globe"
        case .pronunciation: return "mic.fill"
        case .grammar: return "textformat"
        case .vocabulary: return "text.book.closed.fill"
        }
    }
}

enum MoodLevel: String, CaseIterable, Codable {
    case excellent = "excellent"
    case good = "good"
    case neutral = "neutral"
    case tired = "tired"
    case stressed = "stressed"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .neutral: return "Neutral"
        case .tired: return "Tired"
        case .stressed: return "Stressed"
        }
    }
    
    var emoji: String {
        switch self {
        case .excellent: return "😄"
        case .good: return "😊"
        case .neutral: return "😐"
        case .tired: return "😴"
        case .stressed: return "😰"
        }
    }
}

enum EnergyLevel: String, CaseIterable, Codable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var displayName: String {
        switch self {
        case .high: return "High Energy"
        case .medium: return "Medium Energy"
        case .low: return "Low Energy"
        }
    }
    
    var color: String {
        switch self {
        case .high: return "green"
        case .medium: return "orange"
        case .low: return "red"
        }
    }
}

enum StudyEnvironment: String, CaseIterable, Codable {
    case home = "home"
    case library = "library"
    case commute = "commute"
    case office = "office"
    case cafe = "cafe"
    case outdoor = "outdoor"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .home: return "Home"
        case .library: return "Library"
        case .commute: return "Commuting"
        case .office: return "Office"
        case .cafe: return "Café"
        case .outdoor: return "Outdoor"
        case .other: return "Other"
        }
    }
    
    var icon: String {
        switch self {
        case .home: return "house.fill"
        case .library: return "building.columns.fill"
        case .commute: return "car.fill"
        case .office: return "building.fill"
        case .cafe: return "cup.and.saucer.fill"
        case .outdoor: return "tree.fill"
        case .other: return "location.fill"
        }
    }
}

@Model
final class UserLearningData {
    var id: UUID
    var userId: UUID
    var preferredLanguages: [Language]
    var categoryPerformance: [String: Double] // LessonCategory.rawValue -> performance score
    var averageSessionDuration: Double
    var preferredTimeOfDay: String
    var strongestCategory: String
    var weakestCategory: String
    var learningVelocity: Double
    var retentionRate: Double
    var lastUpdated: Date
    
    init(
        id: UUID = UUID(),
        userId: UUID,
        preferredLanguages: [Language],
        categoryPerformance: [String: Double],
        averageSessionDuration: Double,
        preferredTimeOfDay: String,
        strongestCategory: String,
        weakestCategory: String,
        learningVelocity: Double,
        retentionRate: Double,
        lastUpdated: Date = Date()
    ) {
        self.id = id
        self.userId = userId
        self.preferredLanguages = preferredLanguages
        self.categoryPerformance = categoryPerformance
        self.averageSessionDuration = averageSessionDuration
        self.preferredTimeOfDay = preferredTimeOfDay
        self.strongestCategory = strongestCategory
        self.weakestCategory = weakestCategory
        self.learningVelocity = learningVelocity
        self.retentionRate = retentionRate
        self.lastUpdated = lastUpdated
    }
} 