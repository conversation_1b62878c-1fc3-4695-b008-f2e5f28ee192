import Foundation

// MARK: - Prompt Template
struct PromptTemplate: Identifiable, Codable {
    let id: String
    let name: String
    let category: PromptCategory
    let systemPrompt: String
    let userPromptTemplate: String
    let variables: [String]
    let tags: [String]
    let isActive: Bool
    let isCustom: Bool
    let parentPromptId: String?
    let createdAt: Date
    let updatedAt: Date
    
    init(
        id: String,
        name: String,
        category: PromptCategory,
        systemPrompt: String,
        userPromptTemplate: String,
        variables: [String] = [],
        tags: [String] = [],
        isActive: Bool = true,
        isCustom: Bool = false,
        parentPromptId: String? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.name = name
        self.category = category
        self.systemPrompt = systemPrompt
        self.userPromptTemplate = userPromptTemplate
        self.variables = variables
        self.tags = tags
        self.isActive = isActive
        self.isCustom = isCustom
        self.parentPromptId = parentPromptId
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - Prompt Categories
enum PromptCategory: String, CaseIterable, Codable {
    case learning = "learning"
    case conversation = "conversation"
    case writing = "writing"
    case cultural = "cultural"
    case assessment = "assessment"
    case feedback = "feedback"
    case creative = "creative"
    case technical = "technical"
    
    var displayName: String {
        switch self {
        case .learning: return "Learning Assistant"
        case .conversation: return "Conversation Practice"
        case .writing: return "Writing Coach"
        case .cultural: return "Cultural Guide"
        case .assessment: return "Assessment"
        case .feedback: return "Feedback"
        case .creative: return "Creative Writing"
        case .technical: return "Technical Support"
        }
    }
    
    var icon: String {
        switch self {
        case .learning: return "book.fill"
        case .conversation: return "bubble.left.and.bubble.right.fill"
        case .writing: return "pencil.and.outline"
        case .cultural: return "globe.asia.australia.fill"
        case .assessment: return "checkmark.circle.fill"
        case .feedback: return "star.fill"
        case .creative: return "paintbrush.fill"
        case .technical: return "gear.circle.fill"
        }
    }
}

// MARK: - Prompt Learning Scenarios
enum PromptLearningScenario: String, CaseIterable {
    case generalLearning = "general_learning"
    case conversationPractice = "conversation_practice"
    case writingPractice = "writing_practice"
    case culturalExploration = "cultural_exploration"

    var displayName: String {
        switch self {
        case .generalLearning: return "General Learning"
        case .conversationPractice: return "Conversation Practice"
        case .writingPractice: return "Writing Practice"
        case .culturalExploration: return "Cultural Exploration"
        }
    }
}

// MARK: - Prompt Proficiency Levels
enum PromptProficiencyLevel: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"

    var displayName: String {
        switch self {
        case .beginner: return "Beginner (A1-A2)"
        case .intermediate: return "Intermediate (B1-B2)"
        case .advanced: return "Advanced (C1-C2)"
        }
    }
}

// MARK: - Prompt User Progress
struct PromptUserProgress: Codable {
    let completedLessons: [String]
    let strengths: [String]
    let weakAreas: [String]
    let preferredLearningStyle: PromptLearningStyle
    let totalStudyTime: TimeInterval
    let lastActiveDate: Date

    enum PromptLearningStyle: String, Codable {
        case visual = "visual"
        case auditory = "auditory"
        case kinesthetic = "kinesthetic"
        case mixed = "mixed"
    }
}

// MARK: - Executable Prompt
struct ExecutablePrompt {
    let systemPrompt: String
    let userPrompt: String
    let metadata: PromptMetadata
}

// MARK: - Prompt Metadata
struct PromptMetadata {
    let templateId: String
    let templateName: String
    let category: PromptCategory
    let variables: [String: String]
    let executedAt: Date
}

// MARK: - Chat Interaction
struct ChatInteraction: Codable {
    let id: String
    let userMessage: String
    let aiResponse: String
    let promptTemplateId: String
    let timestamp: Date
    let userSatisfaction: Int? // 1-5 rating
    let responseTime: TimeInterval
    let contextTags: [String]
}

// MARK: - Interaction Analysis
struct InteractionAnalysis {
    let commonQuestionTypes: [PromptQuestionType]
    let difficultyAreas: [String]
    let successfulApproaches: [String]
    let userPreferences: [String]
}

enum PromptQuestionType: String, CaseIterable {
    case vocabulary = "vocabulary"
    case grammar = "grammar"
    case pronunciation = "pronunciation"
    case culture = "culture"
    case translation = "translation"
    case conversation = "conversation"
}

// MARK: - Prompt Optimization
struct PromptOptimization {
    let type: OptimizationType
    let description: String
    let implementation: String
    
    func apply(to prompt: String) -> String {
        switch type {
        case .addContext:
            return prompt + "\n\nADDITIONAL CONTEXT:\n\(implementation)"
        case .modifyTone:
            return prompt.replacingOccurrences(of: "You are", with: "You are \(implementation)")
        case .enhanceInstructions:
            return prompt + "\n\nENHANCED INSTRUCTIONS:\n\(implementation)"
        case .addExamples:
            return prompt + "\n\nEXAMPLES:\n\(implementation)"
        }
    }
}

enum OptimizationType: String, CaseIterable {
    case addContext = "add_context"
    case modifyTone = "modify_tone"
    case enhanceInstructions = "enhance_instructions"
    case addExamples = "add_examples"
}

// MARK: - Prompt Performance Metrics
struct PromptPerformanceMetrics {
    let templateId: String
    let totalUsages: Int
    let averageUserSatisfaction: Double
    let averageResponseTime: TimeInterval
    let successRate: Double
    let commonIssues: [String]
    let improvementSuggestions: [String]
}

// MARK: - Advanced Prompt Features
struct AdvancedPromptFeatures {
    let dynamicVariables: [DynamicVariable]
    let conditionalLogic: [ConditionalRule]
    let multiLanguageSupport: [LanguageVariant]
    let personalityTraits: [PersonalityTrait]
}

struct DynamicVariable {
    let name: String
    let type: VariableType
    let source: VariableSource
    let defaultValue: String?
}

enum VariableType: String, CaseIterable {
    case text = "text"
    case number = "number"
    case date = "date"
    case userProgress = "user_progress"
    case lessonContent = "lesson_content"
}

enum VariableSource: String, CaseIterable {
    case userInput = "user_input"
    case database = "database"
    case calculation = "calculation"
    case external = "external"
}

struct ConditionalRule {
    let condition: String
    let action: PromptAction
    let priority: Int
}

enum PromptAction: String, CaseIterable {
    case addSection = "add_section"
    case removeSection = "remove_section"
    case modifyTone = "modify_tone"
    case changeExample = "change_example"
}

struct LanguageVariant {
    let language: String
    let systemPrompt: String
    let userPromptTemplate: String
}

struct PersonalityTrait {
    let name: String
    let description: String
    let promptModification: String
    let intensity: Double // 0.0 to 1.0
}

// MARK: - Prompt Template Builder
class PromptTemplateBuilder {
    private var template = PromptTemplate(
        id: UUID().uuidString,
        name: "",
        category: .learning,
        systemPrompt: "",
        userPromptTemplate: ""
    )
    
    func withName(_ name: String) -> PromptTemplateBuilder {
        template = PromptTemplate(
            id: template.id,
            name: name,
            category: template.category,
            systemPrompt: template.systemPrompt,
            userPromptTemplate: template.userPromptTemplate,
            variables: template.variables,
            tags: template.tags,
            isActive: template.isActive,
            isCustom: template.isCustom,
            parentPromptId: template.parentPromptId,
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
        )
        return self
    }
    
    func withCategory(_ category: PromptCategory) -> PromptTemplateBuilder {
        template = PromptTemplate(
            id: template.id,
            name: template.name,
            category: category,
            systemPrompt: template.systemPrompt,
            userPromptTemplate: template.userPromptTemplate,
            variables: template.variables,
            tags: template.tags,
            isActive: template.isActive,
            isCustom: template.isCustom,
            parentPromptId: template.parentPromptId,
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
        )
        return self
    }
    
    func withSystemPrompt(_ systemPrompt: String) -> PromptTemplateBuilder {
        template = PromptTemplate(
            id: template.id,
            name: template.name,
            category: template.category,
            systemPrompt: systemPrompt,
            userPromptTemplate: template.userPromptTemplate,
            variables: template.variables,
            tags: template.tags,
            isActive: template.isActive,
            isCustom: template.isCustom,
            parentPromptId: template.parentPromptId,
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
        )
        return self
    }
    
    func withUserPromptTemplate(_ userPromptTemplate: String) -> PromptTemplateBuilder {
        template = PromptTemplate(
            id: template.id,
            name: template.name,
            category: template.category,
            systemPrompt: template.systemPrompt,
            userPromptTemplate: userPromptTemplate,
            variables: template.variables,
            tags: template.tags,
            isActive: template.isActive,
            isCustom: template.isCustom,
            parentPromptId: template.parentPromptId,
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
        )
        return self
    }
    
    func withVariables(_ variables: [String]) -> PromptTemplateBuilder {
        template = PromptTemplate(
            id: template.id,
            name: template.name,
            category: template.category,
            systemPrompt: template.systemPrompt,
            userPromptTemplate: template.userPromptTemplate,
            variables: variables,
            tags: template.tags,
            isActive: template.isActive,
            isCustom: template.isCustom,
            parentPromptId: template.parentPromptId,
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
        )
        return self
    }
    
    func withTags(_ tags: [String]) -> PromptTemplateBuilder {
        template = PromptTemplate(
            id: template.id,
            name: template.name,
            category: template.category,
            systemPrompt: template.systemPrompt,
            userPromptTemplate: template.userPromptTemplate,
            variables: template.variables,
            tags: tags,
            isActive: template.isActive,
            isCustom: template.isCustom,
            parentPromptId: template.parentPromptId,
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
        )
        return self
    }
    
    func build() -> PromptTemplate {
        return template
    }
}
