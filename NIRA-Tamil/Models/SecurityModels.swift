//
//  SecurityModels.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation

// MARK: - Security Error Types (Only unique types, avoiding duplicates)

// Note: ValidationError is defined in InputValidationService.swift

enum NetworkSecurityError: LocalizedError {
    case insecureScheme
    case untrustedDomain
    case certificateValidationFailed
    case httpError(Int)
    case invalidResponse
    case requestTimeout
    case rateLimitExceeded
    case fileTooLarge
    case invalidHost
    case localNetworkAccess
    case invalidMimeType
    case responseTooLarge
    case maxRetriesExceeded

    var errorDescription: String? {
        switch self {
        case .insecureScheme:
            return "Insecure connection scheme"
        case .untrustedDomain:
            return "Untrusted domain"
        case .certificateValidationFailed:
            return "Certificate validation failed"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .invalidResponse:
            return "Invalid response format"
        case .requestTimeout:
            return "Request timeout"
        case .rateLimitExceeded:
            return "Rate limit exceeded"
        case .fileTooLarge:
            return "File too large"
        case .invalidHost:
            return "Invalid host"
        case .localNetworkAccess:
            return "Local network access denied"
        case .invalidMimeType:
            return "Invalid MIME type"
        case .responseTooLarge:
            return "Response too large"
        case .maxRetriesExceeded:
            return "Maximum retries exceeded"
        }
    }
}

enum SecureStorageError: LocalizedError {
    case encryptionFailed
    case decryptionFailed
    case keyGenerationFailed
    case keychainError(OSStatus)
    case dataCorrupted
    case accessDenied
    case invalidData

    var errorDescription: String? {
        switch self {
        case .encryptionFailed:
            return "Data encryption failed"
        case .decryptionFailed:
            return "Data decryption failed"
        case .keyGenerationFailed:
            return "Encryption key generation failed"
        case .keychainError(let status):
            return "Keychain access error: \(status)"
        case .dataCorrupted:
            return "Stored data is corrupted"
        case .accessDenied:
            return "Access to secure storage denied"
        case .invalidData:
            return "Invalid data format"
        }
    }
}

// Note: AuthenticationError is defined in AuthenticationService.swift

// MARK: - Network HTTP Methods

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - User Permissions

enum UserPermission: String, CaseIterable {
    case aiChat = "ai_chat"
    case voiceRecording = "voice_recording"
    case fileUpload = "file_upload"
    case dataExport = "data_export"
    case adminAccess = "admin_access"

    var displayName: String {
        switch self {
        case .aiChat:
            return "AI Chat"
        case .voiceRecording:
            return "Voice Recording"
        case .fileUpload:
            return "File Upload"
        case .dataExport:
            return "Data Export"
        case .adminAccess:
            return "Admin Access"
        }
    }
}

// MARK: - Security Configuration Types

struct SecurityPolicy {
    let maxInputLength: Int
    let maxFileSize: Int64
    let allowedFileTypes: [String]
    let rateLimitRequests: Int
    let rateLimitWindow: TimeInterval
    let sessionTimeout: TimeInterval
    let maxFailedAttempts: Int
    let lockoutDuration: TimeInterval
}

struct EncryptionConfig {
    let algorithm: String
    let keySize: Int
    let ivSize: Int
    let saltSize: Int
}

// MARK: - Audit and Monitoring Types

struct SecurityAuditEvent {
    let id: UUID
    let timestamp: Date
    let eventType: SecurityEventType
    let severity: SecuritySeverity
    let description: String
    let userID: String?
    let ipAddress: String?
    let metadata: [String: Any]
}

enum SecurityEventType: String {
    case authentication = "authentication"
    case authorization = "authorization"
    case dataAccess = "data_access"
    case dataModification = "data_modification"
    case securityViolation = "security_violation"
    case systemError = "system_error"
}

enum SecuritySeverity: String {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
}

// MARK: - API Response Types

struct SecureAPIResponse<T: Codable>: Codable {
    let data: T?
    let success: Bool
    let message: String?
    let timestamp: Date
    let requestId: String
}

struct APIError: Codable, LocalizedError {
    let code: String
    let message: String
    let details: [String: String]?

    var errorDescription: String? {
        return message
    }
}

// Note: ErrorStatistics is defined in ErrorHandlingService.swift

// Note: AuthenticationError extensions are defined in AuthenticationService.swift