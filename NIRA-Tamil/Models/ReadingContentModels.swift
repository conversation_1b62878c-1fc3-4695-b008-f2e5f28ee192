import Foundation

// MARK: - Reading Content Models

/// Main model for reading content stored in Supabase
struct ReadingContent: Identifiable, Codable {
    let id: UUID
    let contentId: String
    let titleEnglish: String
    let titleTamil: String
    let titleRomanization: String?
    let category: ReadingCategory
    let difficultyLevel: CEFRLevel
    let contentTamil: String
    let contentRomanization: String?
    let contentTranslation: String?
    let audioUrl: String?
    let estimatedReadingTime: Int?
    let prerequisiteLessons: [String]
    let tags: [String]
    let culturalContext: String?
    let source: ReadingSource
    let sourceReference: String?
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case contentId = "content_id"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case titleRomanization = "title_romanization"
        case category
        case difficultyLevel = "difficulty_level"
        case contentTamil = "content_tamil"
        case contentRomanization = "content_romanization"
        case contentTranslation = "content_translation"
        case audioUrl = "audio_url"
        case estimatedReadingTime = "estimated_reading_time"
        case prerequisiteLessons = "prerequisite_lessons"
        case tags
        case culturalContext = "cultural_context"
        case source
        case sourceReference = "source_reference"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

/// Reading content categories
enum ReadingCategory: String, CaseIterable, Codable {
    case scriptBasics = "script_basics"
    case simpleReading = "simple_reading"
    case storyReading = "story_reading"
    case academicReading = "academic_reading"
    
    var displayName: String {
        switch self {
        case .scriptBasics:
            return "Script Basics"
        case .simpleReading:
            return "Simple Reading"
        case .storyReading:
            return "Story Reading"
        case .academicReading:
            return "Academic Reading"
        }
    }
    
    var description: String {
        switch self {
        case .scriptBasics:
            return "Learn Tamil letters, vowels, and basic script"
        case .simpleReading:
            return "Practice with simple words and sentences"
        case .storyReading:
            return "Read Tamil stories and cultural tales"
        case .academicReading:
            return "Advanced texts, poetry, and literature"
        }
    }
    
    var icon: String {
        switch self {
        case .scriptBasics:
            return "textformat.abc"
        case .simpleReading:
            return "book.fill"
        case .storyReading:
            return "book.pages.fill"
        case .academicReading:
            return "graduationcap.fill"
        }
    }
}

/// Reading content sources
enum ReadingSource: String, CaseIterable, Codable {
    case tnTextbook = "tn_textbook"
    case thirukkural = "thirukkural"
    case folkTale = "folk_tale"
    case lessonVocabulary = "lesson_vocabulary"
    case custom = "custom"
    
    var displayName: String {
        switch self {
        case .tnTextbook:
            return "Tamil Textbook"
        case .thirukkural:
            return "Thirukkural"
        case .folkTale:
            return "Folk Tale"
        case .lessonVocabulary:
            return "Lesson Vocabulary"
        case .custom:
            return "Custom Content"
        }
    }
}

/// Model for tracking user reading progress
struct ReadingProgress: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let contentId: String
    let readingTimeSeconds: Int
    let completionPercentage: Double
    let comprehensionScore: Int?
    let lastReadAt: Date
    let isCompleted: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case contentId = "content_id"
        case readingTimeSeconds = "reading_time_seconds"
        case completionPercentage = "completion_percentage"
        case comprehensionScore = "comprehension_score"
        case lastReadAt = "last_read_at"
        case isCompleted = "is_completed"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

/// Reading session data for tracking current reading
struct ReadingSession {
    let content: ReadingContent
    var startTime: Date
    var currentPosition: Int // Character position in content
    var readingSpeed: Double // Characters per minute
    var pausedTime: TimeInterval
    var isActive: Bool
    
    init(content: ReadingContent) {
        self.content = content
        self.startTime = Date()
        self.currentPosition = 0
        self.readingSpeed = 0.0
        self.pausedTime = 0.0
        self.isActive = true
    }
    
    var elapsedTime: TimeInterval {
        return Date().timeIntervalSince(startTime) - pausedTime
    }
    
    var completionPercentage: Double {
        guard content.contentTamil.count > 0 else { return 0.0 }
        return Double(currentPosition) / Double(content.contentTamil.count) * 100.0
    }
}

/// Reading statistics for analytics
struct ReadingStats {
    let totalReadingTime: TimeInterval
    let averageReadingSpeed: Double
    let completedContent: Int
    let totalContent: Int
    let favoriteCategory: ReadingCategory?
    let currentStreak: Int
    let longestStreak: Int
    
    var completionRate: Double {
        guard totalContent > 0 else { return 0.0 }
        return Double(completedContent) / Double(totalContent) * 100.0
    }
}

/// Reading preferences for user customization
struct ReadingPreferences: Codable {
    var fontSize: CGFloat
    var showRomanization: Bool
    var showTranslation: Bool
    var autoPlayAudio: Bool
    var preferredVoice: String // "male" or "female"
    var readingSpeed: Double // Playback speed multiplier
    var highlightCurrentLine: Bool
    
    static let `default` = ReadingPreferences(
        fontSize: 18.0,
        showRomanization: true,
        showTranslation: false,
        autoPlayAudio: false,
        preferredVoice: "female",
        readingSpeed: 1.0,
        highlightCurrentLine: true
    )
}

// MARK: - Extensions

extension ReadingContent {
    /// Check if content is unlocked based on prerequisite lessons
    func isUnlocked(completedLessons: [String]) -> Bool {
        return prerequisiteLessons.allSatisfy { completedLessons.contains($0) }
    }
    
    /// Get difficulty color for UI
    var difficultyColor: Color {
        return difficultyLevel.uiColor
    }
    
    /// Get estimated reading time display
    var readingTimeDisplay: String {
        guard let time = estimatedReadingTime else { return "Unknown" }
        return "\(time) min"
    }
    
    /// Get content preview (first 100 characters)
    var contentPreview: String {
        let maxLength = 100
        if contentTamil.count <= maxLength {
            return contentTamil
        }
        let endIndex = contentTamil.index(contentTamil.startIndex, offsetBy: maxLength)
        return String(contentTamil[..<endIndex]) + "..."
    }
}

extension ReadingProgress {
    /// Get completion status display
    var completionDisplay: String {
        if isCompleted {
            return "Completed"
        } else if completionPercentage > 0 {
            return "\(Int(completionPercentage))% Complete"
        } else {
            return "Not Started"
        }
    }
    
    /// Get reading time display
    var readingTimeDisplay: String {
        let minutes = readingTimeSeconds / 60
        let seconds = readingTimeSeconds % 60
        
        if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
}

// MARK: - Color Extension for CEFRLevel

extension CEFRLevel {
    var uiColor: Color {
        switch self {
        case .a1:
            return .green
        case .a2:
            return .blue
        case .b1:
            return .orange
        case .b2:
            return .purple
        case .c1:
            return .red
        case .c2:
            return .black
        }
    }
}

import SwiftUI

// MARK: - Color Import
// Note: Color is imported from SwiftUI above
