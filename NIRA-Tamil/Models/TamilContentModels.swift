//
//  TamilContentModels.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation

// MARK: - CEFR Tamil Lesson Models

struct TamilLesson: Codable, Identifiable {
    let id = UUID()
    let lessonNumber: Int
    let levelCode: String
    let titleEnglish: String
    let titleTamil: String
    let durationMinutes: Int
    let focus: String
    let vocabulary: [TamilVocabulary]
    let conversations: [TamilConversation]
    let grammar: [TamilGrammar]
    let practice: [TamilPractice]

    enum CodingKeys: String, CodingKey {
        case lessonNumber = "lesson_number"
        case levelCode = "level_code"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case durationMinutes = "duration_minutes"
        case focus
        case vocabulary
        case conversations
        case grammar
        case practice
    }
}

struct TamilVocabulary: Codable, Identifiable {
    let id = UUID()
    let vocabId: String
    let englishWord: String
    let tamilTranslation: String
    let romanization: String
    let ipa: String
    let audioWordUrl: String
    let exampleSentenceEnglish: String
    let exampleSentenceTamil: String
    let exampleSentenceRomanization: String
    let audioSentenceUrl: String
    let partOfSpeech: String
    let culturalNotes: String
    let relatedTerms: String
    
    enum CodingKeys: String, CodingKey {
        case vocabId = "vocab_id"
        case englishWord = "english_word"
        case tamilTranslation = "tamil_translation"
        case romanization
        case ipa
        case audioWordUrl = "audio_word_url"
        case exampleSentenceEnglish = "example_sentence_english"
        case exampleSentenceTamil = "example_sentence_tamil"
        case exampleSentenceRomanization = "example_sentence_romanization"
        case audioSentenceUrl = "audio_sentence_url"
        case partOfSpeech = "part_of_speech"
        case culturalNotes = "cultural_notes"
        case relatedTerms = "related_terms"
    }
}

struct TamilConversation: Codable, Identifiable {
    let id = UUID()
    let conversationId: String
    let titleEnglish: String
    let titleTamil: String
    let context: String
    let participants: String
    let formality: String
    let dialogue: [TamilDialogue]
    let educationalIntegration: String
    
    enum CodingKeys: String, CodingKey {
        case conversationId = "conversation_id"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case context
        case participants
        case formality
        case dialogue
        case educationalIntegration = "educational_integration"
    }
}

struct TamilDialogue: Codable, Identifiable {
    let id = UUID()
    let speaker: String
    let lineEnglish: String
    let lineTamil: String
    let lineRomanization: String
    let audioUrl: String

    enum CodingKeys: String, CodingKey {
        case speaker
        case lineEnglish = "line_english"
        case lineTamil = "line_tamil"
        case lineRomanization = "line_romanization"
        case audioUrl = "audio_url"
    }
}

struct TamilGrammar: Codable, Identifiable {
    let id = UUID()
    let grammarId: String
    let titleEnglish: String
    let titleTamil: String
    let ruleEnglish: String
    let ruleTamil: String
    let explanation: String
    let examples: [TamilGrammarExample]
    let tips: String
    let difficulty: String

    enum CodingKeys: String, CodingKey {
        case grammarId = "grammar_id"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case ruleEnglish = "rule_english"
        case ruleTamil = "rule_tamil"
        case explanation
        case examples
        case tips
        case difficulty
    }
}

struct TamilGrammarExample: Codable, Identifiable {
    let id = UUID()
    let exampleEnglish: String
    let exampleTamil: String
    let exampleRomanization: String
    let audioUrl: String
    let breakdown: String

    enum CodingKeys: String, CodingKey {
        case exampleEnglish = "example_english"
        case exampleTamil = "example_tamil"
        case exampleRomanization = "example_romanization"
        case audioUrl = "audio_url"
        case breakdown
    }
}

struct TamilPractice: Codable, Identifiable {
    let id = UUID()
    let practiceId: String
    let type: String
    let titleEnglish: String
    let titleTamil: String
    let instructionsEnglish: String
    let instructionsTamil: String
    let audioInstructionsUrl: String
    let exercises: [TamilPracticeExercise]
    let difficulty: String

    enum CodingKeys: String, CodingKey {
        case practiceId = "practice_id"
        case type
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case instructionsEnglish = "instructions_english"
        case instructionsTamil = "instructions_tamil"
        case audioInstructionsUrl = "audio_instructions_url"
        case exercises
        case difficulty
    }
}

struct TamilPracticeExercise: Codable, Identifiable {
    let id = UUID()
    let exerciseId: String
    let exerciseType: String // "multiple_choice", "true_false", "fill_blank", "match_following"
    let question: String
    let questionTamil: String
    let options: [String]
    let optionsTamil: [String]
    let correctAnswer: Int
    let correctAnswers: [Int]? // For multiple correct answers in matching
    let blanks: [String]? // For fill in the blanks
    let matchPairs: [MatchPair]? // For match the following
    let explanation: String
    let audioQuestionUrl: String
    let points: Int

    enum CodingKeys: String, CodingKey {
        case exerciseId = "exercise_id"
        case exerciseType = "exercise_type"
        case question
        case questionTamil = "question_tamil"
        case options
        case optionsTamil = "options_tamil"
        case correctAnswer = "correct_answer"
        case correctAnswers = "correct_answers"
        case blanks
        case matchPairs = "match_pairs"
        case explanation
        case audioQuestionUrl = "audio_question_url"
        case points
    }
}

struct MatchPair: Codable, Identifiable {
    let id = UUID()
    let left: String
    let leftTamil: String
    let right: String
    let rightTamil: String

    enum CodingKeys: String, CodingKey {
        case left
        case leftTamil = "left_tamil"
        case right
        case rightTamil = "right_tamil"
    }
}

// MARK: - Tamil Nadu Government Textbook Models

struct TNTextbook: Codable, Identifiable {
    let id: UUID
    let standard: Int
    let subject: String
    let pages: [String: String] // Page number to content mapping

    init(standard: Int, subject: String, pages: [String: String]) {
        self.id = UUID()
        self.standard = standard
        self.subject = subject
        self.pages = pages
    }
    
    var standardDisplay: String {
        return "Std \(standard)"
    }
    
    var pageCount: Int {
        return pages.count
    }
}

// MARK: - Content Level Enums

enum CEFRLevel: String, CaseIterable, Codable {
    case a1 = "A1"
    case a2 = "A2"
    case b1 = "B1"
    case b2 = "B2"
    case c1 = "C1"
    case c2 = "C2"
    
    var displayName: String {
        switch self {
        case .a1: return "Beginner (A1)"
        case .a2: return "Elementary (A2)"
        case .b1: return "Intermediate (B1)"
        case .b2: return "Upper Intermediate (B2)"
        case .c1: return "Advanced (C1)"
        case .c2: return "Proficient (C2)"
        }
    }
    
    var description: String {
        switch self {
        case .a1: return "Basic greetings, simple phrases"
        case .a2: return "Everyday expressions, personal information"
        case .b1: return "Travel, work, school situations"
        case .b2: return "Complex topics, abstract ideas"
        case .c1: return "Fluent, flexible language use"
        case .c2: return "Near-native proficiency"
        }
    }
    
    var color: String {
        switch self {
        case .a1: return "green"
        case .a2: return "blue"
        case .b1: return "orange"
        case .b2: return "purple"
        case .c1: return "red"
        case .c2: return "indigo"
        }
    }
}

enum ContentTrack: String, CaseIterable, Codable {
    case conversational = "conversational"
    case academic = "academic"

    var displayName: String {
        switch self {
        case .conversational: return "Conversational"
        case .academic: return "Academic"
        }
    }

    var description: String {
        switch self {
        case .conversational: return ""
        case .academic: return ""
        }
    }
}

// MARK: - Content Metadata

struct ContentMetadata: Codable {
    let totalLessons: Int
    let totalVocabulary: Int
    let totalConversations: Int
    let availableLevels: [CEFRLevel]
    let availableTracks: [ContentTrack]
    let lastUpdated: Date
    let contentVersion: String
}
