//
//  TamilCulturalModels.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation
import SwiftUI
import SwiftUI

// MARK: - Thirukkural Models

struct Thirukkural: Identifiable, Codable {
    let id: UUID
    let number: Int
    let chapter: String
    let chapterEnglish: String
    let tamilText: String
    let transliteration: String
    let englishTranslation: String
    let meaning: String
    let explanation: String
    let keywords: [String]
    let category: KuralCategory
    let section: KuralSection
    let modernRelevance: String
    let culturalContext: String
    let lifeApplication: String
    let relatedConcepts: [String]
    let difficulty: InsightDifficulty
    let audioURL: String?

    init(id: UUID = UUID(), number: Int, chapter: String, chapterEnglish: String, tamilText: String, transliteration: String, englishTranslation: String, meaning: String, explanation: String, keywords: [String], category: KuralCategory, section: KuralSection, modernRelevance: String, culturalContext: String, lifeApplication: String, relatedConcepts: [String], difficulty: InsightDifficulty, audioURL: String? = nil) {
        self.id = id
        self.number = number
        self.chapter = chapter
        self.chapterEnglish = chapterEnglish
        self.tamilText = tamilText
        self.transliteration = transliteration
        self.englishTranslation = englishTranslation
        self.meaning = meaning
        self.explanation = explanation
        self.keywords = keywords
        self.category = category
        self.section = section
        self.modernRelevance = modernRelevance
        self.culturalContext = culturalContext
        self.lifeApplication = lifeApplication
        self.relatedConcepts = relatedConcepts
        self.difficulty = difficulty
        self.audioURL = audioURL
    }
    
    init(number: Int, chapter: String, chapterEnglish: String, tamilText: String, transliteration: String, englishTranslation: String, meaning: String, explanation: String, keywords: [String], category: KuralCategory, section: KuralSection, modernRelevance: String, culturalContext: String, lifeApplication: String, relatedConcepts: [String], difficulty: InsightDifficulty, audioURL: String? = nil) {
        self.id = UUID()
        self.number = number
        self.chapter = chapter
        self.chapterEnglish = chapterEnglish
        self.tamilText = tamilText
        self.transliteration = transliteration
        self.englishTranslation = englishTranslation
        self.meaning = meaning
        self.explanation = explanation
        self.keywords = keywords
        self.category = category
        self.section = section
        self.modernRelevance = modernRelevance
        self.culturalContext = culturalContext
        self.lifeApplication = lifeApplication
        self.relatedConcepts = relatedConcepts
        self.difficulty = difficulty
        self.audioURL = audioURL
    }
}

enum KuralCategory: String, Codable, CaseIterable {
    case virtue = "virtue"
    case wealth = "wealth"
    case love = "love"
    
    var displayName: String {
        switch self {
        case .virtue: return "அறம் (Virtue)"
        case .wealth: return "பொருள் (Wealth)"
        case .love: return "காமம் (Love)"
        }
    }
}

enum KuralSection: String, Codable, CaseIterable {
    case dharma = "dharma"
    case artha = "artha"
    case kama = "kama"
}

// MARK: - Tamil Calendar Models

struct TamilEvent: Identifiable, Codable {
    let id: UUID
    let name: String
    let tamilName: String
    let date: Date
    let type: EventType
    let significance: String
    let description: String
    let traditions: [String]
    let modernCelebration: String
    let region: TamilRegion
    let isAuspicious: Bool
    let culturalImportance: CulturalImportance
    let learningContent: [LearningContent]
    
    init(id: UUID = UUID(), name: String, tamilName: String, date: Date, type: EventType, significance: String, description: String, traditions: [String], modernCelebration: String, region: TamilRegion, isAuspicious: Bool, culturalImportance: CulturalImportance, learningContent: [LearningContent]) {
        self.id = id
        self.name = name
        self.tamilName = tamilName
        self.date = date
        self.type = type
        self.significance = significance
        self.description = description
        self.traditions = traditions
        self.modernCelebration = modernCelebration
        self.region = region
        self.isAuspicious = isAuspicious
        self.culturalImportance = culturalImportance
        self.learningContent = learningContent
    }
}

enum EventType: String, Codable, CaseIterable {
    case festival = "festival"
    case harvest = "harvest"
    case religious = "religious"
    case cultural = "cultural"
    case seasonal = "seasonal"
    case historical = "historical"
}

enum TamilRegion: String, Codable, CaseIterable {
    case all = "all"
    case tamilNadu = "tamil_nadu"
    case sriLanka = "sri_lanka"
    case singapore = "singapore"
    case malaysia = "malaysia"
    case global = "global"
    
    var displayName: String {
        switch self {
        case .all: return "All Regions"
        case .tamilNadu: return "Tamil Nadu"
        case .sriLanka: return "Sri Lanka"
        case .singapore: return "Singapore"
        case .malaysia: return "Malaysia"
        case .global: return "Global"
        }
    }
}

enum CulturalImportance: String, Codable, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
}

struct LearningContent: Identifiable, Codable {
    let id: UUID
    let title: String
    let content: String
    let type: ContentType
    let difficulty: String

    init(id: UUID = UUID(), title: String, content: String, type: ContentType, difficulty: String) {
        self.id = id
        self.title = title
        self.content = content
        self.type = type
        self.difficulty = difficulty
    }
}

enum ContentType: String, Codable, CaseIterable {
    case vocabulary = "vocabulary"
    case culture = "culture"
    case history = "history"
    case tradition = "tradition"
    case language = "language"
}

// MARK: - Cultural Insights Models

enum CulturalCategory: String, Codable, CaseIterable {
    case music = "music"
    case food = "food"
    case architecture = "architecture"
    case literature = "literature"
    case festivals = "festivals"
    case arts = "arts"
    case traditions = "traditions"
    case philosophy = "philosophy"
    
    var displayName: String {
        switch self {
        case .music: return "இசை (Music)"
        case .food: return "உணவு (Food)"
        case .architecture: return "கட்டிடக்கலை (Architecture)"
        case .literature: return "இலக்கியம் (Literature)"
        case .festivals: return "திருவிழாக்கள் (Festivals)"
        case .arts: return "கலைகள் (Arts)"
        case .traditions: return "மரபுகள் (Traditions)"
        case .philosophy: return "தத்துவம் (Philosophy)"
        }
    }
    
    var icon: String {
        switch self {
        case .music: return "music.note"
        case .food: return "fork.knife"
        case .architecture: return "building.columns"
        case .literature: return "book"
        case .festivals: return "party.popper"
        case .arts: return "paintbrush"
        case .traditions: return "hands.sparkles"
        case .philosophy: return "brain.head.profile"
        }
    }
}

enum InsightDifficulty: String, Codable, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
    
    var color: Color {
        switch self {
        case .beginner: return .green
        case .intermediate: return .orange
        case .advanced: return .red
        }
    }
}

// MARK: - Tamil News Models

struct TamilNewsItem: Identifiable, Codable {
    let id: UUID
    let title: String
    let tamilTitle: String
    let summary: String
    let content: String
    let source: String
    let publishedAt: Date
    let category: NewsCategory
    let region: TamilRegion
    let imageURL: String?
    let tags: [String]
    let culturalRelevance: CulturalImportance
    let learningValue: LearningValue
    let isBreaking: Bool
    
    init(id: UUID = UUID(), title: String, tamilTitle: String, summary: String, content: String, source: String, publishedAt: Date, category: NewsCategory, region: TamilRegion, imageURL: String? = nil, tags: [String], culturalRelevance: CulturalImportance, learningValue: LearningValue, isBreaking: Bool = false) {
        self.id = id
        self.title = title
        self.tamilTitle = tamilTitle
        self.summary = summary
        self.content = content
        self.source = source
        self.publishedAt = publishedAt
        self.category = category
        self.region = region
        self.imageURL = imageURL
        self.tags = tags
        self.culturalRelevance = culturalRelevance
        self.learningValue = learningValue
        self.isBreaking = isBreaking
    }
}

enum NewsCategory: String, Codable, CaseIterable {
    case culture = "culture"
    case education = "education"
    case arts = "arts"
    case technology = "technology"
    case community = "community"
    case heritage = "heritage"
    case language = "language"
    
    var displayName: String {
        switch self {
        case .culture: return "Culture"
        case .education: return "Education"
        case .arts: return "Arts"
        case .technology: return "Technology"
        case .community: return "Community"
        case .heritage: return "Heritage"
        case .language: return "Language"
        }
    }
    
    var icon: String {
        switch self {
        case .culture: return "globe.asia.australia"
        case .education: return "graduationcap"
        case .arts: return "paintbrush.pointed"
        case .technology: return "laptopcomputer"
        case .community: return "person.3"
        case .heritage: return "building.columns"
        case .language: return "textformat.abc"
        }
    }
}

enum LearningValue: String, Codable, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        }
    }
}

// MARK: - Tamil Date Models

struct TamilDate: Codable {
    let tamilDay: Int
    let tamilMonth: TamilMonth
    let tamilYear: Int
    let season: TamilSeason
    let nakshatra: String
    let tithi: String
    
    var displayString: String {
        return "\(tamilDay) \(tamilMonth.tamilName) \(tamilYear)"
    }
}

enum TamilMonth: String, Codable, CaseIterable {
    case chithirai = "chithirai"
    case vaikasi = "vaikasi"
    case aani = "aani"
    case aadi = "aadi"
    case aavani = "aavani"
    case purattasi = "purattasi"
    case aippasi = "aippasi"
    case karthikai = "karthikai"
    case margazhi = "margazhi"
    case thai = "thai"
    case maasi = "maasi"
    case panguni = "panguni"
    
    var tamilName: String {
        switch self {
        case .chithirai: return "சித்திரை"
        case .vaikasi: return "வைகாசி"
        case .aani: return "ஆனி"
        case .aadi: return "ஆடி"
        case .aavani: return "ஆவணி"
        case .purattasi: return "புரட்டாசி"
        case .aippasi: return "ஐப்பசி"
        case .karthikai: return "கார்த்திகை"
        case .margazhi: return "மார்கழி"
        case .thai: return "தை"
        case .maasi: return "மாசி"
        case .panguni: return "பங்குனி"
        }
    }
}

enum TamilSeason: String, Codable, CaseIterable {
    case spring = "spring"
    case summer = "summer"
    case monsoon = "monsoon"
    case autumn = "autumn"
    case winter = "winter"
    case prewinter = "prewinter"
    
    var tamilName: String {
        switch self {
        case .spring: return "இளவேனில்"
        case .summer: return "முதுவேனில்"
        case .monsoon: return "கார்"
        case .autumn: return "குளிர்"
        case .winter: return "முன்பனி"
        case .prewinter: return "பின்பனி"
        }
    }
}
