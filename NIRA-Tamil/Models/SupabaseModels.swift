import Foundation
// import Supabase // Disabled for compilation

// MARK: - SupabaseAnyCodable for flexible JSON handling
struct SupabaseAnyCodable: Codable {
    let value: Any

    init(_ value: Any) {
        self.value = value
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let intValue = try? container.decode(Int.self) {
            value = intValue
        } else if let doubleValue = try? container.decode(Double.self) {
            value = doubleValue
        } else if let stringValue = try? container.decode(String.self) {
            value = stringValue
        } else if let boolValue = try? container.decode(Bool.self) {
            value = boolValue
        } else if let arrayValue = try? container.decode([SupabaseAnyCodable].self) {
            value = arrayValue.map { $0.value }
        } else if let dictValue = try? container.decode([String: SupabaseAnyCodable].self) {
            value = dictValue.mapValues { $0.value }
        } else {
            value = NSNull()
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        switch value {
        case let intValue as Int:
            try container.encode(intValue)
        case let doubleValue as Double:
            try container.encode(doubleValue)
        case let stringValue as String:
            try container.encode(stringValue)
        case let boolValue as Bool:
            try container.encode(boolValue)
        case let arrayValue as [Any]:
            let codableArray = arrayValue.map { SupabaseAnyCodable($0) }
            try container.encode(codableArray)
        case let dictValue as [String: Any]:
            let codableDict = dictValue.mapValues { SupabaseAnyCodable($0) }
            try container.encode(codableDict)
        default:
            try container.encodeNil()
        }
    }
}

// MARK: - FSRS Models

struct SupabaseCard: Identifiable, Codable {
    let id: UUID
    let itemId: UUID
    let state: String
    let stability: Double
    let difficulty: Double
    let elapsedDays: Int
    let scheduledDays: Int
    let reps: Int
    let lapses: Int
    let lastReview: Date?
    let due: Date
    let createdAt: Date
    let updatedAt: Date

    init(id: UUID = UUID(), itemId: UUID, state: String, stability: Double, difficulty: Double, elapsedDays: Int, scheduledDays: Int, reps: Int, lapses: Int, lastReview: Date?, due: Date, createdAt: Date = Date(), updatedAt: Date = Date()) {
        self.id = id
        self.itemId = itemId
        self.state = state
        self.stability = stability
        self.difficulty = difficulty
        self.elapsedDays = elapsedDays
        self.scheduledDays = scheduledDays
        self.reps = reps
        self.lapses = lapses
        self.lastReview = lastReview
        self.due = due
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

struct SupabaseReviewLog: Identifiable, Codable {
    let id: UUID
    let cardId: UUID
    let rating: Int
    let state: String
    let due: Date
    let stability: Double
    let difficulty: Double
    let elapsedDays: Int
    let lastElapsedDays: Int
    let scheduledDays: Int
    let reviewTime: Date
    let createdAt: Date

    init(id: UUID = UUID(), cardId: UUID, rating: Int, state: String, due: Date, stability: Double, difficulty: Double, elapsedDays: Int, lastElapsedDays: Int, scheduledDays: Int, reviewTime: Date = Date(), createdAt: Date = Date()) {
        self.id = id
        self.cardId = cardId
        self.rating = rating
        self.state = state
        self.due = due
        self.stability = stability
        self.difficulty = difficulty
        self.elapsedDays = elapsedDays
        self.lastElapsedDays = lastElapsedDays
        self.scheduledDays = scheduledDays
        self.reviewTime = reviewTime
        self.createdAt = createdAt
    }
}

// MARK: - Learning Companion Models

enum CompanionPersona: String, CaseIterable, Codable {
    case beginnerEnthusiast = "beginnerEnthusiast"
    case busyProfessional = "busyProfessional"
    case culturalSeeker = "culturalSeeker"
    case socialLearner = "socialLearner"
    case nriHelper = "nriHelper"
    case masterGuide = "masterGuide"
}

struct LearningCompanion: Identifiable, Codable {
    let id: UUID
    let name: String
    let persona: CompanionPersona
    let language: Language
    let avatar: String
    let description: String
    let systemPrompt: String
    let specialties: [String]
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    init(id: UUID = UUID(), name: String, persona: CompanionPersona, language: Language, avatar: String, description: String, systemPrompt: String, specialties: [String], isActive: Bool = true, createdAt: Date = Date(), updatedAt: Date = Date()) {
        self.id = id
        self.name = name
        self.persona = persona
        self.language = language
        self.avatar = avatar
        self.description = description
        self.systemPrompt = systemPrompt
        self.specialties = specialties
        self.isActive = isActive
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}
import SwiftUI

// MARK: - Database Models matching Supabase schema

// MARK: - User Models

struct SupabaseUserProfile: Codable, Identifiable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let preferredLanguages: [String]
    let createdAt: Date
    var lastActiveDate: Date
    let isEmailVerified: Bool
    let subscriptionTier: String
    let totalLessonsCompleted: Int
    let currentStreak: Int
    let longestStreak: Int
    let totalStudyTimeMinutes: Int

    enum CodingKeys: String, CodingKey {
        case id, email
        case firstName = "first_name"
        case lastName = "last_name"
        case preferredLanguages = "preferred_languages"
        case createdAt = "created_at"
        case lastActiveDate = "last_active_date"
        case isEmailVerified = "is_email_verified"
        case subscriptionTier = "subscription_tier"
        case totalLessonsCompleted = "total_lessons_completed"
        case currentStreak = "current_streak"
        case longestStreak = "longest_streak"
        case totalStudyTimeMinutes = "total_study_time_minutes"
    }
}

// MARK: - Language Models

struct SupabaseLanguageModel: Codable, Identifiable {
    let id: UUID
    let code: String
    let name: String
    let nativeName: String
    let isActive: Bool
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, code, name
        case nativeName = "native_name"
        case isActive = "is_active"
        case createdAt = "created_at"
    }
}

struct SupabaseLanguageLevel: Codable, Identifiable {
    let id: UUID
    let languageId: UUID
    let levelCode: String
    let levelName: String
    let description: String?
    let orderIndex: Int
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case languageId = "language_id"
        case levelCode = "level_code"
        case levelName = "level_name"
        case description
        case orderIndex = "order_index"
        case createdAt = "created_at"
    }
}

// MARK: - Topic Models

struct SupabaseTopic: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String?
    let iconName: String?
    let colorHex: String?
    let isActive: Bool
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description
        case iconName = "icon_name"
        case colorHex = "color_hex"
        case isActive = "is_active"
        case createdAt = "created_at"
    }
}

// MARK: - Learning Path Models

struct SupabaseLearningPath: Codable, Identifiable {
    let id: UUID
    let languageId: UUID
    let agentId: UUID
    let name: String
    let description: String
    let level: String
    let estimatedHours: Int?
    let sequenceOrder: Int?
    let prerequisites: [String]?
    let learningObjectives: [String]?
    let culturalFocus: [String]?
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description, level, prerequisites
        case languageId = "language_id"
        case agentId = "agent_id"
        case estimatedHours = "estimated_hours"
        case sequenceOrder = "sequence_order"
        case learningObjectives = "learning_objectives"
        case culturalFocus = "cultural_focus"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    // Memberwise initializer for creating instances directly
    init(
        id: UUID,
        languageId: UUID,
        agentId: UUID,
        name: String,
        description: String,
        level: String,
        estimatedHours: Int? = nil,
        sequenceOrder: Int? = nil,
        prerequisites: [String]? = nil,
        learningObjectives: [String]? = nil,
        culturalFocus: [String]? = nil,
        isActive: Bool,
        createdAt: Date,
        updatedAt: Date
    ) {
        self.id = id
        self.languageId = languageId
        self.agentId = agentId
        self.name = name
        self.description = description
        self.level = level
        self.estimatedHours = estimatedHours
        self.sequenceOrder = sequenceOrder
        self.prerequisites = prerequisites
        self.learningObjectives = learningObjectives
        self.culturalFocus = culturalFocus
        self.isActive = isActive
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // Custom initializer to handle null sequence_order gracefully
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(UUID.self, forKey: .id)
        languageId = try container.decode(UUID.self, forKey: .languageId)
        agentId = try container.decode(UUID.self, forKey: .agentId)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        level = try container.decode(String.self, forKey: .level)
        estimatedHours = try container.decodeIfPresent(Int.self, forKey: .estimatedHours)
        
        // Handle sequence_order with fallback for null values
        sequenceOrder = try container.decodeIfPresent(Int.self, forKey: .sequenceOrder)
        
        prerequisites = try container.decodeIfPresent([String].self, forKey: .prerequisites)
        learningObjectives = try container.decodeIfPresent([String].self, forKey: .learningObjectives)
        culturalFocus = try container.decodeIfPresent([String].self, forKey: .culturalFocus)
        isActive = try container.decode(Bool.self, forKey: .isActive)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
    }
}

// MARK: - Lesson Models

struct SupabaseLesson: Codable, Identifiable {
    let id: UUID
    let pathId: UUID
    let title: String
    let description: String?
    let lessonType: String
    let difficultyLevel: Int?
    let estimatedDuration: Int?
    let sequenceOrder: Int?
    let learningObjectives: [String]?
    let vocabularyFocus: [String]?
    let grammarConcepts: [String]?
    let culturalNotes: String?
    let prerequisiteLessons: [String]?
    let contentMetadata: SupabaseAnyCodable?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    // Audio support
    let audioUrl: String?
    let hasAudio: Bool?
    let audioMetadata: SupabaseAnyCodable?

    enum CodingKeys: String, CodingKey {
        case id
        case title = "title_english"  // Map to actual database column
        case description = "description_english"  // Map to actual database column
        case pathId = "path_id"
        case lessonType = "lesson_type"
        case difficultyLevel = "difficulty_score"  // Map to actual database column
        case estimatedDuration = "estimated_duration_minutes"  // Map to actual database column
        case sequenceOrder = "sequence_order"
        case learningObjectives = "learning_objectives"
        case vocabularyFocus = "vocabulary_focus"
        case grammarConcepts = "grammar_concepts"
        case culturalNotes = "cultural_notes"
        case prerequisiteLessons = "prerequisite_lessons"
        case contentMetadata = "content_metadata"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case audioUrl = "audio_url"
        case hasAudio = "has_audio"
        case audioMetadata = "audio_metadata"
    }

    // Memberwise initializer for creating instances directly
    init(
        id: UUID,
        pathId: UUID,
        title: String,
        description: String? = nil,
        lessonType: String,
        difficultyLevel: Int? = nil,
        estimatedDuration: Int? = nil,
        sequenceOrder: Int? = nil,
        learningObjectives: [String]? = nil,
        vocabularyFocus: [String]? = nil,
        grammarConcepts: [String]? = nil,
        culturalNotes: String? = nil,
        prerequisiteLessons: [String]? = nil,
        contentMetadata: SupabaseAnyCodable? = nil,
        isActive: Bool? = nil,
        createdAt: Date? = nil,
        updatedAt: Date? = nil,
        audioUrl: String? = nil,
        hasAudio: Bool? = nil,
        audioMetadata: SupabaseAnyCodable? = nil
    ) {
        self.id = id
        self.pathId = pathId
        self.title = title
        self.description = description
        self.lessonType = lessonType
        self.difficultyLevel = difficultyLevel
        self.estimatedDuration = estimatedDuration
        self.sequenceOrder = sequenceOrder
        self.learningObjectives = learningObjectives
        self.vocabularyFocus = vocabularyFocus
        self.grammarConcepts = grammarConcepts
        self.culturalNotes = culturalNotes
        self.prerequisiteLessons = prerequisiteLessons
        self.contentMetadata = contentMetadata
        self.isActive = isActive
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.audioUrl = audioUrl
        self.hasAudio = hasAudio
        self.audioMetadata = audioMetadata
    }

    // Custom initializer to handle null sequence_order gracefully
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(UUID.self, forKey: .id)
        pathId = try container.decode(UUID.self, forKey: .pathId)
        title = try container.decode(String.self, forKey: .title)
        description = try container.decodeIfPresent(String.self, forKey: .description)
        lessonType = try container.decode(String.self, forKey: .lessonType)
        difficultyLevel = try container.decodeIfPresent(Int.self, forKey: .difficultyLevel)
        estimatedDuration = try container.decodeIfPresent(Int.self, forKey: .estimatedDuration)
        
        // Handle sequence_order with fallback for null values
        sequenceOrder = try container.decodeIfPresent(Int.self, forKey: .sequenceOrder)
        
        learningObjectives = try container.decodeIfPresent([String].self, forKey: .learningObjectives)
        vocabularyFocus = try container.decodeIfPresent([String].self, forKey: .vocabularyFocus)
        grammarConcepts = try container.decodeIfPresent([String].self, forKey: .grammarConcepts)
        culturalNotes = try container.decodeIfPresent(String.self, forKey: .culturalNotes)
        prerequisiteLessons = try container.decodeIfPresent([String].self, forKey: .prerequisiteLessons)
        contentMetadata = try container.decodeIfPresent(SupabaseAnyCodable.self, forKey: .contentMetadata)
        isActive = try container.decodeIfPresent(Bool.self, forKey: .isActive)
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt)
        updatedAt = try container.decodeIfPresent(Date.self, forKey: .updatedAt)
        audioUrl = try container.decodeIfPresent(String.self, forKey: .audioUrl)
        hasAudio = try container.decodeIfPresent(Bool.self, forKey: .hasAudio)
        audioMetadata = try container.decodeIfPresent(SupabaseAnyCodable.self, forKey: .audioMetadata)
    }

    // Computed properties for UI
    var difficultyText: String {
        switch difficultyLevel ?? 1 {
        case 1: return "A1"
        case 2: return "A2"
        case 3: return "B1"
        case 4: return "B2"
        case 5: return "C1"
        case 6: return "C2"
        default: return "A1"
        }
    }

    var difficultyColor: Color {
        return Color.getLevelColor(for: difficultyLevel ?? 1)
    }

    var formattedDuration: String {
        let duration = estimatedDuration ?? 15
        let hours = duration / 60
        let minutes = duration % 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    // For backward compatibility with existing UI code
    var content: String {
        return description ?? ""
    }

    var languageName: String? {
        return nil // Will be populated by the service
    }
}

// MARK: - Tamil Lesson Specific Models

struct TamilSupabaseLesson: Codable, Identifiable {
    let id: String
    let lessonNumber: Int
    let levelCode: String
    let titleEnglish: String
    let titleTamil: String
    let titleRomanization: String?
    let descriptionEnglish: String?
    let descriptionTamil: String?
    let focus: String?
    let durationMinutes: Int
    let difficultyScore: Int
    let prerequisites: [String]?
    let tags: [String]?
    let culturalContext: String?
    let isActive: Bool
    let createdAt: String
    let updatedAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case lessonNumber = "lesson_number"
        case levelCode = "level_code"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case titleRomanization = "title_romanization"
        case descriptionEnglish = "description_english"
        case descriptionTamil = "description_tamil"
        case focus
        case durationMinutes = "duration_minutes"
        case difficultyScore = "difficulty_score"
        case prerequisites
        case tags
        case culturalContext = "cultural_context"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct TamilSupabaseVocabulary: Codable, Identifiable {
    let id: String
    let lessonId: String
    let vocabId: String
    let englishWord: String
    let tamilTranslation: String
    let romanization: String
    let ipaPronunciation: String?
    let partOfSpeech: String?
    let difficultyLevel: Int
    let frequencyRank: Int?
    let culturalNotes: String?
    let relatedTerms: [String]?
    let exampleSentenceEnglish: String?
    let exampleSentenceTamil: String?
    let exampleSentenceRomanization: String?
    let audioWordUrl: String?
    let audioSentenceUrl: String?
    let imageUrl: String?
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case lessonId = "lesson_id"
        case vocabId = "vocab_id"
        case englishWord = "english_word"
        case tamilTranslation = "tamil_translation"
        case romanization
        case ipaPronunciation = "ipa_pronunciation"
        case partOfSpeech = "part_of_speech"
        case difficultyLevel = "difficulty_level"
        case frequencyRank = "frequency_rank"
        case culturalNotes = "cultural_notes"
        case relatedTerms = "related_terms"
        case exampleSentenceEnglish = "example_sentence_english"
        case exampleSentenceTamil = "example_sentence_tamil"
        case exampleSentenceRomanization = "example_sentence_romanization"
        case audioWordUrl = "audio_word_url"
        case audioSentenceUrl = "audio_sentence_url"
        case imageUrl = "image_url"
        case createdAt = "created_at"
    }
}

struct TamilSupabaseConversation: Codable, Identifiable {
    let id: String
    let lessonId: String
    let conversationId: String
    let titleEnglish: String
    let titleTamil: String
    let contextDescription: String?
    let participants: String?
    let formalityLevel: String?
    let culturalSetting: String?
    let audioFullUrl: String?
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case lessonId = "lesson_id"
        case conversationId = "conversation_id"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case contextDescription = "context_description"
        case participants
        case formalityLevel = "formality_level"
        case culturalSetting = "cultural_setting"
        case audioFullUrl = "audio_full_url"
        case createdAt = "created_at"
    }
}

// MARK: - Conversation Line Model (Enhanced with Romanization & Pronunciation)
struct TamilSupabaseConversationLine: Codable, Identifiable {
    let id: String
    let conversationId: String
    let lineNumber: Int
    let speakerName: String
    let speakerRole: String // 'person1', 'person2', 'narrator'

    // Core Text Content
    let textEnglish: String
    let textTamil: String
    let textRomanized: String

    // Pronunciation Guides
    let pronunciationIpa: String?
    let pronunciationSimple: String

    // Audio Files
    let audioUrl: String?
    let audioSlowUrl: String?

    // Learning Metadata
    let difficultyLevel: Int
    let keyPhrases: [String]
    let grammarNotes: String?
    let culturalNotes: String?

    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case conversationId = "conversation_id"
        case lineNumber = "line_number"
        case speakerName = "speaker_name"
        case speakerRole = "speaker_role"
        case textEnglish = "text_english"
        case textTamil = "text_tamil"
        case textRomanized = "text_romanized"
        case pronunciationIpa = "pronunciation_ipa"
        case pronunciationSimple = "pronunciation_simple"
        case audioUrl = "audio_url"
        case audioSlowUrl = "audio_slow_url"
        case difficultyLevel = "difficulty_level"
        case keyPhrases = "key_phrases"
        case grammarNotes = "grammar_notes"
        case culturalNotes = "cultural_notes"
        case createdAt = "created_at"
    }
}

// MARK: - Conversation with Lines (Combined Model)
struct ConversationWithLines {
    let conversation: TamilSupabaseConversation
    let lines: [TamilSupabaseConversationLine]

    var totalLines: Int {
        lines.count
    }

    var speakers: [String] {
        Array(Set(lines.map { $0.speakerName })).sorted()
    }

    var averageDifficulty: Double {
        guard !lines.isEmpty else { return 0 }
        let total = lines.reduce(0) { $0 + $1.difficultyLevel }
        return Double(total) / Double(lines.count)
    }
}

struct TamilSupabaseGrammarTopic: Codable, Identifiable {
    let id: String
    let lessonId: String
    let grammarId: String
    let titleEnglish: String
    let titleTamil: String
    let titleRomanization: String?
    let ruleEnglish: String
    let ruleTamil: String
    let explanation: String?
    let difficultyLevel: Int
    let tips: [String]?
    let commonMistakes: [String]?
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case lessonId = "lesson_id"
        case grammarId = "grammar_id"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case titleRomanization = "title_romanization"
        case ruleEnglish = "rule_english"
        case ruleTamil = "rule_tamil"
        case explanation
        case difficultyLevel = "difficulty_level"
        case tips
        case commonMistakes = "common_mistakes"
        case createdAt = "created_at"
    }
}

struct TamilSupabaseGrammarExample: Codable, Identifiable {
    let id: String
    let grammarTopicId: String
    let exampleEnglish: String
    let exampleTamil: String
    let exampleRomanization: String
    let audioUrl: String?
    let exampleOrder: Int
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case grammarTopicId = "grammar_topic_id"
        case exampleEnglish = "example_english"
        case exampleTamil = "example_tamil"
        case exampleRomanization = "example_romanization"
        case audioUrl = "audio_url"
        case exampleOrder = "example_order"
        case createdAt = "created_at"
    }
}

struct TamilSupabasePracticeExercise: Codable, Identifiable {
    let id: String
    let lessonId: String
    let exerciseId: String
    let exerciseType: String
    let titleEnglish: String
    let titleTamil: String
    let instructionsEnglish: String
    let instructionsTamil: String
    let difficultyLevel: Int
    let pointsValue: Int
    let timeLimitSeconds: Int?
    let createdAt: String

    // NEW: Detailed exercise data for interactive practice
    var questionData: ExerciseQuestionData?

    enum CodingKeys: String, CodingKey {
        case id, lessonId = "lesson_id", exerciseId = "exercise_id"
        case exerciseType = "exercise_type", titleEnglish = "title_english"
        case titleTamil = "title_tamil", instructionsEnglish = "instructions_english"
        case instructionsTamil = "instructions_tamil", difficultyLevel = "difficulty_level"
        case pointsValue = "points_value", timeLimitSeconds = "time_limit_seconds"
        case createdAt = "created_at"
    }
}

// NEW: Detailed exercise question data for interactive practice
struct ExerciseQuestionData: Codable {
    let question: String
    let questionTamil: String
    let options: [String]
    let optionsTamil: [String]
    let optionsRomanization: [String]?  // NEW: Romanization for Tamil options (basic learners need this!)
    let optionsPronunciation: [String]? // NEW: Pronunciation guides for Tamil options
    let optionsAudioUrls: [String]?     // NEW: Audio URLs for Tamil options pronunciation
    let correctAnswer: Int
    let correctAnswers: [Int]? // For multiple correct answers
    let explanation: String
    let explanationTamil: String
    let audioQuestionUrl: String?
    let matchPairs: [ExerciseMatchPair]? // For matching exercises
    let fillBlanks: [String]? // For fill-in-the-blank exercises
}

struct ExerciseMatchPair: Codable {
    let left: String
    let leftTamil: String
    let right: String
    let rightTamil: String
}

// MARK: - Composite Models

struct CompleteTamilLesson {
    let lesson: TamilSupabaseLesson
    let vocabulary: [TamilSupabaseVocabulary]
    let conversations: [TamilSupabaseConversation]
    let grammarTopics: [TamilSupabaseGrammarTopic]
    let practiceExercises: [TamilSupabasePracticeExercise]
}

// MARK: - Extensions for Compatibility

extension TamilSupabaseLesson {
    /// Convert to existing TamilLesson format for compatibility
    func toTamilLesson() -> TamilLesson {
        return TamilLesson(
            lessonNumber: lessonNumber,
            levelCode: levelCode,
            titleEnglish: titleEnglish,
            titleTamil: titleTamil,
            durationMinutes: durationMinutes,
            focus: focus ?? "",
            vocabulary: [], // Will be populated separately
            conversations: [], // Will be populated separately
            grammar: [], // Will be populated separately
            practice: [] // Will be populated separately
        )
    }
}

extension TamilSupabaseVocabulary {
    /// Convert to existing TamilVocabulary format for compatibility
    func toTamilVocabulary() -> TamilVocabulary {
        return TamilVocabulary(
            vocabId: vocabId,
            englishWord: englishWord,
            tamilTranslation: tamilTranslation,
            romanization: romanization,
            ipa: ipaPronunciation ?? "/\(romanization)/",
            audioWordUrl: audioWordUrl ?? "",
            exampleSentenceEnglish: exampleSentenceEnglish ?? "",
            exampleSentenceTamil: exampleSentenceTamil ?? "",
            exampleSentenceRomanization: exampleSentenceRomanization ?? "",
            audioSentenceUrl: audioSentenceUrl ?? "",
            partOfSpeech: partOfSpeech ?? "",
            culturalNotes: culturalNotes ?? "",
            relatedTerms: relatedTerms?.joined(separator: ", ") ?? ""
        )
    }
}

struct SupabaseVocabularyItem: Codable, Identifiable {
    let id: UUID
    let word: String
    let translation: String
    let partOfSpeech: String?
    let context: String?
    let difficulty: String?
    let pronunciation: String?
    let example: String?
    let exampleTranslation: String?

    enum CodingKeys: String, CodingKey {
        case id, word, translation, context, difficulty, pronunciation, example
        case partOfSpeech = "part_of_speech"
        case exampleTranslation = "example_translation"
    }

    init(id: UUID = UUID(), word: String, translation: String, partOfSpeech: String? = nil, context: String? = nil, difficulty: String? = nil, pronunciation: String? = nil, example: String? = nil, exampleTranslation: String? = nil) {
        self.id = id
        self.word = word
        self.translation = translation
        self.partOfSpeech = partOfSpeech
        self.context = context
        self.difficulty = difficulty
        self.pronunciation = pronunciation
        self.example = example
        self.exampleTranslation = exampleTranslation
    }
}

struct SupabaseExercise: Codable, Identifiable {
    let id: UUID
    let type: String
    let question: String
    let options: [String]?
    let correctAnswer: String
    let explanation: String?
    let points: Int
    let difficulty: Int

    enum CodingKeys: String, CodingKey {
        case id, type, question, options, explanation, points, difficulty
        case correctAnswer = "correct_answer"
    }
}

// MARK: - Progress Models

struct SupabaseUserProgress: Codable, Identifiable {
    let id: UUID
    let userId: String
    let lessonId: UUID
    let status: String
    let score: Int?
    let timeSpentSeconds: Int
    let completedAt: Date?
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, status, score
        case userId = "user_id"
        case lessonId = "lesson_id"
        case timeSpentSeconds = "time_spent_seconds"
        case completedAt = "completed_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Learning Session Models

struct SupabaseLearningSession: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let lessonId: UUID
    let sessionType: SupabaseSessionType
    let startTime: Date
    let endTime: Date?
    let durationSeconds: Int?
    let interactionsData: SupabaseAnyCodable
    let performanceMetrics: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case lessonId = "lesson_id"
        case sessionType = "session_type"
        case startTime = "start_time"
        case endTime = "end_time"
        case durationSeconds = "duration_seconds"
        case interactionsData = "interactions_data"
        case performanceMetrics = "performance_metrics"
        case createdAt = "created_at"
    }
}

enum SupabaseSessionType: String, Codable, CaseIterable {
    case practice = "practice"
    case review = "review"
    case test = "test"

    var displayName: String {
        switch self {
        case .practice: return "Practice"
        case .review: return "Review"
        case .test: return "Test"
        }
    }
}

// MARK: - AI Agent Models

struct SupabaseAIAgent: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String?
    let personalityTraits: SupabaseAnyCodable
    let languageId: UUID
    let specializations: [String]
    let systemPrompt: String
    let avatarUrl: String?
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description
        case personalityTraits = "personality_traits"
        case languageId = "language_id"
        case specializations
        case systemPrompt = "system_prompt"
        case avatarUrl = "avatar_url"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Chat Models

struct SupabaseChatConversation: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let agentId: UUID
    let lessonId: UUID?
    let title: String?
    let status: SupabaseConversationStatus
    let metadata: SupabaseAnyCodable
    let createdAt: Date
    let updatedAt: Date

    // Optional relationship data
    var agent: SupabaseAIAgent?
    var lesson: SupabaseLesson?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case agentId = "agent_id"
        case lessonId = "lesson_id"
        case title, status, metadata
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case agent, lesson
    }
}

enum SupabaseConversationStatus: String, Codable, CaseIterable {
    case active = "active"
    case completed = "completed"
    case archived = "archived"
    case deleted = "deleted"

    var displayName: String {
        switch self {
        case .active: return "Active"
        case .completed: return "Completed"
        case .archived: return "Archived"
        case .deleted: return "Deleted"
        }
    }
}

struct SupabaseChatMessage: Codable, Identifiable {
    let id: UUID
    let conversationId: UUID
    let senderType: SupabaseSenderType
    let content: String
    let messageType: SupabaseMessageType
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case conversationId = "conversation_id"
        case senderType = "sender_type"
        case content
        case messageType = "message_type"
        case metadata
        case createdAt = "created_at"
    }
}

enum SupabaseSenderType: String, Codable, CaseIterable {
    case user = "user"
    case agent = "agent"

    var displayName: String {
        switch self {
        case .user: return "You"
        case .agent: return "AI"
        }
    }
}

enum SupabaseMessageType: String, Codable, CaseIterable {
    case text = "text"
    case audio = "audio"
    case image = "image"
    case lessonContent = "lesson_content"

    var displayName: String {
        switch self {
        case .text: return "Text"
        case .audio: return "Audio"
        case .image: return "Image"
        case .lessonContent: return "Lesson"
        }
    }
}

// MARK: - Vector Search Models

struct SupabaseLessonEmbedding: Codable, Identifiable {
    let id: UUID
    let lessonId: UUID
    let contentText: String
    let embedding: [Double]  // Vector embedding
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case lessonId = "lesson_id"
        case contentText = "content_text"
        case embedding, metadata
        case createdAt = "created_at"
    }
}

// MARK: - Cache Models

struct SupabaseContentCache: Codable, Identifiable {
    let id: UUID
    let cacheKey: String
    let contentData: SupabaseAnyCodable
    let contentType: String
    let expiresAt: Date?
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case cacheKey = "cache_key"
        case contentData = "content_data"
        case contentType = "content_type"
        case expiresAt = "expires_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Helper Types

// Type-erased codable for JSON fields - defined in SupabaseClient.swift

// MARK: - Extensions for UI

extension SupabaseUserProfile {
    var fullName: String {
        "\(firstName ?? "") \(lastName ?? "")"
    }

    var initials: String {
        let first = firstName?.prefix(1).uppercased() ?? ""
        let last = lastName?.prefix(1).uppercased() ?? ""
        return "\(first)\(last)"
    }
}

extension SupabaseLesson {
    var difficultyStars: String {
        String(repeating: "⭐", count: difficultyLevel ?? 1)
    }
}

extension SupabaseUserProgress {
    var progressPercentage: Int {
        switch status {
        case "completed": return 100
        case "in_progress": return 50
        case "started": return 25
        default: return 0
        }
    }

    var formattedTimeSpent: String {
        let hours = timeSpentSeconds / 3600
        let minutes = (timeSpentSeconds % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

extension SupabaseChatMessage {
    var isFromUser: Bool {
        senderType == .user
    }

    var isFromAgent: Bool {
        senderType == .agent
    }

    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
}

// MARK: - Type Aliases for backward compatibility

typealias UserProfile = SupabaseUserProfile
typealias LanguageModel = SupabaseLanguageModel
typealias LanguageLevel = SupabaseLanguageLevel
typealias Topic = SupabaseTopic
typealias UserProgress = SupabaseUserProgress
typealias AIAgent = SupabaseAIAgent
typealias ChatConversation = SupabaseChatConversation
typealias ChatMessage = SupabaseChatMessage
typealias LessonEmbedding = SupabaseLessonEmbedding
typealias ContentCache = SupabaseContentCache

// MARK: - Enhanced Knowledge Base Models

struct SupabaseKnowledgeBase: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let agentId: UUID?
    let language: String
    let fileName: String
    let fileType: String
    let fileSize: Int64
    let filePath: String
    let title: String?
    let description: String?
    let content: String?
    let extractedText: String?
    let metadata: SupabaseAnyCodable
    let tags: [String]
    let isProcessed: Bool
    let processingStatus: ProcessingStatus
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case agentId = "agent_id"
        case language
        case fileName = "file_name"
        case fileType = "file_type"
        case fileSize = "file_size"
        case filePath = "file_path"
        case title, description, content
        case extractedText = "extracted_text"
        case metadata, tags
        case isProcessed = "is_processed"
        case processingStatus = "processing_status"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

enum ProcessingStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case processing = "processing"
    case completed = "completed"
    case failed = "failed"

    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .processing: return "Processing"
        case .completed: return "Completed"
        case .failed: return "Failed"
        }
    }

    var color: Color {
        switch self {
        case .pending: return .orange
        case .processing: return .blue
        case .completed: return .green
        case .failed: return .red
        }
    }
}

// MARK: - Enhanced Conversation Models

struct SupabaseEnhancedConversation: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let agentId: UUID
    let agentName: String
    let language: String
    let title: String?
    let status: SupabaseConversationStatus
    let messageCount: Int
    let lastMessageAt: Date?
    let knowledgeBaseIds: [UUID]
    let metadata: SupabaseAnyCodable
    let createdAt: Date
    let updatedAt: Date

    // Optional relationship data
    var agent: SupabaseAIAgent?
    var messages: [SupabaseEnhancedMessage]?
    var knowledgeBase: [SupabaseKnowledgeBase]?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case agentId = "agent_id"
        case agentName = "agent_name"
        case language, title, status
        case messageCount = "message_count"
        case lastMessageAt = "last_message_at"
        case knowledgeBaseIds = "knowledge_base_ids"
        case metadata
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case agent, messages, knowledgeBase
    }
}

struct SupabaseEnhancedMessage: Codable, Identifiable {
    let id: UUID
    let conversationId: UUID
    let senderType: SupabaseSenderType
    let content: String
    let messageType: SupabaseMessageType
    let attachments: [SupabaseMessageAttachment]
    let voiceData: SupabaseVoiceData?
    let aiMetadata: SupabaseAIMetadata?
    let responseTime: Double?
    let confidence: Double?
    let grammarCorrections: [String]
    let culturalNotes: [String]
    let vocabularyHighlights: [SupabaseVocabularyHighlight]
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case conversationId = "conversation_id"
        case senderType = "sender_type"
        case content
        case messageType = "message_type"
        case attachments
        case voiceData = "voice_data"
        case aiMetadata = "ai_metadata"
        case responseTime = "response_time"
        case confidence
        case grammarCorrections = "grammar_corrections"
        case culturalNotes = "cultural_notes"
        case vocabularyHighlights = "vocabulary_highlights"
        case metadata
        case createdAt = "created_at"
    }
}

struct SupabaseMessageAttachment: Codable, Identifiable {
    let id: UUID
    let messageId: UUID
    let fileName: String
    let fileType: String
    let fileSize: Int64
    let filePath: String
    let thumbnailPath: String?
    let isProcessed: Bool
    let extractedText: String?
    let metadata: SupabaseAnyCodable
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case messageId = "message_id"
        case fileName = "file_name"
        case fileType = "file_type"
        case fileSize = "file_size"
        case filePath = "file_path"
        case thumbnailPath = "thumbnail_path"
        case isProcessed = "is_processed"
        case extractedText = "extracted_text"
        case metadata
        case createdAt = "created_at"
    }
}

struct SupabaseVoiceData: Codable {
    let audioPath: String
    let duration: Double
    let transcription: String?
    let language: String?
    let confidence: Double?
    let isProcessed: Bool

    enum CodingKeys: String, CodingKey {
        case audioPath = "audio_path"
        case duration, transcription, language, confidence
        case isProcessed = "is_processed"
    }
}

struct SupabaseAIMetadata: Codable {
    let model: String
    let temperature: Double?
    let maxTokens: Int?
    let promptTokens: Int?
    let completionTokens: Int?
    let totalTokens: Int?
    let finishReason: String?
    let processingTime: Double?

    enum CodingKeys: String, CodingKey {
        case model, temperature
        case maxTokens = "max_tokens"
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
        case finishReason = "finish_reason"
        case processingTime = "processing_time"
    }
}

struct SupabaseVocabularyHighlight: Codable, Identifiable {
    let id: UUID
    let word: String
    let definition: String
    let partOfSpeech: String?
    let difficulty: String?
    let language: String
    let culturalContext: String?

    enum CodingKeys: String, CodingKey {
        case id, word, definition
        case partOfSpeech = "part_of_speech"
        case difficulty, language
        case culturalContext = "cultural_context"
    }
}

// MARK: - Simulation Models

struct SupabaseSimulationPersona: Codable, Identifiable {
    let id: UUID
    let name: String
    let displayName: String
    let description: String
    let targetAudience: String
    let difficultyRange: String
    let colorTheme: String
    let iconName: String
    let learningObjectives: [String]?
    let typicalScenarios: [String]?
    let vocabularyFocus: [String]?
    let personalityTraits: SupabaseAnyCodable?
    let teachingStyle: String?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?
    let sortOrder: Int?

    enum CodingKeys: String, CodingKey {
        case id, name, description
        case displayName = "display_name"
        case targetAudience = "target_audience"
        case difficultyRange = "difficulty_range"
        case colorTheme = "color_theme"
        case iconName = "icon_name"
        case learningObjectives = "learning_objectives"
        case typicalScenarios = "typical_scenarios"
        case vocabularyFocus = "vocabulary_focus"
        case personalityTraits = "personality_traits"
        case teachingStyle = "teaching_style"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case sortOrder = "sort_order"
    }
}

struct SupabaseSimulation: Codable, Identifiable {
    let id: UUID
    let personaId: UUID
    let languageId: UUID
    let title: String
    let description: String
    let difficultyLevel: String
    let estimatedDuration: Int
    let scenarioType: String
    let learningObjectives: [String]?
    let vocabularyFocus: [String]?
    let conversationStarters: SupabaseAnyCodable?
    let successCriteria: SupabaseAnyCodable?
    let culturalNotes: String?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id, title, description
        case personaId = "persona_id"
        case languageId = "language_id"
        case difficultyLevel = "difficulty_level"
        case estimatedDuration = "estimated_duration"
        case scenarioType = "scenario_type"
        case learningObjectives = "learning_objectives"
        case vocabularyFocus = "vocabulary_focus"
        case conversationStarters = "conversation_starters"
        case successCriteria = "success_criteria"
        case culturalNotes = "cultural_notes"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Supabase Client Extensions

extension NIRASupabaseClient {
    // Note: client property is defined in SupabaseClient.swift

    func getLessons(language: String) async throws -> [SupabaseLesson] {
        // Convert language name to database code
        let languageCode = getLanguageCode(for: language)
        print("🔍 Converting language '\(language)' to code '\(languageCode)'")

        // Mock implementation for compilation
        let languageResponse: [SupabaseLanguageModel] = []

        guard languageResponse.first?.id != nil else {
            print("⚠️ Language '\(language)' (code: '\(languageCode)') not found in database")
            return []
        }

        // Mock implementation for compilation
        let pathsResponse: [SupabaseLearningPath] = []

        let pathIds = pathsResponse.map { $0.id.uuidString }

        guard !pathIds.isEmpty else {
            print("⚠️ No learning paths found for language '\(language)'")
            return []
        }

        // Mock implementation for compilation
        let response: [SupabaseLesson] = []

        print("✅ Loaded \(response.count) lessons for \(language) (\(languageCode))")
        return response
    }

    private func getLanguageCode(for language: String) -> String {
        // Convert from app language names to database language codes
        switch language.lowercased() {
        case "english": return "en"
        case "spanish": return "es"
        case "french": return "fr"
        case "german": return "de"
        case "italian": return "it"
        case "portuguese": return "pt"
        case "japanese": return "ja"
        case "korean": return "ko"
        case "chinese": return "zh"
        case "arabic": return "ar"
        case "hindi": return "hi"
        case "tamil": return "ta"
        case "telugu": return "te"
        case "vietnamese": return "vi"
        case "indonesian": return "id"
        default: return language.lowercased() // fallback to original
        }
    }

    func getLesson(id: UUID) async throws -> SupabaseLesson? {
        // Mock implementation for compilation
        return nil
    }

    func createLesson(_ lesson: SupabaseLesson) async throws {
        // Mock implementation for compilation
    }

    func updateLesson(_ lesson: SupabaseLesson) async throws {
        // Mock implementation for compilation
    }

    func deleteLesson(id: UUID) async throws {
        // Mock implementation for compilation
    }

    // MARK: - FSRS Methods

    func fetchReviewSchedule() async throws -> [SupabaseReviewLog] {
        // Mock implementation for compilation
        return []
    }

    func fetchCard(for itemId: UUID) async -> SupabaseCard? {
        // Mock implementation for compilation
        return nil
    }

    func saveCard(_ card: SupabaseCard) async throws {
        // Mock implementation for compilation
    }

    func saveReviewLog(_ log: SupabaseReviewLog) async throws {
        // Mock implementation for compilation
    }

    func fetchReviewLogs(lastDays: Int) async throws -> [SupabaseReviewLog] {
        // Mock implementation for compilation
        return []
    }

    // MARK: - Authentication Methods

    func signUp(email: String, password: String) async throws {
        // Mock implementation for compilation
    }

    func signIn(email: String, password: String) async throws {
        // Mock implementation for compilation
    }

    func signOut() async throws {
        // Mock implementation for compilation
    }

    func resetPassword(email: String) async throws {
        // Mock implementation for compilation
    }

    func updateUserProfile(userId: UUID, profileData: [String: AnyJSON]) async throws {
        // Mock implementation for compilation
    }
}

// MARK: - Auth Types

struct Auth {
    struct User: Identifiable {
        let id: UUID
        let email: String?
        let createdAt: Date
        let updatedAt: Date

        init(id: UUID = UUID(), email: String? = nil, createdAt: Date = Date(), updatedAt: Date = Date()) {
            self.id = id
            self.email = email
            self.createdAt = createdAt
            self.updatedAt = updatedAt
        }
    }
}

enum AuthError: Error {
    case invalidCredentials
    case userNotFound
    case emailAlreadyExists
    case weakPassword
    case networkError
    case unknown
}

typealias AnyJSON = Any

// MARK: - Content Upload Helper Types

struct SupabaseLessonContentData: Codable {
    let introduction: String
    let vocabulary: [SupabaseVocabularyItem]
    let dialogues: [SupabaseDialogueItem]
    let exercises: [SupabaseExerciseItem]
    let grammarPoints: [SupabaseGrammarPoint]
}

struct SupabaseLessonMetadata: Codable {
    let estimatedDuration: Int
    let skillFocus: [String]
    let culturalNotes: String
}

struct SupabaseDialogueItem: Codable, Identifiable {
    let id: UUID
    let speaker: String
    let text: String
    let translation: String
    let culturalNote: String?

    init(speaker: String, text: String, translation: String, culturalNote: String?) {
        self.id = UUID()
        self.speaker = speaker
        self.text = text
        self.translation = translation
        self.culturalNote = culturalNote
    }
}

struct SupabaseGrammarPoint: Codable, Identifiable {
    let id: UUID
    let rule: String
    let explanation: String
    let examples: [String]
    let tips: String

    init(rule: String, explanation: String, examples: [String], tips: String) {
        self.id = UUID()
        self.rule = rule
        self.explanation = explanation
        self.examples = examples
        self.tips = tips
    }
}

struct SupabaseExerciseItem: Codable, Identifiable {
    let id: UUID
    let type: String
    let question: String
    let options: [String]?
    let correctAnswer: SupabaseAnyCodable
    let explanation: String
    let hints: [String]?
    let points: Int
    let pairs: [ExercisePair]?

    init(type: String, question: String, options: [String]?, correctAnswer: Int, explanation: String, hints: [String]?, points: Int, pairs: [SupabaseExercisePair]?) {
        self.id = UUID()
        self.type = type
        self.question = question
        self.options = options
        self.correctAnswer = SupabaseAnyCodable(correctAnswer)
        self.explanation = explanation
        self.hints = hints
        self.points = points
        self.pairs = pairs?.map { ExercisePair(left: $0.left, right: $0.right) }
    }
}

struct ExercisePair: Codable {
    let left: String
    let right: String
}

struct SupabaseExercisePair: Codable {
    let left: String
    let right: String
}

// MARK: - Helper Types
// Note: SupabaseError and SupabaseAnyCodable are defined in SupabaseClient.swift to avoid conflicts

