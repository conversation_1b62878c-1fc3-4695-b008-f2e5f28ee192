import Foundation
import CoreLocation

// MARK: - Core Panchang Models

struct DailyPanchang: Codable, Identifiable {
    let id: UUID
    let date: Date
    let location: LocationInfo
    let tamilDate: PanchangTamilDate
    let sunTimes: SunTimes
    let moonTimes: MoonTimes?
    let tithi: Tithi
    let nakshatra: <PERSON><PERSON><PERSON>ra
    let yoga: Yoga
    let karana: <PERSON>na
    let lunarMonth: LunarMonth
    let season: Season
    let muhurat: [Muhurat]
    let inauspiciousTimes: [InauspiciousTime]
    let festivals: [TamilFestival]
    let significance: String?

    // Custom date decoding to handle different date formats
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        location = try container.decode(LocationInfo.self, forKey: .location)
        tamilDate = try container.decode(PanchangTamilDate.self, forKey: .tamilDate)
        sunTimes = try container.decode(SunTimes.self, forKey: .sunTimes)
        moonTimes = try container.decodeIfPresent(MoonTimes.self, forKey: .moonTimes)
        tithi = try container.decode(Tithi.self, forKey: .tithi)
        nakshatra = try container.decode(Naks<PERSON>ra.self, forKey: .nakshatra)
        yoga = try container.decode(Yoga.self, forKey: .yoga)
        karana = try container.decode(Karana.self, forKey: .karana)
        lunarMonth = try container.decode(LunarMonth.self, forKey: .lunarMonth)
        season = try container.decode(Season.self, forKey: .season)
        muhurat = try container.decode([Muhurat].self, forKey: .muhurat)
        inauspiciousTimes = try container.decode([InauspiciousTime].self, forKey: .inauspiciousTimes)
        festivals = try container.decode([TamilFestival].self, forKey: .festivals)
        significance = try container.decodeIfPresent(String.self, forKey: .significance)

        // Handle different date formats
        if let dateString = try? container.decode(String.self, forKey: .date) {
            // Try different date formats with proper timezone handling
            let formatters: [DateFormatter] = [
                {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd"
                    formatter.timeZone = TimeZone(identifier: "UTC")
                    return formatter
                }(),
                {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                    formatter.timeZone = TimeZone(identifier: "UTC")
                    return formatter
                }(),
                {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
                    return formatter
                }()
            ]

            var parsedDate: Date?
            for formatter in formatters {
                parsedDate = formatter.date(from: dateString)
                if parsedDate != nil { break }
            }

            // Also try ISO8601 formatter as fallback
            if parsedDate == nil {
                let isoFormatter = ISO8601DateFormatter()
                parsedDate = isoFormatter.date(from: dateString)
            }

            date = parsedDate ?? Date()
        } else {
            date = try container.decode(Date.self, forKey: .date)
        }

        // Initialize timestamp fields
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? Date()
        updatedAt = try container.decodeIfPresent(Date.self, forKey: .updatedAt) ?? Date()
    }

    let createdAt: Date
    let updatedAt: Date

    init(id: UUID = UUID(), date: Date, location: LocationInfo, tamilDate: PanchangTamilDate, sunTimes: SunTimes, moonTimes: MoonTimes? = nil, tithi: Tithi, nakshatra: Nakshatra, yoga: Yoga, karana: Karana, lunarMonth: LunarMonth, season: Season, muhurat: [Muhurat] = [], inauspiciousTimes: [InauspiciousTime] = [], festivals: [TamilFestival] = [], significance: String? = nil, createdAt: Date = Date(), updatedAt: Date = Date()) {
        self.id = id
        self.date = date
        self.location = location
        self.tamilDate = tamilDate
        self.sunTimes = sunTimes
        self.moonTimes = moonTimes
        self.tithi = tithi
        self.nakshatra = nakshatra
        self.yoga = yoga
        self.karana = karana
        self.lunarMonth = lunarMonth
        self.season = season
        self.muhurat = muhurat
        self.inauspiciousTimes = inauspiciousTimes
        self.festivals = festivals
        self.significance = significance
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - Location Info

struct LocationInfo: Codable {
    let latitude: Double
    let longitude: Double
    let timezone: Double
    let cityName: String?
    let countryName: String?
    
    init(latitude: Double, longitude: Double, timezone: Double, cityName: String? = nil, countryName: String? = nil) {
        self.latitude = latitude
        self.longitude = longitude
        self.timezone = timezone
        self.cityName = cityName
        self.countryName = countryName
    }
    
    init(from location: CLLocation, timezone: Double = 5.5) {
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.timezone = timezone
        self.cityName = nil
        self.countryName = nil
    }
}

// MARK: - Panchang Tamil Date

struct PanchangTamilDate: Codable {
    let day: Int
    let month: PanchangTamilMonth
    let year: Int
    let paksha: Paksha
    let season: PanchangTamilSeason
    let era: TamilEra

    var displayString: String {
        return "\(month.tamilName) \(day), \(year)"
    }

    var shortDisplayString: String {
        return "\(month.tamilShortName) \(day)"
    }
}

enum PanchangTamilMonth: String, Codable, CaseIterable {
    case chithirai = "chithirai"
    case vaikasi = "vaikasi"
    case aani = "aani"
    case aadi = "aadi"
    case aavani = "aavani"
    case purattasi = "purattasi"
    case aippasi = "aippasi"
    case karthigai = "karthigai"
    case margazhi = "margazhi"
    case thai = "thai"
    case maasi = "maasi"
    case panguni = "panguni"
    
    var tamilName: String {
        switch self {
        case .chithirai: return "சித்திரை"
        case .vaikasi: return "வைகாசி"
        case .aani: return "ஆனி"
        case .aadi: return "ஆடி"
        case .aavani: return "ஆவணி"
        case .purattasi: return "புரட்டாசி"
        case .aippasi: return "ஐப்பசி"
        case .karthigai: return "கார்த்திகை"
        case .margazhi: return "மார்கழி"
        case .thai: return "தை"
        case .maasi: return "மாசி"
        case .panguni: return "பங்குனி"
        }
    }
    
    var tamilShortName: String {
        switch self {
        case .chithirai: return "சித்"
        case .vaikasi: return "வை"
        case .aani: return "ஆனி"
        case .aadi: return "ஆடி"
        case .aavani: return "ஆவ"
        case .purattasi: return "புர"
        case .aippasi: return "ஐப்"
        case .karthigai: return "கார்"
        case .margazhi: return "மார்"
        case .thai: return "தை"
        case .maasi: return "மாசி"
        case .panguni: return "பங்"
        }
    }
    
    var englishName: String {
        return rawValue.capitalized
    }
}

enum Paksha: String, Codable, CaseIterable {
    case shukla = "shukla"
    case krishna = "krishna"
    
    var tamilName: String {
        switch self {
        case .shukla: return "சுக்ல பக்ஷம்"
        case .krishna: return "கிருஷ்ண பக்ஷம்"
        }
    }
    
    var englishName: String {
        switch self {
        case .shukla: return "Waxing Moon"
        case .krishna: return "Waning Moon"
        }
    }
}

enum PanchangTamilSeason: String, Codable, CaseIterable {
    case spring = "spring"
    case summer = "summer"
    case monsoon = "monsoon"
    case autumn = "autumn"
    case winter = "winter"
    case prewinter = "prewinter"
    
    var tamilName: String {
        switch self {
        case .spring: return "வசந்த காலம்"
        case .summer: return "கோடை காலம்"
        case .monsoon: return "மழை காலம்"
        case .autumn: return "இலையுதிர் காலம்"
        case .winter: return "குளிர் காலம்"
        case .prewinter: return "முன் குளிர் காலம்"
        }
    }
}

enum TamilEra: String, Codable, CaseIterable {
    case sakaEra = "saka_era"
    case vikramEra = "vikram_era"
    case kaliYuga = "kali_yuga"
    
    var tamilName: String {
        switch self {
        case .sakaEra: return "சக வருடம்"
        case .vikramEra: return "விக்ரம வருடம்"
        case .kaliYuga: return "கலியுகம்"
        }
    }
}

// MARK: - Time Information

struct SunTimes: Codable {
    let sunrise: Date
    let sunset: Date
    let noon: Date?
    let twilightBegin: Date?
    let twilightEnd: Date?

    var dayLength: TimeInterval {
        return sunset.timeIntervalSince(sunrise)
    }

    var formattedSunrise: String {
        return DateFormatter.timeFormatter.string(from: sunrise)
    }

    var formattedSunset: String {
        return DateFormatter.timeFormatter.string(from: sunset)
    }

    enum CodingKeys: String, CodingKey {
        case sunrise, sunset, noon, twilightBegin, twilightEnd
    }

    // Regular initializer
    init(sunrise: Date, sunset: Date, noon: Date? = nil, twilightBegin: Date? = nil, twilightEnd: Date? = nil) {
        self.sunrise = sunrise
        self.sunset = sunset
        self.noon = noon
        self.twilightBegin = twilightBegin
        self.twilightEnd = twilightEnd
    }

    // Custom decoding to handle both Unix timestamps and ISO date strings
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        sunrise = try Self.decodeFlexibleDate(from: container, forKey: .sunrise)
        sunset = try Self.decodeFlexibleDate(from: container, forKey: .sunset)
        noon = try Self.decodeFlexibleDateIfPresent(from: container, forKey: .noon)
        twilightBegin = try Self.decodeFlexibleDateIfPresent(from: container, forKey: .twilightBegin)
        twilightEnd = try Self.decodeFlexibleDateIfPresent(from: container, forKey: .twilightEnd)
    }

    private static func decodeFlexibleDate(from container: KeyedDecodingContainer<CodingKeys>, forKey key: CodingKeys) throws -> Date {
        // Try to decode as Date first (Unix timestamp)
        if let date = try? container.decode(Date.self, forKey: key) {
            return date
        }

        // Try to decode as String (ISO format)
        if let dateString = try? container.decode(String.self, forKey: key) {
            return try parseISODateString(dateString)
        }

        throw DecodingError.dataCorrupted(DecodingError.Context(
            codingPath: container.codingPath + [key],
            debugDescription: "Unable to decode date from any supported format"
        ))
    }

    private static func decodeFlexibleDateIfPresent(from container: KeyedDecodingContainer<CodingKeys>, forKey key: CodingKeys) throws -> Date? {
        // Try to decode as Date first (Unix timestamp)
        if let date = try? container.decodeIfPresent(Date.self, forKey: key) {
            return date
        }

        // Try to decode as String (ISO format)
        if let dateString = try? container.decodeIfPresent(String.self, forKey: key) {
            return dateString != nil ? try parseISODateString(dateString) : nil
        }

        return nil
    }

    static func parseISODateString(_ dateString: String) throws -> Date {
        // Try ISO8601 formatter first
        let isoFormatter = ISO8601DateFormatter()
        if let date = isoFormatter.date(from: dateString) {
            return date
        }

        // Try custom formatters for different ISO variations
        let formatters: [DateFormatter] = [
            {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
                return formatter
            }(),
            {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                formatter.timeZone = TimeZone(identifier: "UTC")
                return formatter
            }(),
            {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
                return formatter
            }()
        ]

        for formatter in formatters {
            if let date = formatter.date(from: dateString) {
                return date
            }
        }

        throw DecodingError.dataCorrupted(DecodingError.Context(
            codingPath: [],
            debugDescription: "Unable to parse date string: \(dateString)"
        ))
    }
}

struct MoonTimes: Codable {
    let moonrise: Date?
    let moonset: Date?
    let phase: MoonPhase
    let illumination: Double // 0.0 to 1.0

    var formattedMoonrise: String? {
        guard let moonrise = moonrise else { return nil }
        return DateFormatter.timeFormatter.string(from: moonrise)
    }

    var formattedMoonset: String? {
        guard let moonset = moonset else { return nil }
        return DateFormatter.timeFormatter.string(from: moonset)
    }

    enum CodingKeys: String, CodingKey {
        case moonrise, moonset, phase, illumination
    }

    // Regular initializer
    init(moonrise: Date? = nil, moonset: Date? = nil, phase: MoonPhase = .newMoon, illumination: Double = 0.0) {
        self.moonrise = moonrise
        self.moonset = moonset
        self.phase = phase
        self.illumination = illumination
    }

    // Custom decoding to handle both Unix timestamps and ISO date strings
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        moonrise = try Self.decodeFlexibleDateIfPresent(from: container, forKey: .moonrise)
        moonset = try Self.decodeFlexibleDateIfPresent(from: container, forKey: .moonset)

        // Provide default values for missing fields
        phase = try container.decodeIfPresent(MoonPhase.self, forKey: .phase) ?? .newMoon
        illumination = try container.decodeIfPresent(Double.self, forKey: .illumination) ?? 0.0
    }

    private static func decodeFlexibleDateIfPresent(from container: KeyedDecodingContainer<CodingKeys>, forKey key: CodingKeys) throws -> Date? {
        // Try to decode as Date first (Unix timestamp)
        if let date = try? container.decodeIfPresent(Date.self, forKey: key) {
            return date
        }

        // Try to decode as String (ISO format)
        if let dateString = try? container.decodeIfPresent(String.self, forKey: key) {
            return dateString != nil ? try SunTimes.parseISODateString(dateString) : nil
        }

        return nil
    }
}

enum MoonPhase: String, Codable, CaseIterable {
    case newMoon = "new_moon"
    case waxingCrescent = "waxing_crescent"
    case firstQuarter = "first_quarter"
    case waxingGibbous = "waxing_gibbous"
    case fullMoon = "full_moon"
    case waningGibbous = "waning_gibbous"
    case lastQuarter = "last_quarter"
    case waningCrescent = "waning_crescent"
    
    var tamilName: String {
        switch self {
        case .newMoon: return "அமாவாசை"
        case .waxingCrescent: return "வளர்பிறை"
        case .firstQuarter: return "சதுர்த்தி"
        case .waxingGibbous: return "வளர்பிறை"
        case .fullMoon: return "பூர்ணிமை"
        case .waningGibbous: return "தேய்பிறை"
        case .lastQuarter: return "சதுர்த்தி"
        case .waningCrescent: return "தேய்பிறை"
        }
    }
    
    var emoji: String {
        switch self {
        case .newMoon: return "🌑"
        case .waxingCrescent: return "🌒"
        case .firstQuarter: return "🌓"
        case .waxingGibbous: return "🌔"
        case .fullMoon: return "🌕"
        case .waningGibbous: return "🌖"
        case .lastQuarter: return "🌗"
        case .waningCrescent: return "🌘"
        }
    }

    static func fromString(_ phase: String) -> MoonPhase {
        switch phase.lowercased() {
        case "new", "new moon", "new_moon":
            return .newMoon
        case "waxing crescent", "waxing_crescent":
            return .waxingCrescent
        case "first quarter", "first_quarter":
            return .firstQuarter
        case "waxing gibbous", "waxing_gibbous":
            return .waxingGibbous
        case "full", "full moon", "full_moon":
            return .fullMoon
        case "waning gibbous", "waning_gibbous":
            return .waningGibbous
        case "last quarter", "last_quarter":
            return .lastQuarter
        case "waning crescent", "waning_crescent":
            return .waningCrescent
        default:
            return .newMoon
        }
    }
}

// MARK: - Panchang Elements

struct Tithi: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let paksha: Paksha
    let completionTime: Date?
    let percentage: Double
    let lord: String?
    let significance: String?
    
    var displayName: String {
        return "\(name) (\(tamilName))"
    }
    
    var isAuspicious: Bool {
        // Certain tithis are considered more auspicious
        let auspiciousTithis = [1, 3, 5, 7, 10, 11, 13, 15] // Pratipada, Tritiya, Panchami, etc.
        return auspiciousTithis.contains(number)
    }
}

struct Nakshatra: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let lord: String
    let tamilLord: String
    let startTime: Date
    let endTime: Date
    let percentage: Double
    let pada: Int?
    let significance: String?
    
    var displayName: String {
        return "\(name) (\(tamilName))"
    }
    
    var lordDisplayName: String {
        return "\(lord) (\(tamilLord))"
    }
}

struct Yoga: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let completionTime: Date?
    let percentage: Double?
    let significance: String?
    
    var displayName: String {
        return "\(name) (\(tamilName))"
    }
}

struct Karana: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let completionTime: Date?
    let percentage: Double?
    let type: KaranaType
    
    var displayName: String {
        return "\(name) (\(tamilName))"
    }
}

enum KaranaType: String, Codable, CaseIterable {
    case fixed = "fixed"
    case movable = "movable"
    
    var tamilName: String {
        switch self {
        case .fixed: return "நிலையான"
        case .movable: return "நகரும்"
        }
    }
}

// MARK: - Additional Information

struct LunarMonth: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let fullName: String
    let isAdhika: Bool // Extra month
    let isKshaya: Bool // Lost month
    
    var displayName: String {
        var name = "\(self.name) (\(tamilName))"
        if isAdhika {
            name += " - அதிக மாதம்"
        }
        if isKshaya {
            name += " - க்ஷய மாதம்"
        }
        return name
    }
}

struct Season: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let description: String?
    
    var displayName: String {
        return "\(name) (\(tamilName))"
    }
}

// MARK: - Muhurat and Timing Information

struct Muhurat: Codable, Identifiable {
    let id: UUID
    let type: MuhuratType
    let name: String
    let tamilName: String
    let startTime: Date
    let endTime: Date
    let significance: String
    let isAuspicious: Bool
    let activities: [String]
    
    init(id: UUID = UUID(), type: MuhuratType, name: String, tamilName: String, startTime: Date, endTime: Date, significance: String, isAuspicious: Bool, activities: [String] = []) {
        self.id = id
        self.type = type
        self.name = name
        self.tamilName = tamilName
        self.startTime = startTime
        self.endTime = endTime
        self.significance = significance
        self.isAuspicious = isAuspicious
        self.activities = activities
    }
    
    var duration: TimeInterval {
        return endTime.timeIntervalSince(startTime)
    }
    
    var formattedTimeRange: String {
        let formatter = DateFormatter.timeFormatter
        return "\(formatter.string(from: startTime)) - \(formatter.string(from: endTime))"
    }
}

enum MuhuratType: String, Codable, CaseIterable {
    case brahmaMuhurat = "brahma_muhurat"
    case abhijitMuhurat = "abhijit_muhurat"
    case godhuli = "godhuli"
    case rahuKalam = "rahu_kalam"
    case yamaGandam = "yama_gandam"
    case gulikaKalam = "gulika_kalam"
    case durMuhurat = "dur_muhurat"
    case varjyam = "varjyam"
    case amritKaal = "amrit_kaal"
    
    var tamilName: String {
        switch self {
        case .brahmaMuhurat: return "பிரம்ம முகூர்த்தம்"
        case .abhijitMuhurat: return "அபிஜித் முகூர்த்தம்"
        case .godhuli: return "கோதூளி"
        case .rahuKalam: return "ராகு காலம்"
        case .yamaGandam: return "யம கண்டம்"
        case .gulikaKalam: return "குளிக காலம்"
        case .durMuhurat: return "துர் முகூர்த்தம்"
        case .varjyam: return "வர்ஜ்யம்"
        case .amritKaal: return "அமிர்த காலம்"
        }
    }
    
    var isAuspicious: Bool {
        switch self {
        case .brahmaMuhurat, .abhijitMuhurat, .godhuli, .amritKaal:
            return true
        case .rahuKalam, .yamaGandam, .gulikaKalam, .durMuhurat, .varjyam:
            return false
        }
    }
}

struct InauspiciousTime: Codable, Identifiable {
    let id: UUID
    let type: MuhuratType
    let name: String
    let tamilName: String
    let startTime: Date
    let endTime: Date
    let warning: String
    let avoidActivities: [String]
    
    init(id: UUID = UUID(), type: MuhuratType, name: String, tamilName: String, startTime: Date, endTime: Date, warning: String, avoidActivities: [String] = []) {
        self.id = id
        self.type = type
        self.name = name
        self.tamilName = tamilName
        self.startTime = startTime
        self.endTime = endTime
        self.warning = warning
        self.avoidActivities = avoidActivities
    }
}

// MARK: - Extensions

extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "en_US")
        return formatter
    }()
    
    static let tamilDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "ta_IN")
        formatter.dateStyle = .long
        return formatter
    }()
}
