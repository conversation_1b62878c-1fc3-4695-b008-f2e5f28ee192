import Foundation
import SwiftData

@Model
final class Lesson {
    var id: UUID
    var title: String
    var lessonDescription: String
    var language: Language
    var difficulty: Difficulty
    var category: LessonCategory
    var estimatedDuration: Int // in minutes
    var exercises: [Exercise]
    var culturalContext: CulturalContext?
    var prerequisitesData: String // Stored as comma-separated UUIDs
    var tagsData: String // Stored as pipe-separated strings
    var audioURL: String?
    var imageURL: String?
    var totalPoints: Int
    var isAIGenerated: Bool
    var createdDate: Date
    var updatedDate: Date
    var version: String

    init(
        id: UUID = UUID(),
        title: String,
        lessonDescription: String,
        language: Language,
        difficulty: Difficulty,
        category: LessonCategory,
        estimatedDuration: Int,
        exercises: [Exercise] = [],
        culturalContext: CulturalContext? = nil,
        prerequisites: [UUID] = [],
        tags: [String] = [],
        audioURL: String? = nil,
        imageURL: String? = nil,
        totalPoints: Int = 0,
        isAIGenerated: Bool = false,
        createdDate: Date = Date(),
        updatedDate: Date = Date(),
        version: String = "1.0"
    ) {
        self.id = id
        self.title = title
        self.lessonDescription = lessonDescription
        self.language = language
        self.difficulty = difficulty
        self.category = category
        self.estimatedDuration = estimatedDuration
        self.exercises = exercises
        self.culturalContext = culturalContext
        self.prerequisitesData = prerequisites.swiftDataCompatible
        self.tagsData = tags.swiftDataCompatible
        self.audioURL = audioURL
        self.imageURL = imageURL
        self.totalPoints = totalPoints
        self.isAIGenerated = isAIGenerated
        self.createdDate = createdDate
        self.updatedDate = updatedDate
        self.version = version
    }

    // MARK: - Computed Properties for Array Access

    var prerequisites: [UUID] {
        get {
            return Array<UUID>.fromSwiftDataString(prerequisitesData)
        }
        set {
            prerequisitesData = newValue.swiftDataCompatible
        }
    }

    var tags: [String] {
        get {
            return Array<String>.fromSwiftDataString(tagsData)
        }
        set {
            tagsData = newValue.swiftDataCompatible
        }
    }

    var isLocked: Bool {
        // Logic to determine if lesson is locked based on prerequisites
        return false // Simplified for now
    }

    var exerciseCount: Int {
        return exercises.count
    }
}

enum Difficulty: String, CaseIterable, Codable {
    case beginner = "beginner"
    case elementary = "elementary"
    case intermediate = "intermediate"
    case upperIntermediate = "upper_intermediate"
    case advanced = "advanced"
    case expert = "expert"

    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .elementary: return "Elementary"
        case .intermediate: return "Intermediate"
        case .upperIntermediate: return "Upper Intermediate"
        case .advanced: return "Advanced"
        case .expert: return "Expert"
        }
    }

    var color: String {
        switch self {
        case .beginner: return "green"
        case .elementary: return "blue"
        case .intermediate: return "orange"
        case .upperIntermediate: return "purple"
        case .advanced: return "red"
        case .expert: return "black"
        }
    }

    var icon: String {
        switch self {
        case .beginner: return "leaf.fill"
        case .elementary: return "seedling"
        case .intermediate: return "tree.fill"
        case .upperIntermediate: return "mountain.2.fill"
        case .advanced: return "flame.fill"
        case .expert: return "crown.fill"
        }
    }
}

enum LessonCategory: String, CaseIterable, Codable {
    case vocabulary = "vocabulary"
    case grammar = "grammar"
    case pronunciation = "pronunciation"
    case listening = "listening"
    case reading = "reading"
    case writing = "writing"
    case speaking = "speaking"
    case culture = "culture"
    case business = "business"
    case travel = "travel"
    case conversation = "conversation"
    case simulation = "simulation"

    var displayName: String {
        switch self {
        case .vocabulary: return "Vocabulary"
        case .grammar: return "Grammar"
        case .pronunciation: return "Pronunciation"
        case .listening: return "Listening"
        case .reading: return "Reading"
        case .writing: return "Writing"
        case .speaking: return "Speaking"
        case .culture: return "Culture"
        case .business: return "Business"
        case .travel: return "Travel"
        case .conversation: return "Conversation"
        case .simulation: return "Simulation"
        }
    }

    var icon: String {
        switch self {
        case .vocabulary: return "book.fill"
        case .grammar: return "textformat"
        case .pronunciation: return "mic.fill"
        case .listening: return "ear.fill"
        case .reading: return "doc.text.fill"
        case .writing: return "pencil"
        case .speaking: return "bubble.left.and.bubble.right.fill"
        case .culture: return "globe"
        case .business: return "briefcase.fill"
        case .travel: return "airplane"
        case .conversation: return "person.2.fill"
        case .simulation: return "gamecontroller.fill"
        }
    }

    var primaryColor: String {
        switch self {
        case .vocabulary: return "blue"
        case .grammar: return "green"
        case .pronunciation: return "orange"
        case .listening: return "purple"
        case .reading: return "indigo"
        case .writing: return "pink"
        case .speaking: return "red"
        case .culture: return "teal"
        case .business: return "gray"
        case .travel: return "yellow"
        case .conversation: return "cyan"
        case .simulation: return "mint"
        }
    }
}

@Model
final class Exercise {
    var id: UUID
    var type: ExerciseType
    var question: String
    var optionsData: String // Stored as pipe-separated strings
    var correctAnswer: Int?
    var correctAnswersData: String // Stored as comma-separated integers
    var explanation: String?
    var audioURL: String?
    var imageURL: String?
    var videoURL: String?
    var points: Int
    var timeLimit: Int? // in seconds
    var hintsData: String // Stored as pipe-separated strings
    var culturalNote: String?
    var pronunciation: PronunciationData?
    var difficulty: Difficulty
    var tagsData: String // Stored as pipe-separated strings

    init(
        id: UUID = UUID(),
        type: ExerciseType,
        question: String,
        options: [String] = [],
        correctAnswer: Int? = nil,
        correctAnswers: [Int] = [],
        explanation: String? = nil,
        audioURL: String? = nil,
        imageURL: String? = nil,
        videoURL: String? = nil,
        points: Int = 10,
        timeLimit: Int? = nil,
        hints: [String] = [],
        culturalNote: String? = nil,
        pronunciation: PronunciationData? = nil,
        difficulty: Difficulty = .beginner,
        tags: [String] = []
    ) {
        self.id = id
        self.type = type
        self.question = question
        self.optionsData = options.swiftDataCompatible
        self.correctAnswer = correctAnswer
        self.correctAnswersData = correctAnswers.swiftDataCompatible
        self.explanation = explanation
        self.audioURL = audioURL
        self.imageURL = imageURL
        self.videoURL = videoURL
        self.points = points
        self.timeLimit = timeLimit
        self.hintsData = hints.swiftDataCompatible
        self.culturalNote = culturalNote
        self.pronunciation = pronunciation
        self.difficulty = difficulty
        self.tagsData = tags.swiftDataCompatible
    }

    var hasAudio: Bool {
        return audioURL != nil
    }

    var hasImage: Bool {
        return imageURL != nil
    }

    var hasVideo: Bool {
        return videoURL != nil
    }

    var hasPronunciation: Bool {
        return pronunciation != nil
    }

    // MARK: - Computed Properties for Array Access

    var options: [String] {
        get {
            return Array<String>.fromSwiftDataString(optionsData)
        }
        set {
            optionsData = newValue.swiftDataCompatible
        }
    }

    var correctAnswers: [Int] {
        get {
            return Array<Int>.fromSwiftDataString(correctAnswersData)
        }
        set {
            correctAnswersData = newValue.swiftDataCompatible
        }
    }

    var hints: [String] {
        get {
            return Array<String>.fromSwiftDataString(hintsData)
        }
        set {
            hintsData = newValue.swiftDataCompatible
        }
    }

    var tags: [String] {
        get {
            return Array<String>.fromSwiftDataString(tagsData)
        }
        set {
            tagsData = newValue.swiftDataCompatible
        }
    }
}

enum ExerciseType: String, CaseIterable, Codable {
    case multipleChoice = "multiple_choice"
    case fillInBlank = "fill_in_blank"
    case matching = "matching"
    case pronunciation = "pronunciation"
    case listening = "listening"
    case translation = "translation"
    case conversation = "conversation"
    case dragAndDrop = "drag_and_drop"
    case shortAnswer = "short_answer"
    case essay = "essay"
    case rolePlay = "role_play"
    case culturalSimulation = "cultural_simulation"

    var displayName: String {
        switch self {
        case .multipleChoice: return "Multiple Choice"
        case .fillInBlank: return "Fill in the Blank"
        case .matching: return "Matching"
        case .pronunciation: return "Pronunciation"
        case .listening: return "Listening"
        case .translation: return "Translation"
        case .conversation: return "Conversation"
        case .dragAndDrop: return "Drag and Drop"
        case .shortAnswer: return "Short Answer"
        case .essay: return "Essay"
        case .rolePlay: return "Role Play"
        case .culturalSimulation: return "Cultural Simulation"
        }
    }

    var icon: String {
        switch self {
        case .multipleChoice: return "list.bullet"
        case .fillInBlank: return "text.cursor"
        case .matching: return "arrow.left.arrow.right"
        case .pronunciation: return "mic.fill"
        case .listening: return "ear.fill"
        case .translation: return "arrow.left.and.right"
        case .conversation: return "bubble.left.and.bubble.right"
        case .dragAndDrop: return "move.3d"
        case .shortAnswer: return "text.alignleft"
        case .essay: return "doc.text"
        case .rolePlay: return "theatermasks.fill"
        case .culturalSimulation: return "globe"
        }
    }
}

@Model
final class PronunciationData {
    var id: UUID
    var targetText: String
    var phonetics: String
    var audioURL: String
    var acceptanceThreshold: Double // 0.0 to 1.0
    var language: Language
    var dialect: String?
    var stressMarkersData: String // Stored as comma-separated integers
    var tonalMarkers: [TonalMarker] // For tonal languages like Tamil

    init(
        id: UUID = UUID(),
        targetText: String,
        phonetics: String,
        audioURL: String,
        acceptanceThreshold: Double = 0.8,
        language: Language,
        dialect: String? = nil,
        stressMarkers: [Int] = [],
        tonalMarkers: [TonalMarker] = []
    ) {
        self.id = id
        self.targetText = targetText
        self.phonetics = phonetics
        self.audioURL = audioURL
        self.acceptanceThreshold = acceptanceThreshold
        self.language = language
        self.dialect = dialect
        self.stressMarkersData = stressMarkers.swiftDataCompatible
        self.tonalMarkers = tonalMarkers
    }

    // MARK: - Computed Properties for Array Access

    var stressMarkers: [Int] {
        get {
            return Array<Int>.fromSwiftDataString(stressMarkersData)
        }
        set {
            stressMarkersData = newValue.swiftDataCompatible
        }
    }
}

@Model
final class TonalMarker {
    var id: UUID
    var position: Int
    var tone: ToneType
    var strength: Double

    init(
        id: UUID = UUID(),
        position: Int,
        tone: ToneType,
        strength: Double = 1.0
    ) {
        self.id = id
        self.position = position
        self.tone = tone
        self.strength = strength
    }
}

enum ToneType: String, CaseIterable, Codable {
    case high = "high"
    case low = "low"
    case rising = "rising"
    case falling = "falling"
    case risingFalling = "rising_falling"
    case fallingRising = "falling_rising"

    var displayName: String {
        switch self {
        case .high: return "High Tone"
        case .low: return "Low Tone"
        case .rising: return "Rising Tone"
        case .falling: return "Falling Tone"
        case .risingFalling: return "Rising-Falling Tone"
        case .fallingRising: return "Falling-Rising Tone"
        }
    }
}

@Model
final class CulturalContext {
    var id: UUID
    var scenario: CulturalScenario
    var setting: String
    var participantsData: String // Stored as pipe-separated strings
    var socialNormsData: String // Stored as pipe-separated strings
    var etiquetteData: String // Stored as pipe-separated strings
    var commonPhrasesData: String // Stored as pipe-separated strings
    var backgroundInfo: String
    var tipsData: String // Stored as pipe-separated strings
    var doAndDontsData: String // Stored as pipe-separated strings
    var regionalVariationsData: String // Stored as pipe-separated strings
    var historicalContext: String?
    var modernUsage: String?

    init(
        id: UUID = UUID(),
        scenario: CulturalScenario,
        setting: String,
        participants: [String] = [],
        socialNorms: [String] = [],
        etiquette: [String] = [],
        commonPhrases: [String] = [],
        backgroundInfo: String = "",
        tips: [String] = [],
        doAndDonts: [String] = [],
        regionalVariations: [String] = [],
        historicalContext: String? = nil,
        modernUsage: String? = nil
    ) {
        self.id = id
        self.scenario = scenario
        self.setting = setting
        self.participantsData = participants.swiftDataCompatible
        self.socialNormsData = socialNorms.swiftDataCompatible
        self.etiquetteData = etiquette.swiftDataCompatible
        self.commonPhrasesData = commonPhrases.swiftDataCompatible
        self.backgroundInfo = backgroundInfo
        self.tipsData = tips.swiftDataCompatible
        self.doAndDontsData = doAndDonts.swiftDataCompatible
        self.regionalVariationsData = regionalVariations.swiftDataCompatible
        self.historicalContext = historicalContext
        self.modernUsage = modernUsage
    }

    // MARK: - Computed Properties for Array Access

    var participants: [String] {
        get {
            return Array<String>.fromSwiftDataString(participantsData)
        }
        set {
            participantsData = newValue.swiftDataCompatible
        }
    }

    var socialNorms: [String] {
        get {
            return Array<String>.fromSwiftDataString(socialNormsData)
        }
        set {
            socialNormsData = newValue.swiftDataCompatible
        }
    }

    var etiquette: [String] {
        get {
            return Array<String>.fromSwiftDataString(etiquetteData)
        }
        set {
            etiquetteData = newValue.swiftDataCompatible
        }
    }

    var commonPhrases: [String] {
        get {
            return Array<String>.fromSwiftDataString(commonPhrasesData)
        }
        set {
            commonPhrasesData = newValue.swiftDataCompatible
        }
    }

    var tips: [String] {
        get {
            return Array<String>.fromSwiftDataString(tipsData)
        }
        set {
            tipsData = newValue.swiftDataCompatible
        }
    }

    var doAndDonts: [String] {
        get {
            return Array<String>.fromSwiftDataString(doAndDontsData)
        }
        set {
            doAndDontsData = newValue.swiftDataCompatible
        }
    }

    var regionalVariations: [String] {
        get {
            return Array<String>.fromSwiftDataString(regionalVariationsData)
        }
        set {
            regionalVariationsData = newValue.swiftDataCompatible
        }
    }
}

enum CulturalScenario: String, CaseIterable, Codable {
    case frenchCafe = "french_cafe"
    case tokyoSubway = "tokyo_subway"
    case spanishMarket = "spanish_market"
    case tamilFestival = "tamil_festival"
    case englishBusinessMeeting = "english_business_meeting"
    case frenchBakery = "french_bakery"
    case japaneseRestaurant = "japanese_restaurant"
    case spanishBeach = "spanish_beach"
    case tamilWedding = "tamil_wedding"
    case englishUniversity = "english_university"

    var displayName: String {
        switch self {
        case .frenchCafe: return "French Café"
        case .tokyoSubway: return "Tokyo Subway"
        case .spanishMarket: return "Spanish Market"
        case .tamilFestival: return "Tamil Festival"
        case .englishBusinessMeeting: return "English Business Meeting"
        case .frenchBakery: return "French Bakery"
        case .japaneseRestaurant: return "Japanese Restaurant"
        case .spanishBeach: return "Spanish Beach"
        case .tamilWedding: return "Tamil Wedding"
        case .englishUniversity: return "English University"
        }
    }

    var language: Language {
        switch self {
        case .frenchCafe, .frenchBakery: return .french
        case .tokyoSubway, .japaneseRestaurant: return .japanese
        case .spanishMarket, .spanishBeach: return .spanish
        case .tamilFestival, .tamilWedding: return .tamil
        case .englishBusinessMeeting, .englishUniversity: return .english
        }
    }

    var imageName: String {
        switch self {
        case .frenchCafe: return "cafe_scene"
        case .tokyoSubway: return "subway_scene"
        case .spanishMarket: return "market_scene"
        case .tamilFestival: return "festival_scene"
        case .englishBusinessMeeting: return "business_scene"
        case .frenchBakery: return "bakery_scene"
        case .japaneseRestaurant: return "restaurant_scene"
        case .spanishBeach: return "beach_scene"
        case .tamilWedding: return "wedding_scene"
        case .englishUniversity: return "university_scene"
        }
    }

    var backgroundColor: String {
        switch self {
        case .frenchCafe, .frenchBakery: return "french_blue"
        case .tokyoSubway, .japaneseRestaurant: return "japanese_red"
        case .spanishMarket, .spanishBeach: return "spanish_orange"
        case .tamilFestival, .tamilWedding: return "tamil_green"
        case .englishBusinessMeeting, .englishUniversity: return "english_navy"
        }
    }
}