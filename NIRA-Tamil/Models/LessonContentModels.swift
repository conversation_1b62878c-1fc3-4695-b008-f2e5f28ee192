import Foundation

// MARK: - Lesson Content Models

/// Model for vocabulary items with audio support
struct LessonVocabularyItem: Identifiable, Codable {
    let id: UUID
    let word: String
    let translation: String?
    let pronunciation: String?
    let partOfSpeech: String?
    let example: String?
    let wordAudioURL: String?
    let exampleAudioURL: String?
    
    init(word: String, translation: String? = nil, pronunciation: String? = nil, partOfSpeech: String? = nil, example: String? = nil, wordAudioURL: String? = nil, exampleAudioURL: String? = nil) {
        self.id = UUID()
        self.word = word
        self.translation = translation
        self.pronunciation = pronunciation
        self.partOfSpeech = partOfSpeech
        self.example = example
        self.wordAudioURL = wordAudioURL
        self.exampleAudioURL = exampleAudioURL
    }
}

/// Model for dialogue items in conversations
struct LessonDialogueItem: Identifiable, Codable {
    let id: UUID
    let speaker: String
    let text: String
    let translation: String?
    let pronunciation: String?
    let culturalNote: String?
    let audioURL: String?
    
    init(speaker: String, text: String, translation: String? = nil, pronunciation: String? = nil, culturalNote: String? = nil, audioURL: String? = nil) {
        self.id = UUID()
        self.speaker = speaker
        self.text = text
        self.translation = translation
        self.pronunciation = pronunciation
        self.culturalNote = culturalNote
        self.audioURL = audioURL
    }
}

/// Model for grammar points with examples
struct LessonGrammarPoint: Identifiable, Codable {
    let id: UUID
    let rule: String
    let ruleTamil: String?
    let ruleRomanization: String?
    let explanation: String
    let examples: [GrammarExample]
    let tips: [String]?
    let commonMistakes: [String]?
    let difficultyLevel: Int

    init(rule: String, ruleTamil: String? = nil, ruleRomanization: String? = nil, explanation: String, examples: [GrammarExample] = [], tips: [String]? = nil, commonMistakes: [String]? = nil, difficultyLevel: Int = 1) {
        self.id = UUID()
        self.rule = rule
        self.ruleTamil = ruleTamil
        self.ruleRomanization = ruleRomanization
        self.explanation = explanation
        self.examples = examples
        self.tips = tips
        self.commonMistakes = commonMistakes
        self.difficultyLevel = difficultyLevel
    }
}

/// Model for grammar examples with audio and romanization
struct GrammarExample: Identifiable, Codable {
    let id: UUID
    let english: String
    let tamil: String
    let romanization: String
    let audioURL: String?

    init(english: String, tamil: String, romanization: String, audioURL: String? = nil) {
        self.id = UUID()
        self.english = english
        self.tamil = tamil
        self.romanization = romanization
        self.audioURL = audioURL
    }
}

/// Model for lesson exercises with various types
struct LessonExerciseItem: Identifiable, Codable {
    let id: UUID
    let type: String
    let question: String
    let options: [String]
    let correctAnswer: Int
    let explanation: String?
    let points: Int
    let optionsPronunciations: [String]?
    let questionAudioURL: String?
    
    init(type: String, question: String, options: [String] = [], correctAnswer: Int = 0, explanation: String? = nil, points: Int = 10, optionsPronunciations: [String]? = nil, questionAudioURL: String? = nil) {
        self.id = UUID()
        self.type = type
        self.question = question
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.points = points
        self.optionsPronunciations = optionsPronunciations
        self.questionAudioURL = questionAudioURL
    }
} 