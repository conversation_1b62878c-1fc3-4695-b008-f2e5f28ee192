//
//  ChatModels.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI
import Foundation

// MARK: - Chat View Model

@MainActor
class ChatViewModel: ObservableObject {
    @Published var messages: [AIChatMessage] = []
    @Published var isLoading = false
    @Published var isOnline = true
    @Published var isTyping = false
    @Published var isRecording = false
    @Published var suggestions: [String] = []
    @Published var showingAttachmentPicker = false
    @Published var showingLiveVoiceInterface = false

    private var currentAgent: LanguageTutor?
    private var currentConversation: SupabaseChatConversation?
    private let chatHistoryService = ChatHistoryService.shared
    private let promptService = PromptManagementService.shared
    private let geminiService = GeminiService.shared
    private var currentPromptTemplate: PromptTemplate?
    
    func initializeChat(with agent: LanguageTutor) {
        self.currentAgent = agent
        self.isOnline = true

        Task {
            await createOrLoadConversation(with: agent)
        }

        // Set initial suggestions
        suggestions = getSuggestionsForLanguage(agent.language)
    }

    private func createOrLoadConversation(with agent: LanguageTutor) async {
        do {
            // Create a new conversation for this chat session
            let agentModel = ChatAgent(
                id: "agent_\(agent.name.lowercased())",
                name: agent.name,
                description: agent.description,
                expertise: agent.specialties,
                rating: 4.9,
                isOnline: true,
                profileImageName: "agent_\(agent.persona.rawValue)",
                emoji: agent.avatar
            )

            currentConversation = try await chatHistoryService.createNewConversation(with: agentModel)

            // Set up contextual prompt for this agent
            await setupContextualPrompt(for: agent)

            // Generate contextual welcome message using prompt
            let welcomeContent = generateContextualWelcomeMessage(for: agent)

            let welcomeMessage = AIChatMessage(
                content: welcomeContent,
                isFromUser: false,
                timestamp: Date()
            )
            messages.append(welcomeMessage)

            // Save welcome message to Supabase
            _ = try await chatHistoryService.saveMessage(
                content: welcomeContent,
                isFromUser: false,
                messageType: .text
            )

        } catch {
            print("Failed to create conversation: \(error)")
            // Fallback to local-only chat
            let welcomeMessage = AIChatMessage(
                content: "Hello! I'm \(agent.name), your \(agent.language.displayName) tutor. How can I help you today?",
                isFromUser: false,
                timestamp: Date()
            )
            messages.append(welcomeMessage)
        }
    }

    private func setupContextualPrompt(for agent: LanguageTutor) async {
        // Determine learning scenario based on agent type
        let scenario: PromptLearningScenario = {
            switch agent.persona {
            case .socialLearner:
                return .conversationPractice
            case .culturalSeeker:
                return .culturalExploration
            case .beginnerEnthusiast, .busyProfessional:
                return .generalLearning
            case .traveler:
                return .writingPractice
            }
        }()

        // Mock user progress (in real app, this would come from user data)
        let userProgress = PromptUserProgress(
            completedLessons: ["A1_BASIC_GREETINGS", "A1_FAMILY_MEMBERS"],
            strengths: ["vocabulary", "enthusiasm"],
            weakAreas: ["pronunciation", "grammar"],
            preferredLearningStyle: .mixed,
            totalStudyTime: 3600, // 1 hour
            lastActiveDate: Date()
        )

        // Build contextual prompt
        currentPromptTemplate = promptService.buildContextualPrompt(
            for: scenario,
            userLevel: .beginner, // This would come from user profile
            userProgress: userProgress,
            currentLesson: "A1_BASIC_GREETINGS"
        )
    }

    private func generateContextualWelcomeMessage(for agent: LanguageTutor) -> String {
        guard let template = currentPromptTemplate else {
            return "Hello! I'm \(agent.name), your \(agent.language.displayName) tutor. How can I help you today?"
        }

        // Use prompt template to generate personalized welcome
        let variables = [
            "agent_name": agent.name,
            "language": agent.language.displayName,
            "user_level": "beginner"
        ]

        _ = promptService.executePrompt(template, with: variables)

        // For now, return a contextual welcome based on agent type
        switch agent.persona {
        case .socialLearner:
            return "வணக்கம்! (Vanakkam!) I'm \(agent.name), your Tamil conversation partner. I'm here to help you practice speaking Tamil naturally. Since you're just starting out, we'll begin with simple conversations and I'll provide romanization to help with pronunciation. What would you like to talk about today?"

        case .culturalSeeker:
            return "வணக்கம்! I'm \(agent.name), your Tamil cultural guide. I'll help you understand the rich traditions and heritage of Tamil culture while learning the language. As a beginner, I'll explain cultural concepts in simple terms and connect them to everyday Tamil expressions. What aspect of Tamil culture interests you most?"

        case .beginnerEnthusiast:
            return "வணக்கம்! I'm \(agent.name), your enthusiastic Tamil learning companion! I'll make learning Tamil fun and engaging with games, stories, and encouragement. Since you're beginning your journey, we'll start with basic vocabulary and simple phrases. Ready to have some fun learning Tamil?"

        case .busyProfessional:
            return "வணக்கம்! I'm \(agent.name), your professional Tamil tutor. I'll help you learn Tamil efficiently for business and professional contexts. We'll focus on practical vocabulary and phrases you can use immediately. What professional scenario would you like to practice?"

        case .traveler:
            return "வணக்கம்! I'm \(agent.name), your Tamil travel companion. I'll teach you essential phrases and cultural tips for traveling in Tamil-speaking regions. We'll start with basic travel vocabulary and important expressions. Where are you planning to visit?"
        }
    }
    
    func sendMessage(_ text: String) async {
        guard let agent = currentAgent else { return }

        // Add user message
        let userMessage = AIChatMessage(
            content: text,
            isFromUser: true,
            timestamp: Date()
        )
        messages.append(userMessage)

        // Save user message to Supabase
        do {
            _ = try await chatHistoryService.saveMessage(
                content: text,
                isFromUser: true,
                messageType: .text
            )
        } catch {
            print("Failed to save user message: \(error)")
        }

        // Clear suggestions
        suggestions = []

        // Show typing indicator
        isTyping = true
        isLoading = true

        // Generate AI response using Gemini
        let response = await generateAIResponseWithGemini(for: text, agent: agent)
        let aiMessage = AIChatMessage(
            content: response.content,
            isFromUser: false,
            timestamp: Date(),
            responseTime: response.responseTime,
            vocabularyHighlights: response.vocabularyHighlights,
            grammarTips: response.grammarTips,
            culturalNotes: response.culturalNotes
        )

        messages.append(aiMessage)

        // Save AI response to Supabase
        do {
            let metadata: [String: Any] = [
                "responseTime": response.responseTime,
                "vocabularyHighlights": response.vocabularyHighlights?.map { ["word": $0.word, "definition": $0.definition] } ?? [],
                "grammarTips": response.grammarTips ?? [],
                "culturalNotes": response.culturalNotes ?? []
            ]

            _ = try await chatHistoryService.saveMessage(
                content: response.content,
                isFromUser: false,
                messageType: .text,
                metadata: metadata
            )
        } catch {
            print("Failed to save AI message: \(error)")
        }

        // Hide typing indicator
        isTyping = false
        isLoading = false

        // Update suggestions
        suggestions = getFollowUpSuggestions(for: text, language: agent.language)
    }
    
    func sendAttachment(_ attachment: MessageAttachment) {
        let attachmentMessage = AIChatMessage(
            content: "Shared: \(attachment.name)",
            isFromUser: true,
            timestamp: Date(),
            attachments: [attachment]
        )
        messages.append(attachmentMessage)

        // Save attachment message to Supabase
        Task {
            do {
                let metadata: [String: Any] = [
                    "attachments": [
                        [
                            "name": attachment.name,
                            "type": attachment.type.rawValue,
                            "size": attachment.size,
                            "url": attachment.url
                        ]
                    ]
                ]

                _ = try await chatHistoryService.saveMessage(
                    content: "Shared: \(attachment.name)",
                    isFromUser: true,
                    messageType: .image, // Use appropriate type based on attachment
                    metadata: metadata
                )
            } catch {
                print("Failed to save attachment message: \(error)")
            }
        }
    }
    
    func startLiveVoiceConversation() {
        showingLiveVoiceInterface = true
    }
    
    func toggleVoiceRecording() {
        isRecording.toggle()
        if isRecording {
            startVoiceRecording()
        } else {
            stopVoiceRecording()
        }
    }
    
    func startVoiceRecording() {
        isRecording = true
        // TODO: Implement actual voice recording with AVAudioRecorder
        print("🎤 Starting voice recording...")

        // Simulate recording for now
        Task {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            if isRecording {
                // Simulate voice input
                await sendMessage("Hello, I want to learn Tamil")
                isRecording = false
            }
        }
    }

    func stopVoiceRecording() {
        isRecording = false
        print("🛑 Stopping voice recording...")
        // TODO: Process recorded audio and convert to text
    }
    
    // MARK: - Helper Methods
    
    private func getSuggestionsForLanguage(_ language: Language) -> [String] {
        switch language {
        case .french:
            return ["Bonjour!", "Comment allez-vous?", "Merci beaucoup", "Au revoir"]
        case .spanish:
            return ["¡Hola!", "¿Cómo está usted?", "Muchas gracias", "Adiós"]
        case .japanese:
            return ["こんにちは", "元気ですか？", "ありがとう", "さようなら"]
        case .tamil:
            return ["வணக்கம்", "எப்படி இருக்கீங்க?", "நன்றி", "போய்ட்டு வரேன்"]
        default:
            return ["Hello!", "How are you?", "Thank you", "Goodbye"]
        }
    }
    
    private func getFollowUpSuggestions(for message: String, language: Language) -> [String] {
        // Simple suggestion logic based on message content
        if message.lowercased().contains("hello") || message.lowercased().contains("hi") {
            return ["Tell me about yourself", "What should I learn first?", "Can you help me with pronunciation?"]
        } else if message.lowercased().contains("thank") {
            return ["What's next?", "Can we practice more?", "Tell me about culture"]
        } else {
            return ["Can you explain more?", "Give me an example", "What about grammar?"]
        }
    }
    
    private func generateAIResponseWithGemini(for message: String, agent: LanguageTutor) async -> (content: String, responseTime: Double, vocabularyHighlights: [VocabularyHighlight]?, grammarTips: [String]?, culturalNotes: [String]?) {
        let startTime = Date()

        do {
            // Get conversation history for context
            let conversationHistory = messages.suffix(5).map { $0.content }

            // Generate response using Gemini
            let content = try await geminiService.generateChatResponse(
                message: message,
                agentContext: agent.systemPrompt,
                conversationHistory: Array(conversationHistory)
            )

            let responseTime = Date().timeIntervalSince(startTime)

            // Extract additional learning elements
            let vocabularyHighlights = generateVocabularyForMessage(message)
            let grammarTips = extractGrammarTips(from: content)
            let culturalNotes = extractCulturalNotes(from: content)

            return (content, responseTime, vocabularyHighlights, grammarTips, culturalNotes)

        } catch {
            print("Gemini API error: \(error)")
            // Fallback to local response
            return generateAIResponse(for: message, agent: agent)
        }
    }

    private func generateAIResponse(for message: String, agent: LanguageTutor) -> (content: String, responseTime: Double, vocabularyHighlights: [VocabularyHighlight]?, grammarTips: [String]?, culturalNotes: [String]?) {

        let responseTime = Double.random(in: 0.5...2.0)

        // Use prompt template to generate contextual response
        guard let template = currentPromptTemplate else {
            return generateFallbackAIResponse(for: message, agent: agent)
        }

        // Execute prompt with user input
        let variables = [
            "query": message,
            "user_input": message,
            "topic": extractTopicFromText(message),
            "writing_request": message,
            "cultural_topic": message
        ]

        let executablePrompt = promptService.executePrompt(template, with: variables)

        // Generate enhanced response using prompt context
        var content = generateEnhancedResponse(
            userInput: message,
            systemPrompt: executablePrompt.systemPrompt,
            agent: agent
        )
        var vocabularyHighlights: [VocabularyHighlight]? = nil
        var grammarTips: [String]? = nil
        var culturalNotes: [String]? = nil
        
        if message.lowercased().contains("hello") || message.lowercased().contains("hi") {
            content = "Hello! I'm excited to help you learn \(agent.language.displayName). What would you like to focus on today?"
        } else if message.lowercased().contains("pronunciation") {
            content = "Great question! Pronunciation is key to being understood. Let's start with some basic sounds in \(agent.language.displayName)."
            grammarTips = ["Focus on vowel sounds first", "Practice with native audio"]
        } else if message.lowercased().contains("culture") {
            content = "Culture and language go hand in hand! Understanding cultural context will make your \(agent.language.displayName) much more natural."
            culturalNotes = ["Greetings vary by time of day", "Formal vs informal speech is important"]
        } else {
            // Generate dynamic response based on user input
            content = generateDynamicResponse(for: message, agent: agent)
            vocabularyHighlights = generateVocabularyForMessage(message)
        }
        
        return (content, responseTime, vocabularyHighlights, grammarTips, culturalNotes)
    }

    private func extractGrammarTips(from content: String) -> [String]? {
        // Extract grammar tips from AI response
        if content.lowercased().contains("grammar") || content.lowercased().contains("இலக்கணம்") {
            return ["Tamil uses agglutination", "Verb endings change based on tense", "Word order is Subject-Object-Verb"]
        }
        return nil
    }

    private func extractCulturalNotes(from content: String) -> [String]? {
        // Extract cultural notes from AI response
        if content.lowercased().contains("culture") || content.lowercased().contains("பண்பாடு") {
            return ["Tamil culture spans over 2000 years", "Respect for elders is deeply embedded in language", "Classical literature influences modern Tamil"]
        }
        return nil
    }

    private func generateDynamicResponse(for message: String, agent: LanguageTutor) -> String {
        let lowercased = message.lowercased()

        // Grammar-related questions
        if lowercased.contains("grammar") || lowercased.contains("இலக்கணம்") {
            return "Tamil grammar is fascinating! Let me explain the key concepts. Tamil has agglutinative grammar where words are formed by adding suffixes. What specific grammar topic interests you?"
        }

        // Vocabulary questions
        if lowercased.contains("vocabulary") || lowercased.contains("word") || lowercased.contains("meaning") {
            return "Building vocabulary is essential! Tamil has a rich vocabulary with words from ancient literature. Which category of words would you like to learn - daily conversation, family, food, or emotions?"
        }

        // Writing questions
        if lowercased.contains("write") || lowercased.contains("script") || lowercased.contains("letter") {
            return "Tamil script is beautiful! It has 12 vowels and 18 consonants. Each letter has its own unique shape. Would you like to start with vowels (உயிர் எழுத்துக்கள்) or consonants (மெய் எழுத்துக்கள்)?"
        }

        // Cultural questions
        if lowercased.contains("culture") || lowercased.contains("tradition") || lowercased.contains("festival") {
            return "Tamil culture is incredibly rich! From ancient literature like Thirukkural to vibrant festivals like Pongal, there's so much to explore. What aspect of Tamil culture interests you most?"
        }

        // Learning progress questions
        if lowercased.contains("learn") || lowercased.contains("study") || lowercased.contains("practice") {
            return "Great attitude! Learning Tamil opens doors to a 2000-year-old literary tradition. I recommend starting with basic greetings and family words. Shall we begin with some essential phrases?"
        }

        // Default response with variety
        let responses = [
            "That's an interesting question! Tamil is a classical language with deep roots. How can I help you explore this topic further?",
            "I love your curiosity! Tamil has many fascinating aspects. What specific area would you like to dive into?",
            "Excellent question! Let me share some insights about Tamil language and culture. What would you like to know more about?",
            "That's a great topic to explore! Tamil offers rich learning opportunities. Shall we break this down step by step?",
            "Wonderful! Tamil learning is a journey of discovery. What particular aspect interests you most right now?"
        ]

        return responses.randomElement() ?? responses[0]
    }

    private func generateVocabularyForMessage(_ message: String) -> [VocabularyHighlight]? {
        let lowercased = message.lowercased()

        if lowercased.contains("family") || lowercased.contains("குடும்பம்") {
            return [
                VocabularyHighlight(word: "குடும்பம்", definition: "Family", pronunciation: "kudumbam"),
                VocabularyHighlight(word: "அம்மா", definition: "Mother", pronunciation: "amma"),
                VocabularyHighlight(word: "அப்பா", definition: "Father", pronunciation: "appa")
            ]
        } else if lowercased.contains("food") || lowercased.contains("உணவு") {
            return [
                VocabularyHighlight(word: "உணவு", definition: "Food", pronunciation: "unavu"),
                VocabularyHighlight(word: "சாதம்", definition: "Rice", pronunciation: "saatham"),
                VocabularyHighlight(word: "தண்ணீர்", definition: "Water", pronunciation: "thanneer")
            ]
        } else if lowercased.contains("greeting") || lowercased.contains("வணக்கம்") {
            return [
                VocabularyHighlight(word: "வணக்கம்", definition: "Hello/Greetings", pronunciation: "vanakkam"),
                VocabularyHighlight(word: "நன்றி", definition: "Thank you", pronunciation: "nandri"),
                VocabularyHighlight(word: "மன்னிக்கவும்", definition: "Excuse me/Sorry", pronunciation: "mannikkavum")
            ]
        }

        return nil
    }

    private func generateFallbackAIResponse(for message: String, agent: LanguageTutor) -> (content: String, responseTime: Double, vocabularyHighlights: [VocabularyHighlight]?, grammarTips: [String]?, culturalNotes: [String]?) {
        let responseTime = Double.random(in: 0.5...2.0)

        // Simple response generation based on message content
        var content = ""
        var vocabularyHighlights: [VocabularyHighlight]? = nil
        let grammarTips: [String]? = nil
        let culturalNotes: [String]? = nil

        let lowercased = message.lowercased()

        if lowercased.contains("hello") || lowercased.contains("hi") || lowercased.contains("வணக்கம்") {
            content = "வணக்கம்! (Vanakkam!) Hello! How can I help you learn Tamil today?"
        } else if lowercased.contains("family") || lowercased.contains("குடும்பம்") {
            content = "Great topic! Family is குடும்பம் (kudumbam) in Tamil. Let me teach you some family member names."
            vocabularyHighlights = [VocabularyHighlight(word: "குடும்பம்", definition: "Family", pronunciation: "kudumbam")]
        } else if lowercased.contains("food") || lowercased.contains("உணவு") {
            content = "Food is உணவு (unavu) in Tamil. Tamil cuisine is delicious! What would you like to learn about?"
            vocabularyHighlights = [VocabularyHighlight(word: "உணவு", definition: "Food", pronunciation: "unavu")]
        } else {
            content = "That's interesting! Let me help you with that in Tamil. Can you tell me more about what you'd like to learn?"
        }

        return (content, responseTime, vocabularyHighlights, grammarTips, culturalNotes)
    }

    private func extractTopicFromText(_ text: String) -> String {
        // Simple topic extraction (in real app, this would be more sophisticated)
        let lowercased = text.lowercased()

        if lowercased.contains("family") || lowercased.contains("குடும்பம்") {
            return "family"
        } else if lowercased.contains("food") || lowercased.contains("உணவு") {
            return "food"
        } else if lowercased.contains("greeting") || lowercased.contains("வணக்கம்") {
            return "greetings"
        } else if lowercased.contains("culture") || lowercased.contains("பண்பாடு") {
            return "culture"
        } else {
            return "general"
        }
    }

    private func generateEnhancedResponse(
        userInput: String,
        systemPrompt: String,
        agent: LanguageTutor
    ) -> String {
        // Enhanced response generation using system prompt context
        let baseResponse = generateBaseResponse(for: userInput, agent: agent)

        // Add contextual enhancements based on system prompt
        var enhancedResponse = baseResponse

        // Add level-appropriate guidance
        if systemPrompt.contains("simple vocabulary") {
            enhancedResponse += "\n\n💡 *Tip: I'm using simple words to help you learn step by step.*"
        }

        // Add romanization if mentioned in prompt
        if systemPrompt.contains("romanization") && enhancedResponse.contains("வணக்கம்") {
            enhancedResponse = enhancedResponse.replacingOccurrences(
                of: "வணக்கம்",
                with: "வணக்கம் (Vanakkam)"
            )
        }

        // Add encouragement for beginners
        if systemPrompt.contains("building confidence") {
            enhancedResponse += "\n\n🌟 *You're doing great! Keep practicing and you'll improve quickly.*"
        }

        return enhancedResponse
    }

    private func generateBaseResponse(for message: String, agent: LanguageTutor) -> String {
        let lowercased = message.lowercased()

        if lowercased.contains("hello") || lowercased.contains("hi") || lowercased.contains("வணக்கம்") {
            return "வணக்கம்! (Vanakkam!) Hello! How can I help you learn Tamil today?"
        } else if lowercased.contains("family") || lowercased.contains("குடும்பம்") {
            return "Great topic! Family is குடும்பம் (kudumbam) in Tamil. Let me teach you some family member names."
        } else if lowercased.contains("food") || lowercased.contains("உணவு") {
            return "Food is உணவு (unavu) in Tamil. Tamil cuisine is delicious! What would you like to learn about?"
        } else {
            return "That's interesting! Let me help you with that in Tamil. Can you tell me more about what you'd like to learn?"
        }
    }
}

// MARK: - AI Chat Message

struct AIChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let status: MessageStatus
    let attachments: [MessageAttachment]?
    let responseTime: Double?
    let vocabularyHighlights: [VocabularyHighlight]?
    let grammarTips: [String]?
    let culturalNotes: [String]?
    
    init(
        content: String,
        isFromUser: Bool,
        timestamp: Date,
        status: MessageStatus = .sent,
        attachments: [MessageAttachment]? = nil,
        responseTime: Double? = nil,
        vocabularyHighlights: [VocabularyHighlight]? = nil,
        grammarTips: [String]? = nil,
        culturalNotes: [String]? = nil
    ) {
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.status = status
        self.attachments = attachments
        self.responseTime = responseTime
        self.vocabularyHighlights = vocabularyHighlights
        self.grammarTips = grammarTips
        self.culturalNotes = culturalNotes
    }
}

// MARK: - Message Status

enum MessageStatus {
    case sending
    case sent
    case delivered
    case read
    case failed
}

// MARK: - Message Attachment

struct MessageAttachment: Identifiable {
    let id = UUID()
    let type: AttachmentType
    let url: String
    let name: String
    let size: Int64
    
    init(type: AttachmentType, url: String, name: String, size: Int64) {
        self.type = type
        self.url = url
        self.name = name
        self.size = size
    }
}

// MARK: - Attachment Type

enum AttachmentType: String, CaseIterable {
    case image = "image"
    case audio = "audio"
    case document = "document"
    case video = "video"
}

// MARK: - Vocabulary Highlight

struct VocabularyHighlight {
    let word: String
    let definition: String
    let pronunciation: String?
    let example: String?
    
    init(word: String, definition: String, pronunciation: String? = nil, example: String? = nil) {
        self.word = word
        self.definition = definition
        self.pronunciation = pronunciation
        self.example = example
    }
}

// MARK: - Chat Attachment

struct ChatAttachment: Identifiable, Codable {
    let id: UUID
    let fileName: String
    let originalFileName: String
    let mimeType: String
    let size: Int64
    let url: String
    let storagePath: String
    let conversationId: UUID
    let uploadedAt: Date
    let isProcessed: Bool
    var thumbnailUrl: String?
    var extractedText: String?
    var metadata: [String: Any]?

    init(
        id: UUID = UUID(),
        fileName: String,
        originalFileName: String,
        mimeType: String,
        size: Int64,
        url: String,
        storagePath: String,
        conversationId: UUID,
        uploadedAt: Date = Date(),
        isProcessed: Bool = false,
        thumbnailUrl: String? = nil,
        extractedText: String? = nil,
        metadata: [String: Any]? = nil
    ) {
        self.id = id
        self.fileName = fileName
        self.originalFileName = originalFileName
        self.mimeType = mimeType
        self.size = size
        self.url = url
        self.storagePath = storagePath
        self.conversationId = conversationId
        self.uploadedAt = uploadedAt
        self.isProcessed = isProcessed
        self.thumbnailUrl = thumbnailUrl
        self.extractedText = extractedText
        self.metadata = metadata
    }

    // Custom coding to handle metadata dictionary
    enum CodingKeys: String, CodingKey {
        case id, fileName, originalFileName, mimeType, size, url, storagePath, conversationId, uploadedAt, isProcessed, thumbnailUrl, extractedText
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        fileName = try container.decode(String.self, forKey: .fileName)
        originalFileName = try container.decode(String.self, forKey: .originalFileName)
        mimeType = try container.decode(String.self, forKey: .mimeType)
        size = try container.decode(Int64.self, forKey: .size)
        url = try container.decode(String.self, forKey: .url)
        storagePath = try container.decode(String.self, forKey: .storagePath)
        conversationId = try container.decode(UUID.self, forKey: .conversationId)
        uploadedAt = try container.decode(Date.self, forKey: .uploadedAt)
        isProcessed = try container.decode(Bool.self, forKey: .isProcessed)
        thumbnailUrl = try container.decodeIfPresent(String.self, forKey: .thumbnailUrl)
        extractedText = try container.decodeIfPresent(String.self, forKey: .extractedText)
        metadata = nil // Metadata is not encoded/decoded for simplicity
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(fileName, forKey: .fileName)
        try container.encode(originalFileName, forKey: .originalFileName)
        try container.encode(mimeType, forKey: .mimeType)
        try container.encode(size, forKey: .size)
        try container.encode(url, forKey: .url)
        try container.encode(storagePath, forKey: .storagePath)
        try container.encode(conversationId, forKey: .conversationId)
        try container.encode(uploadedAt, forKey: .uploadedAt)
        try container.encode(isProcessed, forKey: .isProcessed)
        try container.encodeIfPresent(thumbnailUrl, forKey: .thumbnailUrl)
        try container.encodeIfPresent(extractedText, forKey: .extractedText)
        // Metadata is not encoded for simplicity
    }
}

// MARK: - Live Voice Interface View
// Note: LiveVoiceInterfaceView is defined in Views/LiveVoiceInterfaceView.swift