import Foundation
import SwiftUI

// MARK: - User Progress Models

struct AdaptiveUserProgress: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let lessonId: UUID
    let languageId: UUID

    // Progress Metrics
    var completionStatus: CompletionStatus
    var accuracyScore: Double?
    var timeSpentSeconds: Int
    var attemptsCount: Int

    // Performance Details
    var vocabularyMastery: [String: VocabularyMasteryData]?
    var exerciseResults: [String: AdaptiveExerciseResult]?
    var mistakesPattern: [String: Int]?

    // Learning Behavior
    var sessionDuration: Int?
    var engagementScore: Double?
    var difficultyRating: Int?

    // Timestamps
    var startedAt: Date?
    var completedAt: Date?
    var lastReviewedAt: Date?
    let createdAt: Date
    var updatedAt: Date

    enum CompletionStatus: String, Codable, CaseIterable {
        case notStarted = "not_started"
        case inProgress = "in_progress"
        case completed = "completed"
        case mastered = "mastered"

        var displayName: String {
            switch self {
            case .notStarted: return "Not Started"
            case .inProgress: return "In Progress"
            case .completed: return "Completed"
            case .mastered: return "Mastered"
            }
        }

        var color: Color {
            switch self {
            case .notStarted: return .gray
            case .inProgress: return .orange
            case .completed: return .green
            case .mastered: return .purple
            }
        }

        var icon: String {
            switch self {
            case .notStarted: return "circle"
            case .inProgress: return "clock"
            case .completed: return "checkmark.circle"
            case .mastered: return "star.circle"
            }
        }
    }
}

struct VocabularyMasteryData: Codable {
    let attempts: Int
    let correct: Int
    let lastSeen: Date

    var masteryLevel: Double {
        guard attempts > 0 else { return 0.0 }
        return Double(correct) / Double(attempts)
    }
}

struct AdaptiveExerciseResult: Codable {
    let exerciseId: String
    let attempts: Int
    let correct: Bool
    let timeSpent: Int
    let hintsUsed: Int
}

// MARK: - Learning Analytics Models

struct LearningAnalytics: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let lessonId: UUID?
    let sessionId: UUID

    // Interaction Details
    let interactionType: InteractionType
    let contentId: String?
    let contentType: ContentType?

    // Performance Data
    let isCorrect: Bool?
    let responseTimeMs: Int?
    let hintUsed: Bool
    let attemptsBeforeCorrect: Int?

    // Context
    let difficultyLevel: Int?
    let timeOfDay: Int
    let deviceType: String?

    // Metadata
    let metadata: [String: SupabaseAnyCodable]?
    let createdAt: Date

    enum InteractionType: String, Codable {
        case lessonStart = "lesson_start"
        case lessonComplete = "lesson_complete"
        case exerciseAttempt = "exercise_attempt"
        case vocabularyReview = "vocabulary_review"
        case grammarPractice = "grammar_practice"
        case dialogueInteraction = "dialogue_interaction"
        case hintRequest = "hint_request"
        case pauseSession = "pause_session"
        case resumeSession = "resume_session"
    }

    enum ContentType: String, Codable {
        case vocabulary
        case exercise
        case grammar
        case dialogue
        case cultural
    }
}

// MARK: - Adaptive Learning Profile

struct AdaptiveLearningProfile: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let languageId: UUID

    // Learning Preferences (AI-determined)
    var optimalSessionLength: Int? // Minutes
    var preferredDifficultyProgression: Double?
    var learningStyleWeights: LearningStyleWeights?

    // Performance Patterns
    var strengthAreas: [String]?
    var weaknessAreas: [String]?
    var learningVelocity: Double? // Lessons per week
    var retentionRate: Double?

    // Behavioral Patterns
    var optimalStudyTimes: [StudyTimeData]?
    var engagementPatterns: [String: Double]?
    var challengePreference: ChallengePreference?

    // Spaced Repetition Data
    var forgettingCurveParams: ForgettingCurveParams?
    var nextReviewSchedule: [String: Date]?

    let createdAt: Date
    var updatedAt: Date

    enum ChallengePreference: String, Codable {
        case easy, moderate, challenging
    }
}

struct LearningStyleWeights: Codable {
    let visual: Double
    let auditory: Double
    let kinesthetic: Double
    let reading: Double
}

struct StudyTimeData: Codable {
    let hour: Int
    let performance: Double
    let engagementLevel: Double
}

struct ForgettingCurveParams: Codable {
    let initialStrength: Double
    let decayRate: Double
    let difficultyFactor: Double
}

// MARK: - Achievements System

struct LearningAchievement: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String
    let category: AchievementCategory
    let difficulty: AchievementDifficulty

    // Visual Elements
    let iconName: String?
    let colorHex: String?
    let badgeImageUrl: String?

    // Criteria
    let criteria: AchievementCriteria
    let pointsReward: Int

    // Metadata
    let isActive: Bool
    let isSecret: Bool
    let createdAt: Date

    enum AchievementCategory: String, Codable, CaseIterable {
        case progress, performance, consistency, social, special

        var displayName: String {
            switch self {
            case .progress: return "Progress"
            case .performance: return "Performance"
            case .consistency: return "Consistency"
            case .social: return "Social"
            case .special: return "Special"
            }
        }

        var icon: String {
            switch self {
            case .progress: return "chart.line.uptrend.xyaxis"
            case .performance: return "target"
            case .consistency: return "calendar"
            case .social: return "person.2"
            case .special: return "star"
            }
        }
    }

    enum AchievementDifficulty: String, Codable, CaseIterable {
        case bronze, silver, gold, platinum

        var color: Color {
            switch self {
            case .bronze: return .brown
            case .silver: return .gray
            case .gold: return .yellow
            case .platinum: return .purple
            }
        }

        var pointsMultiplier: Double {
            switch self {
            case .bronze: return 1.0
            case .silver: return 1.5
            case .gold: return 2.0
            case .platinum: return 3.0
            }
        }
    }
}

struct AchievementCriteria: Codable {
    let type: CriteriaType
    let count: Int?
    let language: String?
    let timeframe: String? // "daily", "weekly", "monthly"
    let accuracyThreshold: Double?
    let consecutiveDays: Int?

    enum CriteriaType: String, Codable {
        case lessonCompletion = "lesson_completion"
        case streakMaintenance = "streak_maintenance"
        case accuracyAchievement = "accuracy_achievement"
        case timeSpent = "time_spent"
        case vocabularyMastery = "vocabulary_mastery"
        case perfectScore = "perfect_score"
        case speedCompletion = "speed_completion"
        case socialInteraction = "social_interaction"
    }
}

struct UserLearningAchievement: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let achievementId: UUID
    let earnedAt: Date
    let progressData: [String: SupabaseAnyCodable]?
    var isShowcased: Bool
    let sharedAt: Date?

    // Computed properties for display
    var achievement: LearningAchievement? // This would be populated via join or separate fetch
}

// MARK: - Study Sessions

struct StudySession: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let languageId: UUID

    // Session Details
    let sessionType: SessionType
    var lessonsCompleted: Int
    var exercisesAttempted: Int
    var exercisesCorrect: Int

    // Performance Metrics
    var totalTimeSeconds: Int
    var averageAccuracy: Double?
    var wordsLearned: Int
    var wordsReviewed: Int

    // Engagement Metrics
    var interactionsCount: Int
    var hintsUsed: Int
    var breaksTaken: Int

    // Context
    let deviceType: String?
    let locationContext: String?

    // Timestamps
    let startedAt: Date
    let endedAt: Date?
    let createdAt: Date

    enum SessionType: String, Codable {
        case lesson, review, practice, assessment

        var displayName: String {
            switch self {
            case .lesson: return "Lesson"
            case .review: return "Review"
            case .practice: return "Practice"
            case .assessment: return "Assessment"
            }
        }

        var icon: String {
            switch self {
            case .lesson: return "book"
            case .review: return "arrow.clockwise"
            case .practice: return "pencil"
            case .assessment: return "checkmark.seal"
            }
        }
    }
}

// MARK: - Learning Path Progress

struct LearningPathProgress: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let pathId: UUID

    // Progress Details
    var currentStep: Int
    var totalSteps: Int
    var completedSteps: [Int]
    var currentLessonId: UUID?

    // Performance Metrics
    var overallAccuracy: Double
    var timeSpentMinutes: Int
    var lastActivityDate: Date

    // Adaptive Adjustments
    var difficultyAdjustments: [String: Double]
    var recommendedNextSteps: [UUID]

    // Timestamps
    let startedAt: Date
    var lastUpdated: Date

    // Computed properties
    var progressPercentage: Double {
        guard totalSteps > 0 else { return 0.0 }
        return Double(completedSteps.count) / Double(totalSteps)
    }

    var isCompleted: Bool {
        return completedSteps.count >= totalSteps
    }
}

// MARK: - Learning Goals

struct LearningGoal: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let languageId: UUID

    // Goal Details
    let goalType: GoalType
    let targetValue: Int
    var currentValue: Int
    let description: String

    // Timeline
    let targetDate: Date?
    let createdAt: Date
    let completedAt: Date?

    // Status
    var isActive: Bool
    var isAchieved: Bool

    // Computed properties
    var progressPercentage: Double {
        guard targetValue > 0 else { return 0.0 }
        return min(Double(currentValue) / Double(targetValue), 1.0)
    }

    var isOverdue: Bool {
        guard let targetDate = targetDate else { return false }
        return Date() > targetDate && !isAchieved
    }

    var rawValue: String {
        return goalType.rawValue
    }

    enum GoalType: String, Codable, CaseIterable {
        case dailyLessons = "daily_lessons"
        case weeklyTime = "weekly_time"
        case cefrLevel = "cefr_level"
        case vocabularyCount = "vocabulary_count"
        case streakDays = "streak_days"
        case accuracyTarget = "accuracy_target"

        var displayName: String {
            switch self {
            case .dailyLessons: return "Daily Lessons"
            case .weeklyTime: return "Weekly Study Time"
            case .cefrLevel: return "CEFR Level"
            case .vocabularyCount: return "Vocabulary Words"
            case .streakDays: return "Study Streak"
            case .accuracyTarget: return "Accuracy Target"
            }
        }

        var unit: String {
            switch self {
            case .dailyLessons: return "lessons"
            case .weeklyTime: return "minutes"
            case .cefrLevel: return "level"
            case .vocabularyCount: return "words"
            case .streakDays: return "days"
            case .accuracyTarget: return "%"
            }
        }

        var icon: String {
            switch self {
            case .dailyLessons: return "book.circle"
            case .weeklyTime: return "clock.circle"
            case .cefrLevel: return "graduationcap.circle"
            case .vocabularyCount: return "textbook.circle"
            case .streakDays: return "flame.circle"
            case .accuracyTarget: return "target"
            }
        }
    }
}

// MARK: - Content Recommendations

struct ContentRecommendation: Codable, Identifiable {
    let id: UUID
    let userId: UUID

    // Recommendation Details
    let contentType: String
    let contentId: UUID?
    let recommendationReason: String
    let confidenceScore: Double

    // Interaction Tracking
    let shownAt: Date
    let clickedAt: Date?
    let completedAt: Date?
    let dismissedAt: Date?

    // Metadata
    let algorithmVersion: String?
    let contextData: [String: SupabaseAnyCodable]?

    // Computed properties
    var wasInteractedWith: Bool {
        clickedAt != nil || dismissedAt != nil
    }

    var wasCompleted: Bool {
        completedAt != nil
    }
}

// MARK: - Analytics Summary Models

struct LearningAnalyticsSummary: Codable {
    let userId: UUID
    let languageId: UUID
    let timeframe: AnalyticsTimeframe

    // Performance Metrics
    let totalLessonsCompleted: Int
    let totalTimeSpent: Int // seconds
    let averageAccuracy: Double
    let totalWordsLearned: Int

    // Engagement Metrics
    let studyDaysCount: Int
    let averageSessionLength: Int // seconds
    let totalInteractions: Int

    // Progress Metrics
    let currentStreak: Int
    let longestStreak: Int
    let cefrLevelProgress: [String: Double] // level -> progress percentage

    // Behavioral Insights
    let optimalStudyHour: Int?
    let preferredSessionLength: Int?
    let strongestSkillAreas: [String]
    let improvementAreas: [String]

    enum AnalyticsTimeframe: String, Codable {
        case daily, weekly, monthly, allTime = "all_time"
    }
}