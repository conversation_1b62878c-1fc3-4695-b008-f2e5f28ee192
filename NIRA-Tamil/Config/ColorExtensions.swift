//
//  ColorExtensions.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

// MARK: - NIRA Color Extensions

extension Color {
    // MARK: - Modern Blue/Teal Theme Colors
    static let niraThemeBlue = Color(red: 0.15, green: 0.39, blue: 0.92)        // #2563EB
    static let niraThemeTeal = Color(red: 0.03, green: 0.57, blue: 0.70)        // #0891B2
    static let niraThemeCyan = Color(red: 0.02, green: 0.71, blue: 0.83)        // #06B6D4
    static let niraThemeIndigo = Color(red: 0.31, green: 0.31, blue: 0.82)      // #4F46E5
    static let niraThemeSuccess = Color(red: 0.02, green: 0.59, blue: 0.41)     // #059669
    static let niraThemeWarning = Color(red: 0.92, green: 0.35, blue: 0.05)     // #EA580C

    // MARK: - Gradient Collections
    // Note: Using direct Asset Catalog references to avoid conflicts with auto-generated extensions
    static let primaryGradient = [niraThemeBlue, niraThemeTeal]
    static let accentGradient = [niraThemeCyan, niraThemeBlue]
    static let successGradient = [niraThemeSuccess, niraThemeSuccess.opacity(0.7)]
    static let cardGradient = [Color("NiraCard"), Color("NiraSurface")]

    // European-Inspired Premium Gradients
    static let alpineGradient = [Color(red: 0.2, green: 0.3, blue: 0.5), Color(red: 0.4, green: 0.5, blue: 0.7)]
    static let mediterraneanGradient = [Color(red: 0.1, green: 0.4, blue: 0.6), Color(red: 0.2, green: 0.6, blue: 0.8)]
    static let nordicGradient = [Color(red: 0.3, green: 0.4, blue: 0.5), Color(red: 0.5, green: 0.6, blue: 0.7)]
    static let tuscanGradient = [Color(red: 0.6, green: 0.4, blue: 0.3), Color(red: 0.7, green: 0.5, blue: 0.4)]
    static let parisianGradient = [Color(red: 0.4, green: 0.3, blue: 0.5), Color(red: 0.6, green: 0.4, blue: 0.6)]
    static let swissGradient = [Color(red: 0.2, green: 0.5, blue: 0.4), Color(red: 0.3, green: 0.6, blue: 0.5)]

    // Premium Authentication Gradients
    static let authPrimaryGradient = [Color(red: 0.15, green: 0.25, blue: 0.45), Color(red: 0.25, green: 0.35, blue: 0.55)]
    static let authAccentGradient = [Color(red: 0.3, green: 0.5, blue: 0.7), Color(red: 0.4, green: 0.6, blue: 0.8)]
    static let authSurfaceGradient = [Color.white.opacity(0.95), Color.white.opacity(0.85)]

    // Dark mode adaptive colors (using Asset Catalog references)
    static let cardBackground = Color("NiraCard")
    static let primaryText = Color("NiraPrimaryText")
    static let secondaryText = Color("NiraSecondaryText")

    // Glassmorphism colors
    static let glassBackground = Color("NiraOverlay")
    static let glassBorder = Color("NiraPrimaryText").opacity(0.1)

    // MARK: - Level-Specific Gradients (A1-C2) - Vibrant Rainbow Progression
    static let levelA1Gradient = [emojiGreen, Color(red: 0.2, green: 0.8, blue: 0.4)]        // Green - Growth & Beginnings
    static let levelA2Gradient = [emojiOrange, Color(red: 1.0, green: 0.6, blue: 0.2)]       // Orange - Energy & Progress
    static let levelB1Gradient = [niraThemeBlue, Color(red: 0.3, green: 0.6, blue: 1.0)]     // Blue - Stability & Trust
    static let levelB2Gradient = [emojiPurple, Color(red: 0.7, green: 0.4, blue: 0.9)]       // Purple - Creativity & Sophistication
    static let levelC1Gradient = [Color(red: 1.0, green: 0.3, blue: 0.5), Color(red: 0.9, green: 0.2, blue: 0.4)]   // Pink/Red - Passion & Expertise
    static let levelC2Gradient = [emojiYellow, Color(red: 1.0, green: 0.8, blue: 0.2)]       // Gold/Yellow - Mastery & Achievement

    // MARK: - Level Colors (Single Colors) - Vibrant Rainbow Progression
    static let levelA1Color = emojiGreen           // Green - Growth & Beginnings
    static let levelA2Color = emojiOrange          // Orange - Energy & Progress
    static let levelB1Color = niraThemeBlue       // Blue - Stability & Trust
    static let levelB2Color = emojiPurple         // Purple - Creativity & Sophistication
    static let levelC1Color = Color(red: 1.0, green: 0.3, blue: 0.5)  // Pink - Passion & Expertise
    static let levelC2Color = emojiYellow         // Gold - Mastery & Achievement

    // Emoji-inspired colors - More Refined
    static let emojiYellow = Color(red: 0.9, green: 0.8, blue: 0.3)
    static let emojiPink = Color(red: 0.9, green: 0.4, blue: 0.6)
    static let emojiBlue = Color(red: 0.3, green: 0.6, blue: 0.9)
    static let emojiGreen = Color(red: 0.3, green: 0.8, blue: 0.4)
    static let emojiPurple = Color(red: 0.6, green: 0.3, blue: 0.8)
    static let emojiOrange = Color(red: 0.9, green: 0.5, blue: 0.2)

    // MARK: - Gradient Start Colors (for AuthenticationView)
    static let niraGradientStart = Color("NiraPrimary")
    static let niraGradientEnd = Color("NiraSecondary")

    // MARK: - Helper Functions for Level Colors
    static func getLevelGradient(for difficultyLevel: Int) -> [Color] {
        switch difficultyLevel {
        case 1: return levelA1Gradient
        case 2: return levelA2Gradient
        case 3: return levelB1Gradient
        case 4: return levelB2Gradient
        case 5: return levelC1Gradient
        case 6: return levelC2Gradient
        default: return levelA1Gradient
        }
    }

    static func getLevelColor(for difficultyLevel: Int) -> Color {
        switch difficultyLevel {
        case 1: return levelA1Color
        case 2: return levelA2Color
        case 3: return levelB1Color
        case 4: return levelB2Color
        case 5: return levelC1Color
        case 6: return levelC2Color
        default: return levelA1Color
        }
    }

    static func getLevelGradientByName(_ levelName: String) -> [Color] {
        switch levelName {
        case "All": return [.gray, .gray.opacity(0.8)]
        case "Beginner Foundations": return levelA1Gradient
        case "Elementary Essentials": return levelA2Gradient
        case "Intermediate Conversations": return levelB1Gradient
        case "Advanced Communication": return levelB2Gradient
        case "Professional Mastery": return levelC1Gradient
        case "Expert Fluency": return levelC2Gradient
        default: return [.gray, .gray.opacity(0.8)]
        }
    }

    static func getLevelColorByName(_ levelName: String) -> Color {
        switch levelName {
        case "All": return .gray
        case "Beginner Foundations": return levelA1Color
        case "Elementary Essentials": return levelA2Color
        case "Intermediate Conversations": return levelB1Color
        case "Advanced Communication": return levelB2Color
        case "Professional Mastery": return levelC1Color
        case "Expert Fluency": return levelC2Color
        default: return .gray
        }
    }

    // MARK: - Language-specific Colors (using vibrant rainbow system)
    static let frenchColor = emojiBlue
    static let spanishColor = emojiOrange
    static let germanColor = emojiYellow
    static let italianColor = emojiGreen
    static let portugueseColor = emojiPurple
    static let englishColor = niraThemeBlue
    static let japaneseColor = Color(red: 1.0, green: 0.3, blue: 0.5) // Pink
    static let tamilColor = emojiGreen

    // NIRA Brand Colors
    static let niraInfo = niraThemeBlue
}

// MARK: - Premium Design System

extension Font {
    // European-inspired typography hierarchy
    static let displayLarge = Font.system(size: 57, weight: .light, design: .default)
    static let displayMedium = Font.system(size: 45, weight: .light, design: .default)
    static let displaySmall = Font.system(size: 36, weight: .regular, design: .default)

    static let headlineLarge = Font.system(size: 32, weight: .medium, design: .default)
    static let headlineMedium = Font.system(size: 28, weight: .medium, design: .default)
    static let headlineSmall = Font.system(size: 24, weight: .medium, design: .default)

    static let titleLarge = Font.system(size: 22, weight: .semibold, design: .default)
    static let titleMedium = Font.system(size: 16, weight: .semibold, design: .default)
    static let titleSmall = Font.system(size: 14, weight: .semibold, design: .default)

    static let bodyLarge = Font.system(size: 16, weight: .regular, design: .default)
    static let bodyMedium = Font.system(size: 14, weight: .regular, design: .default)
    static let bodySmall = Font.system(size: 12, weight: .regular, design: .default)

    static let labelLarge = Font.system(size: 14, weight: .medium, design: .default)
    static let labelMedium = Font.system(size: 12, weight: .medium, design: .default)
    static let labelSmall = Font.system(size: 11, weight: .medium, design: .default)
}

// MARK: - Premium Spacing System

struct Spacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
    static let xxxl: CGFloat = 64
}

// MARK: - Premium Corner Radius System

struct CornerRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 24
    static let xxl: CGFloat = 32
}