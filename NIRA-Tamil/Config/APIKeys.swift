import Foundation

// MARK: - API Configuration Template
// INSTRUCTIONS:
// 1. Copy this file to APIKeys.swift
// 2. Replace all placeholder values with your actual API keys
// 3. Never commit APIKeys.swift to version control

struct APIKeys {
    // Gemini AI API Key
    // Get from: https://ai.google.dev/tutorials/setup
    static let geminiAPIKey = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"

    // Supabase Configuration
    // Get from: https://app.supabase.com/project/your-project/settings/api
    static let supabaseURL = "https://wnsorhbsucjguaoquhvr.supabase.co"
    static let supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

    // OpenAI API Key (optional - for fallback)
    // Get from: https://platform.openai.com/api-keys
    static let openAIAPIKey = "********************************************************************************************************************************************************************"

    // Note: We use Google Text-to-Speech for audio generation
    // No additional API keys needed for Google TTS
}

// MARK: - Development Configuration
struct APIConfig {
    static let isDevelopment = true
    static let useMockData = false // FORCE REAL API INTEGRATION - NO MOCK DATA
    
    // API endpoints
    static let geminiBaseURL = "https://generativelanguage.googleapis.com/v1beta/models"
    static let geminiChatModel = "gemini-2.0-flash-exp:generateContent"
    static let geminiLiteModel = "gemini-2.0-flash-exp:generateContent" // Using Flash Lite 2.0
    static let geminiLiveModel = "gemini-2.0-flash-live-001"
    static let geminiVisionModel = "gemini-2.0-flash-exp:generateContent"
    static let geminiLiveWebSocketURL = "wss://generativelanguage.googleapis.com/ws/v1beta/models/gemini-2.0-flash-live-001"
    
    // Supabase Storage
    static let supabaseStorageBucket = "knowledge-base"
    static let maxFileSize: Int64 = 50 * 1024 * 1024 // 50MB
    static let allowedFileTypes = ["pdf", "doc", "docx", "txt", "md", "jpg", "jpeg", "png", "mp3", "wav", "m4a", "mp4", "mov"]
    
    // Voice Configuration
    static let speechRecognitionTimeout: TimeInterval = 30.0
    static let voiceRecordingMaxDuration: TimeInterval = 300.0 // 5 minutes
    static let audioSampleRate: Double = 16000.0
    
    // AI Response Configuration
    static let maxContextTokens = 8192
    static let responseTimeout: TimeInterval = 30.0
    static let maxRetryAttempts = 3
}

// MARK: - Helper Extensions
extension APIKeys {
    static var isConfigured: Bool {
        return !geminiAPIKey.contains("YOUR_") && 
               !supabaseURL.contains("your-project") &&
               !supabaseAnonKey.contains("YOUR_")
    }
    
    static var supabaseConfigured: Bool {
        return !supabaseURL.contains("your-project") && !supabaseAnonKey.contains("YOUR_")
    }
    
    static func validateConfiguration() throws {
        guard isConfigured else {
            throw ConfigurationError.missingAPIKeys
        }
    }
}

enum ConfigurationError: LocalizedError {
    case missingAPIKeys
    case invalidFileType
    case fileTooLarge
    case uploadFailed
    case networkError
    case speechPermissionDenied
    case voiceRecordingFailed
    case aiServiceUnavailable
    case knowledgeBaseError
    case conversationLoadFailed
    
    var errorDescription: String? {
        switch self {
        case .missingAPIKeys:
            return "Please configure your API keys in APIKeys.swift before using the app. Copy APIKeys.swift.template to APIKeys.swift and add your keys."
        case .invalidFileType:
            return "This file type is not supported. Please upload a PDF, document, image, or audio file."
        case .fileTooLarge:
            return "File is too large. Maximum size is 50MB."
        case .uploadFailed:
            return "Failed to upload file. Please try again."
        case .networkError:
            return "Network connection error. Please check your internet connection."
        case .speechPermissionDenied:
            return "Speech recognition permission denied. Please enable microphone access in Settings."
        case .voiceRecordingFailed:
            return "Voice recording failed. Please try again."
        case .aiServiceUnavailable:
            return "AI service is temporarily unavailable. Please try again later."
        case .knowledgeBaseError:
            return "Knowledge base error. Please try again."
        case .conversationLoadFailed:
            return "Failed to load conversation history. Please try again."
        }
    }
}