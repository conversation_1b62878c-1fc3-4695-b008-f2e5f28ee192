//
//  ExploreAudioService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import Foundation
import AVFoundation
import Combine

// MARK: - Audio Content Types

enum ExploreContentType: String, CaseIterable {
    case literature = "literature"
    case festival = "festival"
    case culturalInsight = "cultural_insight"
    case cinema = "cinema"
    case sports = "sports"
    case location = "location"
    case calendarConcept = "calendar_concept"
}

// MARK: - Audio Generation Request

struct AudioGenerationRequest {
    let contentId: UUID
    let contentType: ExploreContentType
    let tamilText: String
    let romanization: String?
    let voiceType: VoiceType
    let priority: AudioPriority
}

enum VoiceType: String, CaseIterable {
    case female = "ta-IN-Chirp3-HD-Erinome"
    case male = "ta-IN-Chirp3-HD-Iapetus"
    
    var displayName: String {
        switch self {
        case .female: return "Female Voice"
        case .male: return "Male Voice"
        }
    }
}

enum AudioPriority: Int, CaseIterable {
    case high = 1
    case medium = 2
    case low = 3
}

// MARK: - Audio Generation Result

struct ExploreAudioResult {
    let contentId: UUID
    let contentType: ExploreContentType
    let audioURL: String
    let voiceType: VoiceType
    let duration: TimeInterval?
    let fileSize: Int64?
    let generatedAt: Date
}

// MARK: - Explore Audio Service

@MainActor
class ExploreAudioService: ObservableObject {
    static let shared = ExploreAudioService()
    
    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var statusMessage = ""
    @Published var generatedAudioCount = 0
    @Published var totalAudioToGenerate = 0
    @Published var currentlyPlaying: String?
    @Published var playbackError: String?
    
    // Audio generation queue
    private var generationQueue: [AudioGenerationRequest] = []
    private var isProcessingQueue = false
    
    // Services
    private let googleTTSService = GoogleTTSService.shared
    private let audioPlayerService = AudioPlayerService.shared
    private let supabaseClient = NIRASupabaseClient.shared
    
    // Google TTS Configuration
    private let languageCode = "ta-IN"
    private let audioFormat = "MP3"
    private let speakingRate = 0.9 // Slightly slower for learning
    private let pitch = 0.0
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupAudioPlayerObservation()
    }
    
    // MARK: - Public Methods
    
    /// Generate audio for all explore content
    func generateAllExploreAudio() async {
        isGenerating = true
        generationProgress = 0.0
        generatedAudioCount = 0
        statusMessage = "Preparing to generate audio for all explore content..."
        
        do {
            // Collect all content that needs audio generation
            let requests = await collectAudioGenerationRequests()
            totalAudioToGenerate = requests.count
            
            statusMessage = "Generating audio for \(totalAudioToGenerate) items..."
            
            // Process requests in batches to avoid overwhelming the API
            let batchSize = 5
            for batch in Array(requests).chunked(into: batchSize) {
                await processBatch(batch)
                
                // Small delay between batches to respect API limits
                try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            }
            
            statusMessage = "Audio generation completed! Generated \(generatedAudioCount) files."
            
        } catch {
            statusMessage = "Audio generation failed: \(error.localizedDescription)"
            print("❌ Audio generation error: \(error)")
        }
        
        isGenerating = false
    }
    
    /// Generate audio for specific content
    func generateAudio(for request: AudioGenerationRequest) async throws -> ExploreAudioResult {
        statusMessage = "Generating audio for \(request.contentType.rawValue)..."
        
        // Generate audio using Google TTS
        let audioData = try await generateAudioData(
            text: request.tamilText,
            voice: request.voiceType
        )
        
        // Upload to Supabase storage
        let filename = generateFilename(for: request)
        let audioURL = try await uploadAudioToSupabase(
            audioData: audioData,
            filename: filename
        )
        
        // Store audio metadata in database
        try await storeAudioMetadata(
            contentId: request.contentId,
            contentType: request.contentType,
            audioURL: audioURL,
            voiceType: request.voiceType,
            audioData: audioData
        )
        
        generatedAudioCount += 1
        
        return ExploreAudioResult(
            contentId: request.contentId,
            contentType: request.contentType,
            audioURL: audioURL,
            voiceType: request.voiceType,
            duration: nil, // Could be calculated from audio data
            fileSize: Int64(audioData.count),
            generatedAt: Date()
        )
    }
    
    /// Play audio for specific content
    func playAudio(contentId: UUID, contentType: ExploreContentType, voiceType: VoiceType = .female) async {
        do {
            // Check if audio exists in database
            if let audioURL = try await getAudioURL(
                contentId: contentId,
                contentType: contentType,
                voiceType: voiceType
            ) {
                // Play existing audio
                audioPlayerService.playAudio(from: audioURL)
                currentlyPlaying = audioURL
            } else {
                // Generate audio on-demand
                if let request = await createAudioRequest(
                    contentId: contentId,
                    contentType: contentType,
                    voiceType: voiceType
                ) {
                    let result = try await generateAudio(for: request)
                    audioPlayerService.playAudio(from: result.audioURL)
                    currentlyPlaying = result.audioURL
                }
            }
        } catch {
            playbackError = "Failed to play audio: \(error.localizedDescription)"
            print("❌ Audio playback error: \(error)")
        }
    }
    
    /// Stop current audio playback
    func stopAudio() {
        audioPlayerService.stopAudio()
        currentlyPlaying = nil
    }
    
    /// Get romanization for Tamil text
    func getRomanization(for tamilText: String) -> String {
        // This is a simplified romanization - in a real app, you'd use a proper transliteration library
        return romanizeTamilText(tamilText)
    }
    
    // MARK: - Private Methods
    
    private func setupAudioPlayerObservation() {
        audioPlayerService.$isPlaying
            .sink { [weak self] isPlaying in
                if !isPlaying {
                    self?.currentlyPlaying = nil
                }
            }
            .store(in: &cancellables)
        
        audioPlayerService.$playbackError
            .sink { [weak self] error in
                self?.playbackError = error
            }
            .store(in: &cancellables)
    }
    
    private func collectAudioGenerationRequests() async -> [AudioGenerationRequest] {
        var requests: [AudioGenerationRequest] = []
        
        // Literature content
        let literatureService = LiteratureService.shared
        await literatureService.loadContent()
        for content in literatureService.allContent {
            requests.append(AudioGenerationRequest(
                contentId: content.id,
                contentType: .literature,
                tamilText: content.contentTamil,
                romanization: content.romanization,
                voiceType: .female,
                priority: content.isFeatured ? .high : .medium
            ))
        }
        
        // Calendar concepts
        let calendarService = EnhancedCalendarService.shared
        await calendarService.loadContent()
        for concept in calendarService.calendarConcepts {
            requests.append(AudioGenerationRequest(
                contentId: concept.id,
                contentType: .calendarConcept,
                tamilText: concept.conceptNameTamil,
                romanization: concept.romanization,
                voiceType: .female,
                priority: .medium
            ))
        }
        
        // Festivals
        for festival in calendarService.allFestivals {
            requests.append(AudioGenerationRequest(
                contentId: festival.id,
                contentType: .festival,
                tamilText: festival.nameTamil,
                romanization: festival.romanization,
                voiceType: .female,
                priority: festival.culturalImportance == "very_high" ? .high : .medium
            ))
        }
        
        // Cultural locations
        let mapService = CulturalMapService.shared
        await mapService.loadLocations()
        for location in mapService.allLocations {
            requests.append(AudioGenerationRequest(
                contentId: location.id,
                contentType: .location,
                tamilText: location.nameTamil,
                romanization: location.romanization,
                voiceType: .female,
                priority: location.isFeatured ? .high : .low
            ))
        }
        
        // Sort by priority
        return requests.sorted { $0.priority.rawValue < $1.priority.rawValue }
    }
    
    private func processBatch(_ batch: [AudioGenerationRequest]) async {
        for request in batch {
            do {
                let _ = try await generateAudio(for: request)
                generationProgress = Double(generatedAudioCount) / Double(totalAudioToGenerate)
            } catch {
                print("❌ Failed to generate audio for \(request.contentType): \(error)")
            }
        }
    }
    
    private func generateAudioData(text: String, voice: VoiceType) async throws -> Data {
        // Use the existing GoogleTTSService
        return try await googleTTSService.generateAudioData(text: text, filename: "temp")
    }
    
    private func uploadAudioToSupabase(audioData: Data, filename: String) async throws -> String {
        // Use the existing GoogleTTSService upload method
        return try await googleTTSService.uploadAudioToSupabase(audioData: audioData, filename: filename)
    }
    
    private func storeAudioMetadata(
        contentId: UUID,
        contentType: ExploreContentType,
        audioURL: String,
        voiceType: VoiceType,
        audioData: Data
    ) async throws {
        struct AudioRecord: Codable {
            let content_type: String
            let content_id: String
            let text_tamil: String
            let voice_type: String
            let voice_model: String
            let audio_url: String
            let file_size_bytes: Int
            let is_active: Bool
        }

        let audioRecord = AudioRecord(
            content_type: contentType.rawValue,
            content_id: contentId.uuidString,
            text_tamil: "", // Would need to pass this in
            voice_type: voiceType == .female ? "female" : "male",
            voice_model: voiceType.rawValue,
            audio_url: audioURL,
            file_size_bytes: audioData.count,
            is_active: true
        )

        try await supabaseClient.client.from("audio_content")
            .insert(audioRecord)
            .execute()
    }
    
    private func getAudioURL(
        contentId: UUID,
        contentType: ExploreContentType,
        voiceType: VoiceType
    ) async throws -> String? {

        // For literature content, check the literature_content table directly
        if contentType == .literature {
            do {
                let response: [LiteratureAudioRecord] = try await supabaseClient.client
                    .from("literature_content")
                    .select("audio_url, audio_female_url, audio_male_url")
                    .eq("id", value: contentId)
                    .execute()
                    .value

                guard let record = response.first else {
                    print("📚 No literature record found for ID: \(contentId)")
                    return nil
                }

                let audioURL: String?
                switch voiceType {
                case .female:
                    audioURL = record.audioFemaleUrl ?? record.audioUrl
                case .male:
                    audioURL = record.audioMaleUrl ?? record.audioUrl
                }

                if let url = audioURL {
                    print("🎵 Found literature audio URL: \(url)")
                    return url
                } else {
                    print("🔇 No audio URL available for literature content: \(contentId)")
                    return nil
                }

            } catch {
                print("❌ Failed to get literature audio URL: \(error)")
                return nil
            }
        }

        // For other content types, check the audio_content table
        do {
            let response: [AudioContentRecord] = try await supabaseClient.client
                .from("audio_content")
                .select("audio_url")
                .eq("content_id", value: contentId.uuidString)
                .eq("content_type", value: contentType.rawValue)
                .eq("voice_type", value: voiceType == .female ? "female" : "male")
                .eq("is_active", value: true)
                .execute()
                .value

            return response.first?.audioUrl

        } catch {
            print("❌ Failed to get audio URL: \(error)")
            return nil
        }
    }
    
    private func createAudioRequest(
        contentId: UUID,
        contentType: ExploreContentType,
        voiceType: VoiceType
    ) async -> AudioGenerationRequest? {
        do {
            // Fetch the Tamil text for the given content based on content type
            var tamilText: String?

            switch contentType {
            case .literature:
                // Fetch from literature table
                let response: [LiteratureContentRecord] = try await supabaseClient.client
                    .from("literature_content")
                    .select("tamil_text")
                    .eq("id", value: contentId.uuidString)
                    .execute()
                    .value

                tamilText = response.first?.tamilText

            case .festival:
                // Fetch from festivals table
                let response: [FestivalContentRecord] = try await supabaseClient.client
                    .from("festivals")
                    .select("tamil_text")
                    .eq("id", value: contentId.uuidString)
                    .execute()
                    .value

                tamilText = response.first?.tamilText

            case .culturalInsight:
                // Fetch from culture table
                let response: [CultureContentRecord] = try await supabaseClient.client
                    .from("culture_content")
                    .select("tamil_text")
                    .eq("id", value: contentId.uuidString)
                    .execute()
                    .value

                tamilText = response.first?.tamilText
            default:
                // For other content types, return nil for now
                print("❌ Content type \(contentType) not supported for audio generation")
                return nil
            }

            guard let text = tamilText, !text.isEmpty else {
                print("❌ No Tamil text found for content: \(contentId)")
                return nil
            }

            return AudioGenerationRequest(
                contentId: contentId,
                contentType: contentType,
                tamilText: text,
                romanization: nil, // Could be generated if needed
                voiceType: voiceType,
                priority: .medium
            )

        } catch {
            print("❌ Failed to create audio request: \(error)")
            return nil
        }
    }
    
    private func generateFilename(for request: AudioGenerationRequest) -> String {
        let timestamp = Int(Date().timeIntervalSince1970)
        return "\(request.contentType.rawValue)_\(request.contentId.uuidString.prefix(8))_\(request.voiceType == .female ? "f" : "m")_\(timestamp).mp3"
    }
    
    private func romanizeTamilText(_ tamilText: String) -> String {
        // Simplified romanization mapping
        let romanizationMap: [String: String] = [
            "அ": "a", "ஆ": "aa", "இ": "i", "ஈ": "ii", "உ": "u", "ஊ": "uu",
            "எ": "e", "ஏ": "ee", "ஐ": "ai", "ஒ": "o", "ஓ": "oo", "ஔ": "au",
            "க": "ka", "ங": "nga", "ச": "cha", "ஞ": "nya", "ட": "ta", "ண": "na",
            "த": "tha", "ந": "na", "ப": "pa", "ம": "ma", "ய": "ya", "ர": "ra",
            "ல": "la", "வ": "va", "ழ": "zha", "ள": "la", "ற": "ra", "ன": "na"
        ]
        
        var romanized = tamilText
        for (tamil, roman) in romanizationMap {
            romanized = romanized.replacingOccurrences(of: tamil, with: roman)
        }
        
        return romanized
    }
}

// MARK: - Supporting Data Structures

struct LiteratureAudioRecord: Codable {
    let audioUrl: String?
    let audioFemaleUrl: String?
    let audioMaleUrl: String?

    enum CodingKeys: String, CodingKey {
        case audioUrl = "audio_url"
        case audioFemaleUrl = "audio_female_url"
        case audioMaleUrl = "audio_male_url"
    }
}

struct AudioContentRecord: Codable {
    let audioUrl: String

    enum CodingKeys: String, CodingKey {
        case audioUrl = "audio_url"
    }
}

struct LiteratureContentRecord: Codable {
    let tamilText: String

    enum CodingKeys: String, CodingKey {
        case tamilText = "tamil_text"
    }
}

struct FestivalContentRecord: Codable {
    let tamilText: String

    enum CodingKeys: String, CodingKey {
        case tamilText = "tamil_text"
    }
}

struct CultureContentRecord: Codable {
    let tamilText: String

    enum CodingKeys: String, CodingKey {
        case tamilText = "tamil_text"
    }
}


