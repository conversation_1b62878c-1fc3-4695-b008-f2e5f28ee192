//
//  DynamicContentService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import Foundation
import Combine

// MARK: - Content Source Types

enum ContentSource {
    case supabase
    case publicAPI(String)
    case cached
}

// MARK: - Content Update Status

struct ContentUpdateStatus {
    let contentType: String
    let lastUpdated: Date
    let source: ContentSource
    let recordCount: Int
    let isStale: Bool
    
    var staleDuration: TimeInterval {
        Date().timeIntervalSince(lastUpdated)
    }
}

// MARK: - API Integration Models

struct TMDBMovieResponse: Codable {
    let results: [TMDBMovie]
    let totalResults: Int
    let totalPages: Int
    
    enum CodingKeys: String, CodingKey {
        case results
        case totalResults = "total_results"
        case totalPages = "total_pages"
    }
}

struct TMDBMovie: Codable {
    let id: Int
    let title: String
    let originalTitle: String
    let overview: String
    let releaseDate: String
    let posterPath: String?
    let backdropPath: String?
    let voteAverage: Double
    let popularity: Double
    
    enum CodingKeys: String, CodingKey {
        case id, title, overview, popularity
        case originalTitle = "original_title"
        case releaseDate = "release_date"
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case voteAverage = "vote_average"
    }
}

struct NewsAPIResponse: Codable {
    let status: String
    let totalResults: Int
    let articles: [NewsArticle]
}

struct NewsArticle: Codable {
    let title: String
    let description: String?
    let url: String
    let urlToImage: String?
    let publishedAt: String
    let source: NewsSource
}

struct NewsSource: Codable {
    let id: String?
    let name: String
}

// MARK: - Dynamic Content Service

@MainActor
class DynamicContentService: ObservableObject {
    static let shared = DynamicContentService()
    
    @Published var isUpdating = false
    @Published var updateProgress = 0.0
    @Published var statusMessage = ""
    @Published var lastUpdateTime: Date?
    @Published var contentStatus: [ContentUpdateStatus] = []
    @Published var error: Error?
    
    // Cache settings
    private let cacheExpiryInterval: TimeInterval = 3600 // 1 hour
    private let maxCacheAge: TimeInterval = 86400 // 24 hours
    
    // API Keys and endpoints
    private let tmdbAPIKey = "your_tmdb_api_key" // Replace with actual key
    private let newsAPIKey = "your_news_api_key" // Replace with actual key
    
    private let supabaseClient = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        loadContentStatus()
    }
    
    // MARK: - Public Methods
    
    /// Update all dynamic content from various sources
    func updateAllContent() async {
        isUpdating = true
        updateProgress = 0.0
        statusMessage = "Starting content update..."
        error = nil
        
        let updateTasks = [
            ("Cinema Content", updateCinemaContent),
            ("Sports Content", updateSportsContent),
            ("Festival Dates", updateFestivalDates),
            ("News Content", updateNewsContent),
            ("Cultural Events", updateCulturalEvents)
        ]
        
        let totalTasks = updateTasks.count
        
        for (index, (taskName, updateFunction)) in updateTasks.enumerated() {
            statusMessage = "Updating \(taskName)..."
            
            do {
                try await updateFunction()
                updateProgress = Double(index + 1) / Double(totalTasks)
                print("✅ Updated \(taskName)")
            } catch {
                print("❌ Failed to update \(taskName): \(error)")
                self.error = error
            }
            
            // Small delay between updates
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        }
        
        lastUpdateTime = Date()
        statusMessage = "Content update completed!"
        isUpdating = false
        
        // Update content status
        await updateContentStatus()
    }
    
    /// Check if content needs updating
    func checkContentFreshness() async -> Bool {
        guard let lastUpdate = lastUpdateTime else { return true }
        
        let timeSinceUpdate = Date().timeIntervalSince(lastUpdate)
        return timeSinceUpdate > cacheExpiryInterval
    }
    
    /// Force refresh specific content type
    func refreshContent(type: String) async {
        statusMessage = "Refreshing \(type)..."
        
        switch type.lowercased() {
        case "cinema":
            try? await updateCinemaContent()
        case "sports":
            try? await updateSportsContent()
        case "festivals":
            try? await updateFestivalDates()
        case "news":
            try? await updateNewsContent()
        case "events":
            try? await updateCulturalEvents()
        default:
            break
        }
        
        await updateContentStatus()
    }
    
    // MARK: - Content Update Methods
    
    private func updateCinemaContent() async throws {
        // Update Tamil cinema content from TMDB API
        let tamilMovies = try await fetchTamilMoviesFromTMDB()
        
        // Process and store in Supabase
        for movie in tamilMovies {
            struct CinemaContent: Codable {
                let title: String
                let title_tamil: String
                let content_type: String
                let description: String
                let year_released: Int
                let cultural_impact: String
                let modern_relevance: String
                let image_url: String?
                let tags: [String]
                let difficulty_level: String
                let is_featured: Bool
                let created_at: String
                let updated_at: String
            }

            let cinemaContent = CinemaContent(
                title: movie.title,
                title_tamil: movie.originalTitle,
                content_type: "movie",
                description: movie.overview,
                year_released: extractYear(from: movie.releaseDate),
                cultural_impact: "Contemporary Tamil cinema",
                modern_relevance: "Current release",
                image_url: movie.posterPath.map { "https://image.tmdb.org/t/p/w500\($0)" },
                tags: ["current", "cinema", "tamil"],
                difficulty_level: "beginner",
                is_featured: movie.popularity > 100,
                created_at: Date().toISOString(),
                updated_at: Date().toISOString()
            )

            try await supabaseClient.client.from("cinema_content")
                .insert(cinemaContent)
                .execute()
        }
    }
    
    private func updateSportsContent() async throws {
        // Update sports content with current events and news
        // This would integrate with sports APIs for current Tamil sports events
        
        let currentSportsEvents = [
            [
                "name": "Pro Kabaddi League 2025",
                "name_tamil": "ப்ரோ கபடி லீக் 2025",
                "sport_type": "modern",
                "description": "Professional kabaddi league featuring Tamil Nadu teams",
                "description_tamil": "தமிழ்நாடு அணிகள் கலந்துகொள்ளும் தொழில்முறை கபடி லீக்",
                "cultural_significance": "Promotes traditional Tamil sport on national stage",
                "modern_status": "Currently ongoing professional league",
                "tags": ["current", "professional", "kabaddi"],
                "difficulty_level": "intermediate",
                "is_featured": true,
                "created_at": Date().toISOString(),
                "updated_at": Date().toISOString()
            ]
        ]
        
        struct SportsEvent: Codable {
            let name: String
            let name_tamil: String
            let sport_type: String
            let description: String
            let description_tamil: String
            let cultural_significance: String
            let modern_status: String
            let tags: [String]
            let difficulty_level: String
            let is_featured: Bool
            let created_at: String
            let updated_at: String
        }

        for eventData in currentSportsEvents {
            let event = SportsEvent(
                name: eventData["name"] as! String,
                name_tamil: eventData["name_tamil"] as! String,
                sport_type: eventData["sport_type"] as! String,
                description: eventData["description"] as! String,
                description_tamil: eventData["description_tamil"] as! String,
                cultural_significance: eventData["cultural_significance"] as! String,
                modern_status: eventData["modern_status"] as! String,
                tags: eventData["tags"] as! [String],
                difficulty_level: eventData["difficulty_level"] as! String,
                is_featured: eventData["is_featured"] as! Bool,
                created_at: eventData["created_at"] as! String,
                updated_at: eventData["updated_at"] as! String
            )

            try await supabaseClient.client.from("sports_content")
                .insert(event)
                .execute()
        }
    }
    
    private func updateFestivalDates() async throws {
        // Update festival dates with current year calculations
        // This would integrate with calendar APIs for accurate lunar dates
        
        let currentYear = Calendar.current.component(.year, from: Date())
        let updatedFestivals = calculateFestivalDates(for: currentYear)
        
        struct FestivalUpdate: Codable {
            let festival_date: String
            let updated_at: String
        }

        for festival in updatedFestivals {
            let update = FestivalUpdate(
                festival_date: festival["festival_date"] as! String,
                updated_at: Date().toISOString()
            )

            try await supabaseClient.client.from("tamil_festivals")
                .update(update)
                .eq("name", value: festival["name"] as! String)
                .execute()
        }
    }
    
    private func updateNewsContent() async throws {
        // This would integrate with news APIs for Tamil culture and heritage news
        // For now, we'll create sample cultural news
        
        let culturalNews = [
            [
                "title": "Tamil Heritage Month Celebrations Begin",
                "title_tamil": "தமிழ் பாரம்பரிய மாத கொண்டாட்டங்கள் தொடங்கின",
                "description": "Cultural events and exhibitions showcase Tamil heritage across the state",
                "content_type": "news",
                "category": "culture",
                "published_date": Date().toISOString(),
                "is_featured": true,
                "tags": ["heritage", "culture", "celebration"],
                "created_at": Date().toISOString()
            ]
        ]
        
        // Store in a news table (would need to create this table)
        for news in culturalNews {
            // This would go to a news_content table
            print("📰 Would store news: \(news["title"] ?? "")")
        }
    }
    
    private func updateCulturalEvents() async throws {
        // Update current cultural events and exhibitions
        let currentEvents = [
            [
                "event_name": "Chennai Music Season 2025",
                "event_name_tamil": "சென்னை இசை பருவம் 2025",
                "event_type": "music",
                "description": "Annual Carnatic music festival featuring renowned artists",
                "start_date": "2025-12-15",
                "end_date": "2026-01-15",
                "location": "Chennai",
                "cultural_significance": "Premier platform for Carnatic music",
                "is_active": true,
                "tags": ["music", "carnatic", "festival"],
                "created_at": Date().toISOString()
            ]
        ]
        
        // Store in cultural_events table (would need to create this)
        for event in currentEvents {
            print("🎭 Would store event: \(event["event_name"] ?? "")")
        }
    }
    
    // MARK: - API Integration Methods
    
    private func fetchTamilMoviesFromTMDB() async throws -> [TMDBMovie] {
        // This would make actual API calls to TMDB
        // For now, return mock data
        return [
            TMDBMovie(
                id: 1,
                title: "Sample Tamil Movie",
                originalTitle: "மாதிரி தமிழ் படம்",
                overview: "A contemporary Tamil film showcasing cultural themes",
                releaseDate: "2025-01-15",
                posterPath: "/sample_poster.jpg",
                backdropPath: "/sample_backdrop.jpg",
                voteAverage: 7.5,
                popularity: 150.0
            )
        ]
    }
    
    private func calculateFestivalDates(for year: Int) -> [[String: Any]] {
        // This would calculate accurate lunar dates for festivals
        // For now, return sample data
        return [
            [
                "name": "Pongal",
                "festival_date": "2025-01-14"
            ],
            [
                "name": "Tamil New Year",
                "festival_date": "2025-04-14"
            ]
        ]
    }
    
    // MARK: - Content Status Management
    
    private func loadContentStatus() {
        // Load last update times from UserDefaults or database
        if let lastUpdate = UserDefaults.standard.object(forKey: "lastContentUpdate") as? Date {
            lastUpdateTime = lastUpdate
        }
    }
    
    private func updateContentStatus() async {
        // Update content status for monitoring
        contentStatus = [
            ContentUpdateStatus(
                contentType: "Cinema",
                lastUpdated: lastUpdateTime ?? Date.distantPast,
                source: .publicAPI("TMDB"),
                recordCount: await getContentCount("cinema_content"),
                isStale: await checkContentFreshness()
            ),
            ContentUpdateStatus(
                contentType: "Sports",
                lastUpdated: lastUpdateTime ?? Date.distantPast,
                source: .supabase,
                recordCount: await getContentCount("sports_content"),
                isStale: await checkContentFreshness()
            ),
            ContentUpdateStatus(
                contentType: "Festivals",
                lastUpdated: lastUpdateTime ?? Date.distantPast,
                source: .supabase,
                recordCount: await getContentCount("tamil_festivals"),
                isStale: await checkContentFreshness()
            )
        ]
        
        // Save update time
        UserDefaults.standard.set(lastUpdateTime, forKey: "lastContentUpdate")
    }
    
    private func getContentCount(_ tableName: String) async -> Int {
        do {
            _ = try await supabaseClient.client.from(tableName)
                .select("count")
                .execute()

            // Parse count from response
            return 0 // Placeholder
        } catch {
            return 0
        }
    }
    
    // MARK: - Helper Methods
    
    private func extractYear(from dateString: String) -> Int {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        if let date = formatter.date(from: dateString) {
            return Calendar.current.component(.year, from: date)
        }
        return Calendar.current.component(.year, from: Date())
    }
}

// MARK: - Date Extension

// MARK: - Content Caching Service

@MainActor
class ContentCachingService: ObservableObject {
    static let shared = ContentCachingService()

    private let cacheDirectory: URL
    private let fileManager = FileManager.default

    @Published var cacheSize: Int64 = 0
    @Published var cachedItemCount = 0

    private init() {
        // Create cache directory
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        cacheDirectory = documentsPath.appendingPathComponent("ExploreContentCache")

        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        }

        updateCacheStats()
    }

    // MARK: - Cache Management

    func cacheContent<T: Codable>(_ content: T, forKey key: String, expiryInterval: TimeInterval = 3600) {
        let cacheItem = DynamicCacheItem(content: content, expiryDate: Date().addingTimeInterval(expiryInterval))
        let fileURL = cacheDirectory.appendingPathComponent("\(key).cache")

        do {
            let data = try JSONEncoder().encode(cacheItem)
            try data.write(to: fileURL)
            updateCacheStats()
        } catch {
            print("❌ Failed to cache content for key \(key): \(error)")
        }
    }

    func getCachedContent<T: Codable>(forKey key: String, type: T.Type) -> T? {
        let fileURL = cacheDirectory.appendingPathComponent("\(key).cache")

        guard fileManager.fileExists(atPath: fileURL.path) else { return nil }

        do {
            let data = try Data(contentsOf: fileURL)
            let cacheItem = try JSONDecoder().decode(DynamicCacheItem<T>.self, from: data)

            // Check if cache is still valid
            if cacheItem.expiryDate > Date() {
                return cacheItem.content
            } else {
                // Remove expired cache
                try? fileManager.removeItem(at: fileURL)
                updateCacheStats()
                return nil
            }
        } catch {
            print("❌ Failed to read cached content for key \(key): \(error)")
            return nil
        }
    }

    func clearCache() {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for file in files {
                try fileManager.removeItem(at: file)
            }
            updateCacheStats()
        } catch {
            print("❌ Failed to clear cache: \(error)")
        }
    }

    func clearExpiredCache() {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for file in files {
                if let data = try? Data(contentsOf: file),
                   let cacheItem = try? JSONDecoder().decode(AnyDynamicCacheItem.self, from: data),
                   cacheItem.expiryDate <= Date() {
                    try fileManager.removeItem(at: file)
                }
            }
            updateCacheStats()
        } catch {
            print("❌ Failed to clear expired cache: \(error)")
        }
    }

    private func updateCacheStats() {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            cachedItemCount = files.count

            var totalSize: Int64 = 0
            for file in files {
                let attributes = try file.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(attributes.fileSize ?? 0)
            }
            cacheSize = totalSize
        } catch {
            cachedItemCount = 0
            cacheSize = 0
        }
    }
}

// MARK: - Cache Item Models

struct DynamicCacheItem<T: Codable>: Codable {
    let content: T
    let expiryDate: Date
}

struct AnyDynamicCacheItem: Codable {
    let expiryDate: Date
}

// MARK: - Date Extension

extension Date {
    func toISOString() -> String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
}
