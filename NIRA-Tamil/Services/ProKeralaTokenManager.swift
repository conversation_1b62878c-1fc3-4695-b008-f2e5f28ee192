import Foundation

// MARK: - Token Response Models
struct ProKeralaTokenResponse: Codable {
    let access_token: String
    let token_type: String
    let expires_in: Int
    let scope: String?
}

// MARK: - Token Manager
class ProKeralaTokenManager {
    static let shared = ProKeralaTokenManager()
    
    private let tokenURL = "https://api.prokerala.com/token"
    private var currentToken: String?
    private var tokenExpiryDate: Date?
    
    // ProKerala API credentials - NIRA-Tamil Application
    private let clientCredentials = [
        ("6df0ec16-722b-4acd-a574-bfd546c0c270", "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"), // Primary credentials
        ("6df0ec16-722b-4acd-a574-bfd546c0c270", "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx") // Backup (same for now)
    ]
    
    private var currentCredentialIndex = 0
    
    private init() {}
    
    // MARK: - Public Methods
    
    func getValidAccessToken() async throws -> String {
        // Check if current token is still valid
        if let token = currentToken,
           let expiryDate = tokenExpiryDate,
           Date() < expiryDate.addingTimeInterval(-300) { // Refresh 5 minutes before expiry
            return token
        }
        
        // Get new token
        return try await refreshAccessToken()
    }
    
    // MARK: - Private Methods
    
    private func refreshAccessToken() async throws -> String {
        var lastError: Error?
        
        // Try each credential pair
        for _ in 0..<clientCredentials.count {
            do {
                let token = try await requestNewToken()
                return token
            } catch {
                print("❌ Token request failed with credentials \(currentCredentialIndex): \(error)")
                lastError = error
                rotateCredentials()
                
                // Add delay between retries
                try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            }
        }
        
        throw lastError ?? ProKeralaError.tokenRequestFailed
    }
    
    private func requestNewToken() async throws -> String {
        guard let url = URL(string: tokenURL) else {
            throw ProKeralaError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30
        
        let credentials = currentCredentials
        let body = "grant_type=client_credentials&client_id=\(credentials.0)&client_secret=\(credentials.1)"
        request.httpBody = body.data(using: .utf8)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw ProKeralaError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if let errorData = String(data: data, encoding: .utf8) {
                print("❌ Token request error: \(errorData)")
            }
            throw ProKeralaError.httpError(httpResponse.statusCode)
        }
        
        let tokenResponse = try JSONDecoder().decode(ProKeralaTokenResponse.self, from: data)
        
        // Store token and expiry
        currentToken = tokenResponse.access_token
        tokenExpiryDate = Date().addingTimeInterval(TimeInterval(tokenResponse.expires_in))
        
        print("✅ Successfully obtained ProKerala access token")
        return tokenResponse.access_token
    }
    
    private var currentCredentials: (String, String) {
        return clientCredentials[currentCredentialIndex]
    }
    
    private func rotateCredentials() {
        currentCredentialIndex = (currentCredentialIndex + 1) % clientCredentials.count
    }
    
    // MARK: - Token Validation
    
    func isTokenValid() -> Bool {
        guard let token = currentToken,
              let expiryDate = tokenExpiryDate else {
            return false
        }
        
        return !token.isEmpty && Date() < expiryDate
    }
    
    func clearToken() {
        currentToken = nil
        tokenExpiryDate = nil
    }
}

// MARK: - ProKerala Errors
enum ProKeralaError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case httpError(Int)
    case tokenRequestFailed
    case invalidCredentials
    case rateLimitExceeded
    case insufficientCredits
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid ProKerala API URL"
        case .invalidResponse:
            return "Invalid response from ProKerala API"
        case .httpError(let code):
            return "ProKerala API HTTP error: \(code)"
        case .tokenRequestFailed:
            return "Failed to obtain ProKerala access token"
        case .invalidCredentials:
            return "Invalid ProKerala API credentials"
        case .rateLimitExceeded:
            return "ProKerala API rate limit exceeded"
        case .insufficientCredits:
            return "Insufficient ProKerala API credits"
        }
    }
}

// MARK: - Setup Instructions
/*
 SETUP INSTRUCTIONS:
 
 1. Sign up for free ProKerala account at: https://api.prokerala.com/register
 2. Create a new application in your dashboard
 3. Get your Client ID and Client Secret
 4. Replace the placeholder credentials above:
 
    private let clientCredentials = [
        ("your_actual_client_id", "your_actual_client_secret"),
        ("backup_client_id", "backup_client_secret") // Optional
    ]
 
 5. The free tier provides 5,000 credits per month
 6. Each panchang request costs 1 credit
 7. Token automatically refreshes when needed
 
 USAGE:
 
 let tokenManager = ProKeralaTokenManager.shared
 let accessToken = try await tokenManager.getValidAccessToken()
 // Use accessToken in your API requests
 */
