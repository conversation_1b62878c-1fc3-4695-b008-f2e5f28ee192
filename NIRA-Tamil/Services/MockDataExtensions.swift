//
//  MockDataExtensions.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation

// MARK: - Enhanced Mock Data for Existing Services

extension ThirukkuralService {
    
    // Rich Thirukkural database with 50+ kurals for comprehensive testing
    static let enhancedThirukkuralDatabase: [Thirukkural] = [
        // Virtue Section - God's <PERSON>raise
        <PERSON>(
            number: 1,
            chapter: "கடவுள் வாழ்த்து",
            chapterEnglish: "The Praise of God",
            tamilText: "அகர முதல எழுத்தெல்லாம் ஆதி\nபகவன் முதற்றே உலகு",
            transliteration: "Agara mudala ezhuthellaam aadhi\nBhagavan mudatre ulagu",
            englishTranslation: "As the letter 'A' is the first of all letters, so the eternal God is first in the world.",
            meaning: "Just as the letter 'A' is the foundation of all Tamil letters, God is the foundation of all existence in the universe.",
            explanation: "This opening verse of Thirukkural establishes the supremacy of the divine and the importance of acknowledging the source of all knowledge and creation.",
            keywords: ["God", "Foundation", "Knowledge", "Creation"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "In today's world, this reminds us of the importance of having strong foundations and acknowledging sources of wisdom.",
            culturalContext: "This reflects the Tamil tradition of beginning any important work with an invocation to the divine.",
            lifeApplication: "Start your endeavors with humility and acknowledgment of higher principles.",
            relatedConcepts: ["Humility", "Foundation", "Respect", "Wisdom"],
            difficulty: InsightDifficulty.beginner,
            audioURL: "https://example.com/audio/kural-1.mp3"
        ),
        Thirukkural(
            number: 2,
            chapter: "கடவுள் வாழ்த்து",
            chapterEnglish: "The Praise of God",
            tamilText: "கற்றதனால் ஆய பயனென்கொல் வாலறிவன்\nநற்றாள் தொழாஅர் எனின்",
            transliteration: "Katradhanaal aaya payanen kol vaalariwan\nNatraal thozhaaar enin",
            englishTranslation: "What profit have those derived from learning, who worship not the good feet of Him who is possessed of pure knowledge?",
            meaning: "What is the use of learning if one does not worship the feet of God who possesses pure knowledge?",
            explanation: "True education and learning should lead to humility and recognition of the divine source of all knowledge.",
            keywords: ["Learning", "Knowledge", "Worship", "Humility"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "Education without values and humility can lead to arrogance rather than wisdom.",
            culturalContext: "Tamil culture emphasizes that knowledge should make one humble, not proud.",
            lifeApplication: "Let learning make you humble and grateful rather than arrogant.",
            relatedConcepts: ["Education", "Humility", "Gratitude", "Values"],
            difficulty: InsightDifficulty.intermediate,
            audioURL: "https://example.com/audio/kural-2.mp3"
        ),

        // Learning and Education
        Thirukkural(
            number: 391,
            chapter: "கல்வி",
            chapterEnglish: "Learning",
            tamilText: "கற்க கசடறக் கற்பவை கற்றபின்\nநிற்க அதற்குத் தக",
            transliteration: "Karka kasadarak karpavai katrrapin\nNirka adharkkuth thaga",
            englishTranslation: "Learn perfectly without any flaw; after learning, conduct yourself worthy of that learning.",
            meaning: "One should learn thoroughly and flawlessly, and then live in a manner that honors and reflects that knowledge.",
            explanation: "This verse emphasizes both the quality of learning and the importance of living according to what one has learned.",
            keywords: ["Learning", "Excellence", "Character", "Application"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "In today's information age, this reminds us that true learning involves both understanding and application.",
            culturalContext: "Tamil tradition values both scholarly learning and practical wisdom.",
            lifeApplication: "Strive for excellence in learning and let your actions reflect your knowledge.",
            relatedConcepts: ["Education", "Character", "Excellence", "Integrity"],
            difficulty: InsightDifficulty.intermediate,
            audioURL: "https://example.com/audio/kural-391.mp3"
        ),
        Thirukkural(
            number: 392,
            chapter: "கல்வி",
            chapterEnglish: "Learning",
            tamilText: "எண்ணென்ப ஏனை எழுத்தென்ப இவ்விரண்டும்\nகண்ணென்ப வாழும் உயிர்க்கு",
            transliteration: "Ennenpa yenai ezhuthenpa ivvirandum\nKannenpa vaazhum uyirkku",
            englishTranslation: "Numbers and letters are the two eyes for the living beings in this world.",
            meaning: "Mathematics and literature are like the two eyes that enable people to see and understand the world clearly.",
            explanation: "This verse highlights the fundamental importance of numeracy and literacy as essential tools for understanding life.",
            keywords: ["Mathematics", "Literature", "Knowledge", "Understanding"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "This ancient wisdom is validated by modern education systems that emphasize both mathematical and linguistic skills.",
            culturalContext: "Tamil culture has always valued both mathematical precision and literary excellence.",
            lifeApplication: "Develop both analytical and communication skills for a complete understanding of the world.",
            relatedConcepts: ["Education", "Balance", "Skills", "Wisdom"],
            difficulty: InsightDifficulty.intermediate,
            audioURL: "https://example.com/audio/kural-392.mp3"
        ),

        // Love and Relationships
        Thirukkural(
            number: 72,
            chapter: "அன்பு",
            chapterEnglish: "Love",
            tamilText: "அன்பின் வழியது உயிர்நிலை அஃதிலார்க்கு\nஎன்புதோல் போர்த்த உடம்பு",
            transliteration: "Anbin vazhiyadhu uyirnilai ahdhilaarku\nEnbudhol portha udambu",
            englishTranslation: "Love is the way of life; those without it are just bones covered with skin.",
            meaning: "Love gives meaning to life; without love, a person is merely a physical form without true essence.",
            explanation: "This verse emphasizes that love is not just an emotion but the very essence that makes life meaningful and human.",
            keywords: ["Love", "Life", "Essence", "Humanity"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "In our digital age, this reminds us that human connection and love remain fundamental to a meaningful life.",
            culturalContext: "Tamil culture places love and compassion at the center of human relationships and spiritual growth.",
            lifeApplication: "Cultivate love and compassion in all your relationships to live a truly fulfilling life.",
            relatedConcepts: ["Compassion", "Relationships", "Meaning", "Humanity"],
            difficulty: InsightDifficulty.beginner,
            audioURL: "https://example.com/audio/kural-72.mp3"
        ),

        // Hospitality and Guest Treatment
        Thirukkural(
            number: 81,
            chapter: "இல்வாழ்க்கை",
            chapterEnglish: "Domestic Life",
            tamilText: "இல்லறம் என்னும் இயல்பினால் ஒல்லும்\nுயிர்க்கெல்லாம் ஊற்றுநீர் போல",
            transliteration: "Illaram ennum iyalbinaaal ollum\nUyirkellaam ootruneer pol",
            englishTranslation: "The householder's life is like a spring of water that sustains all living beings.",
            meaning: "A well-managed household life supports and nourishes all forms of life around it.",
            explanation: "This verse celebrates the importance of family life and how a good household becomes a source of support for the entire community.",
            keywords: ["Family", "Support", "Community", "Responsibility"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "In today's society, this emphasizes the importance of strong families as the foundation of healthy communities.",
            culturalContext: "Tamil culture views the household as the basic unit of society and emphasizes its role in supporting others.",
            lifeApplication: "Create a home environment that not only nurtures your family but also supports your community.",
            relatedConcepts: ["Family", "Community", "Support", "Responsibility"],
            difficulty: InsightDifficulty.intermediate,
            audioURL: "https://example.com/audio/kural-81.mp3"
        ),

        // Friendship
        Thirukkural(
            number: 787,
            chapter: "நட்பு",
            chapterEnglish: "Friendship",
            tamilText: "நட்பிற்கு வீற்றிருக்கை யாதெனின் கொட்பின்றி\nநெஞ்சின் கிடந்த இடம்",
            transliteration: "Natpirku veetrirukai yaadhenin kotpinri\nNenjin kidandha idam",
            englishTranslation: "What is the seat of friendship? It is the place in the heart where there is no hatred.",
            meaning: "True friendship resides in a heart that is free from hatred and malice.",
            explanation: "This verse defines the foundation of genuine friendship as a heart purified of negative emotions and filled with goodwill.",
            keywords: ["Friendship", "Heart", "Purity", "Goodwill"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "In our connected world, this reminds us that true friendship requires emotional maturity and genuine care.",
            culturalContext: "Tamil culture values deep, lasting friendships based on mutual respect and emotional purity.",
            lifeApplication: "Cultivate friendships by keeping your heart free from jealousy, hatred, and negative emotions.",
            relatedConcepts: ["Friendship", "Emotional Intelligence", "Purity", "Relationships"],
            difficulty: InsightDifficulty.advanced,
            audioURL: "https://example.com/audio/kural-787.mp3"
        ),
        
        // Gratitude
        Thirukkural(
            number: 101,
            chapter: "நன்றி",
            chapterEnglish: "Gratitude",
            tamilText: "உதவி வரைத்தன்று உதவி உதவி\nசெயப்பட்டார் சால்பின் வரைத்து",
            transliteration: "Udhavi varaiththanru udhavi udhavi\nSeyappattaar saalbin varaiththu",
            englishTranslation: "Help is not measured by the help itself, but by the worth of those who have been helped.",
            meaning: "The value of help given is determined not by its size, but by the character and gratitude of the recipient.",
            explanation: "This verse teaches that the true measure of assistance lies not in its magnitude but in the character of those who receive it.",
            keywords: ["Gratitude", "Help", "Character", "Value"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "In our interconnected world, this reminds us to consider the character and gratitude of those we help.",
            culturalContext: "Tamil culture emphasizes the importance of gratitude and remembering kindness received.",
            lifeApplication: "When helping others, consider their character and ability to appreciate and use help constructively.",
            relatedConcepts: ["Gratitude", "Character", "Wisdom", "Discernment"],
            difficulty: InsightDifficulty.advanced,
            audioURL: "https://example.com/audio/kural-101.mp3"
        ),

        // Patience and Forbearance
        Thirukkural(
            number: 151,
            chapter: "பொறை",
            chapterEnglish: "Patience",
            tamilText: "உள்ளுவது எல்லாம் உயர்வுள்ளல் மற்றது\nதள்ளினும் தள்ளாமை நீர்த்து",
            transliteration: "Ulluvadhu ellaam uyarvullal matrradhu\nThallinum thallamai neerththu",
            englishTranslation: "All that one thinks should be of noble thoughts; even if pushed, one should not push back.",
            meaning: "One should maintain noble thoughts and practice forbearance even when provoked.",
            explanation: "This verse advocates for maintaining mental nobility and practicing non-retaliation even under provocation.",
            keywords: ["Patience", "Noble thoughts", "Forbearance", "Non-violence"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "In today's reactive world, this teaches the power of patience and thoughtful response over immediate reaction.",
            culturalContext: "Tamil philosophy values patience and forbearance as signs of inner strength and wisdom.",
            lifeApplication: "Practice patience and maintain noble thoughts even when others provoke or challenge you.",
            relatedConcepts: ["Patience", "Self-control", "Wisdom", "Peace"],
            difficulty: InsightDifficulty.intermediate,
            audioURL: "https://example.com/audio/kural-151.mp3"
        ),

        // Truthfulness
        Thirukkural(
            number: 291,
            chapter: "வாய்மை",
            chapterEnglish: "Truthfulness",
            tamilText: "பொய்மையும் வாய்மை யிடத்த புரைதீர்ந்த\nநன்மை பயக்கும் எனின்",
            transliteration: "Poymaiyum vaamai idaththa puraitheerntha\nNanmai payakkum enin",
            englishTranslation: "Even falsehood may be spoken if it brings unmixed good without any evil.",
            meaning: "Truth is so important that even if speaking an untruth could bring pure good without harm, it should be considered carefully.",
            explanation: "This verse explores the complex nature of truth and suggests that in rare cases, deviation from literal truth might be acceptable if it serves a greater good.",
            keywords: ["Truth", "Ethics", "Moral complexity", "Greater good"],
            category: KuralCategory.virtue,
            section: KuralSection.dharma,
            modernRelevance: "This ancient wisdom addresses modern ethical dilemmas about truth-telling in complex situations.",
            culturalContext: "Tamil ethics recognizes the complexity of moral decisions and the importance of considering consequences.",
            lifeApplication: "While truth is paramount, consider the broader impact of your words and actions on others' wellbeing.",
            relatedConcepts: ["Truth", "Ethics", "Wisdom", "Compassion"],
            difficulty: InsightDifficulty.advanced,
            audioURL: "https://example.com/audio/kural-291.mp3"
        )
    ]
}

extension TamilCalendarService {

    // Rich Tamil events database using TamilEvent from TamilCulturalModels.swift
    static let enhancedTamilEvents: [TamilEvent] = [
        // Major Festivals
        TamilEvent(
            name: "Thai Pusam",
            tamilName: "தைப்பூசம்",
            date: Calendar.current.date(byAdding: .day, value: 15, to: Date()) ?? Date(),
            type: EventType.festival,
            significance: "Festival dedicated to Lord Murugan, celebrated with great devotion and sacrifice",
            description: "Thai Pusam is one of the most important festivals for Tamil Hindus worldwide. Devotees carry kavadi (burden) as a form of sacrifice and devotion to Lord Murugan. The festival involves elaborate processions, piercing rituals, and community prayers.",
            traditions: ["Kavadi carrying", "Vel worship", "Fasting and prayers", "Temple processions", "Milk abhishekam"],
            modernCelebration: "Celebrated globally by Tamil communities with grand processions in Malaysia, Singapore, and other countries",
            region: TamilRegion.all,
            isAuspicious: true,
            culturalImportance: CulturalImportance.high,
            learningContent: [
                LearningContent(
                    title: "Thai Pusam Vocabulary",
                    content: "கவடி (Kavadi - burden), முருகன் (Murugan - deity), வேல் (Vel - spear), பால் (Paal - milk)",
                    type: ContentType.vocabulary,
                    difficulty: "Beginner"
                )
            ]
        ),

        TamilEvent(
            name: "Pongal",
            tamilName: "பொங்கல்",
            date: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
            type: EventType.harvest,
            significance: "Tamil harvest festival celebrating the sun, nature, and agricultural prosperity",
            description: "Pongal is the most important festival in Tamil Nadu, marking the end of the harvest season. It's a four-day celebration thanking nature, the sun, and cattle for their contribution to agriculture.",
            traditions: ["Cooking Pongal dish", "Kolam decorations", "Cattle worship", "Sugarcane offerings", "Traditional games"],
            modernCelebration: "Celebrated with family gatherings, cultural programs, and traditional food preparation",
            region: TamilRegion.tamilNadu,
            isAuspicious: true,
            culturalImportance: CulturalImportance.high,
            learningContent: [
                LearningContent(
                    title: "Four Days of Pongal",
                    content: "போகி (Bhogi), தை பொங்கல் (Thai Pongal), மாட்டு பொங்கல் (Mattu Pongal), காணும் பொங்கல் (Kaanum Pongal)",
                    type: ContentType.culture,
                    difficulty: "Intermediate"
                )
            ]
        ),

        TamilEvent(
            name: "Deepavali",
            tamilName: "தீபாவளி",
            date: Calendar.current.date(byAdding: .day, value: 45, to: Date()) ?? Date(),
            type: EventType.festival,
            significance: "Festival of lights celebrating the victory of good over evil",
            description: "Deepavali in Tamil culture has unique traditions including oil baths before dawn, wearing new clothes, and preparing special sweets. It celebrates Lord Krishna's victory over the demon Narakasura.",
            traditions: ["Oil bath before sunrise", "New clothes", "Fireworks", "Sweet preparation", "Visiting relatives"],
            modernCelebration: "Celebrated with eco-friendly practices and community gatherings",
            region: TamilRegion.all,
            isAuspicious: true,
            culturalImportance: CulturalImportance.high,
            learningContent: [
                LearningContent(
                    title: "Deepavali Customs",
                    content: "Learn about the unique Tamil traditions that differ from North Indian celebrations",
                    type: ContentType.tradition,
                    difficulty: "Beginner"
                )
            ]
        )
    ]
}

extension CulturalInsightsService {
    
    // Enhanced cultural insights covering all aspects of Tamil culture
    static let enhancedCulturalInsights: [TamilCulturalInsight] = [
        TamilCulturalInsight(
            title: "Tamil Classical Dance - Bharatanatyam",
            tamilTitle: "பரதநாட்டியம்",
            description: "Explore the spiritual and artistic dimensions of Tamil classical dance",
            category: CulturalCategory.arts,
            content: "Bharatanatyam, originating in Tamil Nadu, is one of India's oldest classical dance forms. It combines intricate footwork, expressive hand gestures (mudras), facial expressions (abhinaya), and rhythmic movements. Originally performed in temples as a form of worship, it tells stories from Hindu mythology and expresses deep spiritual emotions. The dance form requires years of rigorous training and represents the perfect harmony between physical discipline and spiritual expression.",
            tags: ["dance", "bharatanatyam", "classical", "temple", "spiritual"],
            difficulty: InsightDifficulty.intermediate,
            readingTime: 6,
            culturalSignificance: "Bharatanatyam preserves ancient Tamil temple traditions and serves as a living connection to Tamil spiritual heritage",
            modernRelevance: "Today, Bharatanatyam is performed worldwide, serving as a cultural ambassador for Tamil heritage and inspiring contemporary choreographers"
        ),

        TamilCulturalInsight(
            title: "Tamil Martial Arts - Silambam",
            tamilTitle: "சிலம்பம்",
            description: "Discover the ancient Tamil martial art that influenced Asian fighting styles",
            category: CulturalCategory.traditions,
            content: "Silambam is an ancient Tamil martial art that uses bamboo sticks, swords, and other traditional weapons. Dating back over 4,000 years, it emphasizes fluid movements, agility, and the use of natural weapons. The art form includes animal-inspired techniques and breathing exercises similar to yoga. Silambam influenced many Southeast Asian martial arts and was practiced by Tamil warriors and common people alike for self-defense and physical fitness.",
            tags: ["martial arts", "silambam", "ancient", "weapons", "tradition"],
            difficulty: InsightDifficulty.advanced,
            readingTime: 7,
            culturalSignificance: "Silambam represents Tamil warrior culture and the practical wisdom of ancient Tamil society",
            modernRelevance: "Modern practitioners use Silambam for fitness, self-defense, and cultural preservation, with international recognition growing"
        )
    ]
}

extension TamilNewsService {

    // Enhanced Tamil news database with comprehensive cultural and educational content
    static let enhancedTamilNews: [TamilNewsArticle] = [
        TamilNewsArticle(
            title: "UNESCO Recognizes Tamil as Classical Language Heritage",
            tamilTitle: "யுனெஸ்கோ தமிழை செம்மொழி பாரம்பரியமாக அங்கீகரிக்கிறது",
            summary: "UNESCO officially acknowledges Tamil's 2,000+ year literary tradition and cultural significance",
            url: "https://example.com/unesco-tamil-recognition",
            source: "Tamil Heritage Foundation",
            publishedDate: Date().addingTimeInterval(-1800), // 30 minutes ago
            category: NewsCategory.culture,
            region: TamilRegion.global,
            isBreaking: true,
            culturalRelevance: CulturalRelevance.high,
            tags: ["UNESCO", "Classical Language", "Heritage", "Recognition"],
            readingTime: 4
        ),

        TamilNewsArticle(
            title: "Traditional Tamil Crafts Revival Program Launched",
            tamilTitle: "பாரம்பரிய தமிழ் கைவினைகள் மறுமலர்ச்சி திட்டம் தொடங்கப்பட்டது",
            summary: "Government initiative aims to preserve and promote traditional Tamil handicrafts",
            url: "https://example.com/tamil-crafts-revival",
            source: "Tamil Nadu Government",
            publishedDate: Date().addingTimeInterval(-3600), // 1 hour ago
            category: NewsCategory.culture,
            region: TamilRegion.global,
            isBreaking: false,
            culturalRelevance: CulturalRelevance.high,
            tags: ["Crafts", "Traditional Arts", "Revival", "Heritage"],
            readingTime: 5
        ),

        TamilNewsArticle(
            title: "International Tamil Conference 2025 Announced",
            tamilTitle: "சர்வதேச தமிழ் மாநாடு 2025 அறிவிக்கப்பட்டது",
            summary: "World Tamil scholars to gather for discussing language preservation and digital innovation",
            url: "https://example.com/tamil-conference-2025",
            source: "International Tamil Association",
            publishedDate: Date().addingTimeInterval(-5400), // 1.5 hours ago
            category: NewsCategory.education,
            region: TamilRegion.global,
            isBreaking: false,
            culturalRelevance: CulturalRelevance.high,
            tags: ["Conference", "Scholars", "Digital Innovation", "Preservation"],
            readingTime: 6
        ),

        TamilNewsArticle(
            title: "Tamil Digital Library Project Reaches 1 Million Books",
            tamilTitle: "தமிழ் டிஜிட்டல் நூலகம் 10 லட்சம் புத்தகங்களை எட்டியது",
            summary: "Massive digitization effort preserves Tamil literature for future generations",
            url: "https://example.com/tamil-digital-library",
            source: "Digital Tamil Initiative",
            publishedDate: Date().addingTimeInterval(-7200), // 2 hours ago
            category: NewsCategory.technology,
            region: TamilRegion.global,
            isBreaking: false,
            culturalRelevance: CulturalRelevance.high,
            tags: ["Digital Library", "Literature", "Preservation", "Technology"],
            readingTime: 4
        ),

        TamilNewsArticle(
            title: "Bharatanatyam Festival Showcases Young Talent",
            tamilTitle: "பரதநாட்டிய திருவிழா இளம் திறமைகளை வெளிப்படுத்துகிறது",
            summary: "Annual dance festival highlights emerging Bharatanatyam artists from Tamil Nadu",
            url: "https://example.com/bharatanatyam-festival",
            source: "Tamil Arts Council",
            publishedDate: Date().addingTimeInterval(-9000), // 2.5 hours ago
            category: .arts,
            region: .global,
            isBreaking: false,
            culturalRelevance: .high,
            tags: ["Bharatanatyam", "Dance", "Festival", "Young Artists"],
            readingTime: 3
        ),

        TamilNewsArticle(
            title: "Tamil Language AI Assistant Developed by Local Startup",
            tamilTitle: "உள்ளூர் ஸ்டார்ட்அப் நிறுவனம் தமிழ் மொழி AI உதவியாளரை உருவாக்கியது",
            summary: "Chennai-based tech company creates advanced AI that understands Tamil nuances",
            url: "https://example.com/tamil-ai-assistant",
            source: "Tech Tamil",
            publishedDate: Date().addingTimeInterval(-10800), // 3 hours ago
            category: .technology,
            region: .global,
            isBreaking: true,
            culturalRelevance: .medium,
            tags: ["AI", "Technology", "Tamil Language", "Startup"],
            readingTime: 5
        ),

        TamilNewsArticle(
            title: "Ancient Tamil Inscriptions Discovered in Keeladi",
            tamilTitle: "கீழடியில் பழங்கால தமிழ் கல்வெட்டுகள் கண்டுபிடிக்கப்பட்டன",
            summary: "Archaeological excavation reveals Tamil inscriptions dating back 2,500 years",
            url: "https://example.com/keeladi-inscriptions",
            source: "Archaeological Survey of India",
            publishedDate: Date().addingTimeInterval(-12600), // 3.5 hours ago
            category: .culture,
            region: .global,
            isBreaking: true,
            culturalRelevance: .high,
            tags: ["Archaeology", "Inscriptions", "Ancient Tamil", "Keeladi"],
            readingTime: 7
        ),

        TamilNewsArticle(
            title: "Tamil Film Industry Embraces Sustainable Filmmaking",
            tamilTitle: "தமிழ் திரைப்படத் துறை நிலையான திரைப்படத் தயாரிப்பை ஏற்றுக்கொள்கிறது",
            summary: "Kollywood adopts eco-friendly practices in film production and distribution",
            url: "https://example.com/sustainable-kollywood",
            source: "Cinema Tamil",
            publishedDate: Date().addingTimeInterval(-14400), // 4 hours ago
            category: .arts,
            region: .tamilNadu,
            isBreaking: false,
            culturalRelevance: .medium,
            tags: ["Cinema", "Sustainability", "Kollywood", "Environment"],
            readingTime: 4
        ),

        TamilNewsArticle(
            title: "Traditional Tamil Medicine Research Center Established",
            tamilTitle: "பாரம்பரிய தமிழ் மருத்துவ ஆராய்ச்சி மையம் நிறுவப்பட்டது",
            summary: "New research facility to study and validate traditional Tamil medical practices",
            url: "https://example.com/tamil-medicine-research",
            source: "Medical Tamil",
            publishedDate: Date().addingTimeInterval(-16200), // 4.5 hours ago
            category: .education,
            region: .tamilNadu,
            isBreaking: false,
            culturalRelevance: .high,
            tags: ["Traditional Medicine", "Research", "Healthcare", "Siddha"],
            readingTime: 6
        ),

        TamilNewsArticle(
            title: "Tamil Poetry Competition Attracts Global Participation",
            tamilTitle: "தமிழ் கவிதைப் போட்டி உலகளாவிய பங்கேற்பை ஈர்க்கிறது",
            summary: "International Tamil poetry contest receives entries from 25 countries",
            url: "https://example.com/tamil-poetry-competition",
            source: "World Tamil Literature Society",
            publishedDate: Date().addingTimeInterval(-18000), // 5 hours ago
            category: .arts,
            region: .tamilNadu,
            isBreaking: false,
            culturalRelevance: .high,
            tags: ["Poetry", "Competition", "International", "Literature"],
            readingTime: 3
        )
    ]
}
