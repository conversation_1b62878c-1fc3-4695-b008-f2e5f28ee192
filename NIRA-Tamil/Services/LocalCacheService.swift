//
//  LocalCacheService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2025-01-19.
//  Copyright © 2025 Securight. All rights reserved.
//

import Foundation
import Combine

@MainActor
class LocalCacheService: ObservableObject {
    static let shared = LocalCacheService()

    private let cacheExpiryHours: TimeInterval = 24 // 24 hours

    /// Force clear all caches immediately (for debugging)
    func forceInvalidateAllCaches() async {
        print("🗑️ Force invalidating ALL caches...")
        await MainActor.run {
            cachedLessons.removeAll()
            cacheTimestamps.removeAll()
        }
        await saveCacheToUserDefaults()
        print("✅ All caches force-cleared")
    }
    private var cachedLessons: [String: [TamilSupabaseLesson]] = [:]
    private var cacheTimestamps: [String: Date] = [:]

    private init() {
        print("🗄️ LocalCacheService initialized")
        Task { @MainActor in
            await loadCacheFromUserDefaults()
        }
    }

    // MARK: - Cache Management

    func isCacheValid(for levelCode: String) -> Bool {
        guard let timestamp = cacheTimestamps[levelCode] else { return false }
        let timeSinceCache = Date().timeIntervalSince(timestamp)
        return timeSinceCache < (cacheExpiryHours * 3600)
    }

    func getCachedLessons(for levelCode: String) -> [TamilSupabaseLesson] {
        return cachedLessons[levelCode] ?? []
    }

    func cacheLessons(_ lessons: [TamilSupabaseLesson], for levelCode: String) async {
        cachedLessons[levelCode] = lessons
        cacheTimestamps[levelCode] = Date()
        await saveCacheToUserDefaults()
        print("✅ Cached \(lessons.count) lessons for level \(levelCode)")
    }
    
    func clearCache(for levelCode: String? = nil) async {
        if let levelCode = levelCode {
            cachedLessons.removeValue(forKey: levelCode)
            cacheTimestamps.removeValue(forKey: levelCode)
            print("✅ Cache cleared for level: \(levelCode)")
        } else {
            cachedLessons.removeAll()
            cacheTimestamps.removeAll()
            print("✅ All cache cleared")
        }
        await saveCacheToUserDefaults()
    }
    
    // MARK: - UserDefaults Persistence
    
    private func loadCacheFromUserDefaults() async {
        // Load cached lessons
        if let data = UserDefaults.standard.data(forKey: "cached_lessons"),
           let decoded = try? JSONDecoder().decode([String: [TamilSupabaseLesson]].self, from: data) {
            cachedLessons = decoded
        }

        // Load cache timestamps
        if let data = UserDefaults.standard.data(forKey: "cache_timestamps"),
           let decoded = try? JSONDecoder().decode([String: Date].self, from: data) {
            cacheTimestamps = decoded
        }

        print("📱 Loaded cache from UserDefaults: \(cachedLessons.keys.count) levels")
    }
    
    private func saveCacheToUserDefaults() async {
        // Save cached lessons
        if let encoded = try? JSONEncoder().encode(cachedLessons) {
            UserDefaults.standard.set(encoded, forKey: "cached_lessons")
        }

        // Save cache timestamps
        if let encoded = try? JSONEncoder().encode(cacheTimestamps) {
            UserDefaults.standard.set(encoded, forKey: "cache_timestamps")
        }

        UserDefaults.standard.synchronize()
    }
    
    // MARK: - Cache Statistics
    
    func getCacheInfo() -> String {
        let totalLessons = cachedLessons.values.reduce(0) { $0 + $1.count }
        let validCaches = cacheTimestamps.filter { isCacheValid(for: $0.key) }.count
        return "📊 Cache: \(totalLessons) lessons, \(validCaches) valid levels"
    }
}

// MARK: - Vocabulary Cache

extension LocalCacheService {
    private var vocabularyCache: [String: [TamilSupabaseVocabulary]] {
        get {
            if let data = UserDefaults.standard.data(forKey: "cached_vocabulary"),
               let decoded = try? JSONDecoder().decode([String: [TamilSupabaseVocabulary]].self, from: data) {
                return decoded
            }
            return [:]
        }
        set {
            if let encoded = try? JSONEncoder().encode(newValue) {
                UserDefaults.standard.set(encoded, forKey: "cached_vocabulary")
            }
        }
    }
    
    func getCachedVocabulary(for lessonId: String) -> [TamilSupabaseVocabulary] {
        return vocabularyCache[lessonId] ?? []
    }
    
    func cacheVocabulary(_ vocabulary: [TamilSupabaseVocabulary], for lessonId: String) {
        var cache = vocabularyCache
        cache[lessonId] = vocabulary
        vocabularyCache = cache
        print("✅ Cached \(vocabulary.count) vocabulary items for lesson \(lessonId)")
    }
}

// MARK: - Conversation Cache

extension LocalCacheService {
    private var conversationCache: [String: [TamilSupabaseConversation]] {
        get {
            if let data = UserDefaults.standard.data(forKey: "cached_conversations"),
               let decoded = try? JSONDecoder().decode([String: [TamilSupabaseConversation]].self, from: data) {
                return decoded
            }
            return [:]
        }
        set {
            if let encoded = try? JSONEncoder().encode(newValue) {
                UserDefaults.standard.set(encoded, forKey: "cached_conversations")
            }
        }
    }
    
    func getCachedConversations(for lessonId: String) -> [TamilSupabaseConversation] {
        return conversationCache[lessonId] ?? []
    }
    
    func cacheConversations(_ conversations: [TamilSupabaseConversation], for lessonId: String) {
        var cache = conversationCache
        cache[lessonId] = conversations
        conversationCache = cache
        print("✅ Cached \(conversations.count) conversations for lesson \(lessonId)")
    }
}

// MARK: - Grammar Cache

extension LocalCacheService {
    private var grammarCache: [String: [TamilSupabaseGrammarTopic]] {
        get {
            if let data = UserDefaults.standard.data(forKey: "cached_grammar"),
               let decoded = try? JSONDecoder().decode([String: [TamilSupabaseGrammarTopic]].self, from: data) {
                return decoded
            }
            return [:]
        }
        set {
            if let encoded = try? JSONEncoder().encode(newValue) {
                UserDefaults.standard.set(encoded, forKey: "cached_grammar")
            }
        }
    }
    
    func getCachedGrammar(for lessonId: String) -> [TamilSupabaseGrammarTopic] {
        return grammarCache[lessonId] ?? []
    }
    
    func cacheGrammar(_ grammar: [TamilSupabaseGrammarTopic], for lessonId: String) {
        var cache = grammarCache
        cache[lessonId] = grammar
        grammarCache = cache
        print("✅ Cached \(grammar.count) grammar topics for lesson \(lessonId)")
    }
}

// MARK: - Practice Cache

extension LocalCacheService {
    private var practiceCache: [String: [TamilSupabasePracticeExercise]] {
        get {
            if let data = UserDefaults.standard.data(forKey: "cached_practice"),
               let decoded = try? JSONDecoder().decode([String: [TamilSupabasePracticeExercise]].self, from: data) {
                return decoded
            }
            return [:]
        }
        set {
            if let encoded = try? JSONEncoder().encode(newValue) {
                UserDefaults.standard.set(encoded, forKey: "cached_practice")
            }
        }
    }
    
    func getCachedPractice(for lessonId: String) -> [TamilSupabasePracticeExercise] {
        return practiceCache[lessonId] ?? []
    }
    
    func cachePractice(_ practice: [TamilSupabasePracticeExercise], for lessonId: String) {
        var cache = practiceCache
        cache[lessonId] = practice
        practiceCache = cache
        print("✅ Cached \(practice.count) practice exercises for lesson \(lessonId)")
    }
}
