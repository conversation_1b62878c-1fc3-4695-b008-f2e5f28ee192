import Foundation
import AVFoundation
import Speech
import Combine

@MainActor
class SpeechService: NSObject, ObservableObject {
    @Published var isRecording = false
    @Published var isListening = false
    @Published var transcribedText = ""
    @Published var errorMessage: String?
    @Published var authorizationStatus: SFSpeechRecognizerAuthorizationStatus = .notDetermined
    
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine = AVAudioEngine()
    private var synthesizer = AVSpeechSynthesizer()
    
    override init() {
        super.init()
        // Only setup speech recognizer when needed for recording
        // Text-to-speech doesn't require special permissions
    }
    
    // MARK: - Setup
    
    private func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "es-ES"))
        speechRecognizer?.delegate = self
    }
    
    private func requestSpeechAuthorization() {
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                self?.authorizationStatus = status
            }
        }
    }
    
    // MARK: - Speech Recognition
    
    func startRecording() {
        // Setup speech recognizer if not already done
        if speechRecognizer == nil {
            setupSpeechRecognizer()
        }

        // Request authorization if not already done
        if authorizationStatus == .notDetermined {
            requestSpeechAuthorization()
            return
        }

        guard authorizationStatus == .authorized else {
            errorMessage = "Speech recognition not authorized"
            return
        }
        
        guard !audioEngine.isRunning else {
            stopRecording()
            return
        }
        
        do {
            try startSpeechRecognition()
            isRecording = true
            isListening = true
            transcribedText = ""
            errorMessage = nil
        } catch {
            errorMessage = "Failed to start recording: \(error.localizedDescription)"
        }
    }
    
    func stopRecording() {
        audioEngine.stop()
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        isRecording = false
        isListening = false
    }
    
    private func startSpeechRecognition() throws {
        // Cancel any previous task
        recognitionTask?.cancel()
        recognitionTask = nil
        
        // Configure audio session
        let audioSession = AVAudioSession.sharedInstance()
        try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        
        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw SpeechError.recognitionRequestFailed
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        // Configure audio input
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        // Start audio engine
        audioEngine.prepare()
        try audioEngine.start()
        
        // Start recognition task
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                if let result = result {
                    self?.transcribedText = result.bestTranscription.formattedString
                    
                    if result.isFinal {
                        self?.stopRecording()
                    }
                }
                
                if let error = error {
                    self?.errorMessage = error.localizedDescription
                    self?.stopRecording()
                }
            }
        }
    }
    
    // MARK: - Text-to-Speech
    
    func speak(text: String, language: String = "es-ES") {
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: language)
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        utterance.volume = 1.0
        
        synthesizer.speak(utterance)
    }
    
    func stopSpeaking() {
        synthesizer.stopSpeaking(at: .immediate)
    }
    
    // MARK: - Pronunciation Analysis
    
    func analyzePronunciation(originalText: String, spokenText: String) -> PronunciationAnalysis {
        // Simple pronunciation analysis
        // In a real implementation, this would use more sophisticated algorithms
        
        let similarity = calculateSimilarity(original: originalText, spoken: spokenText)
        let score = max(0, min(100, similarity * 100))
        
        return PronunciationAnalysis(
            transcription: spokenText,
            overallScore: score,
            detailedScores: nil,
            feedback: generateFeedback(score: score),
            improvements: [],
            strengths: score > 80 ? ["Good pronunciation"] : [],
            recommendations: generateSuggestions(original: originalText, spoken: spokenText)
        )
    }
    
    private func calculateSimilarity(original: String, spoken: String) -> Double {
        let originalWords = original.lowercased().components(separatedBy: .whitespacesAndNewlines)
        let spokenWords = spoken.lowercased().components(separatedBy: .whitespacesAndNewlines)
        
        let maxLength = max(originalWords.count, spokenWords.count)
        guard maxLength > 0 else { return 0 }
        
        var matches = 0
        for i in 0..<min(originalWords.count, spokenWords.count) {
            if originalWords[i] == spokenWords[i] {
                matches += 1
            }
        }
        
        return Double(matches) / Double(maxLength)
    }
    
    private func generateFeedback(score: Double) -> String {
        switch score {
        case 90...100:
            return "Excellent pronunciation! Your speech is very clear and accurate."
        case 75..<90:
            return "Good pronunciation! Minor improvements could make it even better."
        case 60..<75:
            return "Fair pronunciation. Focus on clarity and word stress."
        case 40..<60:
            return "Needs improvement. Practice individual words and sounds."
        default:
            return "Keep practicing! Focus on listening and repeating slowly."
        }
    }
    
    private func generateSuggestions(original: String, spoken: String) -> [String] {
        var suggestions: [String] = []
        
        if spoken.count < Int(Double(original.count) * 0.7) {
            suggestions.append("Try to speak all words clearly")
        }
        
        if !spoken.lowercased().contains(original.lowercased().prefix(3)) {
            suggestions.append("Focus on the beginning of the phrase")
        }
        
        suggestions.append("Practice speaking slowly and clearly")
        suggestions.append("Listen to native speakers for reference")
        
        return suggestions
    }
}

// MARK: - SFSpeechRecognizerDelegate

extension SpeechService: SFSpeechRecognizerDelegate {
    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        Task { @MainActor in
            if !available {
                self.errorMessage = "Speech recognition not available"
                self.stopRecording()
            }
        }
    }
}

// MARK: - Supporting Types
// Note: PronunciationAnalysis is defined in PronunciationAssessmentService.swift

enum SpeechError: Error, LocalizedError {
    case recognitionRequestFailed
    case audioEngineError
    case authorizationDenied
    
    var errorDescription: String? {
        switch self {
        case .recognitionRequestFailed:
            return "Failed to create speech recognition request"
        case .audioEngineError:
            return "Audio engine error"
        case .authorizationDenied:
            return "Speech recognition authorization denied"
        }
    }
} 