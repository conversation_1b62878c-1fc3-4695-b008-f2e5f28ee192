import Foundation
import Combine

// MARK: - Tamil News Models

struct TamilNewsArticle: Codable, Identifiable {
    let id: UUID
    let title: String
    let tamilTitle: String?
    let summary: String
    let url: String
    let source: String
    let publishedDate: Date
    let category: NewsCategory
    let region: TamilRegion
    let isBreaking: Bool
    let culturalRelevance: CulturalRelevance
    let tags: [String]
    let readingTime: Int

    init(title: String, tamilTitle: String?, summary: String, url: String, source: String, publishedDate: Date, category: NewsCategory, region: TamilRegion, isBreaking: Bool, culturalRelevance: CulturalRelevance, tags: [String], readingTime: Int) {
        self.id = UUID()
        self.title = title
        self.tamilTitle = tamilTitle
        self.summary = summary
        self.url = url
        self.source = source
        self.publishedDate = publishedDate
        self.category = category
        self.region = region
        self.isBreaking = isBreaking
        self.culturalRelevance = culturalRelevance
        self.tags = tags
        self.readingTime = readingTime
    }
}

// Using enums from TamilCulturalModels.swift

enum CulturalRelevance: String, Codable {
    case high = "High"
    case medium = "Medium"
    case low = "Low"
}

// MARK: - Tamil News Service

@MainActor
class TamilNewsService: ObservableObject {
    static let shared = TamilNewsService()
    
    @Published var latestNews: [TamilNewsArticle] = []
    @Published var breakingNews: [TamilNewsArticle] = []
    @Published var culturalNews: [TamilNewsArticle] = []
    @Published var regionalNews: [TamilNewsArticle] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupPeriodicUpdates()
    }
    
    // MARK: - Public Methods
    
    func loadLatestNews() async {
        isLoading = true
        
        // Generate mock news articles
        let mockArticles = generateMockNews()
        
        latestNews = Array(mockArticles.prefix(10))
        breakingNews = mockArticles.filter { $0.isBreaking }
        culturalNews = mockArticles.filter { $0.culturalRelevance == .high }
        regionalNews = mockArticles.filter { $0.region != .tamilNadu }
        
        isLoading = false
    }
    
    func formatPublishedDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    func getCategoryIcon(_ category: NewsCategory) -> String {
        switch category {
        case .culture: return "theatermasks"
        case .education: return "graduationcap"
        case .arts: return "paintbrush.pointed"
        case .technology: return "laptopcomputer"
        case .community: return "person.3"
        case .heritage: return "building.columns"
        case .language: return "textformat.abc"
        }
    }
    
    // MARK: - Private Methods
    
    private func generateMockNews() -> [TamilNewsArticle] {
        // Combine enhanced mock data with some additional current articles
        return TamilNewsService.enhancedTamilNews + [
            TamilNewsArticle(
                title: "Tamil Nadu Celebrates Pongal Festival with Traditional Fervor",
                tamilTitle: "தமிழ்நாடு பாரம்பரிய உற்சாகத்துடன் பொங்கல் திருவிழாவை கொண்டாடுகிறது",
                summary: "The harvest festival of Pongal is being celebrated across Tamil Nadu with traditional enthusiasm, featuring traditional games, cultural programs, and community feasts.",
                url: "https://example.com/pongal-celebration",
                source: "Tamil Guardian",
                publishedDate: Date().addingTimeInterval(-19800), // 5.5 hours ago
                category: .culture,
                region: .tamilNadu,
                isBreaking: false,
                culturalRelevance: .high,
                tags: ["Pongal", "Festival", "Tamil Nadu", "Tradition"],
                readingTime: 3
            ),
            TamilNewsArticle(
                title: "Chennai Metro Phase 2 Construction Progresses",
                tamilTitle: "சென்னை மெட்ரோ இரண்டாம் கட்ட கட்டுமானம் முன்னேறுகிறது",
                summary: "The second phase of Chennai Metro Rail construction is making significant progress with new stations and improved connectivity.",
                url: "https://example.com/chennai-metro-phase2",
                source: "Chennai Times",
                publishedDate: Date().addingTimeInterval(-21600), // 6 hours ago
                category: .technology,
                region: .tamilNadu,
                isBreaking: false,
                culturalRelevance: .medium,
                tags: ["Chennai", "Metro", "Infrastructure", "Transportation"],
                readingTime: 4
            ),
            TamilNewsArticle(
                title: "Madurai Meenakshi Temple Receives Record Devotees",
                tamilTitle: "மதுரை மீனாக்ஷி கோயிலில் அதிக பக்தர்கள் வருகை",
                summary: "The historic Meenakshi Amman Temple witnessed a record number of devotees during the recent festival season.",
                url: "https://example.com/meenakshi-temple-devotees",
                source: "Madurai Mirror",
                publishedDate: Date().addingTimeInterval(-23400), // 6.5 hours ago
                category: .heritage,
                region: .tamilNadu,
                isBreaking: false,
                culturalRelevance: .high,
                tags: ["Madurai", "Temple", "Devotees", "Festival"],
                readingTime: 3
            )
        ]
    }
    
    private func setupPeriodicUpdates() {
        Timer.publish(every: 1800, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.loadLatestNews()
                }
            }
            .store(in: &cancellables)
    }
}
