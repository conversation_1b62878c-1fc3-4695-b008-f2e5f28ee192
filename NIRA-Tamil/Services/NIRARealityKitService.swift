//
//  NIRARealityKitService.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  iOS 18 RealityKit 4 Integration for AR Cultural Immersion
//

import Foundation
import SwiftUI
import RealityKit
import ARKit
import Combine

// MARK: - Reality Kit Service

@MainActor
class NIRARealityKitService: ObservableObject {
    static let shared = NIRARealityKitService()
    
    @Published var isARSupported = false
    @Published var isSessionActive = false
    @Published var currentEnvironment: CulturalEnvironment?
    @Published var availableEnvironments: [CulturalEnvironment] = []
    @Published var interactiveObjects: [InteractiveObject] = []
    @Published var culturalGuide: CulturalGuideCharacter?
    
    private var arView: ARView?
    private var environmentAnchor: AnchorEntity?
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        checkARSupport()
        setupAvailableEnvironments()
    }
    
    // MARK: - AR Support Check
    
    private func checkARSupport() {
        isARSupported = ARWorldTrackingConfiguration.isSupported
    }
    
    // MARK: - Environment Setup
    
    private func setupAvailableEnvironments() {
        availableEnvironments = [
            CulturalEnvironment(
                id: "tamil-temple",
                name: "Tamil Temple",
                description: "Explore a traditional Tamil temple with interactive elements",
                modelName: "TamilTemple",
                interactiveElements: ["Bell", "Statue", "Offerings", "Inscriptions"],
                vocabulary: ["கோயில்", "மணி", "சிலை", "பூஜை"],
                difficulty: .beginner
            ),
            CulturalEnvironment(
                id: "market-scene",
                name: "Tamil Market",
                description: "Practice shopping conversations in a bustling Tamil market",
                modelName: "TamilMarket",
                interactiveElements: ["Fruits", "Vegetables", "Spices", "Vendor"],
                vocabulary: ["சந்தை", "பழம்", "காய்கறி", "மசாலா"],
                difficulty: .intermediate
            ),
            CulturalEnvironment(
                id: "traditional-home",
                name: "Traditional Home",
                description: "Learn family vocabulary in a traditional Tamil home",
                modelName: "TamilHome",
                interactiveElements: ["Kitchen", "Prayer Room", "Courtyard", "Family"],
                vocabulary: ["வீடு", "சமையலறை", "முற்றம்", "குடும்பம்"],
                difficulty: .beginner
            ),
            CulturalEnvironment(
                id: "festival-celebration",
                name: "Festival Celebration",
                description: "Experience Tamil festivals with music, dance, and traditions",
                modelName: "TamilFestival",
                interactiveElements: ["Decorations", "Music", "Dance", "Food"],
                vocabulary: ["திருவிழா", "அலங்காரம்", "இசை", "நடனம்"],
                difficulty: .advanced
            )
        ]
    }
    
    // MARK: - AR Session Management
    
    func startARSession(with arView: ARView) async throws {
        guard isARSupported else {
            throw ARError.unsupportedConfiguration
        }
        
        self.arView = arView
        
        let configuration = ARWorldTrackingConfiguration()
        configuration.planeDetection = [.horizontal, .vertical]
        configuration.environmentTexturing = .automatic
        
        if ARWorldTrackingConfiguration.supportsSceneReconstruction(.mesh) {
            configuration.sceneReconstruction = .mesh
        }
        
        arView.session.run(configuration)
        isSessionActive = true
        
        // Setup session delegate
        setupSessionDelegate()
    }
    
    func stopARSession() {
        arView?.session.pause()
        isSessionActive = false
        currentEnvironment = nil
        environmentAnchor = nil
    }
    
    // MARK: - Environment Loading
    
    func loadEnvironment(_ environment: CulturalEnvironment) async throws {
        guard let arView = arView else {
            throw ARError.sessionNotActive
        }
        
        // Remove existing environment
        if let existingAnchor = environmentAnchor {
            arView.scene.removeAnchor(existingAnchor)
        }
        
        // Load new environment
        do {
            let environmentEntity = try await loadEnvironmentModel(environment.modelName)
            
            // Create anchor for horizontal plane
            let anchor = AnchorEntity(.plane(.horizontal, classification: .any, minimumBounds: [0.5, 0.5]))
            anchor.addChild(environmentEntity)
            
            // Add interactive components
            await setupInteractiveElements(for: environment, in: environmentEntity)
            
            // Add to scene
            arView.scene.addAnchor(anchor)
            
            environmentAnchor = anchor
            currentEnvironment = environment
            
            // Load cultural guide
            await loadCulturalGuide(for: environment)
            
        } catch {
            throw ARError.modelLoadingFailed
        }
    }
    
    private func loadEnvironmentModel(_ modelName: String) async throws -> ModelEntity {
        // In a real implementation, this would load from Reality Composer Pro
        // For now, we'll create a placeholder
        let mesh = MeshResource.generateBox(size: [2, 0.1, 2])
        let material = SimpleMaterial(color: .brown, isMetallic: false)
        let entity = ModelEntity(mesh: mesh, materials: [material])
        
        // Add some basic geometry to represent the environment
        await addEnvironmentDetails(to: entity, for: modelName)
        
        return entity
    }
    
    private func addEnvironmentDetails(to entity: ModelEntity, for modelName: String) async {
        switch modelName {
        case "TamilTemple":
            await addTempleDetails(to: entity)
        case "TamilMarket":
            await addMarketDetails(to: entity)
        case "TamilHome":
            await addHomeDetails(to: entity)
        case "TamilFestival":
            await addFestivalDetails(to: entity)
        default:
            break
        }
    }
    
    // MARK: - Interactive Elements
    
    private func setupInteractiveElements(for environment: CulturalEnvironment, in entity: ModelEntity) async {
        interactiveObjects.removeAll()
        
        for (index, elementName) in environment.interactiveElements.enumerated() {
            let interactiveObject = InteractiveObject(
                id: UUID(),
                name: elementName,
                tamilName: environment.vocabulary[safe: index] ?? elementName,
                position: generatePosition(for: index),
                type: .vocabulary,
                audioURL: nil
            )
            
            // Create visual representation
            let objectEntity = await createInteractiveEntity(for: interactiveObject)
            entity.addChild(objectEntity)
            
            interactiveObjects.append(interactiveObject)
        }
    }
    
    private func createInteractiveEntity(for object: InteractiveObject) async -> ModelEntity {
        let mesh = MeshResource.generateSphere(radius: 0.1)
        let material = SimpleMaterial(color: .blue, isMetallic: false)
        let entity = ModelEntity(mesh: mesh, materials: [material])
        
        entity.position = object.position
        entity.name = object.id.uuidString
        
        // Add collision component for interaction
        entity.components.set(CollisionComponent(shapes: [.generateSphere(radius: 0.1)]))
        
        // Add input target component
        entity.components.set(InputTargetComponent())
        
        return entity
    }
    
    // MARK: - Cultural Guide
    
    private func loadCulturalGuide(for environment: CulturalEnvironment) async {
        let guide = CulturalGuideCharacter(
            id: UUID(),
            name: "Meera",
            appearance: .traditional,
            personality: .friendly,
            specialization: environment.name,
            animations: ["greeting", "explaining", "pointing", "celebrating"]
        )
        
        // Load guide model and animations
        await loadGuideModel(guide)
        
        culturalGuide = guide
    }
    
    private func loadGuideModel(_ guide: CulturalGuideCharacter) async {
        // In a real implementation, this would load a 3D character model
        // with animations from Reality Composer Pro
    }
    
    // MARK: - Interaction Handling
    
    func handleObjectInteraction(_ objectId: UUID) async {
        guard let object = interactiveObjects.first(where: { $0.id == objectId }) else { return }
        
        // Play pronunciation audio
        await playObjectPronunciation(object)
        
        // Show vocabulary information
        await showVocabularyInfo(object)
        
        // Trigger guide explanation
        await triggerGuideExplanation(for: object)
    }
    
    private func playObjectPronunciation(_ object: InteractiveObject) async {
        // Integrate with existing audio service
        // AudioPlayerService.shared.playPronunciation(object.tamilName)
    }
    
    private func showVocabularyInfo(_ object: InteractiveObject) async {
        // Show vocabulary card in AR space
        NotificationCenter.default.post(
            name: .showARVocabulary,
            object: nil,
            userInfo: ["object": object]
        )
    }
    
    private func triggerGuideExplanation(for object: InteractiveObject) async {
        guard let guide = culturalGuide else { return }
        
        // Animate guide to point at object and provide explanation
        await animateGuide(guide, action: .pointAt(object.position))
        
        // Generate contextual explanation
        let explanation = generateExplanation(for: object, in: currentEnvironment)
        
        NotificationCenter.default.post(
            name: .guideExplanation,
            object: nil,
            userInfo: ["explanation": explanation, "object": object]
        )
    }
    
    // MARK: - Helper Methods
    
    private func setupSessionDelegate() {
        // Setup AR session delegate for plane detection and tracking
    }
    
    private func generatePosition(for index: Int) -> SIMD3<Float> {
        let angle = Float(index) * (2 * .pi / 4) // Distribute around circle
        let radius: Float = 0.8
        return SIMD3<Float>(
            cos(angle) * radius,
            0.1,
            sin(angle) * radius
        )
    }
    
    private func generateExplanation(for object: InteractiveObject, in environment: CulturalEnvironment?) -> String {
        return "This is \(object.tamilName) (\(object.name)) - an important element in Tamil culture."
    }

    private func animateGuide(_ guide: CulturalGuideCharacter, action: CulturalGuideCharacter.Action) async {
        // Animate the cultural guide character based on the action
        switch action {
        case .greeting:
            print("Guide \(guide.name) is greeting the user")
        case .pointAt(let position):
            print("Guide \(guide.name) is pointing at position \(position)")
        case .explaining:
            print("Guide \(guide.name) is explaining a concept")
        case .celebrating:
            print("Guide \(guide.name) is celebrating user success")
        }

        // In a real implementation, this would animate the 3D character
        // For now, we'll just log the action
    }
    
    // MARK: - Environment Detail Methods
    
    private func addTempleDetails(to entity: ModelEntity) async {
        // Add temple-specific elements like pillars, bells, statues
    }
    
    private func addMarketDetails(to entity: ModelEntity) async {
        // Add market stalls, fruits, vegetables, vendors
    }
    
    private func addHomeDetails(to entity: ModelEntity) async {
        // Add traditional home elements like courtyard, kitchen, prayer room
    }
    
    private func addFestivalDetails(to entity: ModelEntity) async {
        // Add festival decorations, stage, performers
    }
}

// MARK: - Supporting Models

struct CulturalEnvironment: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let modelName: String
    let interactiveElements: [String]
    let vocabulary: [String]
    let difficulty: Difficulty
    
    enum Difficulty: String, Codable, CaseIterable {
        case beginner = "beginner"
        case intermediate = "intermediate"
        case advanced = "advanced"
    }
}

struct InteractiveObject: Identifiable, Codable {
    let id: UUID
    let name: String
    let tamilName: String
    let position: SIMD3<Float>
    let type: ObjectType
    let audioURL: String?
    
    enum ObjectType: String, Codable {
        case vocabulary = "vocabulary"
        case cultural = "cultural"
        case interactive = "interactive"
    }
}

struct CulturalGuideCharacter: Identifiable {
    let id: UUID
    let name: String
    let appearance: Appearance
    let personality: Personality
    let specialization: String
    let animations: [String]
    
    enum Appearance: String, CaseIterable {
        case traditional = "traditional"
        case modern = "modern"
        case casual = "casual"
    }
    
    enum Personality: String, CaseIterable {
        case friendly = "friendly"
        case wise = "wise"
        case energetic = "energetic"
    }
    
    enum Action {
        case greeting
        case pointAt(SIMD3<Float>)
        case explaining
        case celebrating
    }
}

enum ARError: Error {
    case unsupportedConfiguration
    case sessionNotActive
    case modelLoadingFailed
    case permissionDenied
}

// MARK: - Extensions

// Array safe subscript extension already defined in GrammarWritingPracticeView.swift

extension Notification.Name {
    static let showARVocabulary = Notification.Name("showARVocabulary")
    static let guideExplanation = Notification.Name("guideExplanation")
}

// MARK: - AR View Wrapper

struct NIRAARView: UIViewRepresentable {
    @StateObject private var realityService = NIRARealityKitService.shared
    
    func makeUIView(context: Context) -> ARView {
        let arView = ARView(frame: .zero)
        
        Task {
            do {
                try await realityService.startARSession(with: arView)
            } catch {
                print("Failed to start AR session: \(error)")
            }
        }
        
        return arView
    }
    
    func updateUIView(_ uiView: ARView, context: Context) {
        // Update AR view if needed
    }
}
