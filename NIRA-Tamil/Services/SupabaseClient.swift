import Foundation
import Combine
import Supabase
import Auth

// MARK: - Real Supabase Client Implementation
// Enhanced Supabase client with full chat history and conversation management

// MARK: - Real Supabase Client
@MainActor
class NIRASupabaseClient: ObservableObject {
    static let shared = NIRASupabaseClient()

    @Published var session: Session?
    @Published var isConnected: Bool = false
    @Published var currentUser: Supabase.User?

    let client: SupabaseClient

    private init() {
        // Initialize with real Supabase configuration from APIKeys
        self.client = SupabaseClient(
            supabaseURL: URL(string: APIKeys.supabaseURL)!,
            supabaseKey: APIKeys.supabaseAnonKey
        )

        Task {
            await initializeSession()
        }
    }

    // MARK: - Session Management

    private func initializeSession() async {
        print("🔄 Initializing Supabase session...")
        do {
            let session = try await client.auth.session
            print("✅ Found existing session for user: \(session.user.id)")
            await MainActor.run {
                self.session = session
                self.currentUser = session.user
                self.isConnected = true
            }
        } catch {
            print("⚠️ No active session: \(error)")
            print("🔑 Will need to authenticate user...")
            await MainActor.run {
                self.isConnected = false
            }
        }
    }

    // Anonymous authentication is disabled in Supabase project
    // Use AuthenticationService for proper user authentication instead
    /*
    func signInAnonymously() async throws {
        print("🔑 Attempting anonymous sign-in...")
        do {
            let session = try await client.auth.signInAnonymously()
            print("✅ Anonymous sign-in successful! User ID: \(session.user.id)")
            await MainActor.run {
                self.session = session
                self.currentUser = session.user
                self.isConnected = true
            }
        } catch {
            print("❌ Anonymous sign-in failed: \(error)")
            throw error
        }
    }
    */

    // MARK: - Session Synchronization

    func syncWithCurrentSession() async {
        print("🔄 Syncing with current Supabase session...")
        do {
            let session = try await client.auth.session
            print("✅ Found current session for user: \(session.user.id)")
            await MainActor.run {
                self.session = session
                self.currentUser = session.user
                self.isConnected = true
            }
            print("✅ SupabaseClient synced with session")
        } catch {
            print("❌ Failed to sync with session: \(error)")
            await MainActor.run {
                self.session = nil
                self.currentUser = nil
                self.isConnected = false
            }
        }
    }

    // MARK: - User Profile

    func getUserProfile() async throws -> SupabaseUserProfile? {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let response: [SupabaseUserProfile] = try await client
            .from("user_profiles")
            .select()
            .eq("id", value: userId)
            .execute()
            .value

        return response.first
    }

    // MARK: - Lesson Content

    func getLessonContentComplete(lessonId: UUID) async throws -> (
        vocabulary: [LessonVocabularyItem],
        conversations: [LessonDialogueItem],
        grammar: [LessonGrammarPoint],
        exercises: [LessonExerciseItem]
    ) {
        async let vocabulary = getLessonVocabulary(lessonId: lessonId)
        async let conversations = getLessonConversations(lessonId: lessonId)
        async let grammar = getLessonGrammar(lessonId: lessonId)
        async let exercises = getLessonExercises(lessonId: lessonId)

        return try await (
            vocabulary: vocabulary,
            conversations: conversations,
            grammar: grammar,
            exercises: exercises
        )
    }

    private func getLessonVocabulary(lessonId: UUID) async throws -> [LessonVocabularyItem] {
        let response: [LessonVocabularyItem] = try await client
            .from("lesson_vocabulary")
            .select()
            .eq("lesson_id", value: lessonId)
            .order("order_index")
            .execute()
            .value

        return response
    }

    private func getLessonConversations(lessonId: UUID) async throws -> [LessonDialogueItem] {
        let response: [LessonDialogueItem] = try await client
            .from("lesson_conversations")
            .select()
            .eq("lesson_id", value: lessonId)
            .order("order_index")
            .execute()
            .value

        return response
    }

    private func getLessonGrammar(lessonId: UUID) async throws -> [LessonGrammarPoint] {
        let response: [LessonGrammarPoint] = try await client
            .from("lesson_grammar")
            .select()
            .eq("lesson_id", value: lessonId)
            .order("order_index")
            .execute()
            .value

        return response
    }

    private func getLessonExercises(lessonId: UUID) async throws -> [LessonExerciseItem] {
        let response: [LessonExerciseItem] = try await client
            .from("lesson_exercises")
            .select()
            .eq("lesson_id", value: lessonId)
            .order("order_index")
            .execute()
            .value

        return response
    }

    // MARK: - Chat History Management

    func createConversation(
        agentId: UUID,
        agentName: String,
        title: String? = nil
    ) async throws -> SupabaseChatConversation {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let conversation = SupabaseChatConversation(
            id: UUID(),
            userId: UUID(uuidString: userId.uuidString)!,
            agentId: agentId,
            lessonId: nil,
            title: title ?? "Chat with \(agentName)",
            status: .active,
            metadata: SupabaseAnyCodable(["agentName": agentName]),
            createdAt: Date(),
            updatedAt: Date()
        )

        let response: [SupabaseChatConversation] = try await client
            .from("chat_conversations")
            .insert(conversation)
            .select()
            .execute()
            .value

        return response.first!
    }

    func saveMessage(
        conversationId: UUID,
        content: String,
        isFromUser: Bool,
        messageType: SupabaseMessageType = .text,
        metadata: [String: Any] = [:]
    ) async throws -> SupabaseChatMessage {
        let message = SupabaseChatMessage(
            id: UUID(),
            conversationId: conversationId,
            senderType: isFromUser ? .user : .agent,
            content: content,
            messageType: messageType,
            metadata: SupabaseAnyCodable(metadata),
            createdAt: Date()
        )

        let response: [SupabaseChatMessage] = try await client
            .from("chat_messages")
            .insert(message)
            .select()
            .execute()
            .value

        // Update conversation's updated_at timestamp
        try await client
            .from("chat_conversations")
            .update(["updated_at": Date().ISO8601String()])
            .eq("id", value: conversationId)
            .execute()

        return response.first!
    }

    func getConversationHistory(conversationId: UUID) async throws -> [SupabaseChatMessage] {
        let response: [SupabaseChatMessage] = try await client
            .from("chat_messages")
            .select()
            .eq("conversation_id", value: conversationId)
            .order("created_at", ascending: true)
            .execute()
            .value

        return response
    }

    func getUserConversations() async throws -> [SupabaseChatConversation] {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let response: [SupabaseChatConversation] = try await client
            .from("chat_conversations")
            .select()
            .eq("user_id", value: userId)
            .order("updated_at", ascending: false)
            .execute()
            .value

        return response
    }

    func deleteConversation(conversationId: UUID) async throws {
        // Delete messages first
        try await client
            .from("chat_messages")
            .delete()
            .eq("conversation_id", value: conversationId)
            .execute()

        // Delete conversation
        try await client
            .from("chat_conversations")
            .delete()
            .eq("id", value: conversationId)
            .execute()
    }

    func searchConversations(query: String) async throws -> [SupabaseChatConversation] {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let response: [SupabaseChatConversation] = try await client
            .from("chat_conversations")
            .select()
            .eq("user_id", value: userId)
            .ilike("title", pattern: "%\(query)%")
            .order("updated_at", ascending: false)
            .execute()
            .value

        return response
    }
}

// MARK: - Mock Supabase Models for Compilation

// Note: SupabaseUserProfile is defined in SupabaseModels.swift

// Note: SupabaseLesson is defined in SupabaseModels.swift

enum SupabaseError: LocalizedError {
    case notAuthenticated
    case networkError(String)
    case databaseError(String)
    case invalidData

    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "User not authenticated"
        case .networkError(let message):
            return "Network error: \(message)"
        case .databaseError(let message):
            return "Database error: \(message)"
        case .invalidData:
            return "Invalid data format"
        }
    }
}



// MARK: - Extensions

extension Date {
    func ISO8601String() -> String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
}



// MARK: - Review Log Database Models

struct ReviewLogRow: Codable {
    let id: UUID
    let card_id: UUID
    let user_id: UUID
    let rating: Int
    let state: String
    let due: Date
    let stability: Double
    let difficulty: Double
    let elapsed_days: Int
    let last_elapsed_days: Int
    let scheduled_days: Int
    let review_time: Date
    let response_time_ms: Int?
    let created_at: Date?
    
    private enum CodingKeys: String, CodingKey {
        case id, card_id, user_id, rating, state, due, stability, difficulty
        case elapsed_days, last_elapsed_days, scheduled_days, review_time
        case response_time_ms, created_at
    }
    
    init(from log: ReviewLog, userId: UUID) {
        self.id = UUID(uuidString: log.id) ?? UUID()
        self.card_id = UUID(uuidString: log.cardId) ?? UUID()
        self.user_id = userId
        self.rating = log.rating.rawValue
        self.state = log.state.rawValue
        self.due = log.due
        self.stability = log.stability
        self.difficulty = log.difficulty
        self.elapsed_days = log.elapsedDays
        self.last_elapsed_days = log.lastElapsedDays
        self.scheduled_days = log.scheduledDays
        self.review_time = log.review
        self.response_time_ms = nil
        self.created_at = nil
    }
    
    func toReviewLog() -> ReviewLog {
        return ReviewLog(
            id: id.uuidString,
            cardId: card_id.uuidString,
            rating: ReviewRating(rawValue: rating) ?? .good,
            state: CardState(rawValue: state) ?? .new,
            due: due,
            stability: stability,
            difficulty: difficulty,
            elapsedDays: elapsed_days,
            lastElapsedDays: last_elapsed_days,
            scheduledDays: scheduled_days,
            review: review_time
        )
    }
}

// MARK: - Assessment Database Models

struct AssessmentResponseRow: Codable {
    let id: UUID
    let assessment_id: UUID
    let item_id: UUID
    let user_id: UUID
    let user_answer: String
    let correct_answer: String
    let is_correct: Bool
    let time_spent: Int
    let attempts: Int
    let confidence_level: Double?
    let created_at: Date?
    
    private enum CodingKeys: String, CodingKey {
        case id, assessment_id, item_id, user_id, user_answer, correct_answer
        case is_correct, time_spent, attempts, confidence_level, created_at
    }
    
    init(from response: MicroAssessmentResponse, userId: UUID) {
        self.id = UUID(uuidString: response.id) ?? UUID()
        self.assessment_id = UUID(uuidString: response.assessmentId) ?? UUID()
        self.item_id = UUID(uuidString: response.itemId) ?? UUID()
        self.user_id = userId
        self.user_answer = response.userAnswer
        self.correct_answer = response.correctAnswer
        self.is_correct = response.isCorrect
        self.time_spent = Int(response.timeSpent * 1000) // Convert to milliseconds
        self.attempts = 1
        self.confidence_level = nil
        self.created_at = nil
    }
}

struct LearningUnitCompletionRow: Codable {
    let id: UUID
    let user_id: UUID
    let lesson_id: UUID?
    let unit_id: String
    let unit_type: String
    let accuracy_score: Double
    let completion_time: Int
    let attempts_needed: Int
    let difficulty_level: Int
    let engagement_score: Double
    let completed_at: Date
    
    private enum CodingKeys: String, CodingKey {
        case id, user_id, lesson_id, unit_id, unit_type, accuracy_score
        case completion_time, attempts_needed, difficulty_level, engagement_score, completed_at
    }
    
    init(from unit: CompletedLearningUnit, userId: UUID) {
        self.id = UUID(uuidString: unit.id) ?? UUID()
        self.user_id = userId
        self.lesson_id = nil
        self.unit_id = unit.id
        self.unit_type = unit.type.rawValue
        self.accuracy_score = unit.performance.accuracyScore
        self.completion_time = Int(unit.performance.completionTime)
        self.attempts_needed = unit.performance.attemptsNeeded
        self.difficulty_level = unit.performance.currentDifficulty
        self.engagement_score = unit.performance.engagementScore
        self.completed_at = unit.completedAt
    }
}

struct AssessmentReportRow: Codable {
    let id: UUID
    let assessment_id: UUID
    let user_id: UUID
    let overall_score: Double
    let total_time_spent: Int
    let completion_percentage: Double
    let skill_scores: [String: Double]
    let improved_areas: [String]
    let areas_for_focus: [String]
    let generated_at: Date
}

