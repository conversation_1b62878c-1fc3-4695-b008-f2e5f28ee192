 //
//  BiometricLearningService.swift
//  NIRA
//
//  Enhanced Biometric Learning Integration Service
//  Monitors learner's physiological state to optimize learning experience
//

import SwiftUI
import Combine
import HealthKit

class BiometricLearningService: ObservableObject {
    @Published var isStressDetected: Bool = false
    @Published var cognitiveLoadLevel: CognitiveLoadLevel = .optimal
    @Published var attentionLevel: Double = 0.75
    @Published var heartRateVariability: Double = 50.0
    @Published var isMonitoringActive: Bool = false
    @Published var isActive: Bool = false
    @Published var currentMetrics: BiometricSnapshot?
    
    private var cancellables = Set<AnyCancellable>()
    private let healthStore = HKHealthStore()
    
    enum CognitiveLoadLevel: String, CaseIterable {
        case low = "Low"
        case optimal = "Optimal"
        case high = "High"
        case overload = "Overload"
        
        var color: Color {
            switch self {
            case .low: return .blue
            case .optimal: return .green
            case .high: return .orange
            case .overload: return .red
            }
        }
    }
    
    init() {
        setupBiometricMonitoring()
        startSimulation()
        isActive = true
    }
    
    private func setupBiometricMonitoring() {
        // Request HealthKit permissions if available
        guard HKHealthStore.isHealthDataAvailable() else {
            print("HealthKit not available on this device")
            isMonitoringActive = false
            return
        }
        
        guard let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate),
              let hrvType = HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN) else {
            print("Failed to create HealthKit quantity types")
            isMonitoringActive = false
            return
        }
        
        let typesToRead: Set<HKObjectType> = [heartRateType, hrvType]
        
        healthStore.requestAuthorization(toShare: nil, read: typesToRead) { [weak self] success, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("HealthKit authorization error: \(error.localizedDescription)")
                    self?.isMonitoringActive = false
                } else {
                    print("HealthKit authorization successful: \(success)")
                    self?.isMonitoringActive = success
                    if success {
                        self?.startRealTimeMonitoring()
                    }
                }
            }
        }
    }
    
    private func startRealTimeMonitoring() {
        // Simulate real-time biometric data updates
        Timer.publish(every: 2.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateBiometricData()
            }
            .store(in: &cancellables)
    }
    
    private func startSimulation() {
        // For demo purposes, simulate biometric changes
        Timer.publish(every: 5.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.simulateBiometricChanges()
            }
            .store(in: &cancellables)
    }
    
    private func updateBiometricData() {
        // Update attention level based on heart rate variability
        let newAttentionLevel = max(0.0, min(1.0, heartRateVariability / 100.0))
        
        withAnimation(.easeInOut(duration: 1.0)) {
            attentionLevel = newAttentionLevel
            
            // Determine cognitive load level
            if attentionLevel > 0.8 {
                cognitiveLoadLevel = .optimal
                isStressDetected = false
            } else if attentionLevel > 0.6 {
                cognitiveLoadLevel = .high
                isStressDetected = false
            } else if attentionLevel > 0.4 {
                cognitiveLoadLevel = .high
                isStressDetected = true
            } else {
                cognitiveLoadLevel = .overload
                isStressDetected = true
            }
            
            // Update current metrics
            currentMetrics = getCurrentSnapshot()
        }
    }
    
    private func simulateBiometricChanges() {
        // Simulate realistic biometric variations
        let variations = [
            (hrv: 45.0, stress: true),
            (hrv: 65.0, stress: false),
            (hrv: 35.0, stress: true),
            (hrv: 75.0, stress: false),
            (hrv: 55.0, stress: false)
        ]
        
        let randomVariation = variations.randomElement()!
        
        withAnimation(.easeInOut(duration: 2.0)) {
            heartRateVariability = randomVariation.hrv
            isStressDetected = randomVariation.stress
        }
    }
    
    // MARK: - Public Methods
    
    func adaptLearningIntensity() -> LearningIntensityRecommendation {
        switch cognitiveLoadLevel {
        case .low:
            return .increaseIntensity
        case .optimal:
            return .maintainCurrent
        case .high:
            return .reduceSlightly
        case .overload:
            return .takeBreak
        }
    }
    
    func getRecommendedBreakDuration() -> TimeInterval {
        switch cognitiveLoadLevel {
        case .low, .optimal:
            return 0
        case .high:
            return 60 // 1 minute
        case .overload:
            return 300 // 5 minutes
        }
    }
    
    func shouldShowStressReductionPrompt() -> Bool {
        return isStressDetected && cognitiveLoadLevel == .overload
    }
    
    func startMonitoring() async {
        isActive = true
        isMonitoringActive = true
    }
    
    func stopMonitoring() async {
        isActive = false
        isMonitoringActive = false
    }
}

// MARK: - Supporting Types

enum LearningIntensityRecommendation {
    case increaseIntensity
    case maintainCurrent
    case reduceSlightly
    case takeBreak
    
    var description: String {
        switch self {
        case .increaseIntensity:
            return "You're doing great! Ready for more challenging content?"
        case .maintainCurrent:
            return "Perfect pace! Keep up the excellent work."
        case .reduceSlightly:
            return "Let's take it a bit easier for optimal learning."
        case .takeBreak:
            return "Time for a quick break to recharge your focus."
        }
    }
    
    var icon: String {
        switch self {
        case .increaseIntensity:
            return "arrow.up.circle.fill"
        case .maintainCurrent:
            return "checkmark.circle.fill"
        case .reduceSlightly:
            return "arrow.down.circle.fill"
        case .takeBreak:
            return "pause.circle.fill"
        }
    }
}

// MARK: - Biometric Data Models

struct BiometricSnapshot {
    let timestamp: Date
    let heartRate: Double
    let heartRateVariability: Double
    let stressLevel: Double
    let cognitiveLoad: BiometricLearningService.CognitiveLoadLevel
    let attentionLevel: Double
}

extension BiometricLearningService {
    func getCurrentSnapshot() -> BiometricSnapshot {
        return BiometricSnapshot(
            timestamp: Date(),
            heartRate: 75.0, // Would be actual data in production
            heartRateVariability: heartRateVariability,
            stressLevel: isStressDetected ? 0.8 : 0.3,
            cognitiveLoad: cognitiveLoadLevel,
            attentionLevel: attentionLevel
        )
    }
} 