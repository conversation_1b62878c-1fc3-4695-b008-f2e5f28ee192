import Foundation
import Combine

// MARK: - Data Models

struct CurriculumPath: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String
    let language: Language
    let targetLevel: ProficiencyLevel
    let estimatedDuration: TimeInterval
    let prerequisites: [UUID]
    let skills: [CurriculumSkillArea]
    let adaptiveRules: [AdaptiveRule]
    let createdAt: Date
    let updatedAt: Date
}

struct CurriculumSkillArea: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String
    let category: SkillCategory
    let weight: Double
    let prerequisites: [UUID]
    let masteryThreshold: Double
}

enum SkillCategory: String, Codable, CaseIterable {
    case vocabulary = "vocabulary"
    case grammar = "grammar"
    case pronunciation = "pronunciation"
    case listening = "listening"
    case speaking = "speaking"
    case reading = "reading"
    case writing = "writing"
    case culture = "culture"
}

enum ProficiencyLevel: String, Codable, CaseIterable {
    case beginner = "beginner"
    case elementary = "elementary"
    case intermediate = "intermediate"
    case upperIntermediate = "upper_intermediate"
    case advanced = "advanced"
    case proficient = "proficient"
    
    var numericValue: Double {
        switch self {
        case .beginner: return 1.0
        case .elementary: return 2.0
        case .intermediate: return 3.0
        case .upperIntermediate: return 4.0
        case .advanced: return 5.0
        case .proficient: return 6.0
        }
    }
}

struct AdaptiveRule: Codable {
    let id: UUID
    let condition: RuleCondition
    let action: RuleAction
    let priority: Int
    let isActive: Bool
}

enum RuleCondition: Codable {
    case skillMastery(skillId: UUID, threshold: Double)
    case consecutiveFailures(count: Int)
    case timeSpent(minutes: Int)
    case streakLength(days: Int)
    case difficultyStuck(level: Double, duration: TimeInterval)
}

enum RuleAction: Codable {
    case skipToLesson(lessonId: UUID)
    case repeatSkill(skillId: UUID)
    case adjustDifficulty(factor: Double)
    case recommendBreak(duration: TimeInterval)
    case suggestReview(skillIds: [UUID])
}

struct LearningRecommendation: Codable, Identifiable {
    let id: UUID
    let type: RecommendationType
    let title: String
    let description: String
    let priority: RecommendationPriority
    let targetSkills: [UUID]
    let estimatedTime: TimeInterval
    let confidence: Double
    let reasoning: String
    let createdAt: Date
}

enum RecommendationType: String, Codable {
    case nextLesson = "next_lesson"
    case skillReview = "skill_review"
    case difficultyAdjustment = "difficulty_adjustment"
    case breakRecommendation = "break_recommendation"
    case practiceExercise = "practice_exercise"
}

enum RecommendationPriority: String, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case urgent = "urgent"
}

struct SkillAssessment: Codable {
    let skillId: UUID
    var currentLevel: Double
    let masteryScore: Double
    let confidence: Double
    var lastAssessed: Date
    var improvementRate: Double
    let strugglingAreas: [String]
    let strengths: [String]
}

// MARK: - Service

@MainActor
class AdaptiveCurriculumService: ObservableObject {
    @Published var currentPath: CurriculumPath?
    @Published var recommendations: [LearningRecommendation] = []
    @Published var skillAssessments: [UUID: SkillAssessment] = [:]
    @Published var isAnalyzing = false
    @Published var adaptationHistory: [AdaptationEvent] = []
    
    private let supabaseClient: NIRASupabaseClient
    private let analyticsService: LearningAnalyticsService
    private let geminiService: GeminiService
    private let intelligentTutoringService: IntelligentTutoringService
    private var cancellables = Set<AnyCancellable>()
    
    init(
        supabaseClient: NIRASupabaseClient? = nil,
        analyticsService: LearningAnalyticsService? = nil,
        geminiService: GeminiService? = nil,
        intelligentTutoringService: IntelligentTutoringService? = nil
    ) {
        self.supabaseClient = supabaseClient ?? .shared
        self.analyticsService = analyticsService ?? .shared
        self.geminiService = geminiService ?? GeminiService.shared
        self.intelligentTutoringService = intelligentTutoringService ?? .shared
        
        setupAnalyticsObserver()
    }
    
    // MARK: - Curriculum Path Management
    
    func createAdaptivePath(
        for language: Language,
        targetLevel: ProficiencyLevel,
        userGoals: [String],
        timeConstraints: TimeInterval?
    ) async throws -> CurriculumPath {
        isAnalyzing = true
        defer { isAnalyzing = false }
        
        // Analyze user's current skills
        let skillAssessments = try await assessCurrentSkills(for: language)
        
        // Generate adaptive curriculum using AI
        let pathRequest = """
        Create an adaptive curriculum path for language learning:
        
        Language: \(language.displayName)
        Target Level: \(targetLevel.rawValue)
        User Goals: \(userGoals.joined(separator: ", "))
        Time Constraints: \(timeConstraints?.description ?? "None")
        Current Skills: \(skillAssessments.map { "\($0.key): \($0.value.currentLevel)" })
        
        Generate a structured learning path with:
        1. Skill areas to focus on
        2. Adaptive rules for progression
        3. Prerequisites and dependencies
        4. Estimated timeline
        
        Return as JSON with curriculum structure.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: pathRequest)
        
        // Parse AI response and create curriculum path
        let path = try parseCurriculumResponse(response, language: language, targetLevel: targetLevel)
        
        currentPath = path
        
        // Track curriculum creation
        analyticsService.trackInteraction(
            userId: UUID(), // Would get current user ID in real implementation
            interactionType: .exerciseAttempt,
            contentType: .exercise,
            contentId: path.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "language": SupabaseAnyCodable(language.rawValue),
                "target_level": SupabaseAnyCodable(targetLevel.rawValue),
                "skills_count": SupabaseAnyCodable(path.skills.count)
            ]
        )
        
        return path
    }
    
    func getNextRecommendation(for userId: UUID) async throws -> LearningRecommendation? {
        guard let path = currentPath else {
            throw AdaptiveCurriculumError.noActivePath
        }
        
        isAnalyzing = true
        defer { isAnalyzing = false }
        
        // Get user's recent performance
        let recentPerformance = try await getRecentPerformance(userId: userId)
        
        // Analyze current skill levels
        let currentSkills = try await assessCurrentSkills(for: path.language)
        
        // Apply adaptive rules
        let recommendation = try await generateRecommendation(
            path: path,
            performance: recentPerformance,
            skills: currentSkills
        )
        
        if let recommendation = recommendation {
            recommendations.append(recommendation)
            
            // Track recommendation
            analyticsService.trackInteraction(
                userId: UUID(), // Would get current user ID in real implementation
                interactionType: .exerciseAttempt,
                contentType: .exercise,
                contentId: recommendation.id.uuidString,
                isCorrect: nil,
                responseTime: nil,
                metadata: [
                    "type": SupabaseAnyCodable(recommendation.type.rawValue),
                    "priority": SupabaseAnyCodable(recommendation.priority.rawValue),
                    "confidence": SupabaseAnyCodable(recommendation.confidence)
                ]
            )
        }
        
        return recommendation
    }
    
    // MARK: - Skill Assessment
    
    func assessCurrentSkills(for language: Language) async throws -> [UUID: SkillAssessment] {
        // Get user progress data
        // Mock database query for compilation
        let progressData: [String: Any] = [:]
        // try await supabaseClient.getUserProgress()
        
        // Analyze performance patterns
        let assessmentRequest = """
        Analyze user's language learning progress and assess current skill levels:
        
        Language: \(language.displayName)
        Progress Data: \(progressData)
        
        For each skill area (vocabulary, grammar, pronunciation, listening, speaking, reading, writing, culture):
        1. Assess current proficiency level (0.0-6.0)
        2. Calculate mastery score (0.0-1.0)
        3. Identify struggling areas
        4. Identify strengths
        5. Estimate improvement rate
        
        Return detailed skill assessment as JSON.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: assessmentRequest)
        
        let assessments = try parseSkillAssessments(response)
        skillAssessments = assessments
        
        return assessments
    }
    
    func updateSkillProgress(skillId: UUID, performance: Double, timeSpent: TimeInterval) async {
        guard var assessment = skillAssessments[skillId] else { return }
        
        // Update skill assessment based on performance
        let weightedScore = (assessment.currentLevel * 0.8) + (performance * 0.2)
        assessment.currentLevel = min(6.0, max(0.0, weightedScore))
        assessment.lastAssessed = Date()
        
        // Calculate improvement rate
        let timeDelta = Date().timeIntervalSince(assessment.lastAssessed)
        if timeDelta > 0 {
            assessment.improvementRate = (performance - assessment.currentLevel) / timeDelta
        }
        
        skillAssessments[skillId] = assessment
        
        // Check for adaptive rule triggers
        await checkAdaptiveRules(skillId: skillId, assessment: assessment)
    }
    
    // MARK: - Adaptive Rules Engine
    
    private func checkAdaptiveRules(skillId: UUID, assessment: SkillAssessment) async {
        guard let path = currentPath else { return }
        
        for rule in path.adaptiveRules where rule.isActive {
            if await evaluateRuleCondition(rule.condition, skillId: skillId, assessment: assessment) {
                await executeRuleAction(rule.action, skillId: skillId)
                
                // Record adaptation event
                let event = AdaptationEvent(
                    id: UUID(),
                    ruleId: rule.id,
                    skillId: skillId,
                    condition: rule.condition,
                    action: rule.action,
                    timestamp: Date()
                )
                adaptationHistory.append(event)
            }
        }
    }
    
    private func evaluateRuleCondition(
        _ condition: RuleCondition,
        skillId: UUID,
        assessment: SkillAssessment
    ) async -> Bool {
        switch condition {
        case .skillMastery(let targetSkillId, let threshold):
            return targetSkillId == skillId && assessment.masteryScore >= threshold
            
        case .consecutiveFailures(let count):
            // Check recent failure count from analytics
            return await getConsecutiveFailures(skillId: skillId) >= count
            
        case .timeSpent(let minutes):
            return await getTotalTimeSpent(skillId: skillId) >= TimeInterval(minutes * 60)
            
        case .streakLength(let days):
            return await getCurrentStreak() >= days
            
        case .difficultyStuck(let level, let duration):
            return assessment.currentLevel <= level && 
                   Date().timeIntervalSince(assessment.lastAssessed) >= duration
        }
    }
    
    private func executeRuleAction(_ action: RuleAction, skillId: UUID) async {
        switch action {
        case .skipToLesson(let lessonId):
            await recommendLesson(lessonId)
            
        case .repeatSkill(let targetSkillId):
            await recommendSkillPractice(targetSkillId)
            
        case .adjustDifficulty(let factor):
            await adjustDifficultyLevel(skillId: skillId, factor: factor)
            
        case .recommendBreak(let duration):
            await recommendBreak(duration: duration)
            
        case .suggestReview(let skillIds):
            await recommendReview(skillIds: skillIds)
        }
    }
    
    // MARK: - Recommendation Generation
    
    private func generateRecommendation(
        path: CurriculumPath,
        performance: [String: Any],
        skills: [UUID: SkillAssessment]
    ) async throws -> LearningRecommendation? {
        
        let recommendationRequest = """
        Generate next learning recommendation based on:
        
        Curriculum Path: \(path.name)
        Current Skills: \(skills.map { "\($0.key): Level \($0.value.currentLevel)" })
        Recent Performance: \(performance)
        
        Consider:
        1. Skill gaps and weaknesses
        2. Learning momentum
        3. Optimal challenge level
        4. Time since last practice
        
        Generate specific, actionable recommendation with reasoning.
        Return as JSON with recommendation details.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: recommendationRequest)
        
        return try parseRecommendationResponse(response)
    }
    
    // MARK: - Helper Methods
    
    private func setupAnalyticsObserver() {
        // Note: LearningAnalyticsService doesn't have published properties
        // This would be implemented when the analytics service is updated
    }
    
    private func processAnalyticsUpdate() async {
        // Note: InteractionEvent is not available in current scope
        // This would be implemented when analytics service provides interaction data
    }
    
    // Note: InteractionEvent is not available in current scope
    // These methods would be used when analytics service provides interaction data
    
    // MARK: - Data Parsing
    
    private func parseCurriculumResponse(
        _ response: String,
        language: Language,
        targetLevel: ProficiencyLevel
    ) throws -> CurriculumPath {
        // Parse AI response and create CurriculumPath
        // Implementation would parse JSON response from AI
        
        return CurriculumPath(
            id: UUID(),
            name: "Adaptive \(language.displayName) Path",
            description: "AI-generated adaptive curriculum",
            language: language,
            targetLevel: targetLevel,
            estimatedDuration: 30 * 24 * 60 * 60, // 30 days
            prerequisites: [],
            skills: generateDefaultSkills(),
            adaptiveRules: generateDefaultRules(),
            createdAt: Date(),
            updatedAt: Date()
        )
    }
    
    private func parseSkillAssessments(_ response: String) throws -> [UUID: SkillAssessment] {
        // Parse AI response and create skill assessments
        var assessments: [UUID: SkillAssessment] = [:]
        
        for _ in SkillCategory.allCases {
            let skillId = UUID()
            assessments[skillId] = SkillAssessment(
                skillId: skillId,
                currentLevel: Double.random(in: 1.0...3.0),
                masteryScore: Double.random(in: 0.3...0.8),
                confidence: Double.random(in: 0.7...0.95),
                lastAssessed: Date(),
                improvementRate: Double.random(in: 0.01...0.1),
                strugglingAreas: [],
                strengths: []
            )
        }
        
        return assessments
    }
    
    private func parseRecommendationResponse(_ response: String) throws -> LearningRecommendation? {
        // Parse AI response and create recommendation
        return LearningRecommendation(
            id: UUID(),
            type: .nextLesson,
            title: "Continue with Grammar Practice",
            description: "Focus on verb conjugations to strengthen your foundation",
            priority: .medium,
            targetSkills: [],
            estimatedTime: 15 * 60, // 15 minutes
            confidence: 0.85,
            reasoning: "Based on recent performance patterns",
            createdAt: Date()
        )
    }
    
    private func generateDefaultSkills() -> [CurriculumSkillArea] {
        return SkillCategory.allCases.map { category in
            CurriculumSkillArea(
                id: UUID(),
                name: category.rawValue.capitalized,
                description: "Core \(category.rawValue) skills",
                category: category,
                weight: 1.0,
                prerequisites: [],
                masteryThreshold: 0.8
            )
        }
    }
    
    private func generateDefaultRules() -> [AdaptiveRule] {
        return [
            AdaptiveRule(
                id: UUID(),
                condition: .skillMastery(skillId: UUID(), threshold: 0.8),
                action: .skipToLesson(lessonId: UUID()),
                priority: 1,
                isActive: true
            )
        ]
    }
    
    // MARK: - Analytics Helper Methods
    
    private func getRecentPerformance(userId: UUID) async throws -> [String: Any] {
        // Get recent performance data from analytics
        return [
            "average_score": 0.75,
            "session_count": 5,
            "total_time": 3600
        ]
    }
    
    private func getConsecutiveFailures(skillId: UUID) async -> Int {
        // Count consecutive failures for skill
        return 0
    }
    
    private func getTotalTimeSpent(skillId: UUID) async -> TimeInterval {
        // Get total time spent on skill
        return 0
    }
    
    private func getCurrentStreak() async -> Int {
        // Get current learning streak
        return 0
    }
    
    // MARK: - Action Methods
    
    private func recommendLesson(_ lessonId: UUID) async {
        // Implement lesson recommendation
    }
    
    private func recommendSkillPractice(_ skillId: UUID) async {
        // Implement skill practice recommendation
    }
    
    private func adjustDifficultyLevel(skillId: UUID, factor: Double) async {
        // Implement difficulty adjustment
    }
    
    private func recommendBreak(duration: TimeInterval) async {
        // Implement break recommendation
    }
    
    private func recommendReview(skillIds: [UUID]) async {
        // Implement review recommendation
    }
}

// MARK: - Supporting Types

struct AdaptationEvent: Codable, Identifiable {
    let id: UUID
    let ruleId: UUID
    let skillId: UUID
    let condition: RuleCondition
    let action: RuleAction
    let timestamp: Date
}

// MARK: - Errors

enum AdaptiveCurriculumError: LocalizedError {
    case noActivePath
    case invalidSkillData
    case assessmentFailed
    case recommendationGenerationFailed
    
    var errorDescription: String? {
        switch self {
        case .noActivePath:
            return "No active curriculum path found"
        case .invalidSkillData:
            return "Invalid skill assessment data"
        case .assessmentFailed:
            return "Failed to assess current skills"
        case .recommendationGenerationFailed:
            return "Failed to generate learning recommendation"
        }
    }
} 