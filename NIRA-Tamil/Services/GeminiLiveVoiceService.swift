import Foundation
import AVFoundation
import Combine
import Network

// MARK: - Gemini Live API Service - Proper Implementation

@MainActor
class GeminiLiveVoiceService: NSObject, ObservableObject {
    static let shared = GeminiLiveVoiceService()

    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var isRecording = false
    @Published var isPlaying = false
    @Published var liveTranscription = ""
    @Published var outputTranscription = ""
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var lastError: Error?
    @Published var usageMetadata: UsageMetadata?

    // MARK: - Private Properties
    private var webSocketTask: URLSessionWebSocketTask?
    private var audioEngine: AVAudioEngine?
    private var audioPlayerNode: AVAudioPlayerNode?
    private var inputFormat: AVAudioFormat?
    private var outputFormat: AVAudioFormat?
    private var currentAgent: LanguageTutor?
    private var conversationId: UUID?
    private var sessionHandle: String?

    // MARK: - Configuration
    private let inputSampleRate: Double = 16000.0
    private let outputSampleRate: Double = 24000.0
    private let channels: UInt32 = 1
    private let bitDepth: UInt32 = 16

    enum ConnectionStatus {
        case disconnected, connecting, connected, error, interrupted
    }

    enum ResponseModality: String {
        case text = "TEXT"
        case audio = "AUDIO"
    }

    enum VoiceError: LocalizedError {
        case connectionFailed
        case audioSetupFailed
        case permissionDenied
        case invalidResponse
        case networkError
        case sessionExpired
        case configurationError

        var errorDescription: String? {
            switch self {
            case .connectionFailed:
                return "Failed to connect to Gemini Live service"
            case .audioSetupFailed:
                return "Failed to setup audio recording"
            case .permissionDenied:
                return "Microphone permission denied"
            case .invalidResponse:
                return "Invalid response from AI service"
            case .networkError:
                return "Network connection error"
            case .sessionExpired:
                return "Session expired, please reconnect"
            case .configurationError:
                return "Invalid configuration"
            }
        }
    }

    // MARK: - Initialization
    override init() {
        super.init()
        setupAudioSession()
    }

    // MARK: - Public Methods

    func startLiveConversation(
        with agent: LanguageTutor,
        conversationId: UUID,
        responseModality: ResponseModality = .audio,
        enableTranscription: Bool = true
    ) async throws {
        print("🎤 Starting live conversation with \(agent.name)")

        self.currentAgent = agent
        self.conversationId = conversationId

        // Request permissions
        try await requestPermissions()

        // Setup audio
        try setupAudioEngine()

        // Connect to Gemini Live
        try await connectToGeminiLive(
            agent: agent,
            responseModality: responseModality,
            enableTranscription: enableTranscription
        )

        // Start recording if audio modality
        if responseModality == .audio {
            try startAudioRecording()
        }

        print("✅ Live conversation started successfully")
    }

    func stopLiveConversation() async {
        print("🛑 Stopping live conversation")

        stopAudioRecording()
        await disconnectFromGeminiLive()

        isRecording = false
        isPlaying = false
        connectionStatus = .disconnected

        print("✅ Live conversation stopped")
    }

    func sendTextMessage(_ message: String) async throws {
        guard isConnected else {
            throw VoiceError.connectionFailed
        }

        let content = LiveAPIContent(
            role: "user",
            parts: [LiveAPIPart(text: message)]
        )

        let clientContent = LiveAPIClientContent(
            turns: [content],
            turnComplete: true
        )

        try await sendMessage(clientContent)
    }

    func sendToolResponse(_ responses: [LiveAPIFunctionResponse]) async throws {
        guard isConnected else {
            throw VoiceError.connectionFailed
        }

        let toolResponse = LiveAPIToolResponse(functionResponses: responses)
        try await sendMessage(toolResponse)
    }

    // MARK: - Private Methods

    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.duckOthers, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("❌ Failed to setup audio session: \(error)")
        }
    }

    private func requestPermissions() async throws {
        let micPermission = await withCheckedContinuation { continuation in
            AVAudioApplication.requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
        guard micPermission else {
            throw VoiceError.permissionDenied
        }
    }

    private func setupAudioEngine() throws {
        audioEngine = AVAudioEngine()
        audioPlayerNode = AVAudioPlayerNode()

        guard let audioEngine = audioEngine,
              let audioPlayerNode = audioPlayerNode else {
            throw VoiceError.audioSetupFailed
        }

        // Setup input format (16kHz for Live API)
        inputFormat = AVAudioFormat(
            standardFormatWithSampleRate: inputSampleRate,
            channels: channels
        )

        // Setup output format (24kHz from Live API)
        outputFormat = AVAudioFormat(
            standardFormatWithSampleRate: outputSampleRate,
            channels: channels
        )

        guard let _ = inputFormat,
              let outputFormat = outputFormat else {
            throw VoiceError.audioSetupFailed
        }

        // Attach and connect nodes
        audioEngine.attach(audioPlayerNode)
        audioEngine.connect(audioPlayerNode, to: audioEngine.mainMixerNode, format: outputFormat)

        audioEngine.prepare()
    }

    private func connectToGeminiLive(
        agent: LanguageTutor,
        responseModality: ResponseModality,
        enableTranscription: Bool
    ) async throws {
        connectionStatus = .connecting

        // Get API key from secure storage with fallback
        let secureKey = SecureAPIKeys.geminiAPIKey
        let apiKey: String
        if !secureKey.isEmpty && !secureKey.contains("PLACEHOLDER") {
            apiKey = secureKey
        } else {
            print("⚠️ No Gemini API key configured - Live voice features will be disabled")
            throw VoiceError.configurationError
        }

        // Construct proper Live API WebSocket URL - Updated to correct endpoint format
        guard let url = URL(string: "wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1beta.GenerativeService/BidiGenerateContent?key=\(apiKey)") else {
            throw VoiceError.connectionFailed
        }

        var request = URLRequest(url: url)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("websocket", forHTTPHeaderField: "Upgrade")
        request.setValue("Upgrade", forHTTPHeaderField: "Connection")
        request.setValue("13", forHTTPHeaderField: "Sec-WebSocket-Version")

        let session = URLSession(configuration: .default)
        webSocketTask = session.webSocketTask(with: request)

        webSocketTask?.resume()

        // Send setup message according to Live API spec
        try await sendSetupMessage(
            agent: agent,
            responseModality: responseModality,
            enableTranscription: enableTranscription
        )

        // Start listening for messages
        startListening()

        connectionStatus = .connected
        isConnected = true
    }

    private func disconnectFromGeminiLive() async {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        isConnected = false
        connectionStatus = .disconnected
    }

    private func sendSetupMessage(
        agent: LanguageTutor,
        responseModality: ResponseModality,
        enableTranscription: Bool
    ) async throws {

        // Build system instruction
        let systemInstruction = LiveAPIContent(
            parts: [LiveAPIPart(text: """
            You are \(agent.name), a friendly language tutor specializing in \(agent.language.displayName).

            Your teaching approach:
            - Be encouraging and patient with language learners
            - Provide gentle corrections with clear explanations
            - Ask follow-up questions to maintain engaging dialogue
            - Include cultural context when relevant
            - Adapt your speaking pace and complexity to the learner's level
            - Use the target language primarily, with English explanations when helpful
            """)]
        )

        // Configure speech settings
        var speechConfig: LiveAPISpeechConfig?
        if responseModality == .audio {
            speechConfig = LiveAPISpeechConfig(
                voiceConfig: LiveAPIVoiceConfig(
                    prebuiltVoiceConfig: LiveAPIPrebuiltVoiceConfig(
                        voiceName: getVoiceName(for: agent.language)
                    )
                ),
                languageCode: getLanguageCode(for: agent.language)
            )
        }

        // Configure real-time input
        let realtimeInputConfig = LiveAPIRealtimeInputConfig(
            automaticActivityDetection: LiveAPIAutomaticActivityDetection(
                disabled: false,
                startOfSpeechSensitivity: .startSensitivityMedium,
                endOfSpeechSensitivity: .endSensitivityMedium,
                prefixPaddingMs: 300,
                silenceDurationMs: 500
            ),
            activityHandling: .noInterruption
        )

        // Configure transcription
        var inputAudioTranscription: LiveAPIInputAudioTranscription?
        var outputAudioTranscription: LiveAPIOutputAudioTranscription?

        if enableTranscription {
            inputAudioTranscription = LiveAPIInputAudioTranscription()
            outputAudioTranscription = LiveAPIOutputAudioTranscription()
        }

        // Build setup configuration
        let setupConfig = LiveAPISetupConfig(
            model: "gemini-2.0-flash-live-001",
            systemInstruction: systemInstruction,
            generationConfig: LiveAPIGenerationConfig(
                temperature: 0.7,
                maxOutputTokens: 1000,
                candidateCount: 1
            ),
            responseModalities: [responseModality.rawValue],
            speechConfig: speechConfig,
            realtimeInputConfig: realtimeInputConfig,
            inputAudioTranscription: inputAudioTranscription,
            outputAudioTranscription: outputAudioTranscription,
            tools: buildTools()
        )

        let setupMessage = LiveAPISetupMessage(setup: setupConfig)
        try await sendMessage(setupMessage)
    }

    private func buildTools() -> [LiveAPITool] {
        // Add basic tools for language learning
        let tools: [LiveAPITool] = [
            // Google Search for real-time information
            LiveAPITool(googleSearch: LiveAPIGoogleSearch()),

            // Function calling for language learning features
            LiveAPITool(functionDeclarations: [
                LiveAPIFunctionDeclaration(
                    name: "explain_grammar",
                    description: "Explain grammar rules and provide examples",
                    parameters: LiveAPIFunctionParameters(
                        type: "object",
                        properties: [
                            "concept": LiveAPIProperty(type: "string", description: "Grammar concept to explain"),
                            "language": LiveAPIProperty(type: "string", description: "Target language")
                        ],
                        required: ["concept", "language"]
                    )
                ),
                LiveAPIFunctionDeclaration(
                    name: "translate_phrase",
                    description: "Translate phrases between languages",
                    parameters: LiveAPIFunctionParameters(
                        type: "object",
                        properties: [
                            "text": LiveAPIProperty(type: "string", description: "Text to translate"),
                            "fromLanguage": LiveAPIProperty(type: "string", description: "Source language"),
                            "toLanguage": LiveAPIProperty(type: "string", description: "Target language")
                        ],
                        required: ["text", "fromLanguage", "toLanguage"]
                    )
                )
            ])
        ]

        return tools
    }

    private func startListening() {
        guard let webSocketTask = webSocketTask else { return }

        Task {
            do {
                let message = try await webSocketTask.receive()
                await handleReceivedMessage(message)

                // Continue listening if still connected
                if isConnected {
                    startListening()
                }
            } catch {
                await MainActor.run {
                    self.lastError = error
                    self.connectionStatus = .error
                    print("❌ WebSocket error: \(error)")
                }
            }
        }
    }

    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            await handleDataMessage(data)
        case .string(let string):
            await handleStringMessage(string)
        @unknown default:
            break
        }
    }

    private func handleDataMessage(_ data: Data) async {
        // Handle binary audio data if needed
        print("📦 Received binary data: \(data.count) bytes")
    }

    private func handleStringMessage(_ string: String) async {
        guard let data = string.data(using: .utf8) else { return }

        // Try to parse as different message types
        if let serverContent = try? JSONDecoder().decode(LiveAPIServerContent.self, from: data) {
            await processServerContent(serverContent)
        } else if let toolCall = try? JSONDecoder().decode(LiveAPIToolCall.self, from: data) {
            await processToolCall(toolCall)
        } else if let goAway = try? JSONDecoder().decode(LiveAPIGoAway.self, from: data) {
            await processGoAway(goAway)
        } else {
            print("📝 Received unknown message: \(string)")
        }
    }

    private func processServerContent(_ content: LiveAPIServerContent) async {
        // Handle model turn content
        if let modelTurn = content.modelTurn {
            for part in modelTurn.parts {
                if let text = part.text {
                    // Handle text response
                    await saveMessageToHistory(content: text, isFromUser: false)
                }

                if let inlineData = part.inlineData {
                    // Handle audio response
                    await playAudioData(inlineData.data, mimeType: inlineData.mimeType)
                }
            }
        }

        // Handle transcriptions
        if let inputTranscription = content.inputTranscription {
            liveTranscription = inputTranscription.text
        }

        if let outputTranscriptionData = content.outputTranscription {
            outputTranscription = outputTranscriptionData.text
        }

        // Handle interruptions
        if content.interrupted == true {
            connectionStatus = .interrupted
            print("🚫 Generation interrupted")
        }

        // Handle usage metadata
        if let usage = content.usageMetadata {
            usageMetadata = usage
        }
    }

    private func processToolCall(_ toolCall: LiveAPIToolCall) async {
        // Handle function calls
        for functionCall in toolCall.functionCalls {
            let response = await handleFunctionCall(functionCall)

            do {
                try await sendToolResponse([response])
            } catch {
                print("❌ Error sending tool response: \(error)")
            }
        }
    }

    private func processGoAway(_ goAway: LiveAPIGoAway) async {
        print("⚠️ Session ending in \(goAway.timeLeft) seconds")

        // Attempt to resume session if handle is available
        if let handle = sessionHandle {
            // Store handle for session resumption
            print("💾 Storing session handle for resumption: \(handle)")
        }
    }

    private func handleFunctionCall(_ functionCall: LiveAPIFunctionCall) async -> LiveAPIFunctionResponse {
        switch functionCall.name {
        case "explain_grammar":
            let explanation = await explainGrammar(functionCall.args)
            return LiveAPIFunctionResponse(
                id: functionCall.id,
                name: functionCall.name,
                response: ["explanation": explanation]
            )

        case "translate_phrase":
            let translation = await translatePhrase(functionCall.args)
            return LiveAPIFunctionResponse(
                id: functionCall.id,
                name: functionCall.name,
                response: ["translation": translation]
            )

        default:
            return LiveAPIFunctionResponse(
                id: functionCall.id,
                name: functionCall.name,
                response: ["error": "Unknown function"]
            )
        }
    }

    private func startAudioRecording() throws {
        guard let audioEngine = audioEngine,
              let inputFormat = inputFormat else {
            throw VoiceError.audioSetupFailed
        }

        let inputNode = audioEngine.inputNode

        // Install tap for real-time audio processing
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: inputFormat) { [weak self] buffer, _ in
            Task {
                await self?.sendAudioBuffer(buffer)
            }
        }

        try audioEngine.start()
        isRecording = true
        print("🎙️ Audio recording started")
    }

    private func stopAudioRecording() {
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        isRecording = false
        print("🛑 Audio recording stopped")
    }

    private func sendAudioBuffer(_ buffer: AVAudioPCMBuffer) async {
        guard isConnected,
              let audioData = buffer.audioBufferToData() else { return }

        let blob = LiveAPIBlob(
            data: audioData.base64EncodedString(),
            mimeType: "audio/pcm;rate=16000"
        )

        let realtimeInput = LiveAPIRealtimeInput(audio: blob)

        do {
            try await sendMessage(realtimeInput)
        } catch {
            print("❌ Error sending audio: \(error)")
        }
    }

    private func playAudioData(_ base64Data: String, mimeType: String) async {
        guard let audioData = Data(base64Encoded: base64Data),
              let audioPlayerNode = audioPlayerNode,
              let outputFormat = outputFormat else { return }

        // Convert audio data to PCM buffer
        if let buffer = createAudioBuffer(from: audioData, format: outputFormat) {
            await MainActor.run {
                isPlaying = true
            }

            audioPlayerNode.scheduleBuffer(buffer, completionCallbackType: .dataPlayedBack) { [weak self] _ in
                Task { @MainActor in
                    self?.isPlaying = false
                }
            }

            if !audioPlayerNode.isPlaying {
                audioPlayerNode.play()
            }
        }
    }

    private func createAudioBuffer(from data: Data, format: AVAudioFormat) -> AVAudioPCMBuffer? {
        let frameCount = UInt32(data.count) / format.streamDescription.pointee.mBytesPerFrame
        guard let buffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount) else {
            return nil
        }

        buffer.frameLength = frameCount

        data.withUnsafeBytes { bytes in
            guard let baseAddress = bytes.baseAddress else { return }
            let int16Pointer = baseAddress.assumingMemoryBound(to: Int16.self)

            if let channelData = buffer.int16ChannelData {
                channelData[0].update(from: int16Pointer, count: Int(frameCount))
            }
        }

        return buffer
    }

    private func sendMessage<T: Codable>(_ message: T) async throws {
        guard let webSocketTask = webSocketTask else {
            throw VoiceError.connectionFailed
        }

        let data = try JSONEncoder().encode(message)
        let wsMessage = URLSessionWebSocketTask.Message.data(data)
        try await webSocketTask.send(wsMessage)
    }

    private func saveMessageToHistory(content: String, isFromUser: Bool) async {
        guard let conversationId = conversationId else { return }

        do {
            _ = try await EnhancedAIService().saveMessage(
                conversationId: conversationId,
                content: content,
                isFromUser: isFromUser,
                attachments: [],
                voiceData: SupabaseVoiceData(
                    audioPath: "",
                    duration: 0.0,
                    transcription: content,
                    language: currentAgent?.language.rawValue ?? "en",
                    confidence: nil,
                    isProcessed: true
                ),
                aiMetadata: nil,
                responseTime: nil,
                confidence: nil,
                grammarCorrections: [],
                culturalNotes: [],
                vocabularyHighlights: []
            )
        } catch {
            print("❌ Error saving message: \(error)")
        }
    }

    // MARK: - Helper Methods

    private func getVoiceName(for language: Language) -> String {
        // Map to Live API voice names
        switch language {
        case .spanish: return "Aoede"
        case .french: return "Charon"
        case .japanese: return "Orus"
        case .tamil: return "Aoede" // Use default voice for Tamil
        case .english: return "Aoede" // Default voice
        case .korean: return "Aoede" // Use default voice for Korean
        case .italian: return "Aoede" // Use default voice for Italian
        case .german: return "Charon" // Use Charon voice for German
        case .hindi: return "Aoede" // Use default voice for Hindi
        case .chinese: return "Orus" // Use Orus voice for Chinese
        case .portuguese: return "Aoede" // Use default voice for Portuguese
        case .telugu: return "Aoede" // Use default voice for Telugu
        case .vietnamese: return "Aoede" // Use default voice for Vietnamese
        case .indonesian: return "Aoede" // Use default voice for Indonesian
        case .arabic: return "Charon" // Use Charon voice for Arabic
        // New 10 languages
        case .kannada: return "Aoede" // Use default voice for Kannada
        case .malayalam: return "Aoede" // Use default voice for Malayalam
        case .bengali: return "Aoede" // Use default voice for Bengali
        case .marathi: return "Aoede" // Use default voice for Marathi
        case .punjabi: return "Aoede" // Use default voice for Punjabi
        case .dutch: return "Charon" // Use Charon voice for Dutch
        case .swedish: return "Charon" // Use Charon voice for Swedish
        case .thai: return "Aoede" // Use default voice for Thai
        case .russian: return "Charon" // Use Charon voice for Russian
        case .norwegian: return "Charon" // Use Charon voice for Norwegian
        // Additional 25 languages
        case .gujarati: return "Aoede" // Use default voice for Gujarati
        case .odia: return "Aoede" // Use default voice for Odia
        case .assamese: return "Aoede" // Use default voice for Assamese
        case .konkani: return "Aoede" // Use default voice for Konkani
        case .sindhi: return "Aoede" // Use default voice for Sindhi
        case .bhojpuri: return "Aoede" // Use default voice for Bhojpuri
        case .maithili: return "Aoede" // Use default voice for Maithili
        case .swahili: return "Aoede" // Use default voice for Swahili
        case .hebrew: return "Charon" // Use Charon voice for Hebrew
        case .greek: return "Charon" // Use Charon voice for Greek
        case .turkish: return "Charon" // Use Charon voice for Turkish
        case .farsi: return "Charon" // Use Charon voice for Farsi
        case .tagalog: return "Aoede" // Use default voice for Tagalog
        case .ukrainian: return "Charon" // Use Charon voice for Ukrainian
        case .danish: return "Charon" // Use Charon voice for Danish
        case .xhosa: return "Aoede" // Use default voice for Xhosa
        case .zulu: return "Aoede" // Use default voice for Zulu
        case .amharic: return "Aoede" // Use default voice for Amharic
        case .quechua: return "Aoede" // Use default voice for Quechua
        case .maori: return "Aoede" // Use default voice for Māori
        case .cherokee: return "Aoede" // Use default voice for Cherokee
        case .navajo: return "Aoede" // Use default voice for Navajo
        case .hawaiian: return "Aoede" // Use default voice for Hawaiian
        case .inuktitut: return "Aoede" // Use default voice for Inuktitut
        case .yoruba: return "Aoede" // Use default voice for Yoruba
        // Additional languages to complete the 50-language expansion
        case .urdu: return "Aoede"
        case .polish: return "Charon"
        case .czech: return "Charon"
        case .hungarian: return "Charon"
        case .romanian: return "Charon"
        case .bulgarian: return "Charon"
        case .croatian: return "Charon"
        case .serbian: return "Charon"
        case .slovak: return "Charon"
        case .slovenian: return "Charon"
        case .estonian: return "Charon"
        case .latvian: return "Charon"
        case .lithuanian: return "Charon"
        case .maltese: return "Aoede"
        case .irish: return "Charon"
        case .welsh: return "Charon"
        case .scots: return "Charon"
        case .manx: return "Charon"
        case .cornish: return "Charon"
        case .breton: return "Charon"
        case .basque: return "Charon"
        case .catalan: return "Charon"
        case .galician: return "Charon"
        }
    }

    private func getLanguageCode(for language: Language) -> String {
        switch language {
        case .spanish: return "es-ES"
        case .french: return "fr-FR"
        case .japanese: return "ja-JP"
        case .tamil: return "ta-IN"
        case .english: return "en-US"
        case .korean: return "ko-KR"
        case .italian: return "it-IT"
        case .german: return "de-DE"
        case .hindi: return "hi-IN"
        case .chinese: return "zh-CN"
        case .portuguese: return "pt-BR"
        case .telugu: return "te-IN"
        case .vietnamese: return "vi-VN"
        case .indonesian: return "id-ID"
        case .arabic: return "ar-SA"
        // New 10 languages
        case .kannada: return "kn-IN"
        case .malayalam: return "ml-IN"
        case .bengali: return "bn-BD"
        case .marathi: return "mr-IN"
        case .punjabi: return "pa-IN"
        case .dutch: return "nl-NL"
        case .swedish: return "sv-SE"
        case .thai: return "th-TH"
        case .russian: return "ru-RU"
        case .norwegian: return "nb-NO"
        // Additional 25 languages
        case .gujarati: return "gu-IN"
        case .odia: return "or-IN"
        case .assamese: return "as-IN"
        case .konkani: return "kok-IN"
        case .sindhi: return "sd-IN"
        case .bhojpuri: return "bho-IN"
        case .maithili: return "mai-IN"
        case .swahili: return "sw-TZ"
        case .hebrew: return "he-IL"
        case .greek: return "el-GR"
        case .turkish: return "tr-TR"
        case .farsi: return "fa-IR"
        case .tagalog: return "tl-PH"
        case .ukrainian: return "uk-UA"
        case .danish: return "da-DK"
        case .xhosa: return "xh-ZA"
        case .zulu: return "zu-ZA"
        case .amharic: return "am-ET"
        case .quechua: return "qu-PE"
        case .maori: return "mi-NZ"
        case .cherokee: return "chr-US"
        case .navajo: return "nv-US"
        case .hawaiian: return "haw-US"
        case .inuktitut: return "iu-CA"
        case .yoruba: return "yo-NG"
        // Additional languages to complete the 50-language expansion
        case .urdu: return "ur-PK"
        case .polish: return "pl-PL"
        case .czech: return "cs-CZ"
        case .hungarian: return "hu-HU"
        case .romanian: return "ro-RO"
        case .bulgarian: return "bg-BG"
        case .croatian: return "hr-HR"
        case .serbian: return "sr-RS"
        case .slovak: return "sk-SK"
        case .slovenian: return "sl-SI"
        case .estonian: return "et-EE"
        case .latvian: return "lv-LV"
        case .lithuanian: return "lt-LT"
        case .maltese: return "mt-MT"
        case .irish: return "ga-IE"
        case .welsh: return "cy-GB"
        case .scots: return "gd-GB"
        case .manx: return "gv-IM"
        case .cornish: return "kw-GB"
        case .breton: return "br-FR"
        case .basque: return "eu-ES"
        case .catalan: return "ca-ES"
        case .galician: return "gl-ES"
        }
    }

    private func explainGrammar(_ args: [String: Any]) async -> String {
        // Implement grammar explanation logic
        return "Grammar explanation for the requested concept."
    }

    private func translatePhrase(_ args: [String: Any]) async -> String {
        // Implement translation logic
        return "Translation of the requested phrase."
    }
}

// MARK: - Live API Message Models (According to Documentation)

// Setup Configuration
struct LiveAPISetupMessage: Codable {
    let setup: LiveAPISetupConfig
}

struct LiveAPISetupConfig: Codable {
    let model: String
    let systemInstruction: LiveAPIContent?
    let generationConfig: LiveAPIGenerationConfig?
    let responseModalities: [String]
    let speechConfig: LiveAPISpeechConfig?
    let realtimeInputConfig: LiveAPIRealtimeInputConfig?
    let inputAudioTranscription: LiveAPIInputAudioTranscription?
    let outputAudioTranscription: LiveAPIOutputAudioTranscription?
    let tools: [LiveAPITool]?

    enum CodingKeys: String, CodingKey {
        case model
        case systemInstruction = "system_instruction"
        case generationConfig = "generation_config"
        case responseModalities = "response_modalities"
        case speechConfig = "speech_config"
        case realtimeInputConfig = "realtime_input_config"
        case inputAudioTranscription = "input_audio_transcription"
        case outputAudioTranscription = "output_audio_transcription"
        case tools
    }
}

// Content Models
struct LiveAPIContent: Codable {
    let role: String?
    let parts: [LiveAPIPart]

    init(role: String? = nil, parts: [LiveAPIPart]) {
        self.role = role
        self.parts = parts
    }
}

struct LiveAPIPart: Codable {
    let text: String?
    let inlineData: LiveAPIInlineData?

    enum CodingKeys: String, CodingKey {
        case text
        case inlineData = "inline_data"
    }

    init(text: String) {
        self.text = text
        self.inlineData = nil
    }

    init(inlineData: LiveAPIInlineData) {
        self.text = nil
        self.inlineData = inlineData
    }
}

struct LiveAPIInlineData: Codable {
    let mimeType: String
    let data: String

    enum CodingKeys: String, CodingKey {
        case mimeType = "mime_type"
        case data
    }
}

// Generation Configuration
struct LiveAPIGenerationConfig: Codable {
    let temperature: Double?
    let maxOutputTokens: Int?
    let candidateCount: Int?

    enum CodingKeys: String, CodingKey {
        case temperature
        case maxOutputTokens = "max_output_tokens"
        case candidateCount = "candidate_count"
    }
}

// Speech Configuration
struct LiveAPISpeechConfig: Codable {
    let voiceConfig: LiveAPIVoiceConfig?
    let languageCode: String?

    enum CodingKeys: String, CodingKey {
        case voiceConfig = "voice_config"
        case languageCode = "language_code"
    }
}

struct LiveAPIVoiceConfig: Codable {
    let prebuiltVoiceConfig: LiveAPIPrebuiltVoiceConfig?

    enum CodingKeys: String, CodingKey {
        case prebuiltVoiceConfig = "prebuilt_voice_config"
    }
}

struct LiveAPIPrebuiltVoiceConfig: Codable {
    let voiceName: String

    enum CodingKeys: String, CodingKey {
        case voiceName = "voice_name"
    }
}

// Real-time Input Configuration
struct LiveAPIRealtimeInputConfig: Codable {
    let automaticActivityDetection: LiveAPIAutomaticActivityDetection?
    let activityHandling: ActivityHandling?

    enum CodingKeys: String, CodingKey {
        case automaticActivityDetection = "automatic_activity_detection"
        case activityHandling = "activity_handling"
    }
}

struct LiveAPIAutomaticActivityDetection: Codable {
    let disabled: Bool?
    let startOfSpeechSensitivity: StartSensitivity?
    let endOfSpeechSensitivity: EndSensitivity?
    let prefixPaddingMs: Int?
    let silenceDurationMs: Int?

    enum CodingKeys: String, CodingKey {
        case disabled
        case startOfSpeechSensitivity = "start_of_speech_sensitivity"
        case endOfSpeechSensitivity = "end_of_speech_sensitivity"
        case prefixPaddingMs = "prefix_padding_ms"
        case silenceDurationMs = "silence_duration_ms"
    }
}

enum StartSensitivity: String, Codable {
    case startSensitivityLow = "START_SENSITIVITY_LOW"
    case startSensitivityMedium = "START_SENSITIVITY_MEDIUM"
    case startSensitivityHigh = "START_SENSITIVITY_HIGH"
}

enum EndSensitivity: String, Codable {
    case endSensitivityLow = "END_SENSITIVITY_LOW"
    case endSensitivityMedium = "END_SENSITIVITY_MEDIUM"
    case endSensitivityHigh = "END_SENSITIVITY_HIGH"
}

enum ActivityHandling: String, Codable {
    case noInterruption = "NO_INTERRUPTION"
    case allowInterruption = "ALLOW_INTERRUPTION"
}

// Transcription Configuration
struct LiveAPIInputAudioTranscription: Codable {
    // Empty struct as per documentation
}

struct LiveAPIOutputAudioTranscription: Codable {
    // Empty struct as per documentation
}

// Tools Configuration
struct LiveAPITool: Codable {
    let functionDeclarations: [LiveAPIFunctionDeclaration]?
    let codeExecution: LiveAPICodeExecution?
    let googleSearch: LiveAPIGoogleSearch?

    enum CodingKeys: String, CodingKey {
        case functionDeclarations = "function_declarations"
        case codeExecution = "code_execution"
        case googleSearch = "google_search"
    }

    init(functionDeclarations: [LiveAPIFunctionDeclaration]) {
        self.functionDeclarations = functionDeclarations
        self.codeExecution = nil
        self.googleSearch = nil
    }

    init(codeExecution: LiveAPICodeExecution) {
        self.functionDeclarations = nil
        self.codeExecution = codeExecution
        self.googleSearch = nil
    }

    init(googleSearch: LiveAPIGoogleSearch) {
        self.functionDeclarations = nil
        self.codeExecution = nil
        self.googleSearch = googleSearch
    }
}

struct LiveAPIFunctionDeclaration: Codable {
    let name: String
    let description: String
    let parameters: LiveAPIFunctionParameters
}

struct LiveAPIFunctionParameters: Codable {
    let type: String
    let properties: [String: LiveAPIProperty]
    let required: [String]
}

struct LiveAPIProperty: Codable {
    let type: String
    let description: String
}

struct LiveAPICodeExecution: Codable {
    // Empty struct as per documentation
}

struct LiveAPIGoogleSearch: Codable {
    // Empty struct as per documentation
}

// Client Messages
struct LiveAPIClientContent: Codable {
    let turns: [LiveAPIContent]
    let turnComplete: Bool

    enum CodingKeys: String, CodingKey {
        case turns
        case turnComplete = "turn_complete"
    }
}

struct LiveAPIRealtimeInput: Codable {
    let audio: LiveAPIBlob?
    let activityStart: LiveAPIActivityStart?
    let activityEnd: LiveAPIActivityEnd?
    let audioStreamEnd: Bool?

    enum CodingKeys: String, CodingKey {
        case audio
        case activityStart = "activity_start"
        case activityEnd = "activity_end"
        case audioStreamEnd = "audio_stream_end"
    }

    init(audio: LiveAPIBlob) {
        self.audio = audio
        self.activityStart = nil
        self.activityEnd = nil
        self.audioStreamEnd = nil
    }

    init(activityStart: LiveAPIActivityStart) {
        self.audio = nil
        self.activityStart = activityStart
        self.activityEnd = nil
        self.audioStreamEnd = nil
    }

    init(activityEnd: LiveAPIActivityEnd) {
        self.audio = nil
        self.activityStart = nil
        self.activityEnd = activityEnd
        self.audioStreamEnd = nil
    }
}

struct LiveAPIBlob: Codable {
    let data: String
    let mimeType: String

    enum CodingKeys: String, CodingKey {
        case data
        case mimeType = "mime_type"
    }
}

struct LiveAPIActivityStart: Codable {
    // Empty struct as per documentation
}

struct LiveAPIActivityEnd: Codable {
    // Empty struct as per documentation
}

// Server Messages
struct LiveAPIServerContent: Codable {
    let modelTurn: LiveAPIModelTurn?
    let inputTranscription: LiveAPITranscription?
    let outputTranscription: LiveAPITranscription?
    let interrupted: Bool?
    let usageMetadata: UsageMetadata?
    let generationComplete: Bool?

    enum CodingKeys: String, CodingKey {
        case modelTurn = "model_turn"
        case inputTranscription = "input_transcription"
        case outputTranscription = "output_transcription"
        case interrupted
        case usageMetadata = "usage_metadata"
        case generationComplete = "generation_complete"
    }
}

struct LiveAPIModelTurn: Codable {
    let parts: [LiveAPIPart]
}

struct LiveAPITranscription: Codable {
    let text: String
}

struct UsageMetadata: Codable {
    let totalTokenCount: Int
    let responseTokensDetails: [ModalityTokenCount]

    enum CodingKeys: String, CodingKey {
        case totalTokenCount = "total_token_count"
        case responseTokensDetails = "response_tokens_details"
    }
}

struct ModalityTokenCount: Codable {
    let modality: String
    let tokenCount: Int

    enum CodingKeys: String, CodingKey {
        case modality
        case tokenCount = "token_count"
    }
}

// Tool Messages
struct LiveAPIToolCall: Codable {
    let functionCalls: [LiveAPIFunctionCall]

    enum CodingKeys: String, CodingKey {
        case functionCalls = "function_calls"
    }
}

struct LiveAPIFunctionCall: Codable {
    let id: String
    let name: String
    let args: [String: Any]

    enum CodingKeys: String, CodingKey {
        case id, name, args
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)

        // Handle args as flexible dictionary
        if let argsData = try? container.decode([String: SupabaseAnyCodable].self, forKey: .args) {
            args = argsData.mapValues { $0.value }
        } else {
            args = [:]
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)

        let argsData = args.mapValues { SupabaseAnyCodable($0) }
        try container.encode(argsData, forKey: .args)
    }
}

struct LiveAPIToolResponse: Codable {
    let functionResponses: [LiveAPIFunctionResponse]

    enum CodingKeys: String, CodingKey {
        case functionResponses = "function_responses"
    }
}

struct LiveAPIFunctionResponse: Codable {
    let id: String
    let name: String
    let response: [String: Any]

    enum CodingKeys: String, CodingKey {
        case id, name, response
    }

    init(id: String, name: String, response: [String: Any]) {
        self.id = id
        self.name = name
        self.response = response
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)

        if let responseData = try? container.decode([String: SupabaseAnyCodable].self, forKey: .response) {
            response = responseData.mapValues { $0.value }
        } else {
            response = [:]
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)

        let responseData = response.mapValues { SupabaseAnyCodable($0) }
        try container.encode(responseData, forKey: .response)
    }
}

// Control Messages
struct LiveAPIGoAway: Codable {
    let timeLeft: Int

    enum CodingKeys: String, CodingKey {
        case timeLeft = "time_left"
    }
}



// MARK: - Audio Buffer Extension

extension AVAudioPCMBuffer {
    func audioBufferToData() -> Data? {
        guard let channelData = int16ChannelData else { return nil }

        let channelDataValue = channelData.pointee
        let frameCount = Int(frameLength)
        let channelDataValueArray = (0..<frameCount).map { channelDataValue[$0] }

        return Data(bytes: channelDataValueArray, count: channelDataValueArray.count * MemoryLayout<Int16>.size)
    }
}