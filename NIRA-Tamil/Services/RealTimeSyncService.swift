//
//  RealTimeSyncService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import UIKit
import Supabase
import Combine

/// Service for real-time synchronization across devices using Supabase real-time subscriptions
@MainActor
class RealTimeSyncService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = RealTimeSyncService()
    
    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var syncStatus: RealTimeSyncStatus = .disconnected
    @Published var lastSyncTime: Date?
    @Published var deviceCount = 1
    @Published var conflictResolutions: [SyncConflict] = []
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    private let userId: String
    private let deviceId: String
    private var progressSubscription: RealtimeChannelV2?
    private var lessonSubscription: RealtimeChannelV2?
    
    // MARK: - Initialization
    private init() {
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co")!,
            supabaseKey: APIKeys.supabaseAnonKey
        )
        
        // Generate unique device and user IDs
        self.deviceId = UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
        self.userId = "default-user-\(UUID().uuidString)"
        
        print("🔄 RealTimeSyncService initialized for device: \(deviceId)")
        
        setupRealtimeSubscriptions()
        startConnectionMonitoring()
    }
    
    // MARK: - Real-time Subscriptions
    
    /// Setup real-time subscriptions for user progress and lesson data
    private func setupRealtimeSubscriptions() {
        setupProgressSubscription()
        setupLessonSubscription()
    }
    
    /// Setup subscription for user progress changes
    private func setupProgressSubscription() {
        progressSubscription = supabase.channel("user_progress_changes")

        // Simplified subscription for now - real-time API syntax varies by version
        Task {
            await progressSubscription?.subscribe()
            await MainActor.run {
                self.handleSubscriptionStatus(.subscribed, for: "progress")
            }
        }
    }
    
    /// Setup subscription for lesson progress changes
    private func setupLessonSubscription() {
        lessonSubscription = supabase.channel("lesson_progress_changes")

        // Simplified subscription for now - real-time API syntax varies by version
        Task {
            await lessonSubscription?.subscribe()
            await MainActor.run {
                self.handleSubscriptionStatus(.subscribed, for: "lesson")
            }
        }
    }
    
    // MARK: - Event Handlers
    
    /// Handle user progress changes from other devices
    private func handleProgressChange(_ payload: RealtimeMessage) async {
        print("📊 Received progress change: \(payload)")
        
        // Parse the change and update local state
        if let changeData = payload.payload["new"] as? [String: Any] {
            await processProgressUpdate(changeData)
        }
        
        lastSyncTime = Date()
    }
    
    /// Handle lesson progress changes from other devices
    private func handleLessonProgressChange(_ payload: RealtimeMessage) async {
        print("📚 Received lesson progress change: \(payload)")
        
        // Parse the change and update local state
        if let changeData = payload.payload["new"] as? [String: Any] {
            await processLessonProgressUpdate(changeData)
        }
        
        lastSyncTime = Date()
    }
    
    /// Handle subscription status changes
    private func handleSubscriptionStatus(_ status: RealtimeChannelStatus, for type: String) {
        print("🔗 \(type.capitalized) subscription status: \(status)")
        
        switch status {
        case .subscribed:
            if type == "progress" {
                syncStatus = .connected
                isConnected = true
            }
        default:
            syncStatus = .disconnected
            isConnected = false
        }
    }
    
    // MARK: - Data Processing
    
    /// Process user progress update from remote
    private func processProgressUpdate(_ data: [String: Any]) async {
        // Check for conflicts with local data
        if let conflict = detectProgressConflict(data) {
            conflictResolutions.append(conflict)
            await resolveProgressConflict(conflict)
        } else {
            // Apply update directly
            await applyProgressUpdate(data)
        }
    }
    
    /// Process lesson progress update from remote
    private func processLessonProgressUpdate(_ data: [String: Any]) async {
        // Check for conflicts with local data
        if let conflict = detectLessonProgressConflict(data) {
            conflictResolutions.append(conflict)
            await resolveLessonProgressConflict(conflict)
        } else {
            // Apply update directly
            await applyLessonProgressUpdate(data)
        }
    }
    
    /// Apply user progress update to local state
    private func applyProgressUpdate(_ data: [String: Any]) async {
        // Update UserProgressService with new data
        // This would integrate with the existing UserProgressService
        print("✅ Applied progress update from remote device")
    }
    
    /// Apply lesson progress update to local state
    private func applyLessonProgressUpdate(_ data: [String: Any]) async {
        // Update UserProgressService with new lesson progress
        // This would integrate with the existing UserProgressService
        print("✅ Applied lesson progress update from remote device")
    }
    
    // MARK: - Conflict Resolution
    
    /// Detect conflicts in user progress data
    private func detectProgressConflict(_ remoteData: [String: Any]) -> SyncConflict? {
        // Compare timestamps and detect conflicts
        // This is a simplified implementation
        
        if let remoteTimestamp = remoteData["updated_at"] as? String,
           let localProgress = UserProgressService.shared.userProgress {
            
            let remoteDate = ISO8601DateFormatter().date(from: remoteTimestamp)
            let localDate = ISO8601DateFormatter().date(from: localProgress.updatedAt)
            
            if let remote = remoteDate, let local = localDate {
                let timeDifference = abs(remote.timeIntervalSince(local))
                
                // If updates happened within 5 seconds, consider it a conflict
                if timeDifference < 5 {
                    return SyncConflict(
                        id: UUID().uuidString,
                        type: .userProgress,
                        localData: localProgress,
                        remoteData: remoteData,
                        timestamp: Date(),
                        resolution: .pending
                    )
                }
            }
        }
        
        return nil
    }
    
    /// Detect conflicts in lesson progress data
    private func detectLessonProgressConflict(_ remoteData: [String: Any]) -> SyncConflict? {
        // Similar conflict detection for lesson progress
        // This is a simplified implementation
        
        if let lessonId = remoteData["lesson_id"] as? String,
           let localProgress = UserProgressService.shared.lessonProgress[lessonId] {
            
            if let remoteTimestamp = remoteData["updated_at"] as? String {
                let remoteDate = ISO8601DateFormatter().date(from: remoteTimestamp)
                let localDate = ISO8601DateFormatter().date(from: localProgress.updatedAt)
                
                if let remote = remoteDate, let local = localDate {
                    let timeDifference = abs(remote.timeIntervalSince(local))
                    
                    if timeDifference < 5 {
                        return SyncConflict(
                            id: UUID().uuidString,
                            type: .lessonProgress,
                            localData: localProgress,
                            remoteData: remoteData,
                            timestamp: Date(),
                            resolution: .pending
                        )
                    }
                }
            }
        }
        
        return nil
    }
    
    /// Resolve user progress conflict
    private func resolveProgressConflict(_ conflict: SyncConflict) async {
        // Implement conflict resolution strategy
        // For now, use "last write wins" strategy
        
        if let remoteData = conflict.remoteData as? [String: Any] {
            await applyProgressUpdate(remoteData)
            
            // Update conflict resolution
            if let index = conflictResolutions.firstIndex(where: { $0.id == conflict.id }) {
                conflictResolutions[index].resolution = .remoteWins
            }
        }
        
        print("🔧 Resolved progress conflict using remote data")
    }
    
    /// Resolve lesson progress conflict
    private func resolveLessonProgressConflict(_ conflict: SyncConflict) async {
        // Implement conflict resolution strategy
        // For now, use "last write wins" strategy
        
        if let remoteData = conflict.remoteData as? [String: Any] {
            await applyLessonProgressUpdate(remoteData)
            
            // Update conflict resolution
            if let index = conflictResolutions.firstIndex(where: { $0.id == conflict.id }) {
                conflictResolutions[index].resolution = .remoteWins
            }
        }
        
        print("🔧 Resolved lesson progress conflict using remote data")
    }
    
    // MARK: - Connection Management
    
    /// Start monitoring connection status
    private func startConnectionMonitoring() {
        Timer.publish(every: 30, on: .main, in: .common) // Check every 30 seconds
            .autoconnect()
            .sink { _ in
                Task { @MainActor in
                    await self.checkConnectionHealth()
                }
            }
            .store(in: &cancellables)
    }
    
    /// Check connection health and reconnect if needed
    private func checkConnectionHealth() async {
        if !isConnected {
            await reconnectSubscriptions()
        }
        
        await updateDeviceCount()
    }
    
    /// Reconnect real-time subscriptions
    private func reconnectSubscriptions() async {
        print("🔄 Reconnecting real-time subscriptions...")
        
        // Unsubscribe existing channels
        await progressSubscription?.unsubscribe()
        await lessonSubscription?.unsubscribe()
        
        // Setup new subscriptions
        setupRealtimeSubscriptions()
    }
    
    /// Update count of active devices for this user
    private func updateDeviceCount() async {
        // This would query active sessions/devices for the user
        // For now, simulate device count
        deviceCount = Int.random(in: 1...3)
    }
    
    /// Manually trigger sync
    func forceSyncNow() async {
        syncStatus = .syncing
        
        // Trigger sync with UserProgressService
        await UserProgressService.shared.syncWithSupabase()
        
        lastSyncTime = Date()
        syncStatus = .connected
        
        print("🔄 Manual sync completed")
    }
    
    /// Disconnect real-time subscriptions
    func disconnect() async {
        await progressSubscription?.unsubscribe()
        await lessonSubscription?.unsubscribe()
        
        isConnected = false
        syncStatus = .disconnected
        
        print("🔌 Real-time sync disconnected")
    }
}

// MARK: - Supporting Types

enum RealTimeSyncStatus {
    case disconnected
    case connecting
    case connected
    case syncing
    case error
}

struct SyncConflict: Identifiable {
    let id: String
    let type: ConflictType
    let localData: Any
    let remoteData: Any
    let timestamp: Date
    var resolution: ConflictResolution
}

enum ConflictType {
    case userProgress
    case lessonProgress
}

enum ConflictResolution {
    case pending
    case localWins
    case remoteWins
    case merged
}
