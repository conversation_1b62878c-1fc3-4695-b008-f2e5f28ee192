import Foundation
import CoreLocation

// MARK: - Panchang API Client
class PanchangAPIClient {
    static let shared = PanchangAPIClient()

    private let session = URLSession.shared
    private let baseURL = "https://json.freeastrologyapi.com"

    // Free Astrology API - 50 requests/day completely FREE (simple registration required)
    private let panchangBaseURL = "https://json.freeastrologyapi.com"
    private let apiKey = "VJ6p5g3sCy718KyUNTRC75t3ODB82xOG2dZGWI8g" // Free Astrology API key (50 requests/day)

    private var currentKeyIndex = 0

    // Rate limiting to prevent 429 errors
    private var lastRequestTime: Date = Date.distantPast
    private let minimumRequestInterval: TimeInterval = 15.0 // 15 seconds between requests (4 per minute max)

    private init() {}
    
    // MARK: - Public Methods
    
    func fetchPanchang(for date: Date, location: CLLocation = CLLocation(latitude: 13.0827, longitude: 80.2707)) async throws -> PanchangResponse {
        // Rate limiting to prevent 429 errors
        let timeSinceLastRequest = Date().timeIntervalSince(lastRequestTime)
        if timeSinceLastRequest < minimumRequestInterval {
            let waitTime = minimumRequestInterval - timeSinceLastRequest
            print("⏳ Rate limiting: waiting \(waitTime) seconds before next request")
            try await Task.sleep(nanoseconds: UInt64(waitTime * 1_000_000_000))
        }

        lastRequestTime = Date()

        // Use ProKerala API (working and reliable!)
        return try await fetchFromProKeralaAPI(date: date, location: location)
    }

    private func fetchFromProKeralaAPI(date: Date, location: CLLocation) async throws -> PanchangResponse {
        print("🔍 Using ProKerala API (reliable and working)")

        // First get access token
        let token = try await getProKeralaToken()

        // Format date for ProKerala API
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssXXX"
        dateFormatter.timeZone = TimeZone(identifier: "Asia/Kolkata")
        let dateString = dateFormatter.string(from: date)

        // Build the API URL with proper URL encoding
        let baseURL = "https://api.prokerala.com/v2/astrology/panchang"
        let coordinates = "\(location.coordinate.latitude),\(location.coordinate.longitude)"

        // Properly URL encode the datetime parameter to handle + sign in timezone
        guard let encodedDateTime = dateString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) else {
            throw PanchangError.invalidURL
        }

        let urlString = "\(baseURL)?ayanamsa=1&coordinates=\(coordinates)&datetime=\(encodedDateTime)&la=en"

        guard let finalURL = URL(string: urlString) else {
            throw PanchangError.invalidURL
        }

        var request = URLRequest(url: finalURL)
        request.httpMethod = "GET"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30

        print("🔍 ProKerala API URL: \(finalURL)")

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw PanchangError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            print("❌ ProKerala API Error: \(httpResponse.statusCode)")
            if let errorData = String(data: data, encoding: .utf8) {
                print("❌ Error response: \(errorData)")
            }
            throw PanchangError.httpError(httpResponse.statusCode)
        }

        print("✅ Successfully loaded panchang using ProKerala API")

        // Convert ProKerala API response to our PanchangResponse format
        return try convertProKeralaAPIResponse(data: data, date: date, location: location)
    }

    private func getProKeralaToken() async throws -> String {
        let tokenURL = URL(string: "https://api.prokerala.com/token")!

        var request = URLRequest(url: tokenURL)
        request.httpMethod = "POST"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30

        let tokenData = "grant_type=client_credentials&client_id=6df0ec16-722b-4acd-a574-bfd546c0c270&client_secret=0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"
        request.httpBody = tokenData.data(using: .utf8)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw PanchangError.authenticationFailed
        }

        let tokenResponse = try JSONDecoder().decode(ProKeralaTokenResponse.self, from: data)
        return tokenResponse.access_token
    }

    // MARK: - ProKerala API Response Conversion

    private func convertProKeralaAPIResponse(data: Data, date: Date, location: CLLocation) throws -> PanchangResponse {
        do {
            // Parse the ProKerala API response
            if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                print("🔍 ProKerala API response received")
                print("🔍 Response data: \(jsonObject)")

                // Check if we have the expected response structure
                if let responseData = jsonObject["data"] as? [String: Any] {
                    // Extract vaara (weekday)
                    let vaara = responseData["vaara"] as? String ?? "Unknown"

                    // Extract tithi information
                    var tithiArray: [TithiData] = []
                    if let tithiData = responseData["tithi"] as? [[String: Any]] {
                        for (index, tithi) in tithiData.enumerated() {
                            let tithiItem = TithiData(
                                id: tithi["id"] as? Int ?? index + 1,
                                index: tithi["index"] as? Int ?? index,
                                name: tithi["name"] as? String ?? "Unknown",
                                paksha: tithi["paksha"] as? String ?? "Unknown",
                                start: extractTimeFromISO(tithi["start"] as? String),
                                end: extractTimeFromISO(tithi["end"] as? String)
                            )
                            tithiArray.append(tithiItem)
                        }
                    }

                    // Extract nakshatra information
                    var nakshatraArray: [NakshatraData] = []
                    if let nakshatraData = responseData["nakshatra"] as? [[String: Any]] {
                        for nakshatra in nakshatraData {
                            let lordInfo = nakshatra["lord"] as? [String: Any]
                            let lordData = LordData(
                                id: lordInfo?["id"] as? Int ?? 1,
                                name: lordInfo?["name"] as? String ?? "Unknown",
                                vedic_name: lordInfo?["vedic_name"] as? String ?? "Unknown"
                            )

                            let nakshatraItem = NakshatraData(
                                id: nakshatra["id"] as? Int ?? 1,
                                name: nakshatra["name"] as? String ?? "Unknown",
                                lord: lordData,
                                start: extractTimeFromISO(nakshatra["start"] as? String),
                                end: extractTimeFromISO(nakshatra["end"] as? String)
                            )
                            nakshatraArray.append(nakshatraItem)
                        }
                    }

                    // Extract yoga information
                    var yogaArray: [YogaData] = []
                    if let yogaData = responseData["yoga"] as? [[String: Any]] {
                        for yoga in yogaData {
                            let yogaItem = YogaData(
                                id: yoga["id"] as? Int ?? 1,
                                name: yoga["name"] as? String ?? "Unknown",
                                start: extractTimeFromISO(yoga["start"] as? String),
                                end: extractTimeFromISO(yoga["end"] as? String)
                            )
                            yogaArray.append(yogaItem)
                        }
                    }

                    // Extract karana information
                    var karanaArray: [KaranaData] = []
                    if let karanaData = responseData["karana"] as? [[String: Any]] {
                        for (index, karana) in karanaData.enumerated() {
                            let karanaItem = KaranaData(
                                id: karana["id"] as? Int ?? index + 1,
                                index: karana["index"] as? Int ?? index,
                                name: karana["name"] as? String ?? "Unknown",
                                start: extractTimeFromISO(karana["start"] as? String),
                                end: extractTimeFromISO(karana["end"] as? String)
                            )
                            karanaArray.append(karanaItem)
                        }
                    }

                    let panchangData = PanchangData(
                        vaara: vaara,
                        tithi: tithiArray,
                        nakshatra: nakshatraArray,
                        yoga: yogaArray,
                        karana: karanaArray,
                        sunrise: extractTimeFromISO(responseData["sunrise"] as? String),
                        sunset: extractTimeFromISO(responseData["sunset"] as? String),
                        moonrise: extractTimeFromISO(responseData["moonrise"] as? String),
                        moonset: extractTimeFromISO(responseData["moonset"] as? String)
                    )

                    return PanchangResponse(
                        status: "success",
                        data: panchangData
                    )
                } else {
                    // Handle other response formats if needed
                    throw PanchangError.invalidResponse
                }

            } else {
                throw PanchangError.invalidResponse
            }
        } catch {
            print("❌ Failed to decode ProKerala API response: \(error)")
            print("❌ Raw response: \(String(data: data, encoding: .utf8) ?? "Unable to decode")")
            throw PanchangError.decodingFailed(error)
        }
    }

    // MARK: - Helper Functions

    private func extractTimeFromISO(_ isoString: String?) -> String {
        guard let isoString = isoString else { return "Unknown" }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssXXX"

        if let date = formatter.date(from: isoString) {
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm"
            timeFormatter.timeZone = TimeZone(identifier: "Asia/Kolkata")
            return timeFormatter.string(from: date)
        }

        return "Unknown"
    }


    
    func fetchMultipleDays(from startDate: Date, days: Int, location: CLLocation = CLLocation(latitude: 13.0827, longitude: 80.2707)) async throws -> [PanchangResponse] {
        var results: [PanchangResponse] = []
        
        for i in 0..<days {
            let date = Calendar.current.date(byAdding: .day, value: i, to: startDate) ?? startDate
            do {
                let panchang = try await fetchPanchang(for: date, location: location)
                results.append(panchang)
                
                // Add small delay to avoid rate limiting
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            } catch {
                print("❌ Failed to fetch panchang for \(date): \(error)")
                // Continue with other dates even if one fails
            }
        }
        
        return results
    }
    
    // MARK: - Private Methods
    

}

// MARK: - Request Models

struct PanchangRequest: Codable {
    let year: Int
    let month: Int
    let date: Int
    let hours: Int
    let minutes: Int
    let seconds: Int
    let latitude: Double
    let longitude: Double
    let timezone: Double
    let config: PanchangConfig
}

struct PanchangConfig: Codable {
    let observationPoint: String
    let ayanamsha: String
}

// MARK: - Response Data Models

struct LocationData: Codable {
    let latitude: Double
    let longitude: Double
    let timezone: String
}

struct TithiData: Codable {
    let id: Int
    let index: Int
    let name: String
    let paksha: String
    let start: String
    let end: String
}

struct NakshatraData: Codable {
    let id: Int
    let name: String
    let lord: LordData
    let start: String
    let end: String
}

struct LordData: Codable {
    let id: Int
    let name: String
    let vedic_name: String
}

struct YogaData: Codable {
    let id: Int
    let name: String
    let start: String
    let end: String
}

struct KaranaData: Codable {
    let id: Int
    let index: Int
    let name: String
    let start: String
    let end: String
}

struct PanchangData: Codable {
    let vaara: String
    let tithi: [TithiData]
    let nakshatra: [NakshatraData]
    let yoga: [YogaData]
    let karana: [KaranaData]
    let sunrise: String
    let sunset: String
    let moonrise: String
    let moonset: String
}

// MARK: - Response Models

struct PanchangResponse: Codable {
    let status: String
    let data: PanchangData
}



// MARK: - Error Types

enum PanchangError: LocalizedError {
    case invalidURL
    case invalidDateFormat
    case encodingFailed(Error)
    case decodingFailed(Error)
    case invalidResponse
    case unauthorizedAPIKey
    case authenticationFailed
    case rateLimitExceeded
    case serverError(Int)
    case httpError(Int)
    case allAPIKeysFailed
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .invalidDateFormat:
            return "Invalid date format for API request"
        case .encodingFailed(let error):
            return "Failed to encode request: \(error.localizedDescription)"
        case .decodingFailed(let error):
            return "Failed to decode response: \(error.localizedDescription)"
        case .invalidResponse:
            return "Invalid response from server"
        case .unauthorizedAPIKey:
            return "Unauthorized API key"
        case .authenticationFailed:
            return "Authentication failed"
        case .rateLimitExceeded:
            return "API rate limit exceeded"
        case .serverError(let code):
            return "Server error: \(code)"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .allAPIKeysFailed:
            return "All API keys failed"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}
