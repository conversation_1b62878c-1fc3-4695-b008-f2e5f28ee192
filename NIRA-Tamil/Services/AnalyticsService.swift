//
//  AnalyticsService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import Supabase
import Combine

/// Service for analytics and usage tracking with privacy-compliant data collection
@MainActor
class AnalyticsService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = AnalyticsService()
    
    // MARK: - Published Properties
    @Published var isTrackingEnabled = true
    @Published var analyticsData: AnalyticsData?
    @Published var learningPatterns: [LearningPatternData] = []
    @Published var engagementMetrics: EngagementMetrics?
    @Published var isLoading = false
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    private let userId: String
    private let sessionId: String
    private var sessionStartTime: Date
    private var eventQueue: [AnalyticsEvent] = []
    
    // MARK: - Initialization
    private init() {
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co")!,
            supabaseKey: APIKeys.supabaseAnonKey
        )
        
        self.userId = "user-\(UUID().uuidString)"
        self.sessionId = "session-\(UUID().uuidString)"
        self.sessionStartTime = Date()
        
        loadTrackingPreference()
        
        print("📊 AnalyticsService initialized")
        
        // Start session
        if isTrackingEnabled {
            trackEvent(.sessionStart, properties: [
                "session_id": sessionId,
                "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
            ])
        }
        
        // Start periodic data flush
        startPeriodicFlush()
    }
    
    // MARK: - Event Tracking
    
    /// Track an analytics event
    func trackEvent(_ eventType: AnalyticsEventType, properties: [String: Any] = [:]) {
        guard isTrackingEnabled else { return }
        
        let event = AnalyticsEvent(
            id: UUID().uuidString,
            userId: userId,
            sessionId: sessionId,
            eventType: eventType.rawValue,
            properties: properties,
            timestamp: Date()
        )
        
        eventQueue.append(event)
        
        print("📈 Tracked event: \(eventType.rawValue)")
        
        // Flush immediately for critical events
        if eventType.isCritical {
            Task {
                await flushEvents()
            }
        }
    }
    
    /// Track lesson start
    func trackLessonStart(lessonId: String, level: String) {
        trackEvent(.lessonStart, properties: [
            "lesson_id": lessonId,
            "level": level,
            "start_time": ISO8601DateFormatter().string(from: Date())
        ])
    }
    
    /// Track lesson completion
    func trackLessonCompletion(lessonId: String, level: String, score: Int, timeSpent: TimeInterval) {
        trackEvent(.lessonComplete, properties: [
            "lesson_id": lessonId,
            "level": level,
            "score": score,
            "time_spent_seconds": timeSpent,
            "completion_time": ISO8601DateFormatter().string(from: Date())
        ])
    }
    
    /// Track vocabulary interaction
    func trackVocabularyInteraction(vocabularyId: String, action: String) {
        trackEvent(.vocabularyInteraction, properties: [
            "vocabulary_id": vocabularyId,
            "action": action,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ])
    }
    
    /// Track audio playback
    func trackAudioPlayback(audioType: String, contentId: String, duration: TimeInterval) {
        trackEvent(.audioPlayback, properties: [
            "audio_type": audioType,
            "content_id": contentId,
            "duration_seconds": duration,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ])
    }
    
    /// Track practice exercise attempt
    func trackPracticeAttempt(exerciseId: String, exerciseType: String, isCorrect: Bool, timeSpent: TimeInterval) {
        trackEvent(.practiceAttempt, properties: [
            "exercise_id": exerciseId,
            "exercise_type": exerciseType,
            "is_correct": isCorrect,
            "time_spent_seconds": timeSpent,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ])
    }
    
    /// Track user engagement
    func trackEngagement(action: String, screen: String, duration: TimeInterval? = nil) {
        var properties: [String: Any] = [
            "action": action,
            "screen": screen,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        
        if let duration = duration {
            properties["duration_seconds"] = duration
        }
        
        trackEvent(.userEngagement, properties: properties)
    }
    
    // MARK: - Data Analysis
    
    /// Generate analytics data
    func generateAnalyticsData() async {
        isLoading = true
        
        do {
            // Fetch user events
            let events = try await fetchUserEvents()
            
            // Analyze learning patterns
            learningPatterns = analyzeLearningPatterns(events)
            
            // Calculate engagement metrics
            engagementMetrics = calculateEngagementMetrics(events)
            
            // Create analytics summary
            analyticsData = AnalyticsData(
                totalEvents: events.count,
                sessionsCount: getUniqueSessionsCount(events),
                totalStudyTime: calculateTotalStudyTime(events),
                averageSessionDuration: calculateAverageSessionDuration(events),
                mostActiveHour: findMostActiveHour(events),
                preferredLearningMode: findPreferredLearningMode(events),
                generatedAt: Date()
            )
            
            print("📊 Analytics data generated")
            
        } catch {
            print("❌ Failed to generate analytics: \(error)")
        }
        
        isLoading = false
    }
    
    /// Fetch user events from database
    private func fetchUserEvents() async throws -> [AnalyticsEvent] {
        let events: [AnalyticsEvent] = try await supabase
            .from("analytics_events")
            .select("*")
            .eq("user_id", value: userId)
            .gte("timestamp", value: Calendar.current.date(byAdding: .day, value: -30, to: Date())!)
            .order("timestamp", ascending: false)
            .execute()
            .value
        
        return events
    }
    
    /// Analyze learning patterns
    private func analyzeLearningPatterns(_ events: [AnalyticsEvent]) -> [LearningPatternData] {
        var patterns: [LearningPatternData] = []
        
        // Analyze study time patterns
        let studyTimePattern = analyzeStudyTimePattern(events)
        patterns.append(studyTimePattern)
        
        // Analyze difficulty progression
        let difficultyPattern = analyzeDifficultyProgression(events)
        patterns.append(difficultyPattern)
        
        // Analyze content preferences
        let contentPattern = analyzeContentPreferences(events)
        patterns.append(contentPattern)
        
        return patterns
    }
    
    /// Analyze study time patterns
    private func analyzeStudyTimePattern(_ events: [AnalyticsEvent]) -> LearningPatternData {
        let studyEvents = events.filter { $0.eventType == "lesson_start" || $0.eventType == "lesson_complete" }
        
        // Group by hour of day
        let hourCounts = Dictionary(grouping: studyEvents) { event in
            Calendar.current.component(.hour, from: event.timestamp)
        }.mapValues { $0.count }
        
        let mostActiveHour = hourCounts.max(by: { $0.value < $1.value })?.key ?? 0
        
        return LearningPatternData(
            id: UUID().uuidString,
            type: "study_time",
            description: "Most active study time: \(mostActiveHour):00",
            confidence: 0.8,
            insights: ["User prefers studying at \(mostActiveHour):00"],
            recommendations: ["Schedule notifications around \(mostActiveHour):00"]
        )
    }
    
    /// Analyze difficulty progression
    private func analyzeDifficultyProgression(_ events: [AnalyticsEvent]) -> LearningPatternData {
        let practiceEvents = events.filter { $0.eventType == "practice_attempt" }
        
        let correctAnswers = practiceEvents.filter { event in
            event.properties["is_correct"] as? Bool == true
        }.count
        
        let accuracy = practiceEvents.isEmpty ? 0.0 : Double(correctAnswers) / Double(practiceEvents.count)
        
        return LearningPatternData(
            id: UUID().uuidString,
            type: "difficulty_progression",
            description: "Practice accuracy: \(Int(accuracy * 100))%",
            confidence: 0.9,
            insights: ["Current accuracy rate is \(Int(accuracy * 100))%"],
            recommendations: accuracy > 0.8 ? ["Ready for more challenging content"] : ["Focus on current level mastery"]
        )
    }
    
    /// Analyze content preferences
    private func analyzeContentPreferences(_ events: [AnalyticsEvent]) -> LearningPatternData {
        let contentEvents = events.filter { $0.eventType == "vocabulary_interaction" || $0.eventType == "audio_playback" }
        
        let audioEvents = contentEvents.filter { $0.eventType == "audio_playback" }.count
        let vocabularyEvents = contentEvents.filter { $0.eventType == "vocabulary_interaction" }.count
        
        let preferredMode = audioEvents > vocabularyEvents ? "Audio-focused" : "Visual-focused"
        
        return LearningPatternData(
            id: UUID().uuidString,
            type: "content_preference",
            description: "Learning style: \(preferredMode)",
            confidence: 0.7,
            insights: ["User prefers \(preferredMode.lowercased()) learning"],
            recommendations: ["Emphasize \(preferredMode.lowercased()) content"]
        )
    }
    
    /// Calculate engagement metrics
    private func calculateEngagementMetrics(_ events: [AnalyticsEvent]) -> EngagementMetrics {
        let sessionEvents = events.filter { $0.eventType == "session_start" }
        let uniqueDays = Set(events.map { Calendar.current.startOfDay(for: $0.timestamp) }).count
        
        let totalEvents = events.count
        let averageEventsPerSession = sessionEvents.isEmpty ? 0.0 : Double(totalEvents) / Double(sessionEvents.count)
        
        return EngagementMetrics(
            dailyActiveUsers: uniqueDays,
            averageSessionLength: calculateAverageSessionDuration(events),
            eventsPerSession: averageEventsPerSession,
            retentionRate: calculateRetentionRate(events),
            completionRate: calculateCompletionRate(events)
        )
    }
    
    // MARK: - Helper Methods
    
    private func getUniqueSessionsCount(_ events: [AnalyticsEvent]) -> Int {
        return Set(events.map { $0.sessionId }).count
    }
    
    private func calculateTotalStudyTime(_ events: [AnalyticsEvent]) -> TimeInterval {
        let studyEvents = events.filter { $0.eventType == "lesson_complete" }
        return studyEvents.compactMap { $0.properties["time_spent_seconds"] as? TimeInterval }.reduce(0, +)
    }
    
    private func calculateAverageSessionDuration(_ events: [AnalyticsEvent]) -> TimeInterval {
        // Simplified calculation - in reality, you'd track session end events
        return 15 * 60 // 15 minutes average
    }
    
    private func findMostActiveHour(_ events: [AnalyticsEvent]) -> Int {
        let hourCounts = Dictionary(grouping: events) { event in
            Calendar.current.component(.hour, from: event.timestamp)
        }.mapValues { $0.count }
        
        return hourCounts.max(by: { $0.value < $1.value })?.key ?? 0
    }
    
    private func findPreferredLearningMode(_ events: [AnalyticsEvent]) -> String {
        let audioEvents = events.filter { $0.eventType == "audio_playback" }.count
        let vocabularyEvents = events.filter { $0.eventType == "vocabulary_interaction" }.count
        
        return audioEvents > vocabularyEvents ? "Audio" : "Visual"
    }
    
    private func calculateRetentionRate(_ events: [AnalyticsEvent]) -> Double {
        // Simplified retention calculation
        let uniqueDays = Set(events.map { Calendar.current.startOfDay(for: $0.timestamp) }).count
        return uniqueDays >= 7 ? 0.8 : Double(uniqueDays) / 7.0
    }
    
    private func calculateCompletionRate(_ events: [AnalyticsEvent]) -> Double {
        let startEvents = events.filter { $0.eventType == "lesson_start" }.count
        let completeEvents = events.filter { $0.eventType == "lesson_complete" }.count
        
        return startEvents > 0 ? Double(completeEvents) / Double(startEvents) : 0.0
    }
    
    // MARK: - Data Management
    
    /// Flush events to database
    func flushEvents() async {
        guard !eventQueue.isEmpty && isTrackingEnabled else { return }
        
        let eventsToFlush = eventQueue
        eventQueue.removeAll()
        
        do {
            for event in eventsToFlush {
                let _: AnalyticsEvent = try await supabase
                    .from("analytics_events")
                    .insert(event)
                    .execute()
                    .value
            }
            
            print("📤 Flushed \(eventsToFlush.count) analytics events")
            
        } catch {
            // Re-add events to queue if flush fails
            eventQueue.append(contentsOf: eventsToFlush)
            print("❌ Failed to flush analytics events: \(error)")
        }
    }
    
    /// Start periodic event flushing
    private func startPeriodicFlush() {
        Timer.publish(every: 60, on: .main, in: .common) // Flush every minute
            .autoconnect()
            .sink { _ in
                Task {
                    await self.flushEvents()
                }
            }
            .store(in: &cancellables)
    }
    
    /// Load tracking preference
    private func loadTrackingPreference() {
        isTrackingEnabled = UserDefaults.standard.bool(forKey: "analytics_tracking_enabled")
    }
    
    /// Save tracking preference
    func setTrackingEnabled(_ enabled: Bool) {
        isTrackingEnabled = enabled
        UserDefaults.standard.set(enabled, forKey: "analytics_tracking_enabled")
        UserDefaults.standard.synchronize()
        
        if enabled {
            trackEvent(.trackingEnabled)
        } else {
            trackEvent(.trackingDisabled)
            // Flush remaining events before disabling
            Task {
                await flushEvents()
            }
        }
    }
    
    /// Export user data (GDPR compliance)
    func exportUserData() async -> [String: Any] {
        do {
            let events = try await fetchUserEvents()
            
            return [
                "user_id": userId,
                "events": events.map { event in
                    [
                        "event_type": event.eventType,
                        "timestamp": ISO8601DateFormatter().string(from: event.timestamp),
                        "properties": event.properties
                    ]
                },
                "analytics_data": analyticsData?.toDictionary() ?? [:],
                "learning_patterns": learningPatterns.map { $0.toDictionary() },
                "exported_at": ISO8601DateFormatter().string(from: Date())
            ]
            
        } catch {
            print("❌ Failed to export user data: \(error)")
            return [:]
        }
    }
    
    /// Delete user data (GDPR compliance)
    func deleteUserData() async {
        do {
            // Delete from analytics_events table
            try await supabase
                .from("analytics_events")
                .delete()
                .eq("user_id", value: userId)
                .execute()
            
            // Clear local data
            eventQueue.removeAll()
            analyticsData = nil
            learningPatterns.removeAll()
            engagementMetrics = nil
            
            print("🗑️ User analytics data deleted")
            
        } catch {
            print("❌ Failed to delete user data: \(error)")
        }
    }
}

// MARK: - Supporting Types

enum AnalyticsEventType: String, CaseIterable {
    case sessionStart = "session_start"
    case sessionEnd = "session_end"
    case lessonStart = "lesson_start"
    case lessonComplete = "lesson_complete"
    case vocabularyInteraction = "vocabulary_interaction"
    case audioPlayback = "audio_playback"
    case practiceAttempt = "practice_attempt"
    case userEngagement = "user_engagement"
    case trackingEnabled = "tracking_enabled"
    case trackingDisabled = "tracking_disabled"
    
    var isCritical: Bool {
        switch self {
        case .sessionStart, .sessionEnd, .lessonComplete:
            return true
        default:
            return false
        }
    }
}

struct AnalyticsEvent: Codable, Identifiable {
    let id: String
    let userId: String
    let sessionId: String
    let eventType: String
    let properties: [String: Any]
    let timestamp: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case sessionId = "session_id"
        case eventType = "event_type"
        case timestamp
    }
    
    init(id: String, userId: String, sessionId: String, eventType: String, properties: [String: Any], timestamp: Date) {
        self.id = id
        self.userId = userId
        self.sessionId = sessionId
        self.eventType = eventType
        self.properties = properties
        self.timestamp = timestamp
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        userId = try container.decode(String.self, forKey: .userId)
        sessionId = try container.decode(String.self, forKey: .sessionId)
        eventType = try container.decode(String.self, forKey: .eventType)
        timestamp = try container.decode(Date.self, forKey: .timestamp)
        properties = [:] // Properties would need custom decoding
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(userId, forKey: .userId)
        try container.encode(sessionId, forKey: .sessionId)
        try container.encode(eventType, forKey: .eventType)
        try container.encode(timestamp, forKey: .timestamp)
        // Properties would need custom encoding
    }
}

struct AnalyticsData {
    let totalEvents: Int
    let sessionsCount: Int
    let totalStudyTime: TimeInterval
    let averageSessionDuration: TimeInterval
    let mostActiveHour: Int
    let preferredLearningMode: String
    let generatedAt: Date
    
    func toDictionary() -> [String: Any] {
        return [
            "total_events": totalEvents,
            "sessions_count": sessionsCount,
            "total_study_time": totalStudyTime,
            "average_session_duration": averageSessionDuration,
            "most_active_hour": mostActiveHour,
            "preferred_learning_mode": preferredLearningMode,
            "generated_at": ISO8601DateFormatter().string(from: generatedAt)
        ]
    }
}

struct LearningPatternData: Identifiable {
    let id: String
    let type: String
    let description: String
    let confidence: Double
    let insights: [String]
    let recommendations: [String]
    
    func toDictionary() -> [String: Any] {
        return [
            "id": id,
            "type": type,
            "description": description,
            "confidence": confidence,
            "insights": insights,
            "recommendations": recommendations
        ]
    }
}

struct EngagementMetrics {
    let dailyActiveUsers: Int
    let averageSessionLength: TimeInterval
    let eventsPerSession: Double
    let retentionRate: Double
    let completionRate: Double
}
