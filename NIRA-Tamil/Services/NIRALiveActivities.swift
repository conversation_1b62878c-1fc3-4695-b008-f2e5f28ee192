//
//  NIRALiveActivities.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  iOS 18 Live Activities for Dynamic Island and Lock Screen
//

import Foundation
import SwiftUI

// iOS 18 Live Activities - will be available when entitlements are enabled
#if canImport(ActivityKit)
import ActivityKit
#endif

#if canImport(WidgetKit)
import WidgetKit
#endif

// MARK: - Live Activity Attributes

struct LessonProgressAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        var currentStep: Int
        var totalSteps: Int
        var lessonTitle: String
        var currentWord: String?
        var timeRemaining: TimeInterval
        var sessionType: SessionType
        var score: Int
        var isActive: Bool
        
        enum SessionType: String, Codable, CaseIterable {
            case vocabulary = "vocabulary"
            case pronunciation = "pronunciation"
            case conversation = "conversation"
            case grammar = "grammar"
            case cultural = "cultural"
            
            var displayName: String {
                switch self {
                case .vocabulary: return "Vocabulary"
                case .pronunciation: return "Pronunciation"
                case .conversation: return "Conversation"
                case .grammar: return "Grammar"
                case .cultural: return "Cultural"
                }
            }
            
            var icon: String {
                switch self {
                case .vocabulary: return "text.book.closed.fill"
                case .pronunciation: return "mic.fill"
                case .conversation: return "bubble.left.and.bubble.right.fill"
                case .grammar: return "textformat.abc"
                case .cultural: return "globe.asia.australia.fill"
                }
            }
            
            var color: Color {
                switch self {
                case .vocabulary: return .blue
                case .pronunciation: return .green
                case .conversation: return .purple
                case .grammar: return .orange
                case .cultural: return .red
                }
            }
        }
    }
    
    var lessonId: String
    var language: String
    var startTime: Date
}

// MARK: - Live Activity Service

@MainActor
class NIRALiveActivityService: ObservableObject {
    static let shared = NIRALiveActivityService()
    
    @Published var currentActivity: Activity<LessonProgressAttributes>?
    @Published var isActivityActive = false
    
    private init() {}
    
    // MARK: - Activity Management
    
    func startLessonActivity(
        lessonId: String,
        lessonTitle: String,
        sessionType: LessonProgressAttributes.ContentState.SessionType,
        totalSteps: Int,
        duration: TimeInterval
    ) async {
        guard ActivityAuthorizationInfo().areActivitiesEnabled else {
            print("Live Activities are not enabled")
            return
        }
        
        let attributes = LessonProgressAttributes(
            lessonId: lessonId,
            language: "Tamil",
            startTime: Date()
        )
        
        let contentState = LessonProgressAttributes.ContentState(
            currentStep: 1,
            totalSteps: totalSteps,
            lessonTitle: lessonTitle,
            currentWord: nil,
            timeRemaining: duration,
            sessionType: sessionType,
            score: 0,
            isActive: true
        )
        
        do {
            let activity = try Activity<LessonProgressAttributes>.request(
                attributes: attributes,
                content: .init(state: contentState, staleDate: nil),
                pushType: nil
            )
            
            currentActivity = activity
            isActivityActive = true
            
            print("✅ Started Live Activity: \(activity.id)")
            
            // Start timer to update activity
            startActivityTimer()
            
        } catch {
            print("❌ Failed to start Live Activity: \(error)")
        }
    }
    
    func updateLessonProgress(
        currentStep: Int,
        currentWord: String? = nil,
        score: Int? = nil
    ) async {
        guard let activity = currentActivity else { return }
        
        var updatedState = activity.content.state
        updatedState.currentStep = currentStep
        
        if let word = currentWord {
            updatedState.currentWord = word
        }
        
        if let newScore = score {
            updatedState.score = newScore
        }
        
        // Update time remaining
        let elapsed = Date().timeIntervalSince(activity.attributes.startTime)
        let originalDuration = updatedState.timeRemaining + elapsed
        updatedState.timeRemaining = max(0, originalDuration - elapsed)
        
        await updateActivity(with: updatedState)
    }
    
    func completeLessonActivity(finalScore: Int) async {
        guard let activity = currentActivity else { return }
        
        var finalState = activity.content.state
        finalState.currentStep = finalState.totalSteps
        finalState.score = finalScore
        finalState.timeRemaining = 0
        finalState.isActive = false
        
        await updateActivity(with: finalState)
        
        // End activity after a brief delay to show completion
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            Task {
                await self.endActivity()
            }
        }
    }
    
    func endActivity() async {
        guard let activity = currentActivity else { return }
        
        let finalState = LessonProgressAttributes.ContentState(
            currentStep: activity.content.state.totalSteps,
            totalSteps: activity.content.state.totalSteps,
            lessonTitle: activity.content.state.lessonTitle,
            currentWord: nil,
            timeRemaining: 0,
            sessionType: activity.content.state.sessionType,
            score: activity.content.state.score,
            isActive: false
        )

        await activity.end(.init(state: finalState, staleDate: nil), dismissalPolicy: .immediate)
        
        currentActivity = nil
        isActivityActive = false
        
        print("✅ Ended Live Activity")
    }
    
    private func updateActivity(with contentState: LessonProgressAttributes.ContentState) async {
        guard let activity = currentActivity else { return }
        
        await activity.update(.init(state: contentState, staleDate: nil))
    }
    
    private func startActivityTimer() {
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            Task { @MainActor in
                guard let self = self,
                      let activity = self.currentActivity,
                      activity.content.state.isActive else {
                    timer.invalidate()
                    return
                }
                
                let elapsed = Date().timeIntervalSince(activity.attributes.startTime)
                var updatedState = activity.content.state
                
                // Calculate remaining time
                let originalDuration = updatedState.timeRemaining + elapsed
                updatedState.timeRemaining = max(0, originalDuration - elapsed)
                
                if updatedState.timeRemaining <= 0 {
                    timer.invalidate()
                    await self.completeLessonActivity(finalScore: updatedState.score)
                } else {
                    await self.updateActivity(with: updatedState)
                }
            }
        }
    }
}

// MARK: - Live Activity Widget

struct LessonProgressLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: LessonProgressAttributes.self) { context in
            // Lock Screen view
            LessonProgressLockScreenView(context: context)
        } dynamicIsland: { context in
            // Dynamic Island views
            DynamicIsland {
                // Expanded view
                DynamicIslandExpandedRegion(.leading) {
                    LessonProgressLeadingView(context: context)
                }
                DynamicIslandExpandedRegion(.trailing) {
                    LessonProgressTrailingView(context: context)
                }
                DynamicIslandExpandedRegion(.bottom) {
                    LessonProgressBottomView(context: context)
                }
            } compactLeading: {
                // Compact leading view
                Image(systemName: context.state.sessionType.icon)
                    .foregroundColor(context.state.sessionType.color)
            } compactTrailing: {
                // Compact trailing view
                Text("\(context.state.currentStep)/\(context.state.totalSteps)")
                    .font(.caption2)
                    .fontWeight(.semibold)
            } minimal: {
                // Minimal view
                Image(systemName: context.state.sessionType.icon)
                    .foregroundColor(context.state.sessionType.color)
            }
        }
    }
}

// MARK: - Lock Screen View

struct LessonProgressLockScreenView: View {
    let context: ActivityViewContext<LessonProgressAttributes>
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: context.state.sessionType.icon)
                    .font(.title2)
                    .foregroundColor(context.state.sessionType.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(context.state.lessonTitle)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("\(context.state.sessionType.displayName) • Tamil")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(context.state.currentStep)/\(context.state.totalSteps)")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Text("Score: \(context.state.score)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Progress bar
            ProgressView(
                value: Double(context.state.currentStep),
                total: Double(context.state.totalSteps)
            )
            .progressViewStyle(LinearProgressViewStyle(tint: context.state.sessionType.color))
            .scaleEffect(x: 1, y: 2, anchor: .center)
            
            HStack {
                if let currentWord = context.state.currentWord {
                    Text("Current: \(currentWord)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                Text(timeRemainingText)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private var timeRemainingText: String {
        let minutes = Int(context.state.timeRemaining) / 60
        let seconds = Int(context.state.timeRemaining) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Dynamic Island Views

struct LessonProgressLeadingView: View {
    let context: ActivityViewContext<LessonProgressAttributes>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Image(systemName: context.state.sessionType.icon)
                .font(.title3)
                .foregroundColor(context.state.sessionType.color)
            
            Text(context.state.sessionType.displayName)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
}

struct LessonProgressTrailingView: View {
    let context: ActivityViewContext<LessonProgressAttributes>
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            Text("\(context.state.currentStep)/\(context.state.totalSteps)")
                .font(.headline)
                .fontWeight(.bold)
            
            Text("Score: \(context.state.score)")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

struct LessonProgressBottomView: View {
    let context: ActivityViewContext<LessonProgressAttributes>
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(context.state.lessonTitle)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text(timeRemainingText)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(
                value: Double(context.state.currentStep),
                total: Double(context.state.totalSteps)
            )
            .progressViewStyle(LinearProgressViewStyle(tint: context.state.sessionType.color))
            
            if let currentWord = context.state.currentWord {
                Text("Current: \(currentWord)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var timeRemainingText: String {
        let minutes = Int(context.state.timeRemaining) / 60
        let seconds = Int(context.state.timeRemaining) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Live Activity Bundle

// @main
struct NIRALiveActivityBundle: WidgetBundle {
    var body: some Widget {
        LessonProgressLiveActivity()
    }
}
