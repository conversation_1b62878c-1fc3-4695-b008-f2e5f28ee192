//
//  ProgressiveLearningService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation
import SwiftUI

@MainActor
class ProgressiveLearningService: ObservableObject {
    static let shared = ProgressiveLearningService()
    
    @Published var currentUserLevel: CEFRLevel = .a1
    @Published var learningPath: [LearningPathNode] = []
    @Published var completedLessons: Set<String> = []
    @Published var masteredVocabulary: Set<String> = []
    @Published var currentStreak: Int = 0
    @Published var totalStudyTime: TimeInterval = 0
    @Published var weeklyGoal: Int = 5 // lessons per week
    @Published var dailyGoal: Int = 1 // lessons per day
    
    // Progress tracking
    @Published var todaysProgress: DailyProgress = DailyProgress()
    @Published var weeklyProgress: WeeklyProgress = WeeklyProgress()
    @Published var overallProgress: OverallProgress = OverallProgress()
    
    private let userDefaults = UserDefaults.standard

    private init() {
        loadUserProgress()
        // Don't generate learning path immediately to avoid circular dependency
        // It will be generated when first accessed
    }
    
    // MARK: - Learning Path Generation

    func generateLearningPath() {
        // Use background queue to avoid publishing changes from within view updates
        Task { [weak self] in
            guard let self = self else { return }

            // Get content service safely to avoid circular dependency
            guard let contentService = await self.getContentService() else { return }

            var newLearningPath: [LearningPathNode] = []

            // Generate path based on current level and available content
            for level in CEFRLevel.allCases {
                let lessons = contentService.getLessonsForLevel(level)

                for lesson in lessons.sorted(by: { $0.lessonNumber < $1.lessonNumber }) {
                    let node = LearningPathNode(
                        id: "\(lesson.levelCode)-L\(lesson.lessonNumber)",
                        lesson: lesson,
                        level: level,
                        isUnlocked: await self.shouldUnlockLesson(lesson, at: level, contentService: contentService),
                        isCompleted: await MainActor.run { self.completedLessons.contains("\(lesson.levelCode)-L\(lesson.lessonNumber)") },
                        prerequisites: await self.getPrerequisites(for: lesson, at: level, contentService: contentService),
                        estimatedDifficulty: await self.calculateDifficulty(for: lesson),
                        adaptiveRecommendation: await self.getAdaptiveRecommendation(for: lesson)
                    )
                    newLearningPath.append(node)
                }
            }

            // Update on main queue
            await MainActor.run {
                self.learningPath = newLearningPath
            }
        }
    }

    private func getContentService() async -> TamilContentService? {
        // Safely get the content service to avoid circular dependency
        return await MainActor.run { TamilContentService.shared }
    }
    
    private func shouldUnlockLesson(_ lesson: TamilLesson, at level: CEFRLevel, contentService: TamilContentService) async -> Bool {
        // ALL LESSONS UNLOCKED: Complete access for testing and exploration
        return true
    }
    
    private func getPrerequisites(for lesson: TamilLesson, at level: CEFRLevel, contentService: TamilContentService) async -> [String] {
        var prerequisites: [String] = []

        // Previous lesson in same level
        if lesson.lessonNumber > 1 {
            prerequisites.append("\(lesson.levelCode)-L\(lesson.lessonNumber - 1)")
        }

        // Key lessons from previous levels
        if level != .a1 {
            let levelIndex = CEFRLevel.allCases.firstIndex(of: level) ?? 0
            let previousLevel = CEFRLevel.allCases[levelIndex - 1]

            // Add final lesson of previous level as prerequisite
            let previousLevelLessons = contentService.getLessonsForLevel(previousLevel)
            if let lastLesson = previousLevelLessons.max(by: { $0.lessonNumber < $1.lessonNumber }) {
                prerequisites.append("\(lastLesson.levelCode)-L\(lastLesson.lessonNumber)")
            }
        }

        return prerequisites
    }
    
    private func calculateDifficulty(for lesson: TamilLesson) async -> Double {
        var difficulty = 0.0
        
        // Base difficulty by level
        switch CEFRLevel(rawValue: lesson.levelCode) ?? .a1 {
        case .a1: difficulty = 1.0
        case .a2: difficulty = 2.0
        case .b1: difficulty = 3.0
        case .b2: difficulty = 4.0
        case .c1: difficulty = 5.0
        case .c2: difficulty = 6.0
        }
        
        // Adjust based on content complexity
        let vocabularyComplexity = Double(lesson.vocabulary.count) / 25.0 // Normalize to expected 25 words
        let conversationComplexity = Double(lesson.conversations.count) / 3.0 // Normalize to expected 3 conversations
        
        difficulty += (vocabularyComplexity + conversationComplexity) * 0.5
        
        return min(difficulty, 10.0) // Cap at 10
    }
    
    private func getAdaptiveRecommendation(for lesson: TamilLesson) async -> AdaptiveRecommendation {
        let userPerformance = calculateUserPerformance()
        let lessonDifficulty = await calculateDifficulty(for: lesson)
        
        if userPerformance > lessonDifficulty + 1.0 {
            return .accelerated
        } else if userPerformance < lessonDifficulty - 1.0 {
            return .reinforcement
        } else {
            return .standard
        }
    }
    
    private func calculateUserPerformance() -> Double {
        // Calculate based on recent completion rate, time spent, etc.
        let recentLessons = Array(completedLessons.suffix(10))
        if recentLessons.isEmpty { return 1.0 }
        
        // Simple performance metric based on completion rate
        let completionRate = Double(recentLessons.count) / 10.0
        return completionRate * 6.0 // Scale to CEFR levels
    }
    
    // MARK: - Progress Tracking
    
    func markLessonCompleted(_ lesson: TamilLesson, studyTime: TimeInterval) async {
        let lessonId = "\(lesson.levelCode)-L\(lesson.lessonNumber)"
        
        // Update completed lessons
        completedLessons.insert(lessonId)
        
        // Update study time
        totalStudyTime += studyTime
        todaysProgress.studyTime += studyTime
        todaysProgress.lessonsCompleted += 1
        
        // Update streak
        updateStreak()
        
        // Check for level progression
        await checkLevelProgression()
        
        // Update learning path
        generateLearningPath()
        
        // Save progress
        saveUserProgress()
        
        // Update content service if available
        await getContentService()?.markLessonCompleted(lesson, studyTime: studyTime)
    }
    
    func markVocabularyMastered(_ vocabulary: TamilVocabulary) async {
        masteredVocabulary.insert(vocabulary.vocabId)
        todaysProgress.vocabularyMastered += 1
        saveUserProgress()
        await getContentService()?.markVocabularyMastered(vocabulary)
    }
    
    private func updateStreak() {
        let today = Calendar.current.startOfDay(for: Date())
        let lastStudyDate = userDefaults.object(forKey: "lastStudyDate") as? Date ?? Date.distantPast
        let lastStudyDay = Calendar.current.startOfDay(for: lastStudyDate)
        
        if Calendar.current.isDate(today, equalTo: lastStudyDay, toGranularity: .day) {
            // Same day, streak continues
            return
        } else if Calendar.current.dateInterval(of: .day, for: lastStudyDay)?.end == today {
            // Consecutive day
            currentStreak += 1
        } else {
            // Streak broken
            currentStreak = 1
        }
        
        userDefaults.set(Date(), forKey: "lastStudyDate")
    }
    
    private func checkLevelProgression() async {
        guard let contentService = await getContentService() else { return }

        let currentLevelLessons = contentService.getLessonsForLevel(currentUserLevel)
        let completedInCurrentLevel = currentLevelLessons.filter { lesson in
            completedLessons.contains("\(lesson.levelCode)-L\(lesson.lessonNumber)")
        }.count

        // Progress to next level if 90% of current level is completed
        let progressionThreshold = max(1, Int(Double(currentLevelLessons.count) * 0.9))

        if completedInCurrentLevel >= progressionThreshold {
            if let nextLevel = getNextLevel() {
                currentUserLevel = nextLevel
                // Trigger celebration or notification
                NotificationCenter.default.post(name: .levelProgression, object: nextLevel)
            }
        }
    }
    
    private func getNextLevel() -> CEFRLevel? {
        let levels = CEFRLevel.allCases
        guard let currentIndex = levels.firstIndex(of: currentUserLevel),
              currentIndex < levels.count - 1 else {
            return nil
        }
        return levels[currentIndex + 1]
    }
    
    // MARK: - Recommendations

    func getNextRecommendedLesson() -> TamilLesson? {
        ensureLearningPathGenerated()
        let unlockedIncomplete = learningPath.filter { $0.isUnlocked && !$0.isCompleted }
        return unlockedIncomplete.first?.lesson
    }

    private func ensureLearningPathGenerated() {
        if learningPath.isEmpty {
            generateLearningPath()
        }
    }
    
    func getReviewLessons() -> [TamilLesson] {
        ensureLearningPathGenerated()
        // Return lessons that might need review based on time since completion
        let _ = Date().addingTimeInterval(-7 * 24 * 60 * 60)

        return learningPath
            .filter { $0.isCompleted }
            .compactMap { node in
                // Simple heuristic: review if completed more than a week ago
                // In a real app, you'd track completion dates
                return node.lesson
            }
            .prefix(3)
            .map { $0 }
    }
    
    // MARK: - Data Persistence
    
    private func saveUserProgress() {
        let progress = UserProgressData(
            currentLevel: currentUserLevel,
            completedLessons: completedLessons,
            masteredVocabulary: masteredVocabulary,
            currentStreak: currentStreak,
            totalStudyTime: totalStudyTime,
            weeklyGoal: weeklyGoal,
            dailyGoal: dailyGoal
        )
        
        if let data = try? JSONEncoder().encode(progress) {
            userDefaults.set(data, forKey: "userProgressData")
        }
    }
    
    private func loadUserProgress() {
        guard let data = userDefaults.data(forKey: "userProgressData"),
              let progress = try? JSONDecoder().decode(UserProgressData.self, from: data) else {
            return
        }

        currentUserLevel = progress.currentLevel
        completedLessons = Set(progress.completedLessons)
        masteredVocabulary = Set(progress.masteredVocabulary)
        currentStreak = progress.currentStreak
        totalStudyTime = progress.totalStudyTime
        weeklyGoal = progress.weeklyGoal
        dailyGoal = progress.dailyGoal
    }
}

// MARK: - Supporting Types

struct LearningPathNode: Identifiable {
    let id: String
    let lesson: TamilLesson
    let level: CEFRLevel
    let isUnlocked: Bool
    let isCompleted: Bool
    let prerequisites: [String]
    let estimatedDifficulty: Double
    let adaptiveRecommendation: AdaptiveRecommendation
}

enum AdaptiveRecommendation {
    case accelerated    // User can handle faster pace
    case standard      // Normal progression
    case reinforcement // Needs more practice
    
    var description: String {
        switch self {
        case .accelerated: return "Ready for challenge"
        case .standard: return "Perfect pace"
        case .reinforcement: return "Take your time"
        }
    }
}

struct DailyProgress {
    var lessonsCompleted: Int = 0
    var vocabularyMastered: Int = 0
    var studyTime: TimeInterval = 0
    var date: Date = Date()
}

struct WeeklyProgress {
    var lessonsCompleted: Int = 0
    var vocabularyMastered: Int = 0
    var studyTime: TimeInterval = 0
    var weekStartDate: Date = Date()
}

struct OverallProgress {
    var totalLessons: Int = 0
    var totalVocabulary: Int = 0
    var totalStudyTime: TimeInterval = 0
    var currentLevel: CEFRLevel = .a1
    var streak: Int = 0
}

struct UserProgressData: Codable {
    let currentLevel: CEFRLevel
    let completedLessons: [String]
    let masteredVocabulary: [String]
    let currentStreak: Int
    let totalStudyTime: TimeInterval
    let weeklyGoal: Int
    let dailyGoal: Int

    init(currentLevel: CEFRLevel, completedLessons: Set<String>, masteredVocabulary: Set<String>, currentStreak: Int, totalStudyTime: TimeInterval, weeklyGoal: Int, dailyGoal: Int) {
        self.currentLevel = currentLevel
        self.completedLessons = Array(completedLessons)
        self.masteredVocabulary = Array(masteredVocabulary)
        self.currentStreak = currentStreak
        self.totalStudyTime = totalStudyTime
        self.weeklyGoal = weeklyGoal
        self.dailyGoal = dailyGoal
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let levelProgression = Notification.Name("levelProgression")
    static let streakAchievement = Notification.Name("streakAchievement")
    static let goalCompleted = Notification.Name("goalCompleted")
}
