import Foundation
import SwiftUI
import Combine

@MainActor
class EnhancedRecommendationEngine: ObservableObject {
    static let shared = EnhancedRecommendationEngine()

    @Published var personalizedRecommendations: [PersonalizedRecommendation] = []
    @Published var reviewRecommendations: [ReviewRecommendation] = []
    @Published var challengeRecommendations: [ChallengeRecommendation] = []
    @Published var socialRecommendations: [SocialRecommendation] = []
    @Published var isGeneratingRecommendations: Bool = false

    private let userPreferencesService = UserPreferencesService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private let curriculumService = CurriculumService.shared
    private var cancellables = Set<AnyCancellable>()

    // Recommendation weights
    private let contentBasedWeight: Double = 0.4
    private let collaborativeWeight: Double = 0.3
    private let performanceBasedWeight: Double = 0.3

    private init() {
        setupSubscriptions()
    }

    // MARK: - Public Methods

    func generateAllRecommendations() async {
        isGeneratingRecommendations = true

        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.generatePersonalizedRecommendations() }
            group.addTask { await self.generateReviewRecommendations() }
            group.addTask { await self.generateChallengeRecommendations() }
            group.addTask { await self.generateSocialRecommendations() }
        }

        await MainActor.run {
            self.isGeneratingRecommendations = false
        }
    }

    func getTopRecommendations(count: Int = 5) -> [AnyRecommendation] {
        var allRecommendations: [AnyRecommendation] = []

        allRecommendations.append(contentsOf: personalizedRecommendations.map { AnyRecommendation.personalized($0) })
        allRecommendations.append(contentsOf: reviewRecommendations.map { AnyRecommendation.review($0) })
        allRecommendations.append(contentsOf: challengeRecommendations.map { AnyRecommendation.challenge($0) })
        allRecommendations.append(contentsOf: socialRecommendations.map { AnyRecommendation.social($0) })

        return Array(allRecommendations.sorted { $0.score > $1.score }.prefix(count))
    }

    func getRecommendationsForSkill(_ skillArea: SkillArea, count: Int = 3) -> [PersonalizedRecommendation] {
        return Array(personalizedRecommendations
            .filter { $0.skillArea == skillArea }
            .sorted { $0.confidenceScore > $1.confidenceScore }
            .prefix(count))
    }

    func getSpacedRepetitionRecommendations() -> [ReviewRecommendation] {
        return reviewRecommendations.filter { $0.reviewType == .spacedRepetition }
    }

    func markRecommendationUsed(_ recommendationId: UUID, outcome: RecommendationOutcome) {
        // Track recommendation effectiveness for future improvements
        recordRecommendationFeedback(recommendationId: recommendationId, outcome: outcome)
    }

    // MARK: - Private Methods

    private func setupSubscriptions() {
        // Regenerate recommendations when user preferences or progress changes
        Publishers.CombineLatest(
            curriculumService.$userSkillLevels,
            analyticsService.$userProgress.map { _ in Date() }
        )
        .debounce(for: .seconds(3), scheduler: RunLoop.main)
        .sink { [weak self] _, _ in
            Task { @MainActor in
                await self?.generateAllRecommendations()
            }
        }
        .store(in: &cancellables)
    }

    private func generatePersonalizedRecommendations() async {
        let contentBased = await generateContentBasedRecommendations()
        let collaborative = await generateCollaborativeRecommendations()
        let performanceBased = await generatePerformanceBasedRecommendations()

        // Combine and weight different recommendation types
        var combinedRecommendations: [PersonalizedRecommendation] = []

        // Merge recommendations with weighted scores
        let allCandidates = Set(contentBased.map { $0.lessonId })
            .union(Set(collaborative.map { $0.lessonId }))
            .union(Set(performanceBased.map { $0.lessonId }))

        for lessonId in allCandidates {
            let contentScore = contentBased.first { $0.lessonId == lessonId }?.confidenceScore ?? 0.0
            let collaborativeScore = collaborative.first { $0.lessonId == lessonId }?.confidenceScore ?? 0.0
            let performanceScore = performanceBased.first { $0.lessonId == lessonId }?.confidenceScore ?? 0.0

            let weightedScore = (contentScore * contentBasedWeight) +
                              (collaborativeScore * collaborativeWeight) +
                              (performanceScore * performanceBasedWeight)

            if let baseRecommendation = contentBased.first(where: { $0.lessonId == lessonId }) ??
                                       collaborative.first(where: { $0.lessonId == lessonId }) ??
                                       performanceBased.first(where: { $0.lessonId == lessonId }) {

                let enhancedRecommendation = PersonalizedRecommendation(
                    lessonId: lessonId,
                    title: baseRecommendation.title,
                    skillArea: baseRecommendation.skillArea,
                    difficulty: baseRecommendation.difficulty,
                    estimatedDuration: baseRecommendation.estimatedDuration,
                    confidenceScore: weightedScore,
                    recommendationReason: generateEnhancedReason(
                        contentScore: contentScore,
                        collaborativeScore: collaborativeScore,
                        performanceScore: performanceScore,
                        skillArea: baseRecommendation.skillArea
                    ),
                    recommendationType: determineRecommendationType(
                        contentScore: contentScore,
                        collaborativeScore: collaborativeScore,
                        performanceScore: performanceScore
                    ),
                    tags: baseRecommendation.tags,
                    prerequisites: baseRecommendation.prerequisites
                )

                combinedRecommendations.append(enhancedRecommendation)
            }
        }

        await MainActor.run {
            self.personalizedRecommendations = Array(combinedRecommendations
                .sorted { $0.confidenceScore > $1.confidenceScore }
                .prefix(10))
        }
    }

    private func generateContentBasedRecommendations() async -> [PersonalizedRecommendation] {
        let _ = userPreferencesService.selectedLanguage
        let userSkillLevels = curriculumService.userSkillLevels

        var recommendations: [PersonalizedRecommendation] = []

        // Get lessons that match user's current skill levels and interests
        for skillArea in SkillArea.allCases {
            let currentLevel = userSkillLevels[skillArea] ?? .beginner
            let targetLevels = [currentLevel, SkillLevel(rawValue: currentLevel.rawValue + 1) ?? currentLevel]

            for targetLevel in targetLevels {
                let lessons = await getLessonsForSkillAndLevel(skillArea: skillArea, level: targetLevel)

                for lesson in lessons {
                    let similarity = calculateContentSimilarity(lesson: lesson, userPreferences: getUserPreferences())

                    if similarity > 0.3 { // Threshold for relevance
                        let recommendation = PersonalizedRecommendation(
                            lessonId: lesson.id,
                            title: lesson.title,
                            skillArea: skillArea,
                            difficulty: targetLevel,
                            estimatedDuration: lesson.estimatedDuration,
                            confidenceScore: similarity,
                            recommendationReason: "Matches your learning preferences in \(skillArea.displayName)",
                            recommendationType: .nextLesson,
                            tags: lesson.topics,
                            prerequisites: lesson.prerequisites
                        )
                        recommendations.append(recommendation)
                    }
                }
            }
        }

        return recommendations
    }

    private func generateCollaborativeRecommendations() async -> [PersonalizedRecommendation] {
        // Find similar users based on learning patterns and preferences
        let similarUsers = await findSimilarUsers()
        var recommendations: [PersonalizedRecommendation] = []

        for similarUser in similarUsers {
            let theirCompletedLessons = await getCompletedLessons(for: similarUser.userId)
            let myCompletedLessons = await getMyCompletedLessons()

            // Find lessons they completed that I haven't
            let candidateLessons = theirCompletedLessons.filter { lesson in
                !myCompletedLessons.contains { $0.id == lesson.id }
            }

            for lesson in candidateLessons {
                let similarity = similarUser.similarityScore
                let performance = await getLessonPerformance(userId: similarUser.userId, lessonId: lesson.id)

                // Weight by similarity and their performance
                let score = similarity * (performance?.overallScore ?? 0.7)

                if score > 0.4 {
                    let recommendation = PersonalizedRecommendation(
                        lessonId: lesson.id,
                        title: lesson.title,
                        skillArea: lesson.skillArea,
                        difficulty: lesson.difficulty,
                        estimatedDuration: lesson.estimatedDuration,
                        confidenceScore: score,
                        recommendationReason: "Recommended by learners with similar progress",
                        recommendationType: .nextLesson,
                        tags: lesson.topics,
                        prerequisites: lesson.prerequisites
                    )
                    recommendations.append(recommendation)
                }
            }
        }

        return recommendations
    }

    private func generatePerformanceBasedRecommendations() async -> [PersonalizedRecommendation] {
        let weakAreas = await identifyWeakAreas()
        var recommendations: [PersonalizedRecommendation] = []

        for weakArea in weakAreas {
            let reinforcementLessons = await getReinforcementLessons(for: weakArea)

            for lesson in reinforcementLessons {
                let urgency = calculateUrgencyScore(for: weakArea)

                let recommendation = PersonalizedRecommendation(
                    lessonId: lesson.id,
                    title: lesson.title,
                    skillArea: weakArea.skillArea,
                    difficulty: lesson.difficulty,
                    estimatedDuration: lesson.estimatedDuration,
                    confidenceScore: urgency,
                    recommendationReason: "Strengthen your \(weakArea.skillArea.displayName) - \(weakArea.specificArea)",
                    recommendationType: .skillReview,
                    tags: lesson.topics,
                    prerequisites: lesson.prerequisites
                )
                recommendations.append(recommendation)
            }
        }

        return recommendations
    }

    private func generateReviewRecommendations() async {
        let spacedRepetition = await generateSpacedRepetitionRecommendations()
        let weakConcepts = await generateWeakConceptReviews()
        let forgettingCurve = await generateForgettingCurveReviews()

        await MainActor.run {
            self.reviewRecommendations = spacedRepetition + weakConcepts + forgettingCurve
        }
    }

    private func generateChallengeRecommendations() async {
        let skillChallenges = await generateSkillChallenges()
        let speedChallenges = await generateSpeedChallenges()
        let accuracyChallenges = await generateAccuracyChallenges()

        await MainActor.run {
            self.challengeRecommendations = skillChallenges + speedChallenges + accuracyChallenges
        }
    }

    private func generateSocialRecommendations() async {
        let friendActivity = await generateFriendActivityRecommendations()
        let groupChallenges = await generateGroupChallengeRecommendations()
        let communityTrends = await generateCommunityTrendRecommendations()

        await MainActor.run {
            self.socialRecommendations = friendActivity + groupChallenges + communityTrends
        }
    }

    // MARK: - Helper Methods

    private func calculateContentSimilarity(lesson: CurriculumLesson, userPreferences: UserLearningPreferences) -> Double {
        var similarity: Double = 0.0

        // Topic similarity
        let topicOverlap = Set(lesson.topics).intersection(Set(userPreferences.preferredTopics))
        similarity += Double(topicOverlap.count) / Double(max(lesson.topics.count, 1)) * 0.4

        // Difficulty preference
        let difficultyMatch = abs(lesson.difficulty.rawValue - userPreferences.preferredDifficulty.rawValue)
        similarity += max(0, 1.0 - Double(difficultyMatch) * 0.2) * 0.3

        // Duration preference
        let durationDiff = abs(lesson.estimatedDuration - userPreferences.preferredSessionLength)
        similarity += max(0, 1.0 - Double(durationDiff) / 30.0) * 0.3

        return min(1.0, similarity)
    }

    private func getUserPreferences() -> UserLearningPreferences {
        // Extract user preferences from their learning history
        return UserLearningPreferences(
            preferredTopics: extractPreferredTopics(),
            preferredDifficulty: extractPreferredDifficulty(),
            preferredSessionLength: userPreferencesService.preferredStudyTime * 60 / 4, // Convert to minutes per session
            learningStyle: extractLearningStyle()
        )
    }

    private func extractPreferredTopics() -> [String] {
        // Analyze completed lessons to find preferred topics
        return ["greetings", "vocabulary", "grammar"] // Placeholder
    }

    private func extractPreferredDifficulty() -> SkillLevel {
        let skillLevels = curriculumService.userSkillLevels.values
        let averageLevel = skillLevels.reduce(0) { $0 + $1.rawValue } / max(skillLevels.count, 1)
        return SkillLevel(rawValue: averageLevel) ?? .beginner
    }

    private func extractLearningStyle() -> LearningStyle {
        // Analyze user behavior to determine learning style
        return .visual // Placeholder
    }

    private func findSimilarUsers() async -> [SimilarUser] {
        // This would use collaborative filtering algorithms
        // For now, return placeholder data
        return [
            SimilarUser(userId: UUID(), similarityScore: 0.8),
            SimilarUser(userId: UUID(), similarityScore: 0.7),
            SimilarUser(userId: UUID(), similarityScore: 0.6)
        ]
    }

    private func identifyWeakAreas() async -> [WeakArea] {
        let userProgress = analyticsService.userProgress.values
        var weakAreas: [WeakArea] = []

        for skillArea in SkillArea.allCases {
            let skillProgress = userProgress.filter { progress in
                // This would check if the progress is related to the skill area
                return true // Placeholder
            }

            let averageScore = skillProgress.reduce(0.0) { $0 + ($1.accuracyScore ?? 0.0) } / Double(max(skillProgress.count, 1))

            if averageScore < 0.7 {
                weakAreas.append(WeakArea(
                    skillArea: skillArea,
                    specificArea: "General \(skillArea.displayName)",
                    weaknessScore: 1.0 - averageScore,
                    lastPracticed: skillProgress.last?.completedAt ?? Date().addingTimeInterval(-86400 * 7)
                ))
            }
        }

        return weakAreas.sorted { $0.weaknessScore > $1.weaknessScore }
    }

    private func generateEnhancedReason(contentScore: Double, collaborativeScore: Double, performanceScore: Double, skillArea: SkillArea) -> String {
        let maxScore = max(contentScore, collaborativeScore, performanceScore)

        if maxScore == contentScore {
            return "Perfect match for your learning style in \(skillArea.displayName)"
        } else if maxScore == collaborativeScore {
            return "Highly rated by similar learners"
        } else {
            return "Targeted practice for your \(skillArea.displayName) improvement"
        }
    }

    private func determineRecommendationType(contentScore: Double, collaborativeScore: Double, performanceScore: Double) -> RecommendationType {
        let maxScore = max(contentScore, collaborativeScore, performanceScore)

        if maxScore == contentScore {
            return .nextLesson
        } else if maxScore == collaborativeScore {
            return .practiceExercise
        } else {
            return .skillReview
        }
    }

    private func recordRecommendationFeedback(recommendationId: UUID, outcome: RecommendationOutcome) {
        // Store feedback for improving future recommendations
        // This would be saved to analytics service or database
    }

    // MARK: - Placeholder Methods (to be implemented with real data)

    private func getLessonsForSkillAndLevel(skillArea: SkillArea, level: SkillLevel) async -> [CurriculumLesson] {
        return [] // Placeholder
    }

    private func getCompletedLessons(for userId: UUID) async -> [CurriculumLesson] {
        return [] // Placeholder
    }

    private func getMyCompletedLessons() async -> [CurriculumLesson] {
        return [] // Placeholder
    }

    private func getLessonPerformance(userId: UUID, lessonId: UUID) async -> LessonPerformance? {
        return nil // Placeholder
    }

    private func getReinforcementLessons(for weakArea: WeakArea) async -> [CurriculumLesson] {
        return [] // Placeholder
    }

    private func calculateUrgencyScore(for weakArea: WeakArea) -> Double {
        let timeSinceLastPractice = Date().timeIntervalSince(weakArea.lastPracticed ?? Date())
        let daysSince = timeSinceLastPractice / 86400

        return min(1.0, weakArea.weaknessScore + (daysSince / 7.0) * 0.1)
    }

    private func generateSpacedRepetitionRecommendations() async -> [ReviewRecommendation] {
        return [] // Placeholder
    }

    private func generateWeakConceptReviews() async -> [ReviewRecommendation] {
        return [] // Placeholder
    }

    private func generateForgettingCurveReviews() async -> [ReviewRecommendation] {
        return [] // Placeholder
    }

    private func generateSkillChallenges() async -> [ChallengeRecommendation] {
        return [] // Placeholder
    }

    private func generateSpeedChallenges() async -> [ChallengeRecommendation] {
        return [] // Placeholder
    }

    private func generateAccuracyChallenges() async -> [ChallengeRecommendation] {
        return [] // Placeholder
    }

    private func generateFriendActivityRecommendations() async -> [SocialRecommendation] {
        return [] // Placeholder
    }

    private func generateGroupChallengeRecommendations() async -> [SocialRecommendation] {
        return [] // Placeholder
    }

    private func generateCommunityTrendRecommendations() async -> [SocialRecommendation] {
        return [] // Placeholder
    }
}

// MARK: - Supporting Models

struct PersonalizedRecommendation: Identifiable {
    let id = UUID()
    let lessonId: UUID
    let title: String
    let skillArea: SkillArea
    let difficulty: SkillLevel
    let estimatedDuration: Int
    let confidenceScore: Double
    let recommendationReason: String
    let recommendationType: RecommendationType
    let tags: [String]
    let prerequisites: [UUID]
}

struct ReviewRecommendation: Identifiable {
    let id = UUID()
    let contentId: UUID
    let title: String
    let reviewType: ReviewType
    let urgencyScore: Double
    let lastReviewed: Date?
    let nextOptimalReview: Date
    let estimatedDuration: Int
}

struct ChallengeRecommendation: Identifiable {
    let id = UUID()
    let challengeId: UUID
    let title: String
    let challengeType: ChallengeType
    let difficulty: SkillLevel
    let estimatedDuration: Int
    let potentialReward: Int
    let participantCount: Int?
}

struct SocialRecommendation: Identifiable {
    let id = UUID()
    let contentId: UUID
    let title: String
    let socialType: SocialType
    let friendsParticipating: [String]
    let popularityScore: Double
    let estimatedDuration: Int
}

// Note: RecommendationType is defined in AdaptiveCurriculumService.swift

enum ReviewType {
    case spacedRepetition
    case weakConcept
    case forgettingCurve
    case comprehensive
}

enum ChallengeType {
    case speed
    case accuracy
    case endurance
    case skill
    case mixed
}

enum SocialType {
    case friendActivity
    case groupChallenge
    case communityTrend
    case leaderboard
}



enum RecommendationOutcome {
    case accepted
    case declined
    case completed
    case abandoned
    case rated(Int) // 1-5 stars
}

struct UserLearningPreferences {
    let preferredTopics: [String]
    let preferredDifficulty: SkillLevel
    let preferredSessionLength: Int
    let learningStyle: LearningStyle
}

struct SimilarUser {
    let userId: UUID
    let similarityScore: Double
}

struct WeakArea {
    let skillArea: SkillArea
    let specificArea: String
    let weaknessScore: Double
    let lastPracticed: Date?
}

enum AnyRecommendation {
    case personalized(PersonalizedRecommendation)
    case review(ReviewRecommendation)
    case challenge(ChallengeRecommendation)
    case social(SocialRecommendation)

    var id: UUID {
        switch self {
        case .personalized(let rec): return rec.id
        case .review(let rec): return rec.id
        case .challenge(let rec): return rec.id
        case .social(let rec): return rec.id
        }
    }

    var title: String {
        switch self {
        case .personalized(let rec): return rec.title
        case .review(let rec): return rec.title
        case .challenge(let rec): return rec.title
        case .social(let rec): return rec.title
        }
    }

    var score: Double {
        switch self {
        case .personalized(let rec): return rec.confidenceScore
        case .review(let rec): return rec.urgencyScore
        case .challenge(let rec): return Double(rec.potentialReward) / 100.0
        case .social(let rec): return rec.popularityScore
        }
    }

    var estimatedDuration: Int {
        switch self {
        case .personalized(let rec): return rec.estimatedDuration
        case .review(let rec): return rec.estimatedDuration
        case .challenge(let rec): return rec.estimatedDuration
        case .social(let rec): return rec.estimatedDuration
        }
    }
}