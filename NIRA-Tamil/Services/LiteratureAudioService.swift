//
//  LiteratureAudioService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 08/07/2025.
//

import Foundation
import AVFoundation
import Supabase

/// Service for generating and managing audio for literature content
/// Uses Google TTS with multiple API keys and stores audio in Supabase
@MainActor
class LiteratureAudioService: ObservableObject {
    static let shared = LiteratureAudioService()
    
    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var statusMessage = ""
    @Published var generatedAudioCount = 0
    @Published var totalAudioToGenerate = 0
    @Published var currentlyPlaying: String?
    @Published var playbackError: String?
    
    // Services
    private let supabaseClient = NIRASupabaseClient.shared
    private let audioPlayerService = AudioPlayerService.shared
    
    // Google TTS Configuration
    private let tamilFemaleVoice = "ta-IN-Chirp3-HD-Erinome"
    private let tamilMaleVoice = "ta-IN-Chirp3-HD-Iapetus"
    private let languageCode = "ta-IN"
    private let audioFormat = "MP3"
    private let speakingRate = 0.9
    private let pitch = 0.0
    
    // Service account management
    private var serviceAccounts: [GoogleServiceAccount] = []
    private var currentAccountIndex = 0
    
    private init() {
        loadServiceAccounts()
    }
    
    // MARK: - Public Methods
    
    /// Generate audio for all literature content
    func generateAllLiteratureAudio() async {
        isGenerating = true
        generationProgress = 0.0
        generatedAudioCount = 0
        statusMessage = "Loading literature content..."
        
        do {
            // Get all literature content from database
            let literatureContent = try await loadAllLiteratureContent()
            totalAudioToGenerate = literatureContent.count * 2 // Both male and female voices
            
            statusMessage = "Generating audio for \(literatureContent.count) literature items..."
            
            // Process content in batches to avoid overwhelming the API
            let batchSize = 3
            for batch in literatureContent.chunked(into: batchSize) {
                await processBatch(batch)
                
                // Delay between batches to respect API limits
                try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            }
            
            statusMessage = "Audio generation completed! Generated \(generatedAudioCount) files."
            
        } catch {
            statusMessage = "Audio generation failed: \(error.localizedDescription)"
            print("❌ Literature audio generation error: \(error)")
        }
        
        isGenerating = false
    }
    
    /// Generate audio for specific literature content
    func generateAudio(for content: LiteratureContent) async throws -> LiteratureAudioResult {
        statusMessage = "Generating audio for: \(content.title)"
        
        // Generate both male and female voice versions
        let femaleAudioData = try await generateAudioData(
            text: content.contentTamil,
            voice: .female
        )
        
        let maleAudioData = try await generateAudioData(
            text: content.contentTamil,
            voice: .male
        )
        
        // Upload to Supabase storage
        let femaleFilename = "literature_\(content.id)_female.mp3"
        let maleFilename = "literature_\(content.id)_male.mp3"
        
        let femaleURL = try await uploadAudioToSupabase(
            audioData: femaleAudioData,
            filename: femaleFilename
        )
        
        let maleURL = try await uploadAudioToSupabase(
            audioData: maleAudioData,
            filename: maleFilename
        )
        
        // Update literature content with audio URLs
        try await updateLiteratureAudioURLs(
            contentId: content.id,
            femaleURL: femaleURL,
            maleURL: maleURL
        )
        
        generatedAudioCount += 2 // Both voices
        
        return LiteratureAudioResult(
            contentId: content.id,
            femaleAudioURL: femaleURL,
            maleAudioURL: maleURL
        )
    }
    
    /// Play audio for literature content
    func playAudio(contentId: UUID, voiceType: VoiceType = .female) async {
        do {
            // Get audio URL from database
            if let audioURL = try await getAudioURL(contentId: contentId, voiceType: voiceType) {
                audioPlayerService.playAudio(from: audioURL)
                currentlyPlaying = audioURL
                playbackError = nil
            } else {
                playbackError = "Audio not found for this content"
                print("❌ No audio found for content: \(contentId)")
            }
        } catch {
            playbackError = "Failed to load audio: \(error.localizedDescription)"
            print("❌ Audio playback error: \(error)")
        }
    }
    
    /// Stop currently playing audio
    func stopAudio() {
        audioPlayerService.stopAudio()
        currentlyPlaying = nil
    }
    
    // MARK: - Private Methods
    
    private func loadServiceAccounts() {
        let keysDirectory = "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys"
        let fileManager = FileManager.default
        
        do {
            let files = try fileManager.contentsOfDirectory(atPath: keysDirectory)
            let jsonFiles = files.filter { $0.hasSuffix(".json") }
            
            for filename in jsonFiles {
                let filePath = "\(keysDirectory)/\(filename)"
                if let data = fileManager.contents(atPath: filePath) {
                    do {
                        let serviceAccount = try JSONDecoder().decode(GoogleServiceAccount.self, from: data)
                        serviceAccounts.append(serviceAccount)
                        print("✅ Loaded service account: \(serviceAccount.projectId)")
                    } catch {
                        print("❌ Failed to decode service account \(filename): \(error)")
                    }
                }
            }
            
            print("📱 Loaded \(serviceAccounts.count) Google TTS service accounts")
            
        } catch {
            print("❌ Failed to load service accounts: \(error)")
        }
    }
    
    private func loadAllLiteratureContent() async throws -> [LiteratureContent] {
        let response: [SupabaseLiteratureContent] = try await supabaseClient.client
            .from("literature_content")
            .select()
            .execute()
            .value
        
        return response.map { supabaseContent in
            LiteratureContent(
                id: supabaseContent.id,
                categoryId: supabaseContent.categoryId,
                title: supabaseContent.title,
                titleTamil: supabaseContent.titleTamil,
                author: supabaseContent.author,
                authorTamil: supabaseContent.authorTamil,
                contentTamil: supabaseContent.contentTamil,
                contentEnglish: supabaseContent.contentEnglish,
                romanization: supabaseContent.romanization,
                culturalContext: supabaseContent.culturalContext,
                historicalSignificance: supabaseContent.historicalSignificance,
                modernRelevance: supabaseContent.modernRelevance,
                difficultyLevel: supabaseContent.difficultyLevel,
                readingTimeMinutes: supabaseContent.readingTimeMinutes,
                audioUrl: supabaseContent.audioUrl,
                audioMaleUrl: supabaseContent.audioMaleUrl,
                audioFemaleUrl: supabaseContent.audioFemaleUrl,
                tags: supabaseContent.tags,
                isFeatured: supabaseContent.isFeatured,
                viewCount: supabaseContent.viewCount
            )
        }
    }
    
    private func processBatch(_ batch: [LiteratureContent]) async {
        for content in batch {
            do {
                _ = try await generateAudio(for: content)
                print("✅ Generated audio for: \(content.title)")
                
                // Update progress
                generationProgress = Double(generatedAudioCount) / Double(totalAudioToGenerate)
                statusMessage = "Generated \(generatedAudioCount)/\(totalAudioToGenerate) audio files"
                
            } catch {
                print("❌ Failed to generate audio for \(content.title): \(error)")
                // Continue with next item
            }
        }
    }
    
    private func generateAudioData(text: String, voice: VoiceType) async throws -> Data {
        guard let serviceAccount = getCurrentServiceAccount() else {
            throw TTSError.noServiceAccount
        }
        
        let voiceName = voice == .female ? tamilFemaleVoice : tamilMaleVoice
        let accessToken = try await generateAccessToken(serviceAccount: serviceAccount)
        
        let url = URL(string: "https://texttospeech.googleapis.com/v1/text:synthesize")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody: [String: Any] = [
            "input": ["text": text],
            "voice": [
                "languageCode": languageCode,
                "name": voiceName
            ],
            "audioConfig": [
                "audioEncoding": audioFormat,
                "speakingRate": speakingRate,
                "pitch": pitch
            ]
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw TTSError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 429 {
                // Rate limited, rotate account and retry
                rotateServiceAccount()
                return try await generateAudioData(text: text, voice: voice)
            }
            throw TTSError.apiError(httpResponse.statusCode)
        }
        
        guard let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let audioContentBase64 = jsonResponse["audioContent"] as? String,
              let audioData = Data(base64Encoded: audioContentBase64) else {
            throw TTSError.invalidAudioData
        }
        
        return audioData
    }
    
    private func getCurrentServiceAccount() -> GoogleServiceAccount? {
        guard !serviceAccounts.isEmpty else { return nil }
        return serviceAccounts[currentAccountIndex]
    }
    
    private func rotateServiceAccount() {
        guard !serviceAccounts.isEmpty else { return }
        currentAccountIndex = (currentAccountIndex + 1) % serviceAccounts.count
        print("🔄 Rotated to service account: \(serviceAccounts[currentAccountIndex].projectId)")
    }

    private func generateAccessToken(serviceAccount: GoogleServiceAccount) async throws -> String {
        // For this implementation, we'll use a simplified approach
        // In production, implement proper JWT signing with the private key
        return "demo_access_token_\(serviceAccount.projectId)"
    }

    private func uploadAudioToSupabase(audioData: Data, filename: String) async throws -> String {
        do {
            // Upload to Supabase storage bucket
            let _ = try await supabaseClient.client.storage
                .from("audio")
                .upload(filename, data: audioData)

            // Get public URL
            let publicURL = try supabaseClient.client.storage
                .from("audio")
                .getPublicURL(path: filename)

            return publicURL.absoluteString

        } catch {
            print("❌ Failed to upload audio to Supabase: \(error)")
            throw TTSError.uploadFailed
        }
    }

    private func updateLiteratureAudioURLs(contentId: UUID, femaleURL: String, maleURL: String) async throws {
        try await supabaseClient.client
            .from("literature_content")
            .update([
                "audio_female_url": femaleURL,
                "audio_male_url": maleURL,
                "audio_url": femaleURL // Default to female voice
            ])
            .eq("id", value: contentId)
            .execute()
    }

    private func getAudioURL(contentId: UUID, voiceType: VoiceType) async throws -> String? {
        let response: [SupabaseLiteratureContent] = try await supabaseClient.client
            .from("literature_content")
            .select("audio_url, audio_female_url, audio_male_url")
            .eq("id", value: contentId)
            .execute()
            .value

        guard let content = response.first else { return nil }

        switch voiceType {
        case .female:
            return content.audioFemaleUrl ?? content.audioUrl
        case .male:
            return content.audioMaleUrl ?? content.audioUrl
        }
    }
}

// MARK: - Supporting Types

struct LiteratureAudioResult {
    let contentId: UUID
    let femaleAudioURL: String
    let maleAudioURL: String
}

struct SupabaseLiteratureContent: Codable {
    let id: UUID
    let categoryId: UUID
    let title: String
    let titleTamil: String
    let author: String?
    let authorTamil: String?
    let contentTamil: String
    let contentEnglish: String?
    let romanization: String?
    let culturalContext: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let difficultyLevel: String
    let readingTimeMinutes: Int
    let audioUrl: String?
    let audioMaleUrl: String?
    let audioFemaleUrl: String?
    let tags: [String]
    let isFeatured: Bool
    let viewCount: Int

    enum CodingKeys: String, CodingKey {
        case id, title, author, tags
        case categoryId = "category_id"
        case titleTamil = "title_tamil"
        case authorTamil = "author_tamil"
        case contentTamil = "content_tamil"
        case contentEnglish = "content_english"
        case romanization
        case culturalContext = "cultural_context"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case difficultyLevel = "difficulty_level"
        case readingTimeMinutes = "reading_time_minutes"
        case audioUrl = "audio_url"
        case audioMaleUrl = "audio_male_url"
        case audioFemaleUrl = "audio_female_url"
        case isFeatured = "is_featured"
        case viewCount = "view_count"
    }
}


