//
//  LiteratureService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import Foundation
import Combine

// MARK: - Literature Models

struct LiteratureCategory: Codable, Identifiable {
    let id: UUID
    let name: String
    let nameTamil: String
    let description: String
    let descriptionTamil: String?
    let emoji: String
    let colorTheme: String
    let sortOrder: Int
    let isActive: Bool
    let contentCount: Int
    
    enum CodingKeys: String, CodingKey {
        case id, name, description, emoji, sortOrder
        case nameTamil = "name_tamil"
        case descriptionTamil = "description_tamil"
        case colorTheme = "color_theme"
        case isActive = "is_active"
        case contentCount = "content_count"
    }
}

struct LiteratureContent: Codable, Identifiable {
    let id: UUID
    let categoryId: UUID
    let title: String
    let titleTamil: String
    let author: String?
    let authorTamil: String?
    let contentTamil: String
    let contentEnglish: String?
    let romanization: String?
    let culturalContext: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let difficultyLevel: String
    let readingTimeMinutes: Int
    let audioUrl: String?
    let audioMaleUrl: String?
    let audioFemaleUrl: String?
    let tags: [String]
    let isFeatured: Bool
    let viewCount: Int
    
    // Computed properties
    var hasAudio: Bool {
        return audioUrl != nil || audioMaleUrl != nil || audioFemaleUrl != nil
    }
    
    var contentPreview: String? {
        if let englishContent = contentEnglish, !englishContent.isEmpty {
            return String(englishContent.prefix(100)) + (englishContent.count > 100 ? "..." : "")
        } else {
            return String(contentTamil.prefix(50)) + (contentTamil.count > 50 ? "..." : "")
        }
    }
    
    // Category reference (will be populated by service)
    var category: LiteratureCategory = LiteratureCategory(
        id: UUID(),
        name: "Unknown",
        nameTamil: "தெரியாத",
        description: "",
        descriptionTamil: nil,
        emoji: "📚",
        colorTheme: "blue",
        sortOrder: 0,
        isActive: true,
        contentCount: 0
    )
    
    enum CodingKeys: String, CodingKey {
        case id, title, author, romanization, tags
        case categoryId = "category_id"
        case titleTamil = "title_tamil"
        case authorTamil = "author_tamil"
        case contentTamil = "content_tamil"
        case contentEnglish = "content_english"
        case culturalContext = "cultural_context"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case difficultyLevel = "difficulty_level"
        case readingTimeMinutes = "reading_time_minutes"
        case audioUrl = "audio_url"
        case audioMaleUrl = "audio_male_url"
        case audioFemaleUrl = "audio_female_url"
        case isFeatured = "is_featured"
        case viewCount = "view_count"
    }
}

// MARK: - Literature Service

@MainActor
class LiteratureService: ObservableObject {
    static let shared = LiteratureService()
    
    @Published var categories: [LiteratureCategory] = []
    @Published var allContent: [LiteratureContent] = []
    @Published var featuredContent: LiteratureContent?
    @Published var recentContent: [LiteratureContent] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let supabaseClient = NIRASupabaseClient.shared
    private let dynamicContentService = DynamicContentService.shared
    private let cachingService = ContentCachingService.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Initialize with empty data, will load from Supabase
    }
    
    // MARK: - Public Methods
    
    func loadContent() async {
        isLoading = true
        error = nil
        
        do {
            // Load categories first
            await loadCategories()
            
            // Then load content
            await loadLiteratureContent()
            
            // Set featured and recent content
            updateFeaturedAndRecentContent()
            
        } catch {
            self.error = error
            print("❌ Error loading literature content: \(error)")
        }
        
        isLoading = false
    }
    
    func getContent(for categoryId: UUID) -> [LiteratureContent] {
        return allContent.filter { $0.categoryId == categoryId }
    }
    
    func getCategory(by id: UUID) -> LiteratureCategory? {
        return categories.first { $0.id == id }
    }
    
    // MARK: - Private Methods
    
    private func loadCategories() async {
        // Check cache first
        if let cachedCategories = cachingService.getCachedContent(forKey: "literature_categories", type: [LiteratureCategory].self) {
            categories = cachedCategories
            return
        }

        let response = try? await supabaseClient.client.from("literature_categories")
            .select("*")
            .eq("is_active", value: true)
            .order("sort_order")
            .execute()

        // Parse response data
        if let response = response,
           let categoriesData = try? JSONDecoder().decode([LiteratureCategory].self, from: response.data) {
            categories = categoriesData

            // Cache the results
            cachingService.cacheContent(categories, forKey: "literature_categories", expiryInterval: 3600)
        } else {
            // Fallback to mock data
            categories = createMockCategories()
        }
    }
    
    private func loadLiteratureContent() async {
        // Check cache first
        if let cachedContent = cachingService.getCachedContent(forKey: "literature_content", type: [LiteratureContent].self) {
            allContent = cachedContent

            // Associate categories with content
            for i in 0..<allContent.count {
                if let category = getCategory(by: allContent[i].categoryId) {
                    allContent[i].category = category
                }
            }
            return
        }

        do {
            let response = try await supabaseClient.client.from("literature_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()

            // Parse response data
            if let contentData = try? JSONDecoder().decode([LiteratureContent].self, from: response.data) {
                allContent = contentData

                // Cache the results
                cachingService.cacheContent(allContent, forKey: "literature_content", expiryInterval: 1800) // 30 minutes
            } else {
                // Fallback to mock data
                allContent = createMockContent()
            }

            // Associate categories with content
            for i in 0..<allContent.count {
                if let category = getCategory(by: allContent[i].categoryId) {
                    allContent[i].category = category
                }
            }

        } catch {
            print("❌ Error loading literature content: \(error)")
            // Fallback to mock data
            allContent = createMockContent()
        }
    }
    
    private func updateFeaturedAndRecentContent() {
        // Set featured content
        featuredContent = allContent.first { $0.isFeatured } ?? allContent.first
        
        // Set recent content (excluding featured)
        recentContent = Array(allContent.filter { !$0.isFeatured }.prefix(5))
    }
    
    // MARK: - Mock Data (temporary until Supabase integration)
    
    private func createMockCategories() -> [LiteratureCategory] {
        return [
            LiteratureCategory(
                id: UUID(uuidString: "11111111-1111-1111-1111-111111111111")!,
                name: "Thirukkural",
                nameTamil: "திருக்குறள்",
                description: "Ancient Tamil wisdom literature by Thiruvalluvar",
                descriptionTamil: "திருவள்ளுவர் அருளிய பழந்தமிழ் நீதி நூல்",
                emoji: "📜",
                colorTheme: "blue",
                sortOrder: 1,
                isActive: true,
                contentCount: 1330
            ),
            LiteratureCategory(
                id: UUID(uuidString: "*************-2222-2222-************")!,
                name: "Bharathiyar Poems",
                nameTamil: "பாரதியார் கவிதைகள்",
                description: "Revolutionary poems by Subramania Bharati",
                descriptionTamil: "சுப்ரமணிய பாரதியின் புரட்சிகர கவிதைகள்",
                emoji: "🎭",
                colorTheme: "orange",
                sortOrder: 2,
                isActive: true,
                contentCount: 150
            ),
            LiteratureCategory(
                id: UUID(uuidString: "*************-3333-3333-************")!,
                name: "Classical Literature",
                nameTamil: "செம்மொழி இலக்கியம்",
                description: "Ancient Tamil classical works and epics",
                descriptionTamil: "பழந்தமிழ் செம்மொழி இலக்கியங்கள்",
                emoji: "🏛️",
                colorTheme: "purple",
                sortOrder: 3,
                isActive: true,
                contentCount: 75
            ),
            LiteratureCategory(
                id: UUID(uuidString: "*************-4444-4444-************")!,
                name: "Modern Literature",
                nameTamil: "நவீன இலக்கியம்",
                description: "Contemporary Tamil literature and poetry",
                descriptionTamil: "நவீன தமிழ் இலக்கியம் மற்றும் கவிதைகள்",
                emoji: "✨",
                colorTheme: "green",
                sortOrder: 4,
                isActive: true,
                contentCount: 200
            )
        ]
    }
    
    private func createMockContent() -> [LiteratureContent] {
        return [
            LiteratureContent(
                id: UUID(),
                categoryId: UUID(uuidString: "11111111-1111-1111-1111-111111111111")!,
                title: "Virtue - Chapter 1",
                titleTamil: "அறத்துப்பால் - அதிகாரம் 1",
                author: "Thiruvalluvar",
                authorTamil: "திருவள்ளுவர்",
                contentTamil: "அகர முதல எழுத்தெல்லாம் ஆதி\nபகவன் முதற்றே உலகு",
                contentEnglish: "A, as its first of letters, every speech maintains;\nThe Primal Deity is first through all the world's domains.",
                romanization: "Agara mudala ezhuththellaam aadhi\nBhagavan mudhatre ulagu",
                culturalContext: "This opening verse establishes the primacy of both language and divinity in Tamil culture",
                historicalSignificance: "Written around 1st century BCE to 5th century CE, represents the pinnacle of Tamil ethical literature",
                modernRelevance: "Emphasizes the importance of education and spiritual foundation in modern life",
                difficultyLevel: "intermediate",
                readingTimeMinutes: 3,
                audioUrl: nil,
                audioMaleUrl: nil,
                audioFemaleUrl: nil,
                tags: ["virtue", "ethics", "wisdom", "spirituality"],
                isFeatured: true,
                viewCount: 1250
            ),
            LiteratureContent(
                id: UUID(),
                categoryId: UUID(uuidString: "*************-2222-2222-************")!,
                title: "Freedom Song",
                titleTamil: "சுதந்திரப் பாடல்",
                author: "Subramania Bharati",
                authorTamil: "சுப்ரமணிய பாரதி",
                contentTamil: "சுதந்திரம் வேண்டும் சுதந்திரம் வேண்டும்\nசுதந்திரம் வேண்டும் நமக்கு",
                contentEnglish: "Freedom we want, freedom we want,\nFreedom we want for us.",
                romanization: "Sudhandhiram vendum sudhandhiram vendum\nSudhandhiram vendum namakku",
                culturalContext: "Written during the Indian independence movement, reflects the Tamil contribution to national freedom struggle",
                historicalSignificance: "Bharati was a key figure in Tamil renaissance and Indian independence movement",
                modernRelevance: "Continues to inspire movements for social justice and human rights",
                difficultyLevel: "intermediate",
                readingTimeMinutes: 4,
                audioUrl: nil,
                audioMaleUrl: nil,
                audioFemaleUrl: nil,
                tags: ["freedom", "independence", "patriotism", "revolution"],
                isFeatured: false,
                viewCount: 890
            )
        ]
    }
}
