import Foundation

@MainActor
class ContentGenerationService: ObservableObject {
    static let shared = ContentGenerationService()

    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var generationStatus = "Ready"

    private let geminiAPIKey = SecureAPIKeys.geminiAPIKey
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"

    private init() {}

    // MARK: - Main Content Generation Methods

    func generateComprehensiveCourse(
        language: String,
        targetLanguage: String = "english",
        levels: [String] = ["A1", "A2", "B1", "B2"],
        lessonsPerLevel: Int = 20
    ) async throws -> GeneratedCourse {

        isGenerating = true
        generationProgress = 0.0
        generationStatus = "Generating course structure..."

        var course = GeneratedCourse(
            language: language,
            targetLanguage: targetLanguage,
            levels: [],
            totalLessons: 0,
            estimatedHours: 0
        )

        let totalSteps = levels.count * lessonsPerLevel
        var currentStep = 0

        for (levelIndex, level) in levels.enumerated() {
            generationStatus = "Generating \(level) level content..."

            let levelContent = try await generateLevelContent(
                language: language,
                level: level,
                lessonsCount: lessonsPerLevel,
                levelIndex: levelIndex
            )

            course.levels.append(levelContent)

            // Update progress
            currentStep += lessonsPerLevel
            generationProgress = Double(currentStep) / Double(totalSteps)
        }

        course.totalLessons = course.levels.reduce(0) { $0 + $1.lessons.count }
        course.estimatedHours = course.totalLessons * 25 / 60 // 25 min per lesson average

        generationStatus = "Course generation complete!"
        isGenerating = false

        return course
    }

    func generateLevelContent(
        language: String,
        level: String,
        lessonsCount: Int,
        levelIndex: Int
    ) async throws -> GeneratedLevel {

        let themes = getThemesForLevel(level: level, levelIndex: levelIndex)
        var lessons: [GeneratedLesson] = []

        for (_, theme) in themes.enumerated() {
            let lessonsForTheme = lessonsCount / themes.count

            for lessonIndex in 0..<lessonsForTheme {
                let lesson = try await generateLesson(
                    language: language,
                    level: level,
                    theme: theme,
                    lessonIndex: lessonIndex,
                    totalInTheme: lessonsForTheme
                )
                lessons.append(lesson)

                // Small delay to avoid API rate limits
                try await Task.sleep(nanoseconds: 500_000_000)
            }
        }

        return GeneratedLevel(
            level: level,
            levelIndex: levelIndex,
            lessons: lessons,
            themes: themes,
            estimatedHours: lessons.count * 25 / 60
        )
    }

    private func generateLesson(
        language: String,
        level: String,
        theme: LearningTheme,
        lessonIndex: Int,
        totalInTheme: Int
    ) async throws -> GeneratedLesson {

        let prompt = createLessonPrompt(
            language: language,
            level: level,
            theme: theme,
            lessonIndex: lessonIndex,
            totalInTheme: totalInTheme
        )

        let response = try await callGeminiAPI(prompt: prompt)
        let lesson = try parseLessonResponse(response)

        return lesson
    }

    // MARK: - Prompt Engineering

    private func createLessonPrompt(
        language: String,
        level: String,
        theme: LearningTheme,
        lessonIndex: Int,
        totalInTheme: Int
    ) -> String {

        let difficultyGuidelines = getDifficultyGuidelines(level: level)
        let exerciseTypes = getExerciseTypesForLevel(level: level)

        return """
        Create a comprehensive \(language) language lesson for \(level) level learners.

        LESSON CONTEXT:
        - Target Language: \(language)
        - Proficiency Level: \(level)
        - Theme: \(theme.name)
        - Lesson: \(lessonIndex + 1) of \(totalInTheme) in this theme
        - Cultural Focus: \(theme.culturalFocus)

        DIFFICULTY GUIDELINES:
        \(difficultyGuidelines)

        REQUIRED LESSON STRUCTURE:
        {
          "title": "Engaging lesson title in English",
          "description": "Brief description of what students will learn",
          "estimatedDuration": 20-30 minutes,
          "vocabulary": [
            {
              "word": "word in \(language)",
              "translation": "English translation",
              "pronunciation": "phonetic pronunciation",
              "partOfSpeech": "noun/verb/adjective/etc",
              "example": "example sentence in \(language)",
              "exampleTranslation": "English translation of example"
            }
            // Include 8-12 vocabulary items
          ],
          "grammarPoints": [
            {
              "rule": "Grammar rule name",
              "explanation": "Clear explanation suitable for \(level) level",
              "examples": ["example 1", "example 2", "example 3"],
              "tips": "Helpful learning tip"
            }
            // Include 2-3 grammar points
          ],
          "culturalContext": {
            "scenario": "Real-world scenario description",
            "setting": "Where this would be used",
            "culturalNotes": "Important cultural insights",
            "doAndDonts": ["Do: advice", "Don't: warning"]
          },
          "dialogues": [
            {
              "speaker": "Speaker name",
              "text": "Text in \(language)",
              "translation": "English translation",
              "culturalNote": "Optional cultural insight"
            }
            // Include 6-10 dialogue exchanges
          ],
          "exercises": [
            \(exerciseTypes.map { createExercisePrompt(type: $0, language: language, level: level) }.joined(separator: ",\n"))
          ]
        }

        IMPORTANT REQUIREMENTS:
        1. All content must be culturally appropriate and authentic
        2. Vocabulary should be practical and immediately useful
        3. Grammar explanations should be clear and concise
        4. Exercises should progress from easy to challenging
        5. Include real-world scenarios and cultural context
        6. Ensure content is suitable for \(level) proficiency level
        7. Make it engaging and fun to learn
        8. Include pronunciation guides for all vocabulary

        Return ONLY valid JSON matching the structure above.
        """
    }

    private func createExercisePrompt(type: ExerciseType, language: String, level: String) -> String {
        switch type {
        case .multipleChoice:
            return """
            {
              "type": "multiple_choice",
              "question": "Question testing vocabulary or grammar",
              "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
              "correctAnswer": 0,
              "explanation": "Why this answer is correct",
              "points": 100,
              "hints": ["Helpful hint"]
            }
            """
        case .fillInBlank:
            return """
            {
              "type": "fill_in_blank",
              "question": "Sentence with ____ blank to fill",
              "correctAnswer": "correct word or phrase",
              "options": ["word1", "word2", "word3"],
              "explanation": "Grammar or vocabulary explanation",
              "points": 120,
              "hints": ["Context clue"]
            }
            """
        case .matching:
            return """
            {
              "type": "matching",
              "question": "Match the \(language) words with their English meanings",
              "pairs": [
                {"left": "\(language) word", "right": "English meaning"},
                {"left": "\(language) word", "right": "English meaning"}
              ],
              "explanation": "Vocabulary reinforcement",
              "points": 150,
              "hints": ["Think about context"]
            }
            """
        case .pronunciation:
            return """
            {
              "type": "pronunciation",
              "question": "Practice pronouncing this phrase",
              "targetPhrase": "Phrase in \(language)",
              "phonetic": "Phonetic pronunciation",
              "audioHint": "Pronunciation tip",
              "points": 80,
              "hints": ["Focus on specific sounds"]
            }
            """
        case .listening:
            return """
            {
              "type": "listening",
              "question": "Listen and type what you hear",
              "audioText": "Text that would be spoken",
              "correctAnswer": "exact text",
              "points": 130,
              "hints": ["Listen for key words"]
            }
            """
        default:
            return """
            {
              "type": "multiple_choice",
              "question": "Default question",
              "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
              "correctAnswer": 0,
              "explanation": "Explanation",
              "points": 100
            }
            """
        }
    }

    // MARK: - Configuration Helpers

    private func getThemesForLevel(level: String, levelIndex: Int) -> [LearningTheme] {
        switch level {
        case "A1":
            return [
                LearningTheme(name: "Basic Greetings & Introductions", culturalFocus: "Polite society interactions"),
                LearningTheme(name: "Family & Personal Information", culturalFocus: "Family structures and relationships"),
                LearningTheme(name: "Food & Dining", culturalFocus: "Dining etiquette and food culture"),
                LearningTheme(name: "Shopping & Numbers", culturalFocus: "Market culture and bargaining"),
                LearningTheme(name: "Time & Daily Routines", culturalFocus: "Work-life balance norms")
            ]
        case "A2":
            return [
                LearningTheme(name: "Travel & Transportation", culturalFocus: "Public transport and travel customs"),
                LearningTheme(name: "Health & Body", culturalFocus: "Healthcare systems and wellness"),
                LearningTheme(name: "Work & Professions", culturalFocus: "Professional culture and etiquette"),
                LearningTheme(name: "Hobbies & Entertainment", culturalFocus: "Leisure activities and social life"),
                LearningTheme(name: "Weather & Seasons", culturalFocus: "Climate impact on culture")
            ]
        case "B1":
            return [
                LearningTheme(name: "Education & Learning", culturalFocus: "Educational systems and values"),
                LearningTheme(name: "Technology & Communication", culturalFocus: "Digital culture and etiquette"),
                LearningTheme(name: "Environment & Nature", culturalFocus: "Environmental awareness and policies"),
                LearningTheme(name: "Sports & Fitness", culturalFocus: "Sports culture and community"),
                LearningTheme(name: "Arts & Culture", culturalFocus: "Creative expression and traditions")
            ]
        case "B2":
            return [
                LearningTheme(name: "Business & Economics", culturalFocus: "Business culture and practices"),
                LearningTheme(name: "Politics & Society", culturalFocus: "Civic engagement and social issues"),
                LearningTheme(name: "Science & Innovation", culturalFocus: "Research culture and progress"),
                LearningTheme(name: "History & Traditions", culturalFocus: "Cultural heritage and identity"),
                LearningTheme(name: "Global Issues", culturalFocus: "International perspectives")
            ]
        default:
            return [LearningTheme(name: "General Topics", culturalFocus: "General cultural awareness")]
        }
    }

    private func getDifficultyGuidelines(level: String) -> String {
        switch level {
        case "A1":
            return """
            - Use present tense primarily
            - Simple sentence structures
            - Common, high-frequency vocabulary
            - Concrete, everyday topics
            - Clear, direct questions
            """
        case "A2":
            return """
            - Introduce past and future tenses
            - Slightly more complex sentences
            - Expand vocabulary to 1000+ words
            - Personal experiences and opinions
            - Simple comparisons
            """
        case "B1":
            return """
            - All major tenses and moods
            - Complex sentence structures
            - Abstract concepts introduction
            - Express opinions and preferences
            - Handle unexpected situations
            """
        case "B2":
            return """
            - Advanced grammar structures
            - Nuanced vocabulary and expressions
            - Complex topics and abstract ideas
            - Argue a point effectively
            - Understand implicit meaning
            """
        default:
            return "Intermediate level content"
        }
    }

    private func getExerciseTypesForLevel(level: String) -> [ExerciseType] {
        switch level {
        case "A1":
            return [.multipleChoice, .fillInBlank, .matching, .pronunciation]
        case "A2":
            return [.multipleChoice, .fillInBlank, .matching, .pronunciation, .listening]
        case "B1":
            return [.multipleChoice, .fillInBlank, .matching, .pronunciation, .listening, .dragAndDrop]
        case "B2":
            return [.multipleChoice, .fillInBlank, .matching, .pronunciation, .listening, .dragAndDrop, .translation]
        default:
            return [.multipleChoice, .fillInBlank]
        }
    }

    // MARK: - API Communication

    private func callGeminiAPI(prompt: String) async throws -> String {
        guard !geminiAPIKey.isEmpty && !geminiAPIKey.contains("PLACEHOLDER") else {
            print("⚠️ No Gemini API key configured - Content generation disabled")
            throw ContentGenerationError.invalidURL
        }

        guard let url = URL(string: "\(baseURL)?key=\(geminiAPIKey)") else {
            throw ContentGenerationError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.8,
                "maxOutputTokens": 8192
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ContentGenerationError.invalidResponse
        }

        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
           let candidates = json["candidates"] as? [[String: Any]],
           let firstCandidate = candidates.first,
           let content = firstCandidate["content"] as? [String: Any],
           let parts = content["parts"] as? [[String: Any]],
           let firstPart = parts.first,
           let text = firstPart["text"] as? String {
            return text
        }

        throw ContentGenerationError.invalidResponse
    }

    private func parseLessonResponse(_ response: String) throws -> GeneratedLesson {
        // Clean the response to extract JSON
        let cleanedResponse = response
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        guard let data = cleanedResponse.data(using: .utf8) else {
            throw ContentGenerationError.invalidJSON
        }

        do {
            let lesson = try JSONDecoder().decode(GeneratedLesson.self, from: data)
            return lesson
        } catch {
            print("JSON parsing error: \(error)")
            print("Response: \(cleanedResponse)")
            throw ContentGenerationError.invalidJSON
        }
    }
}

// MARK: - Supporting Models

struct GeneratedCourse: Codable {
    var language: String
    var targetLanguage: String
    var levels: [GeneratedLevel]
    var totalLessons: Int
    var estimatedHours: Int
}

struct GeneratedLevel: Codable {
    let level: String
    let levelIndex: Int
    let lessons: [GeneratedLesson]
    let themes: [LearningTheme]
    let estimatedHours: Int
}

struct LearningTheme: Codable {
    let name: String
    let culturalFocus: String
}

// MARK: - Error Types

enum ContentGenerationError: LocalizedError {
    case invalidURL
    case invalidResponse
    case invalidJSON
    case apiLimitReached

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid response from API"
        case .invalidJSON:
            return "Could not parse lesson content"
        case .apiLimitReached:
            return "API rate limit reached"
        }
    }
}