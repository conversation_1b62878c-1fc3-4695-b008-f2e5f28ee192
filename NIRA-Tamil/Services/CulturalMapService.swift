//
//  CulturalMapService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import Foundation
import Combine
import CoreLocation

// MARK: - Cultural Location Model

struct CulturalLocation: Codable, Identifiable {
    let id: UUID
    let name: String
    let nameTamil: String
    let locationType: String
    let description: String
    let descriptionTamil: String?
    let historicalSignificance: String?
    let culturalImportance: String?
    let architecturalStyle: String?
    let bestTimeToVisit: String?
    let visitingHours: String?
    let entryFee: String?
    let facilities: [String]
    let latitude: Double
    let longitude: Double
    let address: String?
    let city: String
    let state: String
    let country: String
    let audioGuideUrl: String?
    let imageUrls: [String]
    let virtualTourUrl: String?
    let romanization: String?
    let tags: [String]
    let difficultyLevel: String
    let isFeatured: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, description, latitude, longitude, address, city, state, country, tags
        case nameTamil = "name_tamil"
        case locationType = "location_type"
        case descriptionTamil = "description_tamil"
        case historicalSignificance = "historical_significance"
        case culturalImportance = "cultural_importance"
        case architecturalStyle = "architectural_style"
        case bestTimeToVisit = "best_time_to_visit"
        case visitingHours = "visiting_hours"
        case entryFee = "entry_fee"
        case facilities
        case audioGuideUrl = "audio_guide_url"
        case imageUrls = "image_urls"
        case virtualTourUrl = "virtual_tour_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

// MARK: - Cultural Map Service

@MainActor
class CulturalMapService: ObservableObject {
    static let shared = CulturalMapService()
    
    @Published var allLocations: [CulturalLocation] = []
    @Published var filteredLocations: [CulturalLocation] = []
    @Published var selectedLocationTypeFilter: String = "All" {
        didSet {
            filterLocations()
        }
    }
    @Published var isLoading = false
    @Published var error: Error?
    
    private let supabaseClient = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Initialize with empty data, will load from Supabase
    }
    
    // MARK: - Public Methods
    
    func loadLocations() async {
        isLoading = true
        error = nil
        
        do {
            let response = try await supabaseClient.client.from("cultural_locations")
                .select("*")
                .order("is_featured", ascending: false)
                .order("name")
                .execute()
            
            // For now, use mock data until Supabase integration is complete
            allLocations = createMockLocations()
            filterLocations()
            
        } catch {
            self.error = error
            print("❌ Error loading cultural locations: \(error)")
            // Fallback to mock data
            allLocations = createMockLocations()
            filterLocations()
        }
        
        isLoading = false
    }
    
    func getLocations(by type: String) -> [CulturalLocation] {
        if type.lowercased() == "all" {
            return allLocations
        }
        return allLocations.filter { $0.locationType.lowercased() == type.lowercased().replacingOccurrences(of: " ", with: "_") }
    }
    
    func getFeaturedLocations() -> [CulturalLocation] {
        return allLocations.filter { $0.isFeatured }
    }
    
    func getNearbyLocations(to coordinate: CLLocationCoordinate2D, within radius: Double = 50.0) -> [CulturalLocation] {
        return allLocations.filter { location in
            let locationCoordinate = CLLocationCoordinate2D(latitude: location.latitude, longitude: location.longitude)
            let distance = coordinate.distance(to: locationCoordinate)
            return distance <= radius * 1000 // Convert km to meters
        }
    }
    
    // MARK: - Private Methods
    
    private func filterLocations() {
        filteredLocations = getLocations(by: selectedLocationTypeFilter)
    }
    
    // MARK: - Mock Data (temporary until Supabase integration)
    
    private func createMockLocations() -> [CulturalLocation] {
        return [
            CulturalLocation(
                id: UUID(),
                name: "Meenakshi Amman Temple",
                nameTamil: "மீனாட்சி அம்மன் கோயில்",
                locationType: "temple",
                description: "Historic Hindu temple dedicated to Goddess Meenakshi, famous for its stunning architecture and cultural significance.",
                descriptionTamil: "மீனாட்சி தேவிக்கு அர்ப்பணிக்கப்பட்ட வரலாற்று சிறப்புமிக்க இந்து கோயில்.",
                historicalSignificance: "Built in the 6th century, represents the pinnacle of Dravidian architecture",
                culturalImportance: "Major pilgrimage site and cultural center of Tamil Nadu",
                architecturalStyle: "Dravidian",
                bestTimeToVisit: "October to March",
                visitingHours: "5:00 AM - 12:30 PM, 4:00 PM - 9:30 PM",
                entryFee: "Free",
                facilities: ["Parking", "Guided Tours", "Photography", "Wheelchair Access"],
                latitude: 9.9195,
                longitude: 78.1193,
                address: "Madurai Main, Madurai",
                city: "Madurai",
                state: "Tamil Nadu",
                country: "India",
                audioGuideUrl: nil,
                imageUrls: [],
                virtualTourUrl: nil,
                romanization: "Meenakshi Amman Kovil",
                tags: ["temple", "architecture", "pilgrimage", "culture"],
                difficultyLevel: "beginner",
                isFeatured: true
            ),
            CulturalLocation(
                id: UUID(),
                name: "Thanjavur Brihadeeswarar Temple",
                nameTamil: "தஞ்சாவூர் பெரிய கோயில்",
                locationType: "temple",
                description: "UNESCO World Heritage Site, masterpiece of Chola architecture dedicated to Lord Shiva.",
                descriptionTamil: "யுனெஸ்கோ உலக பாரம்பரிய தளம், சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்ட சோழர் கட்டிடக்கலையின் தலைசிறந்த படைப்பு.",
                historicalSignificance: "Built by Raja Raja Chola I in 1010 CE, showcases advanced engineering and artistry",
                culturalImportance: "Symbol of Tamil architectural excellence and Chola dynasty legacy",
                architecturalStyle: "Chola",
                bestTimeToVisit: "November to February",
                visitingHours: "6:00 AM - 12:30 PM, 4:00 PM - 8:30 PM",
                entryFee: "₹30 for Indians, ₹500 for foreigners",
                facilities: ["Museum", "Parking", "Guided Tours", "Audio Guide"],
                latitude: 10.7825,
                longitude: 79.1317,
                address: "Thanjavur Fort, Thanjavur",
                city: "Thanjavur",
                state: "Tamil Nadu",
                country: "India",
                audioGuideUrl: nil,
                imageUrls: [],
                virtualTourUrl: nil,
                romanization: "Thanjavur Periya Kovil",
                tags: ["unesco", "chola", "architecture", "heritage"],
                difficultyLevel: "intermediate",
                isFeatured: true
            ),
            CulturalLocation(
                id: UUID(),
                name: "Government Museum Chennai",
                nameTamil: "சென்னை அரசு அருங்காட்சியகம்",
                locationType: "museum",
                description: "Second oldest museum in India, housing extensive collections of Tamil art, archaeology, and natural history.",
                descriptionTamil: "இந்தியாவின் இரண்டாவது பழமையான அருங்காட்சியகம், தமிழ் கலை, தொல்லியல் மற்றும் இயற்கை வரலாற்று சேகரிப்புகளைக் கொண்டது.",
                historicalSignificance: "Established in 1851, preserves Tamil cultural heritage and historical artifacts",
                culturalImportance: "Premier institution for Tamil cultural education and research",
                architecturalStyle: "Indo-Saracenic",
                bestTimeToVisit: "Year round",
                visitingHours: "9:30 AM - 5:00 PM (Closed on Fridays)",
                entryFee: "₹15 for adults, ₹10 for children",
                facilities: ["Library", "Research Center", "Gift Shop", "Cafeteria"],
                latitude: 13.0827,
                longitude: 80.2707,
                address: "Pantheon Road, Egmore, Chennai",
                city: "Chennai",
                state: "Tamil Nadu",
                country: "India",
                audioGuideUrl: nil,
                imageUrls: [],
                virtualTourUrl: nil,
                romanization: "Chennai Arasu Arungaatchi Agam",
                tags: ["museum", "heritage", "education", "artifacts"],
                difficultyLevel: "beginner",
                isFeatured: true
            ),
            CulturalLocation(
                id: UUID(),
                name: "Mahabalipuram Shore Temple",
                nameTamil: "மகாபலிபுரம் கடற்கரை கோயில்",
                locationType: "heritage_site",
                description: "Ancient stone temple complex by the sea, UNESCO World Heritage Site showcasing Pallava architecture.",
                descriptionTamil: "கடற்கரையில் அமைந்த பழங்கால கல் கோயில் வளாகம், பல்லவர் கட்டிடக்கலையை வெளிப்படுத்தும் யுனெஸ்கோ உலக பாரம்பரிய தளம்.",
                historicalSignificance: "Built in 8th century CE by Pallava dynasty, represents early Dravidian temple architecture",
                culturalImportance: "Iconic symbol of Tamil Nadu's ancient maritime heritage",
                architecturalStyle: "Pallava",
                bestTimeToVisit: "October to March",
                visitingHours: "6:00 AM - 6:00 PM",
                entryFee: "₹40 for Indians, ₹600 for foreigners",
                facilities: ["Beach Access", "Parking", "Photography", "Souvenir Shops"],
                latitude: 12.6208,
                longitude: 80.1982,
                address: "Mahabalipuram, Chengalpattu District",
                city: "Mahabalipuram",
                state: "Tamil Nadu",
                country: "India",
                audioGuideUrl: nil,
                imageUrls: [],
                virtualTourUrl: nil,
                romanization: "Mahabalipuram Kadarkarai Kovil",
                tags: ["unesco", "pallava", "beach", "heritage"],
                difficultyLevel: "beginner",
                isFeatured: false
            ),
            CulturalLocation(
                id: UUID(),
                name: "Kanchipuram Ekambareswarar Temple",
                nameTamil: "காஞ்சிபுரம் ஏகாம்பரேஸ்வரர் கோயில்",
                locationType: "temple",
                description: "One of the largest temples in Tamil Nadu, dedicated to Lord Shiva, famous for its ancient mango tree.",
                descriptionTamil: "தமிழ்நாட்டின் மிகப்பெரிய கோயில்களில் ஒன்று, சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்டது, பழங்கால மாமரத்திற்கு பிரசித்தம்.",
                historicalSignificance: "Ancient temple with references dating back to 600 CE, represents various architectural periods",
                culturalImportance: "One of the Pancha Bhoota Sthalams representing the element Earth",
                architecturalStyle: "Dravidian",
                bestTimeToVisit: "November to February",
                visitingHours: "5:30 AM - 12:00 PM, 4:00 PM - 9:00 PM",
                entryFee: "Free",
                facilities: ["Parking", "Prasadam Counter", "Accommodation", "Guided Tours"],
                latitude: 12.8342,
                longitude: 79.7036,
                address: "Ekambareswarar Koil Street, Kanchipuram",
                city: "Kanchipuram",
                state: "Tamil Nadu",
                country: "India",
                audioGuideUrl: nil,
                imageUrls: [],
                virtualTourUrl: nil,
                romanization: "Kanchipuram Ekambareswarar Kovil",
                tags: ["temple", "pancha_bhoota", "ancient", "pilgrimage"],
                difficultyLevel: "intermediate",
                isFeatured: false
            )
        ]
    }
}

// MARK: - CLLocationCoordinate2D Extension

extension CLLocationCoordinate2D {
    func distance(to coordinate: CLLocationCoordinate2D) -> Double {
        let location1 = CLLocation(latitude: self.latitude, longitude: self.longitude)
        let location2 = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        return location1.distance(from: location2)
    }
}
