import Foundation
import SwiftUI
import Combine

@MainActor
class UserPreferencesService: ObservableObject {
    static let shared = UserPreferencesService()

    // Tamil-only app - no language selection needed
    let nativeLanguage: Language = .english // Interface language (English)
    let selectedLanguage: Language = .tamil // Target language (Tamil - fixed)
    @Published var dailyGoal: Int = 3
    @Published var weeklyGoal: Int = 21
    @Published var preferredStudyTime: Int = 9 // 9 AM default
    @Published var notificationsEnabled: Bool = true

    private let userDefaults = UserDefaults.standard
    private var cancellables = Set<AnyCancellable>()

    // Keys for UserDefaults (removed language keys since they're fixed)
    private enum Keys {
        static let dailyGoal = "dailyGoal"
        static let weeklyGoal = "weeklyGoal"
        static let preferredStudyTime = "preferredStudyTime"
        static let notificationsEnabled = "notificationsEnabled"
    }

    private init() {
        loadPreferences()
        setupAutoSave()
    }

    // MARK: - Public Methods

    // Language methods removed - Tamil is fixed as target language
    // Interface is English, learning language is Tamil

    func updateDailyGoal(_ goal: Int) {
        dailyGoal = max(1, min(goal, 10)) // Limit between 1-10 lessons
        savePreferences()
    }

    func updateWeeklyGoal(_ goal: Int) {
        weeklyGoal = max(7, min(goal, 70)) // Limit between 7-70 lessons
        savePreferences()
    }

    func getTodaysProgress() -> (completed: Int, goal: Int) {
        let today = Calendar.current.startOfDay(for: Date())
        let _ = Calendar.current.date(byAdding: .day, value: 1, to: today)!

        // This would typically query the database for today's completed lessons
        // For now, we'll use a placeholder that can be replaced with real data
        let completedToday = getTodaysCompletedLessons()

        return (completed: completedToday, goal: dailyGoal)
    }

    func getWeeklyProgress() -> (completed: Int, goal: Int) {
        let calendar = Calendar.current
        let today = Date()
        let weekday = calendar.component(.weekday, from: today)
        let daysFromMonday = (weekday + 5) % 7 // Convert to Monday = 0
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: today)!
        let startOfWeekDay = calendar.startOfDay(for: startOfWeek)

        let completedThisWeek = getWeeklyCompletedLessons(since: startOfWeekDay)

        return (completed: completedThisWeek, goal: weeklyGoal)
    }

    func getLanguageSpecificProgress() -> (completed: Int, total: Int) {
        // Get progress for the currently selected language
        let languageProgress = getProgressForLanguage(selectedLanguage)
        return languageProgress
    }

    func getTodaysLessons() -> [LessonSummary] {
        // Return recommended lessons for today based on selected language
        return getRecommendedLessonsForLanguage(selectedLanguage)
    }

    func getAvailableLessonsCount(for language: Language) -> Int {
        // This would query the lesson database for available lessons in the language
        // Placeholder implementation
        switch language {
        case .french: return 45
        case .spanish: return 38
        case .english: return 52
        case .japanese: return 28
        case .tamil: return 15
        case .korean: return 35
        case .italian: return 42
        case .german: return 48
        case .hindi: return 25
        case .chinese: return 40
        case .portuguese: return 36
        case .telugu: return 18
        case .vietnamese: return 32
        case .indonesian: return 28
        case .arabic: return 45
        // New 10 languages
        case .kannada: return 60
        case .malayalam: return 60
        case .bengali: return 60
        case .marathi: return 60
        case .punjabi: return 60
        case .dutch: return 60
        case .swedish: return 60
        case .thai: return 60
        case .russian: return 60
        case .norwegian: return 60
        // Additional 25 languages
        case .gujarati: return 60
        case .odia: return 60
        case .assamese: return 60
        case .konkani: return 60
        case .sindhi: return 60
        case .bhojpuri: return 60
        case .maithili: return 60
        case .swahili: return 60
        case .hebrew: return 60
        case .greek: return 60
        case .turkish: return 60
        case .farsi: return 60
        case .tagalog: return 60
        case .ukrainian: return 60
        case .danish: return 60
        case .xhosa: return 60
        case .zulu: return 60
        case .amharic: return 60
        case .quechua: return 60
        case .maori: return 60
        case .cherokee: return 60
        case .navajo: return 60
        case .hawaiian: return 60
        case .inuktitut: return 60
        case .yoruba: return 60
        // Additional languages to complete the 50-language expansion
        case .urdu: return 60
        case .polish: return 60
        case .czech: return 60
        case .hungarian: return 60
        case .romanian: return 60
        case .bulgarian: return 60
        case .croatian: return 60
        case .serbian: return 60
        case .slovak: return 60
        case .slovenian: return 60
        case .estonian: return 60
        case .latvian: return 60
        case .lithuanian: return 60
        case .maltese: return 60
        case .irish: return 60
        case .welsh: return 60
        case .scots: return 60
        case .manx: return 60
        case .cornish: return 60
        case .breton: return 60
        case .basque: return 60
        case .catalan: return 60
        case .galician: return 60
        }
    }

    func getUserProgressForLanguage(_ language: Language) -> Double {
        // Return progress percentage for the language (0.0 to 1.0)
        // Placeholder implementation
        switch language {
        case .french: return 0.35
        case .spanish: return 0.12
        case .english: return 0.78
        case .japanese: return 0.05
        case .tamil: return 0.0
        case .korean: return 0.0
        case .italian: return 0.0
        case .german: return 0.0
        case .hindi: return 0.0
        case .chinese: return 0.0
        case .portuguese: return 0.0
        case .telugu: return 0.0
        case .vietnamese: return 0.0
        case .indonesian: return 0.0
        case .arabic: return 0.0
        // New 10 languages
        case .kannada: return 0.0
        case .malayalam: return 0.0
        case .bengali: return 0.0
        case .marathi: return 0.0
        case .punjabi: return 0.0
        case .dutch: return 0.0
        case .swedish: return 0.0
        case .thai: return 0.0
        case .russian: return 0.0
        case .norwegian: return 0.0
        // Additional 25 languages
        case .gujarati: return 0.0
        case .odia: return 0.0
        case .assamese: return 0.0
        case .konkani: return 0.0
        case .sindhi: return 0.0
        case .bhojpuri: return 0.0
        case .maithili: return 0.0
        case .swahili: return 0.0
        case .hebrew: return 0.0
        case .greek: return 0.0
        case .turkish: return 0.0
        case .farsi: return 0.0
        case .tagalog: return 0.0
        case .ukrainian: return 0.0
        case .danish: return 0.0
        case .xhosa: return 0.0
        case .zulu: return 0.0
        case .amharic: return 0.0
        case .quechua: return 0.0
        case .maori: return 0.0
        case .cherokee: return 0.0
        case .navajo: return 0.0
        case .hawaiian: return 0.0
        case .inuktitut: return 0.0
        case .yoruba: return 0.0
        // Additional languages to complete the 50-language expansion
        case .urdu: return 0.0
        case .polish: return 0.0
        case .czech: return 0.0
        case .hungarian: return 0.0
        case .romanian: return 0.0
        case .bulgarian: return 0.0
        case .croatian: return 0.0
        case .serbian: return 0.0
        case .slovak: return 0.0
        case .slovenian: return 0.0
        case .estonian: return 0.0
        case .latvian: return 0.0
        case .lithuanian: return 0.0
        case .maltese: return 0.0
        case .irish: return 0.0
        case .welsh: return 0.0
        case .scots: return 0.0
        case .manx: return 0.0
        case .cornish: return 0.0
        case .breton: return 0.0
        case .basque: return 0.0
        case .catalan: return 0.0
        case .galician: return 0.0
        }
    }

    // MARK: - Private Methods

    private func loadPreferences() {
        // Languages are fixed - only load other preferences
        let savedDailyGoal = userDefaults.integer(forKey: Keys.dailyGoal)
        if savedDailyGoal > 0 {
            dailyGoal = savedDailyGoal
        }

        let savedWeeklyGoal = userDefaults.integer(forKey: Keys.weeklyGoal)
        if savedWeeklyGoal > 0 {
            weeklyGoal = savedWeeklyGoal
        }

        let savedStudyTime = userDefaults.integer(forKey: Keys.preferredStudyTime)
        if savedStudyTime > 0 {
            preferredStudyTime = savedStudyTime
        }

        notificationsEnabled = userDefaults.bool(forKey: Keys.notificationsEnabled)
    }

    private func savePreferences() {
        // Languages are fixed - only save other preferences
        userDefaults.set(dailyGoal, forKey: Keys.dailyGoal)
        userDefaults.set(weeklyGoal, forKey: Keys.weeklyGoal)
        userDefaults.set(preferredStudyTime, forKey: Keys.preferredStudyTime)
        userDefaults.set(notificationsEnabled, forKey: Keys.notificationsEnabled)
    }

    private func setupAutoSave() {
        // Auto-save when any published property changes (no language properties since they're fixed)
        Publishers.CombineLatest3(
            $dailyGoal,
            $weeklyGoal,
            $preferredStudyTime
        )
        .debounce(for: .seconds(2), scheduler: RunLoop.main)
        .sink { [weak self] _, _, _ in
            self?.savePreferences()
        }
        .store(in: &cancellables)

        $notificationsEnabled
            .debounce(for: .seconds(2), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.savePreferences()
            }
            .store(in: &cancellables)
    }

    // MARK: - Placeholder Data Methods (to be replaced with real database queries)

    private func getTodaysCompletedLessons() -> Int {
        // TODO: Replace with actual database query
        // This should query the Progress model for lessons completed today
        return Int.random(in: 0...dailyGoal)
    }

    private func getWeeklyCompletedLessons(since startDate: Date) -> Int {
        // TODO: Replace with actual database query
        return Int.random(in: 0...weeklyGoal)
    }

    private func getProgressForLanguage(_ language: Language) -> (completed: Int, total: Int) {
        // TODO: Replace with actual database query
        let total = getAvailableLessonsCount(for: language)
        let completed = Int(Double(total) * getUserProgressForLanguage(language))
        return (completed: completed, total: total)
    }

    private func getRecommendedLessonsForLanguage(_ language: Language) -> [LessonSummary] {
        // TODO: Replace with actual lesson recommendations
        return [
            LessonSummary(
                id: UUID(),
                title: "Basic Greetings",
                language: language,
                difficulty: .beginner,
                estimatedDuration: 15,
                isCompleted: false
            ),
            LessonSummary(
                id: UUID(),
                title: "Numbers 1-20",
                language: language,
                difficulty: .beginner,
                estimatedDuration: 20,
                isCompleted: false
            ),
            LessonSummary(
                id: UUID(),
                title: "Common Phrases",
                language: language,
                difficulty: .elementary,
                estimatedDuration: 25,
                isCompleted: true
            )
        ]
    }
}

// MARK: - Supporting Models

struct LessonSummary: Identifiable {
    let id: UUID
    let title: String
    let language: Language
    let difficulty: Difficulty
    let estimatedDuration: Int // minutes
    let isCompleted: Bool
}

// MARK: - Notification Extensions

extension UserPreferencesService {
    func scheduleStudyReminders() {
        guard notificationsEnabled else { return }

        // TODO: Implement local notification scheduling
        // This would schedule daily reminders based on preferredStudyTime
    }

    func cancelStudyReminders() {
        // TODO: Cancel scheduled notifications
    }
}