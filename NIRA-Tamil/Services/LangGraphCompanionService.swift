import Foundation
import Combine

// MARK: - LangGraph/CrewAI Multi-Agent Learning Companion Service

@MainActor
class LangGraphCompanionService: ObservableObject {
    static let shared = LangGraphCompanionService()
    
    @Published var isProcessing = false
    @Published var lastError: Error?
    
    private let openAIAPIKey: String
    private let useMockData: Bool
    
    private init() {
        self.openAIAPIKey = APIKeys.openAIAPIKey
        self.useMockData = openAIAPIKey.isEmpty || ProcessInfo.processInfo.environment["USE_MOCK_DATA"] == "true"
    }
    
    // MARK: - Multi-Agent Conversation Management
    
    func startConversation(with companion: LearningCompanion, userId: String) async throws -> CompanionConversation {
        await MainActor.run {
            isProcessing = true
        }
        
        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }
        
        let conversation = CompanionConversation(
            id: UUID(),
            userId: userId,
            companionId: companion.id,
            companionPersona: companion.persona,
            language: companion.language,
            systemPrompt: companion.systemPrompt,
            messages: [],
            createdAt: Date(),
            lastActiveAt: Date()
        )
        
        // Initialize with welcome message
        let welcomeMessage = try await generateWelcomeMessage(for: companion)
        conversation.messages.append(welcomeMessage)
        
        return conversation
    }
    
    func sendMessage(
        to conversation: CompanionConversation,
        message: String,
        companion: LearningCompanion
    ) async throws -> CompanionMessage {
        await MainActor.run {
            isProcessing = true
        }
        
        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }
        
        // Add user message to conversation
        let userMessage = CompanionMessage(
            id: UUID(),
            role: .user,
            content: message,
            timestamp: Date(),
            metadata: CompanionMessageMetadata()
        )
        conversation.messages.append(userMessage)
        
        // Generate AI response using LangGraph/CrewAI approach
        let aiResponse = try await generateAIResponse(
            for: conversation,
            companion: companion,
            userMessage: message
        )
        
        let aiMessage = CompanionMessage(
            id: UUID(),
            role: .assistant,
            content: aiResponse.content,
            timestamp: Date(),
            metadata: aiResponse.metadata
        )
        conversation.messages.append(aiMessage)
        conversation.lastActiveAt = Date()
        
        return aiMessage
    }
    
    // MARK: - AI Response Generation with Abuse Prevention
    
    private func generateAIResponse(
        for conversation: CompanionConversation,
        companion: LearningCompanion,
        userMessage: String
    ) async throws -> (content: String, metadata: CompanionMessageMetadata) {
        
        // Step 1: Content Moderation Agent
        let moderationResult = try await moderateContent(userMessage, language: companion.language)
        if !moderationResult.isAppropriate {
            return (
                content: generateRedirectionMessage(for: companion.persona, language: companion.language),
                metadata: CompanionMessageMetadata(
                    moderationFlag: true,
                    redirectionReason: moderationResult.reason
                )
            )
        }
        
        // Step 2: Context Analysis Agent
        let contextAnalysis = try await analyzeContext(
            conversation: conversation,
            userMessage: userMessage,
            companion: companion
        )
        
        // Step 3: Educational Content Agent
        let educationalResponse = try await generateEducationalResponse(
            context: contextAnalysis,
            companion: companion,
            userMessage: userMessage
        )
        
        // Step 4: Quality Assurance Agent
        let finalResponse = try await qualityAssurance(
            response: educationalResponse,
            companion: companion,
            context: contextAnalysis
        )
        
        return finalResponse
    }
    
    // MARK: - Multi-Agent Pipeline Components
    
    private func moderateContent(_ content: String, language: Language) async throws -> ModerationResult {
        if useMockData {
            // Mock moderation - always appropriate for development
            return ModerationResult(isAppropriate: true, reason: nil)
        }
        
        let moderationPrompt = """
        You are a content moderation agent for a language learning platform.
        
        TASK: Determine if this user message is appropriate for educational language learning.
        
        INAPPROPRIATE CONTENT INCLUDES:
        - Non-educational topics unrelated to \(language.displayName) learning
        - Inappropriate personal questions
        - Requests for non-educational assistance
        - Attempts to bypass educational focus
        - Harmful, offensive, or inappropriate content
        
        APPROPRIATE CONTENT INCLUDES:
        - Questions about \(language.displayName) language
        - Cultural questions related to \(language.displayName)
        - Grammar, vocabulary, pronunciation questions
        - Practice conversations in \(language.displayName)
        - Learning methodology questions
        
        User message: "\(content)"
        
        Respond with only: "APPROPRIATE" or "INAPPROPRIATE: [brief reason]"
        """
        
        let response = try await callOpenAI(prompt: moderationPrompt, maxTokens: 50)
        
        if response.lowercased().contains("inappropriate") {
            let reason = response.components(separatedBy: ": ").last ?? "Content not suitable for language learning"
            return ModerationResult(isAppropriate: false, reason: reason)
        }
        
        return ModerationResult(isAppropriate: true, reason: nil)
    }
    
    private func analyzeContext(
        conversation: CompanionConversation,
        userMessage: String,
        companion: LearningCompanion
    ) async throws -> ContextAnalysis {
        
        let recentMessages = conversation.messages.suffix(10)
        let conversationHistory = recentMessages.map { "\($0.role.rawValue): \($0.content)" }.joined(separator: "\n")
        
        let contextPrompt = """
        You are a context analysis agent for language learning conversations.
        
        COMPANION ROLE: \(companion.persona.rawValue) for \(companion.language.displayName)
        
        CONVERSATION HISTORY:
        \(conversationHistory)
        
        CURRENT USER MESSAGE: "\(userMessage)"
        
        Analyze and provide:
        1. Learning intent (vocabulary/grammar/culture/conversation/pronunciation)
        2. Difficulty level (beginner/intermediate/advanced)
        3. Topic category
        4. Suggested response approach
        
        Format as JSON:
        {
            "intent": "...",
            "difficulty": "...",
            "topic": "...",
            "approach": "..."
        }
        """
        
        let response = try await callOpenAI(prompt: contextPrompt, maxTokens: 200)
        
        // Parse JSON response or provide defaults
        if let data = response.data(using: .utf8),
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: String] {
            return ContextAnalysis(
                intent: json["intent"] ?? "general",
                difficulty: json["difficulty"] ?? "beginner",
                topic: json["topic"] ?? "general",
                approach: json["approach"] ?? "supportive"
            )
        }
        
        return ContextAnalysis(
            intent: "general",
            difficulty: "beginner", 
            topic: "general",
            approach: "supportive"
        )
    }
    
    private func generateEducationalResponse(
        context: ContextAnalysis,
        companion: LearningCompanion,
        userMessage: String
    ) async throws -> String {
        
        let educationalPrompt = """
        \(companion.systemPrompt)
        
        CONTEXT ANALYSIS:
        - Learning Intent: \(context.intent)
        - Difficulty Level: \(context.difficulty)
        - Topic: \(context.topic)
        - Approach: \(context.approach)
        
        USER MESSAGE: "\(userMessage)"
        
        RESPONSE REQUIREMENTS:
        - Stay strictly within your role as \(companion.persona.rawValue)
        - Focus only on \(companion.language.displayName) language learning
        - Provide educational value appropriate for \(context.difficulty) level
        - Be helpful, encouraging, and culturally appropriate
        - If user asks non-educational questions, politely redirect to language learning
        - Keep response length appropriate (2-4 sentences for beginners, more for advanced)
        
        Generate your response:
        """
        
        return try await callOpenAI(prompt: educationalPrompt, maxTokens: 300)
    }
    
    private func qualityAssurance(
        response: String,
        companion: LearningCompanion,
        context: ContextAnalysis
    ) async throws -> (content: String, metadata: CompanionMessageMetadata) {
        
        let qaPrompt = """
        You are a quality assurance agent for language learning responses.
        
        ORIGINAL RESPONSE: "\(response)"
        COMPANION ROLE: \(companion.persona.rawValue)
        LANGUAGE: \(companion.language.displayName)
        CONTEXT: \(context.intent) - \(context.difficulty)
        
        Check if the response:
        1. Stays within educational boundaries
        2. Matches the companion's persona
        3. Is appropriate for the difficulty level
        4. Provides educational value
        
        If the response is good, return: "APPROVED: [original response]"
        If it needs improvement, return: "IMPROVED: [better response]"
        """
        
        let qaResult = try await callOpenAI(prompt: qaPrompt, maxTokens: 400)
        
        let finalContent: String
        if qaResult.hasPrefix("APPROVED:") {
            finalContent = String(qaResult.dropFirst(9)).trimmingCharacters(in: .whitespaces)
        } else if qaResult.hasPrefix("IMPROVED:") {
            finalContent = String(qaResult.dropFirst(9)).trimmingCharacters(in: .whitespaces)
        } else {
            finalContent = response // Fallback to original
        }
        
        let metadata = CompanionMessageMetadata(
            intent: context.intent,
            difficulty: context.difficulty,
            topic: context.topic,
            qualityScore: 0.9 // Could be calculated based on QA analysis
        )
        
        return (content: finalContent, metadata: metadata)
    }
    
    // MARK: - Helper Methods
    
    private func generateWelcomeMessage(for companion: LearningCompanion) async throws -> CompanionMessage {
        let welcomePrompt = """
        \(companion.systemPrompt)
        
        Generate a brief, friendly welcome message as \(companion.name), the \(companion.persona.rawValue) for \(companion.language.displayName).
        
        The message should:
        - Introduce yourself and your role
        - Mention your specialization
        - Invite the user to start learning
        - Be encouraging and appropriate for your persona
        - Be 2-3 sentences maximum
        """
        
        let content = try await callOpenAI(prompt: welcomePrompt, maxTokens: 150)
        
        return CompanionMessage(
            id: UUID(),
            role: .assistant,
            content: content,
            timestamp: Date(),
            metadata: CompanionMessageMetadata(
                intent: "welcome",
                difficulty: "beginner",
                topic: "introduction"
            )
        )
    }
    
    private func generateRedirectionMessage(for persona: CompanionPersona, language: Language) -> String {
        let redirections: [CompanionPersona: String] = [
            .beginnerEnthusiast: "I'm here to help you learn \(language.displayName)! Let's focus on building your language skills. What would you like to practice today?",
            .busyProfessional: "I specialize in practical \(language.displayName) for professional and travel situations. How can I help you with your language learning goals?",
            .culturalSeeker: "I love sharing \(language.displayName) culture and traditions! What aspects of \(language.displayName) culture would you like to explore through language?",
            .socialLearner: "Let's practice \(language.displayName) conversation! What social situations would you like to practice in \(language.displayName)?",
            .nriHelper: "I'm here to help you connect with your heritage through \(language.displayName). What family or cultural topics would you like to explore?",
            .masterGuide: "I'm your comprehensive \(language.displayName) guide. What language topic can I help you master today?"
        ]
        
        return redirections[persona] ?? "Let's focus on learning \(language.displayName)! How can I help you with your language studies?"
    }
    
    private func callOpenAI(prompt: String, maxTokens: Int) async throws -> String {
        if useMockData {
            // Return mock response for development
            return "This is a mock response for development. The actual LangGraph/CrewAI system will provide personalized language learning assistance."
        }
        
        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(openAIAPIKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "user", "content": prompt]
            ],
            "max_tokens": maxTokens,
            "temperature": 0.7
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, _) = try await URLSession.shared.data(for: request)
        
        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
           let choices = json["choices"] as? [[String: Any]],
           let firstChoice = choices.first,
           let message = firstChoice["message"] as? [String: Any],
           let content = message["content"] as? String {
            return content.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        throw CompanionError.apiError
    }
}

// MARK: - Supporting Models

class CompanionConversation: ObservableObject {
    let id: UUID
    let userId: String
    let companionId: UUID
    let companionPersona: CompanionPersona
    let language: Language
    let systemPrompt: String
    @Published var messages: [CompanionMessage]
    let createdAt: Date
    var lastActiveAt: Date
    
    init(id: UUID, userId: String, companionId: UUID, companionPersona: CompanionPersona, language: Language, systemPrompt: String, messages: [CompanionMessage], createdAt: Date, lastActiveAt: Date) {
        self.id = id
        self.userId = userId
        self.companionId = companionId
        self.companionPersona = companionPersona
        self.language = language
        self.systemPrompt = systemPrompt
        self.messages = messages
        self.createdAt = createdAt
        self.lastActiveAt = lastActiveAt
    }
}

struct CompanionMessage: Identifiable {
    let id: UUID
    let role: MessageRole
    let content: String
    let timestamp: Date
    let metadata: CompanionMessageMetadata
}

enum MessageRole: String, Codable {
    case user = "user"
    case assistant = "assistant"
    case system = "system"
}

struct CompanionMessageMetadata {
    let intent: String
    let difficulty: String
    let topic: String
    let qualityScore: Double
    let moderationFlag: Bool
    let redirectionReason: String?
    
    init(intent: String = "general", difficulty: String = "beginner", topic: String = "general", qualityScore: Double = 1.0, moderationFlag: Bool = false, redirectionReason: String? = nil) {
        self.intent = intent
        self.difficulty = difficulty
        self.topic = topic
        self.qualityScore = qualityScore
        self.moderationFlag = moderationFlag
        self.redirectionReason = redirectionReason
    }
}

struct ModerationResult {
    let isAppropriate: Bool
    let reason: String?
}

struct ContextAnalysis {
    let intent: String
    let difficulty: String
    let topic: String
    let approach: String
}

enum CompanionError: LocalizedError {
    case apiError
    case moderationFailed
    case contextAnalysisFailed
    
    var errorDescription: String? {
        switch self {
        case .apiError:
            return "Failed to communicate with AI service"
        case .moderationFailed:
            return "Content moderation failed"
        case .contextAnalysisFailed:
            return "Context analysis failed"
        }
    }
} 