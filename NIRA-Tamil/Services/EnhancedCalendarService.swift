//
//  EnhancedCalendarService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import Foundation
import Combine

// MARK: - Enhanced Calendar Models

struct CalendarConcept: Codable, Identifiable {
    let id: UUID
    let conceptName: String
    let conceptNameTamil: String
    let description: String
    let descriptionTamil: String?
    let explanation: String
    let significance: String?
    let modernApplication: String?
    let calculationMethod: String?
    let audioUrl: String?
    let romanization: String?
    let category: String
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id
        case conceptName = "concept_name"
        case conceptNameTamil = "concept_name_tamil"
        case description
        case descriptionTamil = "description_tamil"
        case explanation
        case significance
        case modernApplication = "modern_application"
        case calculationMethod = "calculation_method"
        case audioUrl = "audio_url"
        case romanization
        case category
        case isActive = "is_active"
    }
}

struct TamilFestival: Codable, Identifiable {
    let id: UUID
    let name: String
    let nameTamil: String
    let romanization: String?
    let religion: String
    let festivalDate: Date?
    let isLunarBased: Bool
    let lunarCalculationInfo: String?
    let durationDays: Int
    let significance: String
    let description: String
    let traditions: [String]
    let modernCelebration: String?
    let regionalVariations: String?
    let culturalImportance: String
    let foodItems: [String]
    let rituals: [String]
    let audioUrl: String?
    let imageUrl: String?
    // Learning content stored separately to maintain Codable compliance
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, romanization, religion, significance, description, traditions
        case nameTamil = "name_tamil"
        case festivalDate = "festival_date"
        case isLunarBased = "is_lunar_based"
        case lunarCalculationInfo = "lunar_calculation_info"
        case durationDays = "duration_days"
        case modernCelebration = "modern_celebration"
        case regionalVariations = "regional_variations"
        case culturalImportance = "cultural_importance"
        case foodItems = "food_items"
        case rituals
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        // learningContent removed for Codable compliance
        case isActive = "is_active"
    }
}

// MARK: - Enhanced Calendar Service

@MainActor
class EnhancedCalendarService: ObservableObject {
    static let shared = EnhancedCalendarService()
    
    @Published var calendarConcepts: [CalendarConcept] = []
    @Published var allFestivals: [TamilFestival] = []
    @Published var filteredFestivals: [TamilFestival] = []
    @Published var selectedReligionFilter: String = "All" {
        didSet {
            filterFestivals()
        }
    }
    @Published var isLoading = false
    @Published var error: Error?
    
    private let supabaseClient = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Initialize with empty data, will load from Supabase
    }
    
    // MARK: - Public Methods
    
    func loadContent() async {
        isLoading = true
        error = nil
        
        // Load calendar concepts
        await loadCalendarConcepts()

        // Load festivals
        await loadFestivals()

        // Apply initial filter
        filterFestivals()
        
        isLoading = false
    }
    
    func getFestivals(for religion: String) -> [TamilFestival] {
        if religion.lowercased() == "all" {
            return allFestivals
        }
        return allFestivals.filter { $0.religion.lowercased() == religion.lowercased() }
    }
    
    func getConcept(by category: String) -> CalendarConcept? {
        return calendarConcepts.first { $0.category == category }
    }
    
    // MARK: - Private Methods
    
    private func loadCalendarConcepts() async {
        do {
            let response = try await supabaseClient.client.from("calendar_concepts")
                .select("*")
                .eq("is_active", value: true)
                .order("category")
                .execute()
            
            // For now, use mock data until Supabase integration is complete
            calendarConcepts = createMockCalendarConcepts()
            
        } catch {
            print("❌ Error loading calendar concepts: \(error)")
            // Fallback to mock data
            calendarConcepts = createMockCalendarConcepts()
        }
    }
    
    private func loadFestivals() async {
        do {
            let response = try await supabaseClient.client.from("tamil_festivals")
                .select("*")
                .eq("is_active", value: true)
                .order("festival_date")
                .execute()
            
            // For now, use mock data until Supabase integration is complete
            allFestivals = createMockFestivals()
            
        } catch {
            print("❌ Error loading festivals: \(error)")
            // Fallback to mock data
            allFestivals = createMockFestivals()
        }
    }
    
    private func filterFestivals() {
        filteredFestivals = getFestivals(for: selectedReligionFilter)
    }
    
    // MARK: - Mock Data (temporary until Supabase integration)
    
    private func createMockCalendarConcepts() -> [CalendarConcept] {
        return [
            CalendarConcept(
                id: UUID(),
                conceptName: "Muhurtham",
                conceptNameTamil: "முகூர்த்தம்",
                description: "Auspicious time for important activities",
                descriptionTamil: "முக்கிய செயல்களுக்கான நல்ல நேரம்",
                explanation: "Muhurtham refers to an auspicious moment calculated based on planetary positions and lunar phases.",
                significance: "Ensures divine blessings and success for new beginnings",
                modernApplication: "Modern families still consult muhurtham for weddings and business launches",
                calculationMethod: nil,
                audioUrl: nil,
                romanization: "Muhurtham",
                category: "muhurtham",
                isActive: true
            ),
            CalendarConcept(
                id: UUID(),
                conceptName: "Raahu Kaalam",
                conceptNameTamil: "ராகு காலம்",
                description: "Inauspicious time period to avoid new activities",
                descriptionTamil: "புதிய செயல்களைத் தவிர்க்க வேண்டிய நேரம்",
                explanation: "Raahu Kaalam is a daily 90-minute period considered inauspicious for starting new activities.",
                significance: "Helps avoid potential obstacles and negative influences",
                modernApplication: "Many people still check Raahu Kaalam before important meetings or travel",
                calculationMethod: nil,
                audioUrl: nil,
                romanization: "Raahu Kaalam",
                category: "raahu_kaalam",
                isActive: true
            ),
            CalendarConcept(
                id: UUID(),
                conceptName: "Yemekandam",
                conceptNameTamil: "எமகண்டம்",
                description: "Time period ruled by Yama, god of death",
                descriptionTamil: "எமன் ஆட்சியில் உள்ள காலம்",
                explanation: "Yemekandam is another inauspicious time period when new activities should be avoided.",
                significance: "Prevents negative outcomes and ensures spiritual protection",
                modernApplication: "Traditional families consider this timing for planning daily activities",
                calculationMethod: nil,
                audioUrl: nil,
                romanization: "Yemekandam",
                category: "yemekandam",
                isActive: true
            )
        ]
    }
    
    private func createMockFestivals() -> [TamilFestival] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        return [
            TamilFestival(
                id: UUID(),
                name: "Pongal",
                nameTamil: "பொங்கல்",
                romanization: "Pongal",
                religion: "hindu",
                festivalDate: dateFormatter.date(from: "2025-01-14"),
                isLunarBased: false,
                lunarCalculationInfo: nil,
                durationDays: 4,
                significance: "Harvest festival celebrating the sun god and agricultural abundance",
                description: "Pongal is the most important harvest festival of Tamil Nadu, celebrated for four days to thank the sun god, cattle, and nature for a bountiful harvest.",
                traditions: ["Boiling rice with milk and jaggery", "Decorating cattle", "Drawing kolam", "Family gatherings"],
                modernCelebration: "Urban celebrations include community events, cultural programs, and traditional food festivals",
                regionalVariations: nil,
                culturalImportance: "very_high",
                foodItems: ["Pongal rice", "Sugarcane", "Turmeric rice", "Payasam"],
                rituals: ["Surya puja", "Cattle worship", "Kolam drawing", "Traditional games"],
                audioUrl: nil,
                imageUrl: nil,
                isActive: true
            ),
            TamilFestival(
                id: UUID(),
                name: "Eid al-Fitr",
                nameTamil: "ஈத் அல் ஃபித்ர்",
                romanization: "Eid al-Fitr",
                religion: "muslim",
                festivalDate: dateFormatter.date(from: "2025-04-10"),
                isLunarBased: true,
                lunarCalculationInfo: "Based on lunar calendar, date varies each year",
                durationDays: 1,
                significance: "Festival marking the end of Ramadan fasting",
                description: "Celebrated by Tamil Muslim communities with special prayers, feasts, and charity giving.",
                traditions: ["Special prayers at mosque", "Sharing sweets with neighbors", "Giving charity (Zakat)", "Family feasts"],
                modernCelebration: "Community celebrations include interfaith harmony events and cultural exchange",
                regionalVariations: nil,
                culturalImportance: "high",
                foodItems: ["Biryani", "Haleem", "Dates", "Sweet vermicelli"],
                rituals: ["Eid prayers", "Charity giving", "Community feasts", "Gift exchange"],
                audioUrl: nil,
                imageUrl: nil,
                isActive: true
            ),
            TamilFestival(
                id: UUID(),
                name: "Christmas",
                nameTamil: "கிறிஸ்துமஸ்",
                romanization: "Christmas",
                religion: "christian",
                festivalDate: dateFormatter.date(from: "2025-12-25"),
                isLunarBased: false,
                lunarCalculationInfo: nil,
                durationDays: 1,
                significance: "Celebration of the birth of Jesus Christ",
                description: "Tamil Christian communities celebrate with midnight mass, carol singing, and traditional festivities.",
                traditions: ["Midnight mass", "Carol singing", "Star decorations", "Community feasts"],
                modernCelebration: "Modern celebrations include cultural programs showcasing Tamil Christian heritage",
                regionalVariations: nil,
                culturalImportance: "high",
                foodItems: ["Christmas cake", "Kusukusu", "Traditional sweets", "Rose cookies"],
                rituals: ["Midnight mass", "Carol singing", "Nativity scenes", "Community service"],
                audioUrl: nil,
                imageUrl: nil,
                isActive: true
            )
        ]
    }
}
