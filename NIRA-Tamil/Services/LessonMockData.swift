//
//  LessonMockData.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation

// MARK: - Comprehensive Lesson Mock Data

extension LessonService {
    
    // Enhanced lesson database with comprehensive Tamil learning content
    static let enhancedLessons: [Lesson] = [
        // Beginner Level Lessons
        Lesson(
            id: UUID(),
            title: "Tamil Greetings & Basic Phrases",
            lessonDescription: "Learn essential Tamil greetings and everyday phrases used in Tamil Nadu. Master the art of respectful communication in Tamil culture.",
            language: .tamil,
            difficulty: .beginner,
            category: .conversation,
            estimatedDuration: 15,
            exercises: enhancedGreetingExercises,
            culturalContext: greetingCulturalContext,
            prerequisites: [],
            tags: ["greetings", "basic", "conversation", "culture", "respect"],
            audioURL: "https://example.com/audio/tamil-greetings.mp3",
            imageURL: "https://example.com/images/tamil-greetings.jpg",
            totalPoints: 100,
            isAIGenerated: false
        ),
        
        Lesson(
            id: UUID(),
            title: "Tamil Family & Relationships",
            lessonDescription: "Explore Tamil family terms and relationship vocabulary with deep cultural context. Understand the importance of family hierarchy in Tamil society.",
            language: .tamil,
            difficulty: .beginner,
            category: .vocabulary,
            estimatedDuration: 20,
            exercises: enhancedFamilyExercises,
            culturalContext: familyCulturalContext,
            prerequisites: [],
            tags: ["family", "relationships", "culture", "vocabulary", "hierarchy"],
            audioURL: "https://example.com/audio/tamil-family.mp3",
            imageURL: "https://example.com/images/tamil-family.jpg",
            totalPoints: 120,
            isAIGenerated: false
        ),
        
        Lesson(
            id: UUID(),
            title: "Tamil Numbers & Counting",
            lessonDescription: "Master Tamil numerals and counting systems. Learn both traditional Tamil numbers and modern usage in daily life.",
            language: .tamil,
            difficulty: .beginner,
            category: .vocabulary,
            estimatedDuration: 18,
            exercises: enhancedNumberExercises,
            culturalContext: numberCulturalContext,
            prerequisites: [],
            tags: ["numbers", "counting", "mathematics", "traditional", "modern"],
            audioURL: "https://example.com/audio/tamil-numbers.mp3",
            imageURL: "https://example.com/images/tamil-numbers.jpg",
            totalPoints: 110,
            isAIGenerated: false
        ),
        
        // Intermediate Level Lessons
        Lesson(
            id: UUID(),
            title: "Tamil Festival Celebrations",
            lessonDescription: "Learn about major Tamil festivals and associated vocabulary. Understand the cultural significance and traditional practices.",
            language: .tamil,
            difficulty: .intermediate,
            category: .culture,
            estimatedDuration: 25,
            exercises: enhancedFestivalExercises,
            culturalContext: festivalCulturalContext,
            prerequisites: [],
            tags: ["festivals", "culture", "traditions", "celebrations", "spirituality"],
            audioURL: "https://example.com/audio/tamil-festivals.mp3",
            imageURL: "https://example.com/images/tamil-festivals.jpg",
            totalPoints: 150,
            isAIGenerated: false
        ),
        
        Lesson(
            id: UUID(),
            title: "Tamil Cuisine & Food Culture",
            lessonDescription: "Discover Tamil food vocabulary and dining customs. Learn about the six tastes philosophy and traditional cooking methods.",
            language: .tamil,
            difficulty: .intermediate,
            category: .culture,
            estimatedDuration: 22,
            exercises: enhancedFoodExercises,
            culturalContext: foodCulturalContext,
            prerequisites: [],
            tags: ["food", "cuisine", "culture", "dining", "ayurveda"],
            audioURL: "https://example.com/audio/tamil-food.mp3",
            imageURL: "https://example.com/images/tamil-food.jpg",
            totalPoints: 140,
            isAIGenerated: false
        ),
        
        // Advanced Level Lessons
        Lesson(
            id: UUID(),
            title: "Tamil Script Fundamentals",
            lessonDescription: "Master the basics of Tamil script writing and reading. Learn vowels, consonants, and combination characters.",
            language: .tamil,
            difficulty: .intermediate,
            category: .writing,
            estimatedDuration: 30,
            exercises: enhancedScriptExercises,
            culturalContext: scriptCulturalContext,
            prerequisites: [],
            tags: ["script", "writing", "reading", "fundamentals", "alphabet"],
            audioURL: "https://example.com/audio/tamil-script.mp3",
            imageURL: "https://example.com/images/tamil-script.jpg",
            totalPoints: 180,
            isAIGenerated: false
        ),
        
        Lesson(
            id: UUID(),
            title: "Tamil Poetry & Literature",
            lessonDescription: "Introduction to Tamil poetry forms and classical literature. Explore Sangam poetry and modern Tamil literature.",
            language: .tamil,
            difficulty: .advanced,
            category: .culture,
            estimatedDuration: 35,
            exercises: enhancedLiteratureExercises,
            culturalContext: literatureCulturalContext,
            prerequisites: [],
            tags: ["poetry", "literature", "sangam", "classical", "modern"],
            audioURL: "https://example.com/audio/tamil-poetry.mp3",
            imageURL: "https://example.com/images/tamil-poetry.jpg",
            totalPoints: 200,
            isAIGenerated: false
        ),
        
        Lesson(
            id: UUID(),
            title: "Tamil Business & Professional Communication",
            lessonDescription: "Learn formal Tamil for business and professional settings. Master respectful communication in workplace contexts.",
            language: .tamil,
            difficulty: .advanced,
            category: .conversation,
            estimatedDuration: 28,
            exercises: enhancedBusinessExercises,
            culturalContext: businessCulturalContext,
            prerequisites: [],
            tags: ["business", "professional", "formal", "workplace", "communication"],
            audioURL: "https://example.com/audio/tamil-business.mp3",
            imageURL: "https://example.com/images/tamil-business.jpg",
            totalPoints: 170,
            isAIGenerated: false
        )
    ]
    
    // MARK: - Exercise Collections
    
    static let enhancedGreetingExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "How do you say 'Hello' in Tamil?",
            options: ["வணக்கம்", "நமஸ்காரம்", "ஆதாப்", "சலாம்"],
            correctAnswer: 0,
            explanation: "வணக்கம் (Vanakkam) is the most common Tamil greeting, used throughout the day. It comes from the root word 'வணங்கு' meaning to bow or worship, showing respect.",
            audioURL: "https://example.com/audio/vanakkam.mp3",
            imageURL: nil,
            points: 10,
            hints: ["This greeting is used at any time of day", "It's a respectful way to greet someone"],
            culturalNote: "வணக்கம் reflects the Tamil value of showing respect to others through humble greeting.",
            difficulty: .beginner
        ),
        Exercise(
            id: UUID(),
            type: .fillInBlank,
            question: "Complete the sentence: நான் _____ பேசுகிறேன் (I speak Tamil)",
            options: ["தமிழ்", "ஆங்கிலம்", "இந்தி", "தெலுங்கு"],
            correctAnswer: 0,
            explanation: "தமிழ் (Tamil) is the word for the Tamil language. This sentence structure 'நான் [language] பேசுகிறேன்' is used to express language proficiency.",
            audioURL: "https://example.com/audio/naan-tamil-pesugireen.mp3",
            imageURL: nil,
            points: 15,
            hints: ["Think about what language we're learning", "The word sounds similar to 'Tamil'"],
            culturalNote: "Tamil is one of the oldest living languages in the world, with literature dating back over 2000 years.",
            difficulty: .beginner
        ),
        Exercise(
            id: UUID(),
            type: .pronunciation,
            question: "Pronounce: எப்படி இருக்கீங்க? (How are you?)",
            options: ["Eppadi irukkinga?", "Eppadi irukkenga?", "Eppadi irukkeenga?", "Eppadi irukinga?"],
            correctAnswer: 0,
            explanation: "எப்படி இருக்கீங்க? (Eppadi irukkinga?) is a common way to ask 'How are you?' in Tamil. The pronunciation emphasizes the respectful form.",
            audioURL: "https://example.com/audio/eppadi-irukkinga.mp3",
            imageURL: nil,
            points: 12,
            hints: ["Listen carefully to the audio", "Pay attention to the respectful ending"],
            culturalNote: "Using the respectful form shows proper Tamil etiquette when speaking to others.",
            difficulty: .beginner
        )
    ]
    
    static let enhancedFamilyExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "What is the Tamil word for 'mother'?",
            options: ["அம்மா", "அப்பா", "அண்ணா", "அக்கா"],
            correctAnswer: 0,
            explanation: "அம்மா (Amma) means mother in Tamil. It's one of the most important and respected words in Tamil culture.",
            audioURL: "https://example.com/audio/amma.mp3",
            imageURL: nil,
            points: 10,
            hints: ["This word is similar across many South Indian languages", "It's a term of endearment"],
            culturalNote: "In Tamil culture, mother is considered the first teacher and is highly revered.",
            difficulty: .beginner
        ),
        Exercise(
            id: UUID(),
            type: .matching,
            question: "Match the Tamil family terms with their English meanings",
            options: ["அப்பா - Father", "தாத்தா - Grandfather", "பாட்டி - Grandmother", "மாமா - Uncle"],
            correctAnswer: 0, // All are correct in this matching exercise
            explanation: "Tamil has specific terms for different family relationships, showing the importance of family hierarchy.",
            audioURL: "https://example.com/audio/family-terms.mp3",
            imageURL: nil,
            points: 20,
            hints: ["Consider the respect levels in Tamil family structure", "Each term has cultural significance"],
            culturalNote: "Tamil family terms reflect the deep respect for elders and the importance of family bonds.",
            difficulty: .beginner
        )
    ]

    // MARK: - Additional Exercise Collections

    static let enhancedNumberExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "What is the Tamil word for 'one'?",
            options: ["ஒன்று", "இரண்டு", "மூன்று", "நான்கு"],
            correctAnswer: 0,
            explanation: "ஒன்று (Onru) means 'one' in Tamil. Tamil has its own unique numeral system.",
            audioURL: "https://example.com/audio/onru.mp3",
            imageURL: nil,
            points: 8,
            hints: ["This is the first number in Tamil counting", "It sounds like 'on-ru'"],
            culturalNote: "Tamil numerals have ancient origins and are still used in traditional contexts.",
            difficulty: .beginner
        )
    ]

    static let enhancedFestivalExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "Which festival is known as the Tamil harvest festival?",
            options: ["பொங்கல்", "தீபாவளி", "நவராத்திரி", "கார்த்திகை"],
            correctAnswer: 0,
            explanation: "பொங்கல் (Pongal) is the Tamil harvest festival celebrating the abundance of nature and thanking the sun.",
            audioURL: "https://example.com/audio/pongal.mp3",
            imageURL: nil,
            points: 15,
            hints: ["This festival is celebrated in January", "It involves cooking a special dish"],
            culturalNote: "Pongal represents the Tamil connection to agriculture and nature.",
            difficulty: .intermediate
        )
    ]

    static let enhancedFoodExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .fillInBlank,
            question: "Tamil cuisine follows the principle of _____ tastes (Aaru Ruchi)",
            options: ["ஆறு", "ஐந்து", "ஏழு", "எட்டு"],
            correctAnswer: 0,
            explanation: "ஆறு (Aaru) means six. Tamil cuisine is based on six tastes: sweet, sour, salty, bitter, pungent, and astringent.",
            audioURL: "https://example.com/audio/aaru-ruchi.mp3",
            imageURL: nil,
            points: 12,
            hints: ["This number represents completeness in Tamil food philosophy", "It's based on Ayurvedic principles"],
            culturalNote: "The six tastes ensure balanced nutrition and health according to Tamil tradition.",
            difficulty: .intermediate
        )
    ]

    static let enhancedScriptExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "Write the Tamil letter for the sound 'ka'",
            options: ["க", "ங", "ச", "ஞ"],
            correctAnswer: 0,
            explanation: "க (ka) is a fundamental consonant in Tamil script. It's part of the 'ka' group of consonants.",
            audioURL: "https://example.com/audio/ka-letter.mp3",
            imageURL: nil,
            points: 18,
            hints: ["This is one of the most common consonants", "It appears in many Tamil words"],
            culturalNote: "Tamil script is one of the oldest writing systems still in use today.",
            difficulty: .intermediate
        )
    ]

    static let enhancedLiteratureExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "Who is the author of Thirukkural?",
            options: ["திருவள்ளுவர்", "கம்பர்", "இளங்கோ", "பாரதி"],
            correctAnswer: 0,
            explanation: "திருவள்ளுவர் (Thiruvalluvar) is the revered author of Thirukkural, a masterpiece of Tamil literature.",
            audioURL: "https://example.com/audio/thiruvalluvar.mp3",
            imageURL: nil,
            points: 20,
            hints: ["This author is considered a saint-poet", "The work contains 1330 couplets"],
            culturalNote: "Thiruvalluvar's work is considered universal wisdom applicable to all humanity.",
            difficulty: .advanced
        )
    ]

    static let enhancedBusinessExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .conversation,
            question: "How do you formally introduce yourself in a business meeting?",
            options: ["என் பெயர் [name]. நான் [company] இல் வேலை செய்கிறேன்.", "நான் [name]. எனக்கு வேலை இருக்கு.", "என்னை [name] என்று அழைக்கலாம்.", "நான் இங்கே வேலை செய்றேன்."],
            correctAnswer: 0,
            explanation: "என் பெயர் [name]. நான் [company] இல் வேலை செய்கிறேன். is the formal way to introduce yourself professionally.",
            audioURL: "https://example.com/audio/business-intro.mp3",
            imageURL: nil,
            points: 25,
            hints: ["Use formal language structure", "Include your company name"],
            culturalNote: "Professional Tamil maintains respectful tone and proper grammar structure.",
            difficulty: .advanced
        )
    ]

    // MARK: - Cultural Context Data

    static let greetingCulturalContext: CulturalContext = CulturalContext(
        scenario: .tamilFestival,
        setting: "Traditional Tamil home entrance",
        participants: ["Host", "Guest", "Family members"],
        socialNorms: ["Remove shoes before entering", "Touch elder's feet for blessings", "Use both hands when receiving items"],
        etiquette: ["Greet elders first", "Use respectful language", "Accept offered food/drink"],
        commonPhrases: ["வணக்கம்", "எப்படி இருக்கீங்க?", "நல்லா இருக்கேன்"],
        backgroundInfo: "Tamil hospitality is legendary, with guests treated as divine beings",
        tips: ["Always greet with வணக்கம்", "Show respect to elders", "Accept hospitality graciously"],
        doAndDonts: ["Do: Use both hands when greeting", "Don't: Point with one finger", "Do: Remove footwear"],
        regionalVariations: ["Chennai: More formal greetings", "Rural areas: Traditional customs stronger"],
        historicalContext: "Tamil greeting customs date back to ancient Sangam literature",
        modernUsage: "Modern Tamils blend traditional respect with contemporary convenience"
    )

    static let familyCulturalContext: CulturalContext = CulturalContext(
        scenario: .tamilWedding,
        setting: "Tamil family gathering",
        participants: ["Extended family members", "Elders", "Children"],
        socialNorms: ["Respect for elders", "Family hierarchy", "Collective decision making"],
        etiquette: ["Seek blessings from elders", "Use appropriate relationship terms", "Share meals together"],
        commonPhrases: ["அம்மா", "அப்பா", "தாத்தா", "பாட்டி"],
        backgroundInfo: "Tamil families are traditionally joint families with strong bonds",
        tips: ["Learn all relationship terms", "Show respect to elders", "Participate in family traditions"],
        doAndDonts: ["Do: Touch elder's feet", "Don't: Interrupt elders", "Do: Help with family duties"],
        regionalVariations: ["Urban: Nuclear families common", "Rural: Joint families prevalent"],
        historicalContext: "Tamil family structure has ancient roots in Sangam literature",
        modernUsage: "Modern families adapt traditions to contemporary lifestyles"
    )

    static let numberCulturalContext: CulturalContext = CulturalContext(
        scenario: .tamilFestival,
        setting: "Traditional Tamil counting and mathematics",
        participants: ["Teacher", "Students", "Merchants"],
        socialNorms: ["Respect for mathematical knowledge", "Practical application in daily life"],
        etiquette: ["Listen carefully to pronunciation", "Practice counting aloud"],
        commonPhrases: ["ஒன்று", "இரண்டு", "மூன்று", "நான்கு"],
        backgroundInfo: "Tamil has its own ancient numeral system with unique cultural significance",
        tips: ["Practice counting daily objects", "Learn both traditional and modern usage"],
        doAndDonts: ["Do: Use Tamil numbers in cultural contexts", "Don't: Mix number systems"],
        regionalVariations: ["Traditional: Ancient Tamil numerals", "Modern: Contemporary usage"],
        historicalContext: "Tamil mathematics has ancient roots in trade and astronomy",
        modernUsage: "Tamil numbers are still used in traditional contexts and cultural celebrations"
    )

    static let festivalCulturalContext: CulturalContext = CulturalContext(
        scenario: .tamilFestival,
        setting: "Tamil festival celebration",
        participants: ["Community members", "Priests", "Families"],
        socialNorms: ["Community participation", "Religious observance", "Cultural preservation"],
        etiquette: ["Dress appropriately", "Participate respectfully", "Follow traditions"],
        commonPhrases: ["பொங்கல்", "தீபாவளி", "நவராத்திரி", "கார்த்திகை"],
        backgroundInfo: "Tamil festivals connect communities to their cultural and spiritual heritage",
        tips: ["Learn festival vocabulary", "Understand cultural significance", "Participate actively"],
        doAndDonts: ["Do: Respect traditions", "Don't: Ignore customs", "Do: Learn meanings"],
        regionalVariations: ["Tamil Nadu: State celebrations", "Global: Diaspora adaptations"],
        historicalContext: "Tamil festivals have ancient origins in agricultural and spiritual cycles",
        modernUsage: "Modern celebrations blend tradition with contemporary practices"
    )

    static let foodCulturalContext: CulturalContext = CulturalContext(
        scenario: .tamilFestival,
        setting: "Traditional Tamil kitchen and dining",
        participants: ["Cooks", "Family members", "Guests"],
        socialNorms: ["Hospitality", "Sharing meals", "Respect for food"],
        etiquette: ["Wash hands before eating", "Accept offered food", "Appreciate preparation"],
        commonPhrases: ["சாப்பாடு", "ருசி", "ஆறு சுவை", "உணவு"],
        backgroundInfo: "Tamil cuisine reflects ancient wisdom about nutrition and health",
        tips: ["Learn food vocabulary", "Understand six tastes", "Appreciate cultural significance"],
        doAndDonts: ["Do: Try traditional foods", "Don't: Waste food", "Do: Learn preparation methods"],
        regionalVariations: ["Regional: Different specialties", "Seasonal: Festival foods"],
        historicalContext: "Tamil food culture has roots in Ayurvedic principles and local agriculture",
        modernUsage: "Modern Tamil cuisine adapts traditional principles to contemporary lifestyles"
    )

    static let scriptCulturalContext: CulturalContext = CulturalContext(
        scenario: .tamilFestival,
        setting: "Tamil script learning environment",
        participants: ["Teachers", "Students", "Scholars"],
        socialNorms: ["Respect for written language", "Careful practice", "Cultural preservation"],
        etiquette: ["Practice regularly", "Show respect for script", "Learn proper formation"],
        commonPhrases: ["எழுத்து", "வரி", "புத்தகம்", "கல்வி"],
        backgroundInfo: "Tamil script is one of the world's oldest continuously used writing systems",
        tips: ["Practice letter formation", "Learn stroke order", "Understand cultural importance"],
        doAndDonts: ["Do: Practice daily", "Don't: Rush learning", "Do: Respect the script"],
        regionalVariations: ["Traditional: Classical forms", "Modern: Contemporary usage"],
        historicalContext: "Tamil script has evolved over 2000 years while maintaining its essence",
        modernUsage: "Modern Tamil script is used in digital media while preserving traditional forms"
    )

    static let literatureCulturalContext: CulturalContext = CulturalContext(
        scenario: .tamilFestival,
        setting: "Tamil literary gathering",
        participants: ["Poets", "Scholars", "Literature enthusiasts"],
        socialNorms: ["Appreciation of literary excellence", "Respect for poets", "Cultural learning"],
        etiquette: ["Listen attentively", "Ask thoughtful questions", "Show appreciation"],
        commonPhrases: ["கவிதை", "இலக்கியம்", "திருக்குறள்", "சங்க இலக்கியம்"],
        backgroundInfo: "Tamil literature spans over 2000 years with rich poetic traditions",
        tips: ["Start with simple poems", "Learn about famous poets", "Understand cultural context"],
        doAndDonts: ["Do: Appreciate beauty", "Don't: Rush understanding", "Do: Learn gradually"],
        regionalVariations: ["Classical: Ancient works", "Modern: Contemporary literature"],
        historicalContext: "Tamil literature includes ancient Sangam poetry and classical works",
        modernUsage: "Modern Tamil literature continues ancient traditions while exploring new themes"
    )

    static let businessCulturalContext: CulturalContext = CulturalContext(
        scenario: .englishBusinessMeeting,
        setting: "Professional Tamil business environment",
        participants: ["Business professionals", "Clients", "Colleagues"],
        socialNorms: ["Professional courtesy", "Respectful communication", "Cultural sensitivity"],
        etiquette: ["Use formal language", "Show respect to seniors", "Maintain professionalism"],
        commonPhrases: ["வணிகம்", "வேலை", "அலுவலகம்", "சந்திப்பு"],
        backgroundInfo: "Tamil business culture blends traditional respect with modern professionalism",
        tips: ["Learn formal vocabulary", "Understand hierarchy", "Practice professional phrases"],
        doAndDonts: ["Do: Use respectful titles", "Don't: Be overly casual", "Do: Show cultural awareness"],
        regionalVariations: ["Urban: International business", "Traditional: Local commerce"],
        historicalContext: "Tamil business traditions have ancient roots in trade and commerce",
        modernUsage: "Modern Tamil business adapts traditional values to global practices"
    )
}
