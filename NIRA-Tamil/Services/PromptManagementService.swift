import Foundation
import Combine

// MARK: - Prompt Management Service
@MainActor
class PromptManagementService: ObservableObject {
    static let shared = PromptManagementService()
    
    @Published var promptTemplates: [PromptTemplate] = []
    @Published var contextualPrompts: [String: PromptTemplate] = [:]
    @Published var userCustomPrompts: [PromptTemplate] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseClient = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        loadDefaultPrompts()
        loadUserCustomPrompts()
    }
    
    // MARK: - Default Prompt Templates
    
    private func loadDefaultPrompts() {
        promptTemplates = [
            // Tamil Learning Assistant Prompts
            PromptTemplate(
                id: "tamil_tutor_general",
                name: "Tamil Learning Assistant",
                category: .learning,
                systemPrompt: """
                You are <PERSON><PERSON>, an expert Tamil language tutor with deep knowledge of Tamil culture, literature, and linguistics. You are patient, encouraging, and culturally sensitive.
                
                Your expertise includes:
                - Tamil grammar, vocabulary, and pronunciation
                - Tamil literature from ancient to modern times
                - Cultural context and traditions
                - Learning psychology and pedagogy
                
                Always:
                - Provide clear, step-by-step explanations
                - Use examples relevant to Tamil culture
                - Encourage learners and celebrate progress
                - Correct mistakes gently with explanations
                - Adapt your teaching style to the learner's level
                """,
                userPromptTemplate: "Help me learn Tamil: {query}",
                variables: ["query"],
                tags: ["tamil", "learning", "culture"],
                isActive: true
            ),
            
            PromptTemplate(
                id: "tamil_conversation_practice",
                name: "Tamil Conversation Partner",
                category: .conversation,
                systemPrompt: """
                You are Arun, a friendly Tamil conversation partner who helps learners practice speaking Tamil in real-world scenarios.
                
                Your role:
                - Engage in natural Tamil conversations
                - Provide gentle corrections and suggestions
                - Introduce new vocabulary contextually
                - Share cultural insights during conversations
                - Adapt complexity to learner's level
                
                Conversation style:
                - Use appropriate Tamil greetings and expressions
                - Include cultural references and context
                - Ask follow-up questions to encourage speaking
                - Provide romanization when helpful
                """,
                userPromptTemplate: "Let's practice Tamil conversation about: {topic}",
                variables: ["topic"],
                tags: ["conversation", "practice", "speaking"],
                isActive: true
            ),
            
            PromptTemplate(
                id: "tamil_writing_coach",
                name: "Tamil Writing Coach",
                category: .writing,
                systemPrompt: """
                You are Kavitha, a Tamil writing specialist who helps learners master Tamil script and composition.
                
                Your expertise:
                - Tamil script formation and stroke order
                - Handwriting improvement techniques
                - Composition and creative writing
                - Grammar and style refinement
                
                Teaching approach:
                - Break down complex characters into simple strokes
                - Provide visual descriptions and memory aids
                - Offer progressive writing exercises
                - Give constructive feedback on writing samples
                """,
                userPromptTemplate: "Help me improve my Tamil writing: {writing_request}",
                variables: ["writing_request"],
                tags: ["writing", "script", "composition"],
                isActive: true
            ),
            
            PromptTemplate(
                id: "tamil_cultural_guide",
                name: "Tamil Cultural Guide",
                category: .cultural,
                systemPrompt: """
                You are a knowledgeable Tamil cultural guide who helps learners understand the rich heritage and traditions of Tamil culture.
                
                Your knowledge spans:
                - Tamil festivals, traditions, and customs
                - Classical and contemporary Tamil literature
                - Tamil music, dance, and arts
                - Historical and modern Tamil society
                - Regional variations and dialects
                
                Always provide:
                - Cultural context for language learning
                - Historical background when relevant
                - Modern applications of traditional concepts
                - Respectful and accurate cultural information
                """,
                userPromptTemplate: "Tell me about Tamil culture: {cultural_topic}",
                variables: ["cultural_topic"],
                tags: ["culture", "traditions", "history"],
                isActive: true
            )
        ]
    }
    
    // MARK: - Context-Aware Prompt Building
    
    func buildContextualPrompt(
        for scenario: PromptLearningScenario,
        userLevel: PromptProficiencyLevel,
        userProgress: PromptUserProgress,
        currentLesson: String? = nil
    ) -> PromptTemplate {
        
        let baseTemplate = getBaseTemplate(for: scenario)
        let contextualEnhancements = buildContextualEnhancements(
            userLevel: userLevel,
            userProgress: userProgress,
            currentLesson: currentLesson
        )
        
        let enhancedSystemPrompt = """
        \(baseTemplate.systemPrompt)
        
        LEARNER CONTEXT:
        - Current Level: \(userLevel.displayName)
        - Lessons Completed: \(userProgress.completedLessons.count)
        - Strengths: \(userProgress.strengths.joined(separator: ", "))
        - Areas for Improvement: \(userProgress.weakAreas.joined(separator: ", "))
        \(currentLesson.map { "- Current Lesson: \($0)" } ?? "")
        
        ADAPTIVE INSTRUCTIONS:
        \(contextualEnhancements)
        """
        
        return PromptTemplate(
            id: "contextual_\(scenario.rawValue)_\(UUID().uuidString)",
            name: "Contextual \(baseTemplate.name)",
            category: baseTemplate.category,
            systemPrompt: enhancedSystemPrompt,
            userPromptTemplate: baseTemplate.userPromptTemplate,
            variables: baseTemplate.variables,
            tags: baseTemplate.tags + ["contextual", "adaptive"],
            isActive: true
        )
    }
    
    private func getBaseTemplate(for scenario: PromptLearningScenario) -> PromptTemplate {
        switch scenario {
        case .generalLearning:
            return promptTemplates.first { $0.id == "tamil_tutor_general" }!
        case .conversationPractice:
            return promptTemplates.first { $0.id == "tamil_conversation_practice" }!
        case .writingPractice:
            return promptTemplates.first { $0.id == "tamil_writing_coach" }!
        case .culturalExploration:
            return promptTemplates.first { $0.id == "tamil_cultural_guide" }!
        }
    }
    
    private func buildContextualEnhancements(
        userLevel: PromptProficiencyLevel,
        userProgress: PromptUserProgress,
        currentLesson: String?
    ) -> String {
        var enhancements: [String] = []
        
        // Level-specific adaptations
        switch userLevel {
        case .beginner:
            enhancements.append("- Use simple vocabulary and basic sentence structures")
            enhancements.append("- Provide romanization alongside Tamil script")
            enhancements.append("- Focus on fundamental concepts and building confidence")
            
        case .intermediate:
            enhancements.append("- Introduce more complex grammar patterns")
            enhancements.append("- Gradually reduce romanization dependency")
            enhancements.append("- Challenge with varied sentence structures")
            
        case .advanced:
            enhancements.append("- Use sophisticated vocabulary and idioms")
            enhancements.append("- Focus on nuanced cultural expressions")
            enhancements.append("- Encourage creative and complex language use")
        }
        
        // Progress-based adaptations
        if userProgress.weakAreas.contains("pronunciation") {
            enhancements.append("- Pay special attention to pronunciation guidance")
            enhancements.append("- Provide phonetic breakdowns for difficult words")
        }
        
        if userProgress.weakAreas.contains("grammar") {
            enhancements.append("- Reinforce grammar rules with clear examples")
            enhancements.append("- Connect new concepts to previously learned grammar")
        }
        
        if userProgress.strengths.contains("vocabulary") {
            enhancements.append("- Build on strong vocabulary knowledge")
            enhancements.append("- Introduce advanced synonyms and word variations")
        }
        
        return enhancements.joined(separator: "\n")
    }
    
    // MARK: - Custom Prompt Management
    
    func createCustomPrompt(
        name: String,
        category: PromptCategory,
        systemPrompt: String,
        userPromptTemplate: String,
        variables: [String] = [],
        tags: [String] = []
    ) async throws -> PromptTemplate {
        
        let customPrompt = PromptTemplate(
            id: "custom_\(UUID().uuidString)",
            name: name,
            category: category,
            systemPrompt: systemPrompt,
            userPromptTemplate: userPromptTemplate,
            variables: variables,
            tags: tags + ["custom"],
            isActive: true,
            isCustom: true,
            createdAt: Date()
        )
        
        // Save to Supabase
        try await saveCustomPromptToDatabase(customPrompt)
        
        // Add to local collection
        userCustomPrompts.append(customPrompt)
        
        return customPrompt
    }
    
    private func saveCustomPromptToDatabase(_ prompt: PromptTemplate) async throws {
        // Implementation for saving to Supabase
        // This would save to a custom_prompts table
    }
    
    private func loadUserCustomPrompts() {
        // Load user's custom prompts from Supabase
        Task {
            // Implementation for loading from database
            userCustomPrompts = []
        }
    }
    
    // MARK: - Prompt Optimization
    
    func optimizePrompt(
        _ template: PromptTemplate,
        basedOnInteractions: [ChatInteraction]
    ) -> PromptTemplate {
        
        let analysis = analyzeInteractionPatterns(interactions: basedOnInteractions)
        let optimizations = generateOptimizations(from: analysis)
        
        let optimizedSystemPrompt = applyOptimizations(
            to: template.systemPrompt,
            optimizations: optimizations
        )
        
        return PromptTemplate(
            id: "optimized_\(template.id)",
            name: "Optimized \(template.name)",
            category: template.category,
            systemPrompt: optimizedSystemPrompt,
            userPromptTemplate: template.userPromptTemplate,
            variables: template.variables,
            tags: template.tags + ["optimized"],
            isActive: true,
            parentPromptId: template.id
        )
    }
    
    private func analyzeInteractionPatterns(interactions: [ChatInteraction]) -> InteractionAnalysis {
        // Analyze user interactions to identify patterns
        return InteractionAnalysis(
            commonQuestionTypes: [],
            difficultyAreas: [],
            successfulApproaches: [],
            userPreferences: []
        )
    }
    
    private func generateOptimizations(from analysis: InteractionAnalysis) -> [PromptOptimization] {
        // Generate specific optimizations based on analysis
        return []
    }
    
    private func applyOptimizations(
        to systemPrompt: String,
        optimizations: [PromptOptimization]
    ) -> String {
        var optimizedPrompt = systemPrompt
        
        for optimization in optimizations {
            optimizedPrompt = optimization.apply(to: optimizedPrompt)
        }
        
        return optimizedPrompt
    }
    
    // MARK: - Prompt Execution
    
    func executePrompt(
        _ template: PromptTemplate,
        with variables: [String: String]
    ) -> ExecutablePrompt {
        
        var finalUserPrompt = template.userPromptTemplate
        
        // Replace variables in user prompt
        for (key, value) in variables {
            finalUserPrompt = finalUserPrompt.replacingOccurrences(
                of: "{\(key)}",
                with: value
            )
        }
        
        return ExecutablePrompt(
            systemPrompt: template.systemPrompt,
            userPrompt: finalUserPrompt,
            metadata: PromptMetadata(
                templateId: template.id,
                templateName: template.name,
                category: template.category,
                variables: variables,
                executedAt: Date()
            )
        )
    }
}
