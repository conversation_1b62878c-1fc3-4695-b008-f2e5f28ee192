//
//  MockDataService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation
import SwiftUI

// MARK: - Centralized Mock Data Service

class MockDataService: ObservableObject {
    static let shared = MockDataService()
    
    private init() {}
    
    // MARK: - User Mock Data
    
    static let mockUsers: [User] = [
        User(
            id: UUID(),
            email: "<EMAIL>",
            firstName: "Priya",
            lastName: "Krishnan",
            auth0ID: "auth0|priya123",
            preferredLanguages: [.tamil],
            currentLevel: .intermediate,
            targetLanguages: [.tamil, .english],
            currentStreak: 15,
            longestStreak: 28,
            totalLessonsCompleted: 47,
            totalPointsEarned: 2340,
            profileImageURL: "https://example.com/priya.jpg"
        ),
        User(
            id: UUID(),
            email: "<EMAIL>",
            firstName: "<PERSON><PERSON><PERSON>",
            lastName: "<PERSON>",
            auth0ID: "auth0|arjun456",
            preferredLanguages: [.english],
            currentLevel: .beginner,
            targetLanguages: [.tamil],
            currentStreak: 7,
            longestStreak: 12,
            totalLessonsCompleted: 23,
            totalPointsEarned: 1150,
            profileImageURL: "https://example.com/arjun.jpg"
        )
    ]
    
    // MARK: - Lesson Mock Data
    
    static let mockLessons: [Lesson] = [
        Lesson(
            id: UUID(),
            title: "Tamil Greetings & Basic Phrases",
            lessonDescription: "Learn essential Tamil greetings and everyday phrases used in Tamil Nadu",
            language: .tamil,
            difficulty: .beginner,
            category: .conversation,
            estimatedDuration: 15,
            exercises: mockExercises,
            culturalContext: mockCulturalContexts[0],
            prerequisites: [],
            tags: ["greetings", "basic", "conversation", "culture"],
            audioURL: "https://example.com/audio/lesson1.mp3",
            imageURL: "https://example.com/images/tamil-greetings.jpg",
            totalPoints: 100,
            isAIGenerated: false
        ),
        Lesson(
            id: UUID(),
            title: "Tamil Family & Relationships",
            lessonDescription: "Explore Tamil family terms and relationship vocabulary with cultural context",
            language: .tamil,
            difficulty: .beginner,
            category: .vocabulary,
            estimatedDuration: 20,
            exercises: mockExercises,
            culturalContext: mockCulturalContexts[1],
            prerequisites: [],
            tags: ["family", "relationships", "culture", "vocabulary"],
            audioURL: "https://example.com/audio/lesson2.mp3",
            imageURL: "https://example.com/images/tamil-family.jpg",
            totalPoints: 120,
            isAIGenerated: false
        ),
        Lesson(
            id: UUID(),
            title: "Tamil Festival Celebrations",
            lessonDescription: "Learn about major Tamil festivals and associated vocabulary",
            language: .tamil,
            difficulty: .intermediate,
            category: .culture,
            estimatedDuration: 25,
            exercises: mockExercises,
            culturalContext: mockCulturalContexts[2],
            prerequisites: [],
            tags: ["festivals", "culture", "traditions", "celebrations"],
            audioURL: "https://example.com/audio/lesson3.mp3",
            imageURL: "https://example.com/images/tamil-festivals.jpg",
            totalPoints: 150,
            isAIGenerated: false
        ),
        Lesson(
            id: UUID(),
            title: "Tamil Script Fundamentals",
            lessonDescription: "Master the basics of Tamil script writing and reading",
            language: .tamil,
            difficulty: .beginner,
            category: .writing,
            estimatedDuration: 30,
            exercises: mockExercises,
            culturalContext: nil,
            prerequisites: [],
            tags: ["script", "writing", "reading", "fundamentals"],
            audioURL: "https://example.com/audio/lesson4.mp3",
            imageURL: "https://example.com/images/tamil-script.jpg",
            totalPoints: 180,
            isAIGenerated: false
        ),
        Lesson(
            id: UUID(),
            title: "Tamil Cuisine & Food Culture",
            lessonDescription: "Discover Tamil food vocabulary and dining customs",
            language: .tamil,
            difficulty: .intermediate,
            category: .culture,
            estimatedDuration: 22,
            exercises: mockExercises,
            culturalContext: mockCulturalContexts[3],
            prerequisites: [],
            tags: ["food", "cuisine", "culture", "dining"],
            audioURL: "https://example.com/audio/lesson5.mp3",
            imageURL: "https://example.com/images/tamil-food.jpg",
            totalPoints: 140,
            isAIGenerated: false
        )
    ]
    
    // MARK: - Exercise Mock Data
    
    static let mockExercises: [Exercise] = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "How do you say 'Hello' in Tamil?",
            options: ["வணக்கம்", "நமஸ்காரம்", "ஆதாப்", "சலாம்"],
            correctAnswer: 0,
            explanation: "வணக்கம் (Vanakkam) is the most common Tamil greeting, used throughout the day.",
            audioURL: "https://example.com/audio/vanakkam.mp3",
            imageURL: nil,
            points: 10,
            hints: ["This greeting is used at any time of day", "It's a respectful way to greet someone"],
            culturalNote: "வணக்கம் comes from the root word 'வணங்கு' meaning to bow or worship, showing respect.",
            difficulty: .beginner
        ),
        Exercise(
            id: UUID(),
            type: .fillInBlank,
            question: "Complete the sentence: நான் _____ பேசுகிறேன் (I speak Tamil)",
            options: ["தமிழ்", "ஆங்கிலம்", "இந்தி", "தெலுங்கு"],
            correctAnswer: 0,
            explanation: "தமிழ் (Tamil) is the word for the Tamil language.",
            audioURL: "https://example.com/audio/naan-tamil-pesugireen.mp3",
            imageURL: nil,
            points: 15,
            hints: ["Think about what language we're learning", "The word sounds similar to 'Tamil'"],
            culturalNote: "Tamil is one of the oldest living languages in the world, with literature dating back over 2000 years.",
            difficulty: .beginner
        )
    ]
    
    // MARK: - Cultural Context Mock Data
    
    static let mockCulturalContexts: [CulturalContext] = [
        CulturalContext(
            scenario: .tamilFestival,
            setting: "Traditional Tamil home entrance",
            participants: ["Host", "Guest", "Family members"],
            socialNorms: ["Remove shoes before entering", "Touch elder's feet for blessings", "Use both hands when receiving items"],
            etiquette: ["Greet elders first", "Use respectful language", "Accept offered food/drink"],
            commonPhrases: ["வணக்கம்", "எப்படி இருக்கீங்க?", "நல்லா இருக்கேன்"],
            backgroundInfo: "Tamil hospitality is legendary, with guests treated as divine beings",
            tips: ["Always greet with வணக்கம்", "Show respect to elders", "Accept hospitality graciously"],
            doAndDonts: ["Do: Use both hands when greeting", "Don't: Point with one finger", "Do: Remove footwear"],
            regionalVariations: ["Chennai: More formal greetings", "Rural areas: Traditional customs stronger"],
            historicalContext: "Tamil greeting customs date back to ancient Sangam literature",
            modernUsage: "Modern Tamils blend traditional respect with contemporary convenience"
        )
    ]
}

// MARK: - Mock Data Extensions

extension MockDataService {
    
    // MARK: - Progress Mock Data
    
    static let mockProgress: [Progress] = [
        Progress(
            userID: mockUsers[0].id,
            lessonID: mockLessons[0].id,
            language: .tamil,
            category: .conversation,
            difficulty: .beginner,
            score: 85,
            maxScore: 100,
            completedAt: Date().addingTimeInterval(-86400), // Yesterday
            timeSpent: 900, // 15 minutes
            exerciseResults: mockExerciseResults,
            isCompleted: true,
            attempts: 1,
            bestScore: 85,
            streakContribution: true,
            difficultyAtCompletion: .beginner,
            feedback: "Great job on pronunciation! Keep practicing the greeting variations.",
            notes: "Struggled with formal vs informal greetings initially"
        ),
        Progress(
            userID: mockUsers[0].id,
            lessonID: mockLessons[1].id,
            language: .tamil,
            category: .vocabulary,
            difficulty: .beginner,
            score: 92,
            maxScore: 120,
            completedAt: Date().addingTimeInterval(-172800), // 2 days ago
            timeSpent: 1200, // 20 minutes
            exerciseResults: mockExerciseResults,
            isCompleted: true,
            attempts: 2,
            bestScore: 92,
            streakContribution: true,
            difficultyAtCompletion: .beginner,
            feedback: "Excellent understanding of family relationships in Tamil culture!",
            notes: "Found the relationship terms fascinating"
        )
    ]
    
    // MARK: - Exercise Results Mock Data
    
    static let mockExerciseResults: [ExerciseResult] = [
        ExerciseResult(
            exerciseID: mockExercises[0].id,
            exerciseType: .multipleChoice,
            userAnswer: "வணக்கம்",
            correctAnswer: "வணக்கம்",
            isCorrect: true,
            score: 10,
            maxScore: 10,
            timeSpent: 15,
            hintsUsed: 0,
            attempts: 1
        ),
        ExerciseResult(
            exerciseID: mockExercises[1].id,
            exerciseType: .fillInBlank,
            userAnswer: "தமிழ்",
            correctAnswer: "தமிழ்",
            isCorrect: true,
            score: 12,
            maxScore: 15,
            timeSpent: 25,
            hintsUsed: 1,
            attempts: 1
        )
    ]

    // MARK: - Achievement Mock Data

    static let mockAchievements: [Achievement] = [
        Achievement(
            type: .weekStreak,
            title: "Tamil Dedication",
            achievementDescription: "Maintained a 7-day learning streak",
            iconName: "flame.fill",
            unlockedDate: Date().addingTimeInterval(-604800), // 1 week ago
            value: 100,
            userID: mockUsers[0].id
        ),
        Achievement(
            type: .firstLesson,
            title: "Tamil Beginner",
            achievementDescription: "Completed your first Tamil lesson",
            iconName: "graduationcap.fill",
            unlockedDate: Date().addingTimeInterval(-259200), // 3 days ago
            value: 200,
            userID: mockUsers[0].id
        ),
        Achievement(
            type: .culturalExpert,
            title: "Cultural Explorer",
            achievementDescription: "Learning about Tamil festivals and culture",
            iconName: "sparkles",
            unlockedDate: Date().addingTimeInterval(-86400), // 1 day ago
            value: 150,
            userID: mockUsers[0].id
        )
    ]

    // MARK: - Thirukkural Mock Data

    static let mockThirukkurals: [Thirukkural] = [
        Thirukkural(
            number: 1,
            chapter: "கடவுள் வாழ்த்து",
            chapterEnglish: "Praise of God",
            tamilText: "அகர முதல எழுத்தெல்லாம் ஆதி\nபகவன் முதற்றே உலகு",
            transliteration: "Agara mudala ezhuththellaam aadhi\nBhagavan mudhatre ulagu",
            englishTranslation: "A, as its first of letters, every speech maintains;\nThe Primal Deity is first through all the world's domains.",
            meaning: "Just as the letter 'A' is the first of all letters, God is the first and foremost of all things in the world.",
            explanation: "This opening verse of Thirukkural establishes the supremacy of the divine. Thiruvalluvar begins by acknowledging that just as 'A' (அ) is the foundation of the Tamil alphabet and all learning, God is the foundation of all existence.",
            keywords: ["God", "First", "Foundation", "Supreme", "Beginning"],
            category: .virtue,
            section: .dharma,
            modernRelevance: "In today's world, this reminds us of the importance of having strong foundations in our values and beliefs.",
            culturalContext: "This reflects the Tamil tradition of beginning any important work with an invocation to the divine.",
            lifeApplication: "Start your endeavors with humility and acknowledgment of higher principles.",
            relatedConcepts: ["Humility", "Foundation", "Respect", "Tradition"],
            difficulty: .beginner,
            audioURL: "https://example.com/audio/kural1.mp3"
        ),
        Thirukkural(
            number: 2,
            chapter: "கடவுள் வாழ்த்து",
            chapterEnglish: "Praise of God",
            tamilText: "கற்றதனால் ஆய பயனென்கொல் வாலறிவன்\nநற்றாள் தொழாஅர் எனின்",
            transliteration: "Katradhanaal aaya payanen kol vaalariwan\nNatraal thozhaaar enin",
            englishTranslation: "What Profit have those derived from learning, who worship not\nThe good feet of Him who is possessed of pure knowledge?",
            meaning: "What is the use of learning if one does not worship the feet of God who possesses pure knowledge?",
            explanation: "True education and learning should lead to humility and recognition of the divine source of all knowledge.",
            keywords: ["Learning", "Knowledge", "Worship", "Humility", "Education"],
            category: .virtue,
            section: .dharma,
            modernRelevance: "Education without values and humility can lead to arrogance rather than wisdom.",
            culturalContext: "Tamil culture emphasizes that knowledge should make one humble, not proud.",
            lifeApplication: "Let learning make you humble and grateful rather than arrogant.",
            relatedConcepts: ["Education", "Humility", "Gratitude", "Wisdom"],
            difficulty: .intermediate,
            audioURL: "https://example.com/audio/kural2.mp3"
        ),
        Thirukkural(
            number: 38,
            chapter: "இல்வாழ்க்கை",
            chapterEnglish: "Domestic Life",
            tamilText: "இல்லதென் இல்லவள் மாண்பானால் உள்ளதென்\nஇல்லவள் மாணாக் கடை",
            transliteration: "Illadhen illaval maanbaanal ulladhen\nIllaval maanaak kadai",
            englishTranslation: "The house is not a house, though goodly wife be there;\nWithout a wife, the house is desert bare.",
            meaning: "A house without a virtuous wife is not truly a home; with a wife lacking virtue, even a palace becomes worthless.",
            explanation: "This kural emphasizes the importance of a virtuous partner in making a house a true home.",
            keywords: ["Home", "Wife", "Virtue", "Partnership", "Family"],
            category: .virtue,
            section: .dharma,
            modernRelevance: "In modern relationships, this speaks to the importance of mutual respect and virtue in partnerships.",
            culturalContext: "Tamil culture places great emphasis on the role of women in maintaining family harmony.",
            lifeApplication: "Value and cultivate virtue in your relationships and partnerships.",
            relatedConcepts: ["Partnership", "Respect", "Family", "Harmony"],
            difficulty: .intermediate,
            audioURL: "https://example.com/audio/kural38.mp3"
        )
    ]

    // MARK: - Tamil Calendar Events Mock Data

    static let mockTamilEvents: [TamilEvent] = [
        TamilEvent(
            id: UUID(),
            name: "Thai Pusam",
            tamilName: "தைப்பூசம்",
            date: Calendar.current.date(byAdding: .day, value: 15, to: Date()) ?? Date(),
            type: .festival,
            significance: "Festival dedicated to Lord Murugan, celebrated with great devotion",
            description: "Thai Pusam is one of the most important festivals for Tamil Hindus, dedicated to Lord Murugan. Devotees carry kavadi (burden) as a form of sacrifice and devotion.",
            traditions: ["Kavadi carrying", "Fasting", "Temple visits", "Prayers to Lord Murugan"],
            modernCelebration: "Celebrated worldwide by Tamil communities with processions and temple ceremonies",
            region: .all,
            isAuspicious: true,
            culturalImportance: .high,
            learningContent: [
                LearningContent(
                    title: "Thai Pusam Vocabulary",
                    content: "கவடி (Kavadi), முருகன் (Murugan), தைப்பூசம் (Thai Pusam)",
                    type: .vocabulary,
                    difficulty: "Beginner"
                )
            ]
        ),
        TamilEvent(
            id: UUID(),
            name: "Pongal",
            tamilName: "பொங்கல்",
            date: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
            type: .harvest,
            significance: "Tamil harvest festival celebrating the sun, nature, and prosperity",
            description: "Pongal is the most important festival in Tamil Nadu, marking the harvest season and thanking nature for its bounty.",
            traditions: ["Cooking Pongal dish", "Decorating homes", "Cattle worship", "Sugarcane offerings"],
            modernCelebration: "Four-day celebration with family gatherings, traditional foods, and cultural programs",
            region: .all,
            isAuspicious: true,
            culturalImportance: .high,
            learningContent: [
                LearningContent(
                    title: "Pongal Traditions",
                    content: "Learn about the four days of Pongal: Bhogi, Thai Pongal, Mattu Pongal, and Kaanum Pongal",
                    type: .culture,
                    difficulty: "Intermediate"
                )
            ]
        )
    ]

    // MARK: - Cultural Insights Mock Data

    static let mockCulturalInsights: [TamilCulturalInsight] = [
        TamilCulturalInsight(
            title: "Tamil Classical Music Ragas",
            tamilTitle: "தமிழ் இசை ராகங்கள்",
            description: "Explore the emotional depth of Carnatic music through traditional ragas",
            category: .music,
            content: "Carnatic music, the classical music tradition of South India, is built on the foundation of ragas - melodic frameworks that evoke specific emotions and moods. Each raga has its own personality, time of day for performance, and emotional character. The Tamil tradition has contributed significantly to this art form with compositions by great musicians like Thyagaraja and Muthuswami Dikshitar.",
            tags: ["music", "ragas", "carnatic", "classical", "emotion"],
            difficulty: .intermediate,
            readingTime: 5,
            culturalSignificance: "Carnatic music represents the pinnacle of Tamil musical achievement and spiritual expression",
            modernRelevance: "Contemporary musicians worldwide study Carnatic ragas for their complexity and emotional depth"
        ),
        TamilCulturalInsight(
            title: "Tamil Cuisine Philosophy",
            tamilTitle: "தமிழ் உணவு தத்துவம்",
            description: "Understanding the science and spirituality behind traditional Tamil food",
            category: .food,
            content: "Tamil cuisine is based on the ancient principle of six tastes (Aaru Ruchi): sweet, sour, salty, bitter, pungent, and astringent. This philosophy, rooted in Ayurveda, ensures balanced nutrition and health. Every Tamil meal is designed to include all six tastes, promoting both physical health and mental well-being. The traditional banana leaf serving enhances flavors and provides natural antimicrobial properties.",
            tags: ["food", "cuisine", "ayurveda", "health", "tradition"],
            difficulty: .beginner,
            readingTime: 4,
            culturalSignificance: "Tamil food culture represents a holistic approach to health and community bonding",
            modernRelevance: "Modern nutritional science validates many traditional Tamil dietary practices and food combinations"
        ),
        TamilCulturalInsight(
            title: "Tamil Architecture Marvels",
            tamilTitle: "தமிழ் கட்டிடக்கலை",
            description: "Discover the engineering genius behind Tamil temple architecture",
            category: .architecture,
            content: "Tamil temple architecture represents one of the world's most sophisticated building traditions. The Dravidian style, characterized by towering gopurams (gateway towers), intricate sculptures, and precise mathematical proportions, demonstrates advanced knowledge of engineering, acoustics, and astronomy. Temples like Brihadeeswarar Temple in Thanjavur showcase techniques that modern engineers still study.",
            tags: ["architecture", "temples", "engineering", "dravidian", "gopuram"],
            difficulty: .advanced,
            readingTime: 7,
            culturalSignificance: "Tamil architecture reflects the civilization's mastery of science, art, and spirituality",
            modernRelevance: "Modern architects study Tamil temple design for sustainable building practices and acoustic engineering"
        ),
        TamilCulturalInsight(
            title: "Tamil Literary Traditions",
            tamilTitle: "தமிழ் இலக்கிய மரபு",
            description: "Journey through the rich literary heritage of Tamil culture",
            category: .literature,
            content: "Tamil literature spans over 2,000 years, from the ancient Sangam poetry to modern works. The Sangam literature (300 BCE - 300 CE) provides insights into ancient Tamil society, love, war, and nature. Works like Thirukkural, Silappatikaram, and Manimekalai are considered masterpieces of world literature, offering timeless wisdom and artistic excellence.",
            tags: ["literature", "sangam", "poetry", "thirukkural", "ancient"],
            difficulty: .intermediate,
            readingTime: 6,
            culturalSignificance: "Tamil literature preserves the philosophical and cultural wisdom of one of the world's oldest civilizations",
            modernRelevance: "Tamil literary works continue to inspire contemporary writers and provide insights into human nature"
        )
    ]

    // MARK: - Tamil News Mock Data

    static let mockTamilNews: [TamilNewsItem] = [
        TamilNewsItem(
            id: UUID(),
            title: "Tamil Language Receives UNESCO Recognition",
            tamilTitle: "தமிழ் மொழிக்கு யுனெஸ்கோ அங்கீகாரம்",
            summary: "UNESCO acknowledges Tamil as one of the world's oldest living languages with rich cultural heritage",
            content: "The United Nations Educational, Scientific and Cultural Organization (UNESCO) has officially recognized Tamil as one of the world's oldest continuously spoken languages. This recognition highlights Tamil's 2,000+ year literary tradition and its significance in preserving ancient knowledge systems. The announcement comes as part of UNESCO's initiative to protect and promote linguistic diversity worldwide.",
            source: "Tamil Cultural Foundation",
            publishedAt: Date().addingTimeInterval(-3600), // 1 hour ago
            category: .culture,
            region: .global,
            imageURL: "https://example.com/images/unesco-tamil.jpg",
            tags: ["UNESCO", "recognition", "language", "heritage"],
            culturalRelevance: .high,
            learningValue: .high,
            isBreaking: true
        ),
        TamilNewsItem(
            id: UUID(),
            title: "Traditional Tamil Crafts Revival Program Launched",
            tamilTitle: "பாரம்பரிய தமிழ் கைவினைகள் மறுமலர்ச்சி திட்டம்",
            summary: "Government initiative aims to preserve and promote traditional Tamil handicrafts and artisan skills",
            content: "The Tamil Nadu government has launched a comprehensive program to revive traditional crafts including Kanchipuram silk weaving, Tanjore painting, and bronze sculpture. The initiative includes training programs for young artisans, market linkages, and international promotion of Tamil crafts. This effort aims to preserve cultural heritage while providing sustainable livelihoods.",
            source: "Tamil Nadu Government",
            publishedAt: Date().addingTimeInterval(-7200), // 2 hours ago
            category: .arts,
            region: .tamilNadu,
            imageURL: "https://example.com/images/tamil-crafts.jpg",
            tags: ["crafts", "artisans", "heritage", "revival"],
            culturalRelevance: .high,
            learningValue: .medium,
            isBreaking: false
        ),
        TamilNewsItem(
            id: UUID(),
            title: "International Tamil Conference Announced",
            tamilTitle: "சர்வதேச தமிழ் மாநாடு அறிவிப்பு",
            summary: "World Tamil scholars to gather for discussing language preservation and digital innovation",
            content: "The 12th World Tamil Conference will be held in Chennai next year, bringing together Tamil scholars, linguists, and technology experts from around the world. The conference will focus on digital preservation of Tamil literature, modern teaching methods, and the role of Tamil in the digital age. Expected participants include representatives from Tamil communities in Sri Lanka, Singapore, Malaysia, and other countries.",
            source: "International Tamil Association",
            publishedAt: Date().addingTimeInterval(-10800), // 3 hours ago
            category: .education,
            region: .global,
            imageURL: "https://example.com/images/tamil-conference.jpg",
            tags: ["conference", "scholars", "digital", "preservation"],
            culturalRelevance: .high,
            learningValue: .high,
            isBreaking: false
        )
    ]

    // MARK: - Learning Session Mock Data

    static let mockLearningSessions: [LearningSession] = [
        LearningSession(
            userID: mockUsers[0].id,
            startTime: Date().addingTimeInterval(-1800), // 30 minutes ago
            endTime: Date().addingTimeInterval(-900), // 15 minutes ago
            totalTimeSpent: 900, // 15 minutes
            lessonsCompleted: [mockLessons[0].id],
            exercisesAttempted: 5,
            exercisesCompleted: 4,
            totalScore: 85,
            maxPossibleScore: 100,
            streakDay: 15,
            language: .tamil,
            sessionType: .regular,
            interruptions: 1,
            notes: "Good focus today, struggled with pronunciation",
            mood: .excellent,
            energyLevel: .high,
            environment: .home,
            achievements: [.weekStreak]
        )
    ]
}
