//
//  AdvancedCachingService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import Combine

/// Advanced caching service with intelligent preloading and offline-first architecture
@MainActor
class AdvancedCachingService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = AdvancedCachingService()
    
    // MARK: - Published Properties
    @Published var cacheStatus: AdvancedCacheStatus = .idle
    @Published var preloadProgress: Double = 0.0
    @Published var offlineMode = false
    @Published var cacheMetrics: CacheMetrics?
    
    // MARK: - Private Properties
    private let localCache = LocalCacheService.shared
    private let supabaseService = SupabaseContentService.shared
    private let audioService = AudioFileManagementService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Cache configuration
    private let maxCacheSize: Int = 500 * 1024 * 1024 // 500MB
    private let preloadLevels: [CEFRLevel] = [.a1, .a2]
    private let cacheExpiryTime: TimeInterval = 24 * 60 * 60 // 24 hours
    
    // MARK: - Initialization
    private init() {
        print("🚀 AdvancedCachingService initialized")
        
        setupNetworkMonitoring()
        loadCacheMetrics()
        
        // Start background cache optimization
        startBackgroundOptimization()
    }
    
    // MARK: - Intelligent Preloading
    
    /// Preload essential content for offline use
    func preloadEssentialContent() async {
        cacheStatus = .preloading
        preloadProgress = 0.0
        
        let totalSteps = 4
        var currentStep = 0
        
        // Step 1: Preload A1 lessons
        await preloadLessons(for: .a1)
        currentStep += 1
        preloadProgress = Double(currentStep) / Double(totalSteps)
        
        // Step 2: Preload A2 lessons
        await preloadLessons(for: .a2)
        currentStep += 1
        preloadProgress = Double(currentStep) / Double(totalSteps)
        
        // Step 3: Preload vocabulary audio
        await preloadVocabularyAudio()
        currentStep += 1
        preloadProgress = Double(currentStep) / Double(totalSteps)
        
        // Step 4: Preload cultural content
        await preloadCulturalContent()
        currentStep += 1
        preloadProgress = Double(currentStep) / Double(totalSteps)
        
        cacheStatus = .ready
        updateCacheMetrics()
        
        print("✅ Essential content preloading completed")
    }
    
    /// Preload lessons for specific level
    private func preloadLessons(for level: CEFRLevel) async {
        await supabaseService.fetchLessons(for: level)

        // Cache lessons locally
        await localCache.cacheLessons(supabaseService.lessons, for: level.rawValue)

        // Preload related content for each lesson
        for lesson in supabaseService.lessons {
            await preloadLessonContent(lessonId: lesson.id)
        }
    }
    
    /// Preload content for specific lesson
    private func preloadLessonContent(lessonId: String) async {
        // Preload vocabulary
        let vocabulary = await supabaseService.fetchVocabulary(for: lessonId)
        localCache.cacheVocabulary(vocabulary, for: lessonId)

        // Preload conversations
        let conversations = await supabaseService.fetchConversations(for: lessonId)
        localCache.cacheConversations(conversations, for: lessonId)

        // Preload grammar topics
        let grammar = await supabaseService.fetchGrammarTopics(for: lessonId)
        localCache.cacheGrammar(grammar, for: lessonId)

        // Preload practice exercises
        let exercises = await supabaseService.fetchPracticeExercises(for: lessonId)
        localCache.cachePractice(exercises, for: lessonId)
    }
    
    /// Preload vocabulary audio files
    private func preloadVocabularyAudio() async {
        // Get all cached vocabulary
        let allVocabulary = localCache.getAllCachedVocabulary()
        
        // Generate and cache audio for high-frequency words
        let highFrequencyWords = allVocabulary.filter { ($0.frequencyRank ?? 999) <= 100 }
        await audioService.generateVocabularyAudio(vocabulary: highFrequencyWords)
    }
    
    /// Preload cultural content
    private func preloadCulturalContent() async {
        // Cache Thirukkural content
        await cacheDailyThirukkural()
        
        // Cache Tamil calendar data
        await cacheTamilCalendar()
        
        // Cache cultural insights
        await cacheCulturalInsights()
    }
    
    // MARK: - Smart Cache Management
    
    /// Optimize cache based on usage patterns
    func optimizeCache() async {
        cacheStatus = .optimizing
        
        // Remove expired content
        await removeExpiredContent()
        
        // Remove least used content if cache is full
        await cleanupLeastUsedContent()
        
        // Preload predicted content
        await preloadPredictedContent()
        
        cacheStatus = .ready
        updateCacheMetrics()
        
        print("🔧 Cache optimization completed")
    }
    
    /// Remove expired cached content
    private func removeExpiredContent() async {
        let expiredKeys = localCache.getExpiredCacheKeys(olderThan: cacheExpiryTime)
        
        for key in expiredKeys {
            localCache.clearCache(forKey: key)
        }
        
        print("🗑️ Removed \(expiredKeys.count) expired cache entries")
    }
    
    /// Clean up least used content when cache is full
    private func cleanupLeastUsedContent() async {
        let currentCacheSize = localCache.getTotalCacheSize()
        
        if currentCacheSize > maxCacheSize {
            let leastUsedKeys = localCache.getLeastUsedCacheKeys(limit: 50)
            
            for key in leastUsedKeys {
                localCache.clearCache(forKey: key)
                
                // Stop when we're under the limit
                if localCache.getTotalCacheSize() < Int(Double(maxCacheSize) * 0.8) {
                    break
                }
            }
            
            print("🧹 Cleaned up least used content")
        }
    }
    
    /// Preload content based on user patterns
    private func preloadPredictedContent() async {
        // Analyze user progress to predict next lessons
        let userProgress = UserProgressService.shared.userProgress
        
        if let progress = userProgress {
            // Predict next level if current level is nearly complete
            let completionRate = UserProgressService.shared.getCompletionPercentage(for: progress.currentLevel)
            
            if completionRate > 0.8 {
                let nextLevel = getNextLevel(current: progress.currentLevel.rawValue)
                if let next = nextLevel {
                    await preloadLessons(for: next)
                }
            }
        }
    }
    
    // MARK: - Offline-First Architecture
    
    /// Check if content is available offline
    func isContentAvailableOffline(lessonId: String) -> Bool {
        return localCache.hasCompleteLesson(lessonId: lessonId)
    }
    
    /// Get offline content for lesson
    func getOfflineContent(lessonId: String) -> AdvancedCompleteTamilLesson? {
        return localCache.getCompleteLesson(lessonId: lessonId)
    }
    
    /// Enable offline mode
    func enableOfflineMode() {
        offlineMode = true
        print("📱 Offline mode enabled")
    }
    
    /// Disable offline mode
    func disableOfflineMode() {
        offlineMode = false
        print("🌐 Online mode enabled")
    }
    
    // MARK: - Network Monitoring
    
    /// Setup network monitoring
    private func setupNetworkMonitoring() {
        // Monitor network connectivity
        // In a real app, you would use Network framework
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                Task {
                    await self.checkNetworkStatus()
                }
            }
            .store(in: &cancellables)
    }
    
    /// Check network status and adjust caching strategy
    private func checkNetworkStatus() async {
        // Simulate network check
        let isConnected = true // In real app, check actual connectivity
        
        if !isConnected && !offlineMode {
            enableOfflineMode()
        } else if isConnected && offlineMode {
            disableOfflineMode()
            
            // Sync when back online
            await syncWithServer()
        }
    }
    
    /// Sync cached data with server when back online
    private func syncWithServer() async {
        // Sync user progress
        await UserProgressService.shared.syncWithSupabase()
        
        // Check for content updates
        await ContentVersioningService.shared.checkForUpdates()
        
        // Sync analytics
        await AnalyticsService.shared.flushEvents()
        
        print("🔄 Synced with server")
    }
    
    // MARK: - Background Optimization
    
    /// Start background cache optimization
    private func startBackgroundOptimization() {
        Timer.publish(every: 3600, on: .main, in: .common) // Every hour
            .autoconnect()
            .sink { _ in
                Task {
                    await self.optimizeCache()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Cache Metrics
    
    /// Update cache metrics
    private func updateCacheMetrics() {
        let totalSize = localCache.getTotalCacheSize()
        let itemCount = localCache.getCacheItemCount()
        let hitRate = localCache.getCacheHitRate()
        
        cacheMetrics = CacheMetrics(
            totalSize: totalSize,
            itemCount: itemCount,
            hitRate: hitRate,
            lastOptimized: Date()
        )
        
        saveCacheMetrics()
    }
    
    /// Load cache metrics
    private func loadCacheMetrics() {
        if let data = UserDefaults.standard.data(forKey: "cache_metrics"),
           let metrics = try? JSONDecoder().decode(CacheMetrics.self, from: data) {
            cacheMetrics = metrics
        }
    }
    
    /// Save cache metrics
    private func saveCacheMetrics() {
        if let metrics = cacheMetrics,
           let data = try? JSONEncoder().encode(metrics) {
            UserDefaults.standard.set(data, forKey: "cache_metrics")
            UserDefaults.standard.synchronize()
        }
    }
    
    // MARK: - Helper Methods
    
    private func getNextLevel(current: String) -> CEFRLevel? {
        switch current {
        case "A1": return .a2
        case "A2": return .b1
        case "B1": return .b2
        case "B2": return .c1
        case "C1": return .c2
        default: return nil
        }
    }
    
    private func cacheDailyThirukkural() async {
        // Cache Thirukkural content
        print("📜 Cached daily Thirukkural")
    }
    
    private func cacheTamilCalendar() async {
        // Cache Tamil calendar data
        print("📅 Cached Tamil calendar")
    }
    
    private func cacheCulturalInsights() async {
        // Cache cultural insights
        print("🏛️ Cached cultural insights")
    }
}

// MARK: - Supporting Types

enum AdvancedCacheStatus {
    case idle
    case preloading
    case optimizing
    case ready
    case error
}

struct CacheMetrics: Codable {
    let totalSize: Int
    let itemCount: Int
    let hitRate: Double
    let lastOptimized: Date
}

// MARK: - LocalCacheService Extensions

extension LocalCacheService {
    
    func getAllCachedVocabulary() -> [TamilSupabaseVocabulary] {
        // Return all cached vocabulary items
        return []
    }
    
    func getExpiredCacheKeys(olderThan timeInterval: TimeInterval) -> [String] {
        // Return keys for expired cache entries
        return []
    }
    
    func getLeastUsedCacheKeys(limit: Int) -> [String] {
        // Return keys for least used cache entries
        return []
    }
    
    func getTotalCacheSize() -> Int {
        // Return total cache size in bytes
        return 0
    }
    
    func getCacheItemCount() -> Int {
        // Return number of cached items
        return 0
    }
    
    func getCacheHitRate() -> Double {
        // Return cache hit rate
        return 0.85
    }
    
    func hasCompleteLesson(lessonId: String) -> Bool {
        // Check if complete lesson is cached
        return false
    }
    
    func getCompleteLesson(lessonId: String) -> AdvancedCompleteTamilLesson? {
        // Return complete cached lesson
        return nil
    }
    
    func cacheAdvancedVocabulary(_ vocabulary: [TamilSupabaseVocabulary], for lessonId: String) {
        // Cache vocabulary for lesson using existing method
        cacheVocabulary(vocabulary, for: lessonId)
    }

    func cacheAdvancedConversations(_ conversations: [TamilSupabaseConversation], for lessonId: String) {
        // Cache conversations for lesson using existing method
        cacheConversations(conversations, for: lessonId)
    }

    func cacheAdvancedGrammar(_ grammar: [TamilSupabaseGrammarTopic], for lessonId: String) {
        // Cache grammar for lesson using existing method
        cacheGrammar(grammar, for: lessonId)
    }

    func cacheAdvancedPracticeExercises(_ exercises: [TamilSupabasePracticeExercise], for lessonId: String) {
        // Cache practice exercises for lesson using existing method
        cachePractice(exercises, for: lessonId)
    }
    
    func clearCache(forKey key: String) {
        // Clear specific cache entry
    }
}

struct AdvancedCompleteTamilLesson {
    let lesson: TamilSupabaseLesson
    let vocabulary: [TamilSupabaseVocabulary]
    let conversations: [TamilSupabaseConversation]
    let grammarTopics: [TamilSupabaseGrammarTopic]
    let practiceExercises: [TamilSupabasePracticeExercise]
}
