import Foundation
import Combine
import Supabase

// MARK: - Reading Content Service

@MainActor
class ReadingContentService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ReadingContentService()
    
    // MARK: - Published Properties
    @Published var readingContent: [ReadingContent] = []
    @Published var userProgress: [ReadingProgress] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var currentSession: ReadingSession?
    @Published var readingPreferences = ReadingPreferences.default
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        // Check if Supabase API keys are configured
        if APIKeys.supabaseConfigured {
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: APIKeys.supabaseURL)!,
                supabaseKey: APIKeys.supabaseAnonKey
            )
            print("📖 ReadingContentService initialized with real Supabase client")
        } else {
            // Use mock client for development
            print("⚠️ ReadingContentService using mock client - API keys not configured")
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: "https://mock.supabase.co")!,
                supabaseKey: "mock-key"
            )
        }
        
        loadReadingPreferences()
    }
    
    // MARK: - Content Loading
    
    /// Load all reading content from Supabase
    func loadReadingContent() async {
        isLoading = true
        errorMessage = nil
        
        do {
            if APIKeys.supabaseConfigured {
                let response: [ReadingContent] = try await supabase
                    .from("reading_content")
                    .select()
                    .eq("is_active", value: true)
                    .order("difficulty_level", ascending: true)
                    .order("category", ascending: true)
                    .execute()
                    .value
                
                readingContent = response
                print("📖 Loaded \(response.count) reading content items from Supabase")
            } else {
                // Load mock data for development
                readingContent = generateMockReadingContent()
                print("📖 Loaded \(readingContent.count) mock reading content items")
            }
        } catch {
            errorMessage = "Failed to load reading content: \(error.localizedDescription)"
            print("❌ Reading content loading failed: \(error)")
            
            // Fallback to mock data
            readingContent = generateMockReadingContent()
        }
        
        isLoading = false
    }
    
    /// Load user reading progress
    func loadUserProgress(userId: UUID) async {
        guard APIKeys.supabaseConfigured else {
            print("⚠️ Skipping progress loading - Supabase not configured")
            return
        }
        
        do {
            let response: [ReadingProgress] = try await supabase
                .from("reading_progress")
                .select()
                .eq("user_id", value: userId)
                .order("last_read_at", ascending: false)
                .execute()
                .value
            
            userProgress = response
            print("📊 Loaded \(response.count) reading progress records")
        } catch {
            print("❌ Failed to load reading progress: \(error)")
        }
    }
    
    // MARK: - Content Filtering
    
    /// Get content by category
    func getContent(for category: ReadingCategory) -> [ReadingContent] {
        return readingContent.filter { $0.category == category }
    }
    
    /// Get content by difficulty level
    func getContent(for level: CEFRLevel) -> [ReadingContent] {
        return readingContent.filter { $0.difficultyLevel == level }
    }
    
    /// Get content by category and level
    func getContent(for category: ReadingCategory, level: CEFRLevel) -> [ReadingContent] {
        return readingContent.filter { $0.category == category && $0.difficultyLevel == level }
    }
    
    /// Get unlocked content based on completed lessons
    func getUnlockedContent(completedLessons: [String]) -> [ReadingContent] {
        return readingContent.filter { $0.isUnlocked(completedLessons: completedLessons) }
    }
    
    // MARK: - Progress Tracking
    
    /// Start a reading session
    func startReadingSession(for content: ReadingContent) {
        currentSession = ReadingSession(content: content)
        print("📖 Started reading session for: \(content.titleEnglish)")
    }
    
    /// Update reading progress
    func updateReadingProgress(position: Int) {
        guard var session = currentSession else { return }
        session.currentPosition = position
        currentSession = session
    }
    
    /// Complete reading session and save progress
    func completeReadingSession() async {
        guard let session = currentSession else { return }
        
        let progress = ReadingProgress(
            id: UUID(),
            userId: UUID(), // TODO: Get actual user ID from auth
            contentId: session.content.contentId,
            readingTimeSeconds: Int(session.elapsedTime),
            completionPercentage: session.completionPercentage,
            comprehensionScore: nil,
            lastReadAt: Date(),
            isCompleted: session.completionPercentage >= 100.0,
            createdAt: Date(),
            updatedAt: Date()
        )
        
        await saveReadingProgress(progress)
        currentSession = nil
    }
    
    /// Save reading progress to Supabase
    private func saveReadingProgress(_ progress: ReadingProgress) async {
        guard APIKeys.supabaseConfigured else {
            print("⚠️ Skipping progress save - Supabase not configured")
            return
        }
        
        do {
            try await supabase
                .from("reading_progress")
                .upsert(progress)
                .execute()
            
            // Update local progress
            if let index = userProgress.firstIndex(where: { $0.contentId == progress.contentId }) {
                userProgress[index] = progress
            } else {
                userProgress.append(progress)
            }
            
            print("💾 Saved reading progress for content: \(progress.contentId)")
        } catch {
            print("❌ Failed to save reading progress: \(error)")
        }
    }
    
    // MARK: - Preferences
    
    /// Load reading preferences from UserDefaults
    private func loadReadingPreferences() {
        if let data = UserDefaults.standard.data(forKey: "ReadingPreferences"),
           let preferences = try? JSONDecoder().decode(ReadingPreferences.self, from: data) {
            readingPreferences = preferences
        }
    }
    
    /// Save reading preferences to UserDefaults
    func saveReadingPreferences() {
        if let data = try? JSONEncoder().encode(readingPreferences) {
            UserDefaults.standard.set(data, forKey: "ReadingPreferences")
        }
    }
    
    // MARK: - Mock Data Generation
    
    private func generateMockReadingContent() -> [ReadingContent] {
        return [
            // Script Basics - A1 Level
            ReadingContent(
                id: UUID(),
                contentId: "script_vowels_a1",
                titleEnglish: "Tamil Vowels",
                titleTamil: "தமிழ் உயிரெழுத்துகள்",
                titleRomanization: "Tamil Uyirezhutthugal",
                category: .scriptBasics,
                difficultyLevel: .a1,
                contentTamil: "அ ஆ இ ஈ உ ஊ எ ஏ ஐ ஒ ஓ ஔ",
                contentRomanization: "a ā i ī u ū e ē ai o ō au",
                contentTranslation: "The twelve Tamil vowels: a, aa, i, ii, u, uu, e, ee, ai, o, oo, au",
                audioUrl: nil,
                estimatedReadingTime: 5,
                prerequisiteLessons: [],
                tags: ["vowels", "script", "basics"],
                culturalContext: "Tamil script has 12 vowels that form the foundation of the writing system.",
                source: .custom,
                sourceReference: nil,
                isActive: true,
                createdAt: Date(),
                updatedAt: Date()
            ),
            
            // Simple Reading - A1 Level
            ReadingContent(
                id: UUID(),
                contentId: "simple_greetings_a1",
                titleEnglish: "Basic Greetings",
                titleTamil: "அடிப்படை வாழ்த்துகள்",
                titleRomanization: "Adippadai Vaazhththugal",
                category: .simpleReading,
                difficultyLevel: .a1,
                contentTamil: "வணக்கம்! நான் ராம். நீங்கள் எப்படி இருக்கிறீர்கள்? நான் நன்றாக இருக்கிறேன்.",
                contentRomanization: "Vanakkam! Naan Raam. Neengal eppadi irukkireergal? Naan nandraaga irukkiren.",
                contentTranslation: "Hello! I am Ram. How are you? I am fine.",
                audioUrl: nil,
                estimatedReadingTime: 3,
                prerequisiteLessons: ["A1_BASIC_GREETINGS"],
                tags: ["greetings", "conversation", "basic"],
                culturalContext: "வணக்கம் is the universal Tamil greeting used throughout the day.",
                source: .lessonVocabulary,
                sourceReference: "A1_BASIC_GREETINGS",
                isActive: true,
                createdAt: Date(),
                updatedAt: Date()
            )
        ]
    }
}

// MARK: - Extensions

extension ReadingContentService {
    /// Get progress for specific content
    func getProgress(for contentId: String) -> ReadingProgress? {
        return userProgress.first { $0.contentId == contentId }
    }
    
    /// Check if content is completed
    func isCompleted(_ contentId: String) -> Bool {
        return getProgress(for: contentId)?.isCompleted ?? false
    }
    
    /// Get completion percentage for content
    func getCompletionPercentage(for contentId: String) -> Double {
        return getProgress(for: contentId)?.completionPercentage ?? 0.0
    }
}
