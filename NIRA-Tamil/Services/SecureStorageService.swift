//
//  SecureStorageService.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation
import Security
import CryptoKit

// MARK: - Secure Storage Service

class SecureStorageService {
    static let shared = SecureStorageService()

    private let keychainService = "com.nira.app.secure"
    private let userDefaults = UserDefaults.standard

    private init() {}

    // MARK: - Keychain Operations

    func storeSecurely(_ data: Data, forKey key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]

        // Delete existing item first
        let deleteQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key
        ]
        SecItemDelete(deleteQuery as CFDictionary)

        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        guard status == errSecSuccess else {
            throw SecureStorageError.keychainError(status)
        }
    }

    func retrieveSecurely(forKey key: String) throws -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                return nil
            }
            throw SecureStorageError.keychainError(status)
        }

        return result as? Data
    }

    func deleteSecurely(forKey key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key
        ]

        let status = SecItemDelete(query as CFDictionary)
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw SecureStorageError.keychainError(status)
        }
    }

    // MARK: - Encrypted User Preferences

    func storeUserPreference<T: Codable>(_ value: T, forKey key: String) throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(value)
        let encryptedData = try encrypt(data)
        try storeSecurely(encryptedData, forKey: "pref_\(key)")
    }

    func getUserPreference<T: Codable>(forKey key: String, type: T.Type) throws -> T? {
        guard let encryptedData = try retrieveSecurely(forKey: "pref_\(key)") else {
            return nil
        }

        let data = try decrypt(encryptedData)
        let decoder = JSONDecoder()
        return try decoder.decode(type, from: data)
    }

    func deleteUserPreference(forKey key: String) throws {
        try deleteSecurely(forKey: "pref_\(key)")
    }

    // MARK: - Session Management

    func storeSessionToken(_ token: String) throws {
        guard let data = token.data(using: .utf8) else {
            throw SecureStorageError.invalidData
        }
        try storeSecurely(data, forKey: "session_token")
    }

    func getSessionToken() throws -> String? {
        guard let data = try retrieveSecurely(forKey: "session_token") else {
            return nil
        }
        return String(data: data, encoding: .utf8)
    }

    func clearSessionToken() throws {
        try deleteSecurely(forKey: "session_token")
    }

    // MARK: - User Data Management

    func clearUserData() async {
        // Clear all user-related secure data
        let userKeys = [
            "session_token",
            "user_profile",
            "cached_lessons",
            "progress_data",
            "ai_conversation_history"
        ]

        for key in userKeys {
            try? deleteSecurely(forKey: key)
        }

        // Clear user preferences (encrypted)
        let prefixedKeys = getAllKeychainKeys().filter { $0.hasPrefix("pref_") }
        for key in prefixedKeys {
            try? deleteSecurely(forKey: key)
        }

        // Clear non-sensitive UserDefaults
        let userDefaultsKeys = [
            "last_app_version",
            "onboarding_completed",
            "tutorial_shown"
        ]

        for key in userDefaultsKeys {
            userDefaults.removeObject(forKey: key)
        }
    }

    // MARK: - Encryption/Decryption

    private func encrypt(_ data: Data) throws -> Data {
        let key = try getOrCreateEncryptionKey()
        let sealedBox = try AES.GCM.seal(data, using: key)
        return sealedBox.combined!
    }

    private func decrypt(_ encryptedData: Data) throws -> Data {
        let key = try getOrCreateEncryptionKey()
        let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
        return try AES.GCM.open(sealedBox, using: key)
    }

    private func getOrCreateEncryptionKey() throws -> SymmetricKey {
        let keyIdentifier = "encryption_key"

        if let keyData = try retrieveSecurely(forKey: keyIdentifier) {
            return SymmetricKey(data: keyData)
        }

        // Create new key
        let key = SymmetricKey(size: .bits256)
        let keyData = key.withUnsafeBytes { Data($0) }
        try storeSecurely(keyData, forKey: keyIdentifier)

        return key
    }

    // MARK: - Utility Methods

    private func getAllKeychainKeys() -> [String] {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecReturnAttributes as String: true,
            kSecMatchLimit as String: kSecMatchLimitAll
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess,
              let items = result as? [[String: Any]] else {
            return []
        }

        return items.compactMap { $0[kSecAttrAccount as String] as? String }
    }

    // MARK: - Data Validation

    func validateStoredData() async -> Bool {
        // Validate integrity of stored data
        let criticalKeys = ["session_token", "encryption_key"]

        for key in criticalKeys {
            do {
                let data = try retrieveSecurely(forKey: key)
                if key == "session_token" && data != nil {
                    // Validate session token format
                    guard let tokenString = String(data: data!, encoding: .utf8),
                          !tokenString.isEmpty else {
                        return false
                    }
                }
            } catch {
                return false
            }
        }

        return true
    }
}

// MARK: - Security Event Types

enum SecurityEvent {
    case signUpAttempt(email: String)
    case signUpSuccess(email: String)
    case signUpFailed(email: String, reason: String)
    case signInAttempt(email: String)
    case signInSuccess(email: String)
    case signInFailed(email: String, reason: String)
    case signOutAttempt(email: String)
    case signOutSuccess
    case signOutFailed(reason: String)
    case passwordResetAttempt(email: String)
    case passwordResetSuccess(email: String)
    case passwordResetFailed(email: String, reason: String)
    case guestModeActivated
    case failedAttemptRecorded(email: String, count: Int)
    case forcedSignOut(reason: String)
    case suspiciousActivity(description: String)
    case dataIntegrityFailure(component: String)

    var description: String {
        switch self {
        case .signUpAttempt(let email):
            return "Sign up attempt for \(email.masked)"
        case .signUpSuccess(let email):
            return "Sign up successful for \(email.masked)"
        case .signUpFailed(let email, let reason):
            return "Sign up failed for \(email.masked): \(reason)"
        case .signInAttempt(let email):
            return "Sign in attempt for \(email.masked)"
        case .signInSuccess(let email):
            return "Sign in successful for \(email.masked)"
        case .signInFailed(let email, let reason):
            return "Sign in failed for \(email.masked): \(reason)"
        case .signOutAttempt(let email):
            return "Sign out attempt for \(email.masked)"
        case .signOutSuccess:
            return "Sign out successful"
        case .signOutFailed(let reason):
            return "Sign out failed: \(reason)"
        case .passwordResetAttempt(let email):
            return "Password reset attempt for \(email.masked)"
        case .passwordResetSuccess(let email):
            return "Password reset successful for \(email.masked)"
        case .passwordResetFailed(let email, let reason):
            return "Password reset failed for \(email.masked): \(reason)"
        case .guestModeActivated:
            return "Guest mode activated"
        case .failedAttemptRecorded(let email, let count):
            return "Failed attempt #\(count) recorded for \(email.masked)"
        case .forcedSignOut(let reason):
            return "Forced sign out: \(reason)"
        case .suspiciousActivity(let description):
            return "Suspicious activity: \(description)"
        case .dataIntegrityFailure(let component):
            return "Data integrity failure in \(component)"
        }
    }
}

// Note: SecureStorageError and AuthenticationError are defined in SecurityModels.swift

// MARK: - String Masking Extension

extension String {
    var masked: String {
        guard self.count > 4 else { return "***" }
        let prefix = String(self.prefix(2))
        let suffix = String(self.suffix(2))
        return "\(prefix)***\(suffix)"
    }
}
