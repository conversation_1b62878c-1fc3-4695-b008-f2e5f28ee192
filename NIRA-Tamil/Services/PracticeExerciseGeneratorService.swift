//
//  PracticeExerciseGeneratorService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 25/06/2025.
//

import Foundation

/// Service to dynamically generate practice exercises from lesson content
class PracticeExerciseGeneratorService: ObservableObject {
    
    // MARK: - Main Generation Function
    
    /// Generate practice exercises from lesson content
    func generatePracticeExercises(
        from vocabulary: [TamilSupabaseVocabulary],
        conversations: [TamilSupabaseConversation],
        grammar: [TamilSupabaseGrammarTopic],
        lessonId: String
    ) -> [TamilSupabasePracticeExercise] {
        
        var exercises: [TamilSupabasePracticeExercise] = []
        
        // Generate vocabulary-based exercises (4 exercises)
        exercises.append(contentsOf: generateVocabularyExercises(from: vocabulary, lessonId: lessonId))
        
        // Generate conversation-based exercises (3 exercises)
        exercises.append(contentsOf: generateConversationExercises(from: conversations, lessonId: lessonId))
        
        // Generate grammar-based exercises (3 exercises)
        exercises.append(contentsOf: generateGrammarExercises(from: grammar, lessonId: lessonId))
        
        return exercises
    }
    
    // MARK: - Vocabulary Exercises
    
    private func generateVocabularyExercises(
        from vocabulary: [TamilSupabaseVocabulary],
        lessonId: String
    ) -> [TamilSupabasePracticeExercise] {
        
        guard !vocabulary.isEmpty else { return [] }
        
        var exercises: [TamilSupabasePracticeExercise] = []
        
        // Exercise 1: Multiple Choice - Tamil to English
        if vocabulary.count >= 4 {
            exercises.append(createVocabularyMultipleChoice(
                vocabulary: vocabulary,
                lessonId: lessonId,
                exerciseNumber: 1,
                direction: .tamilToEnglish
            ))
        }
        
        // Exercise 2: Multiple Choice - English to Tamil
        if vocabulary.count >= 4 {
            exercises.append(createVocabularyMultipleChoice(
                vocabulary: vocabulary,
                lessonId: lessonId,
                exerciseNumber: 2,
                direction: .englishToTamil
            ))
        }
        
        // Exercise 3: Fill in the Blank using example sentences
        if let vocabWithSentence = vocabulary.first(where: { $0.exampleSentenceEnglish?.isEmpty == false }) {
            exercises.append(createFillInTheBlank(
                vocabulary: vocabWithSentence,
                allVocabulary: vocabulary,
                lessonId: lessonId,
                exerciseNumber: 3
            ))
        }
        
        // Exercise 4: Matching pairs
        if vocabulary.count >= 3 {
            exercises.append(createVocabularyMatching(
                vocabulary: vocabulary,
                lessonId: lessonId,
                exerciseNumber: 4
            ))
        }
        
        return exercises
    }
    
    // MARK: - Conversation Exercises
    
    private func generateConversationExercises(
        from conversations: [TamilSupabaseConversation],
        lessonId: String
    ) -> [TamilSupabasePracticeExercise] {
        
        guard !conversations.isEmpty else { return [] }
        
        var exercises: [TamilSupabasePracticeExercise] = []
        
        // Exercise 5: Conversation comprehension
        if let firstConversation = conversations.first {
            exercises.append(createConversationComprehension(
                conversation: firstConversation,
                lessonId: lessonId,
                exerciseNumber: 5
            ))
        }
        
        // Exercise 6: Key phrase identification - use first conversation
        if let firstConversation = conversations.first {
            exercises.append(createKeyPhraseIdentification(
                conversation: firstConversation,
                lessonId: lessonId,
                exerciseNumber: 6
            ))
        }
        
        // Exercise 7: Context matching
        if conversations.count >= 2 {
            exercises.append(createContextMatching(
                conversations: conversations,
                lessonId: lessonId,
                exerciseNumber: 7
            ))
        }
        
        return exercises
    }
    
    // MARK: - Grammar Exercises
    
    private func generateGrammarExercises(
        from grammar: [TamilSupabaseGrammarTopic],
        lessonId: String
    ) -> [TamilSupabasePracticeExercise] {
        
        guard !grammar.isEmpty else { return [] }
        
        var exercises: [TamilSupabasePracticeExercise] = []
        
        // Exercise 8: Grammar rule true/false
        if let firstGrammar = grammar.first {
            exercises.append(createGrammarTrueFalse(
                grammar: firstGrammar,
                lessonId: lessonId,
                exerciseNumber: 8
            ))
        }
        
        // Exercise 9: Grammar example application - use first grammar topic
        if let firstGrammar = grammar.first {
            exercises.append(createGrammarApplication(
                grammar: firstGrammar,
                lessonId: lessonId,
                exerciseNumber: 9
            ))
        }
        
        // Exercise 10: Grammar pattern recognition
        if grammar.count >= 2 {
            exercises.append(createGrammarPatternRecognition(
                grammar: grammar,
                lessonId: lessonId,
                exerciseNumber: 10
            ))
        }
        
        return exercises
    }
}

// MARK: - Exercise Creation Helpers

extension PracticeExerciseGeneratorService {
    
    enum TranslationDirection {
        case tamilToEnglish
        case englishToTamil
    }
    
    private func createVocabularyMultipleChoice(
        vocabulary: [TamilSupabaseVocabulary],
        lessonId: String,
        exerciseNumber: Int,
        direction: TranslationDirection
    ) -> TamilSupabasePracticeExercise {
        
        let targetWord = vocabulary.randomElement()!
        let distractors = vocabulary.filter { $0.id != targetWord.id }.shuffled().prefix(3)
        
        let (question, questionTamil, correctAnswer, options, optionsTamil) = switch direction {
        case .tamilToEnglish:
            (
                "What is the English meaning of '\(targetWord.tamilTranslation)'?",
                "'\(targetWord.tamilTranslation)' என்பதன் ஆங்கில அர்த்தம் என்ன?",
                targetWord.englishWord,
                [targetWord.englishWord] + distractors.map { $0.englishWord },
                [targetWord.englishWord] + distractors.map { $0.englishWord }
            )
        case .englishToTamil:
            (
                "What is the Tamil word for '\(targetWord.englishWord)'?",
                "'\(targetWord.englishWord)' என்பதற்கான தமிழ் சொல் என்ன?",
                targetWord.tamilTranslation,
                [targetWord.tamilTranslation] + distractors.map { $0.tamilTranslation },
                [targetWord.tamilTranslation] + distractors.map { $0.tamilTranslation }
            )
        }
        
        let shuffledOptions = Array(zip(options, optionsTamil)).shuffled()
        let correctIndex = shuffledOptions.firstIndex { $0.0 == correctAnswer } ?? 0

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: direction == .tamilToEnglish ? "Tamil to English" : "English to Tamil",
            titleTamil: direction == .tamilToEnglish ? "தமிழிலிருந்து ஆங்கிலம்" : "ஆங்கிலத்திலிருந்து தமிழ்",
            instructionsEnglish: "Choose the correct translation",
            instructionsTamil: "சரியான மொழிபெயர்ப்பைத் தேர்ந்தெடுக்கவும்",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: shuffledOptions.map { $0.0 },
            optionsTamil: shuffledOptions.map { $0.1 },
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "'\(correctAnswer)' is the correct translation.",
            explanationTamil: "'\(correctAnswer)' சரியான மொழிபெயர்ப்பு.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }
    
    private func createFillInTheBlank(
        vocabulary: TamilSupabaseVocabulary,
        allVocabulary: [TamilSupabaseVocabulary],
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {
        
        let sentence = vocabulary.exampleSentenceEnglish ?? "I am learning Tamil."
        let targetWord = vocabulary.englishWord
        let sentenceWithBlank = sentence.replacingOccurrences(of: targetWord, with: "____")
        
        let distractors = allVocabulary.filter { $0.id != vocabulary.id }.shuffled().prefix(3)
        let options = ([vocabulary.englishWord] + distractors.map { $0.englishWord }).shuffled()
        let correctIndex = options.firstIndex(of: vocabulary.englishWord) ?? 0

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "fill_blank",
            titleEnglish: "Fill in the Blank",
            titleTamil: "வெற்றிடத்தை நிரப்பவும்",
            instructionsEnglish: "Complete the sentence: \(sentenceWithBlank)",
            instructionsTamil: "வாக்கியத்தை முடிக்கவும்: \(vocabulary.exampleSentenceTamil ?? "நான் தமிழ் கற்றுக்கொண்டிருக்கிறேன்")",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 45,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: sentenceWithBlank,
            questionTamil: (vocabulary.exampleSentenceTamil ?? "நான் தமிழ் கற்றுக்கொண்டிருக்கிறேன்").replacingOccurrences(of: vocabulary.tamilTranslation, with: "____"),
            options: options,
            optionsTamil: options, // Same for fill-in-the-blank
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "'\(vocabulary.englishWord)' (\(vocabulary.tamilTranslation)) fits correctly in this context.",
            explanationTamil: "'\(vocabulary.englishWord)' (\(vocabulary.tamilTranslation)) இந்த சூழலில் சரியாக பொருந்துகிறது.",
            audioQuestionUrl: vocabulary.audioSentenceUrl,
            matchPairs: nil,
            fillBlanks: [vocabulary.englishWord]
        )

        return exercise
    }
    
    private func createVocabularyMatching(
        vocabulary: [TamilSupabaseVocabulary],
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {

        let selectedVocab = Array(vocabulary.shuffled().prefix(4))
        let matchPairs = selectedVocab.map { vocab in
            ExerciseMatchPair(
                left: vocab.tamilTranslation,
                leftTamil: vocab.tamilTranslation,
                right: vocab.englishWord,
                rightTamil: vocab.englishWord
            )
        }

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "match_following",
            titleEnglish: "Match Tamil Words",
            titleTamil: "தமிழ் சொற்களை இணைக்கவும்",
            instructionsEnglish: "Match the Tamil words with their English meanings",
            instructionsTamil: "தமிழ் சொற்களை அவற்றின் ஆங்கில அர்த்தங்களுடன் இணைக்கவும்",
            difficultyLevel: 2,
            pointsValue: 20,
            timeLimitSeconds: 60,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: "Match the Tamil words with their English meanings",
            questionTamil: "தமிழ் சொற்களை அவற்றின் ஆங்கில அர்த்தங்களுடன் இணைக்கவும்",
            options: [],
            optionsTamil: [],
            optionsRomanization: nil, // Not needed for matching exercises
            optionsPronunciation: nil, // Not needed for matching exercises
            optionsAudioUrls: nil, // Not needed for matching exercises
            correctAnswer: 0,
            correctAnswers: Array(0..<matchPairs.count),
            explanation: "These are the correct Tamil-English word pairs from this lesson.",
            explanationTamil: "இவை இந்த பாடத்தின் சரியான தமிழ்-ஆங்கில சொல் ஜோடிகள்.",
            audioQuestionUrl: nil,
            matchPairs: matchPairs,
            fillBlanks: nil
        )

        return exercise
    }

    // MARK: - Conversation Exercise Helpers

    private func createConversationComprehension(
        conversation: TamilSupabaseConversation,
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {

        let question = "Based on the conversation '\(conversation.titleEnglish)', what is the main topic?"
        let questionTamil = "'\(conversation.titleTamil)' உரையாடலின் அடிப்படையில், முக்கிய தலைப்பு என்ன?"

        // Generate options based on conversation context
        let contextDescription = conversation.contextDescription ?? "Basic conversation"
        let options = [
            contextDescription,
            "Daily routine",
            "Weather discussion",
            "Shopping"
        ].shuffled()

        let optionsTamil = [
            contextDescription,
            "தினசரி வழக்கம்",
            "வானிலை விவாதம்",
            "கடை வாங்கல்"
        ].shuffled()

        let correctIndex = options.firstIndex(of: contextDescription) ?? 0

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Conversation Understanding",
            titleTamil: "உரையாடல் புரிதல்",
            instructionsEnglish: "Based on the conversation '\(conversation.titleEnglish)', what is the main topic?",
            instructionsTamil: "'\(conversation.titleTamil)' உரையாடலின் அடிப்படையில், முக்கிய தலைப்பு என்ன?",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 45,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: optionsTamil,
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "The conversation is about \(contextDescription).",
            explanationTamil: "உரையாடல் \(contextDescription) பற்றியது.",
            audioQuestionUrl: conversation.audioFullUrl,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createKeyPhraseIdentification(
        conversation: TamilSupabaseConversation,
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {

        let keyPhrase = "வணக்கம்" // Default key phrase since keyPhrases is in conversation lines
        let question = "The phrase '\(keyPhrase)' appears in this conversation."
        let questionTamil = "'\(keyPhrase)' என்ற சொற்றொடர் இந்த உரையாடலில் வருகிறது."

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "true_false",
            titleEnglish: "Key Phrase Recognition",
            titleTamil: "முக்கிய சொற்றொடர் அடையாளம்",
            instructionsEnglish: "The phrase '\(keyPhrase)' appears in this conversation.",
            instructionsTamil: "'\(keyPhrase)' என்ற சொற்றொடர் இந்த உரையாடலில் வருகிறது.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: ["True", "False"],
            optionsTamil: ["உண்மை", "பொய்"],
            optionsRomanization: ["Unmai", "Poi"], // Basic learners need romanization for Tamil options!
            optionsPronunciation: ["UN-mai", "POI"], // Pronunciation guides for Tamil options
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: 0, // Assume true since we're using actual key phrases
            correctAnswers: nil,
            explanation: "Yes, '\(keyPhrase)' is a key phrase used in this conversation.",
            explanationTamil: "ஆம், '\(keyPhrase)' இந்த உரையாடலில் பயன்படுத்தப்படும் முக்கிய சொற்றொடர்.",
            audioQuestionUrl: conversation.audioFullUrl,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createContextMatching(
        conversations: [TamilSupabaseConversation],
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {

        let selectedConversations = Array(conversations.prefix(3))
        let matchPairs = selectedConversations.map { conversation in
            ExerciseMatchPair(
                left: conversation.titleEnglish,
                leftTamil: conversation.titleTamil,
                right: conversation.contextDescription ?? "Basic conversation",
                rightTamil: conversation.contextDescription ?? "அடிப்படை உரையாடல்"
            )
        }

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "match_following",
            titleEnglish: "Context Matching",
            titleTamil: "சூழல் இணைப்பு",
            instructionsEnglish: "Match each conversation with its appropriate context",
            instructionsTamil: "ஒவ்வொரு உரையாடலையும் அதன் பொருத்தமான சூழலுடன் இணைக்கவும்",
            difficultyLevel: 3,
            pointsValue: 20,
            timeLimitSeconds: 60,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: "Match each conversation with its appropriate context",
            questionTamil: "ஒவ்வொரு உரையாடலையும் அதன் பொருத்தமான சூழலுடன் இணைக்கவும்",
            options: [],
            optionsTamil: [],
            optionsRomanization: nil, // Not needed for matching exercises
            optionsPronunciation: nil, // Not needed for matching exercises
            optionsAudioUrls: nil, // Not needed for matching exercises
            correctAnswer: 0,
            correctAnswers: Array(0..<matchPairs.count),
            explanation: "These conversations match their respective contexts.",
            explanationTamil: "இந்த உரையாடல்கள் அவற்றின் சூழல்களுடன் பொருந்துகின்றன.",
            audioQuestionUrl: nil,
            matchPairs: matchPairs,
            fillBlanks: nil
        )

        return exercise
    }

    // MARK: - Grammar Exercise Helpers

    private func createGrammarTrueFalse(
        grammar: TamilSupabaseGrammarTopic,
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {

        let question = "True or False: \(grammar.ruleEnglish)"
        let questionTamil = "உண்மை அல்லது பொய்: \(grammar.ruleTamil)"

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "true_false",
            titleEnglish: "Grammar Rule Check",
            titleTamil: "இலக்கண விதி சரிபார்ப்பு",
            instructionsEnglish: "True or False: \(grammar.ruleEnglish)",
            instructionsTamil: "உண்மை அல்லது பொய்: \(grammar.ruleTamil)",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 45,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: ["True", "False"],
            optionsTamil: ["உண்மை", "பொய்"],
            optionsRomanization: ["Unmai", "Poi"], // Basic learners need romanization for Tamil options!
            optionsPronunciation: ["UN-mai", "POI"], // Pronunciation guides for Tamil options
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: 0, // Assume true since we're using actual grammar explanations
            correctAnswers: nil,
            explanation: "This is a correct grammar rule: \(grammar.ruleEnglish)",
            explanationTamil: "இது சரியான இலக்கண விதி: \(grammar.ruleTamil)",
            audioQuestionUrl: nil, // No audio URL in grammar topic model
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createGrammarApplication(
        grammar: TamilSupabaseGrammarTopic,
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {

        let example = grammar.explanation ?? "வணக்கம்" // Use explanation as example
        let question = "Which sentence correctly applies the rule '\(grammar.titleEnglish)'?"
        let questionTamil = "'\(grammar.titleTamil)' விதியை சரியாகப் பயன்படுத்தும் வாக்கியம் எது?"

        // Generate options with the correct example and some distractors
        let options = [
            example,
            "நான் வீட்டுக்கு போகிறேன்",
            "அவன் புத்தகம் படிக்கிறான்",
            "நாங்கள் சாப்பிடுகிறோம்"
        ].shuffled()

        let correctIndex = options.firstIndex(of: example) ?? 0

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Grammar Application",
            titleTamil: "இலக்கண பயன்பாடு",
            instructionsEnglish: "Which sentence correctly applies the rule '\(grammar.titleEnglish)'?",
            instructionsTamil: "'\(grammar.titleTamil)' விதியை சரியாகப் பயன்படுத்தும் வாக்கியம் எது?",
            difficultyLevel: 3,
            pointsValue: 20,
            timeLimitSeconds: 60,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: options, // Same for Tamil grammar examples
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "'\(example)' correctly demonstrates the grammar rule '\(grammar.titleEnglish)'.",
            explanationTamil: "'\(example)' '\(grammar.titleTamil)' இலக்கண விதியை சரியாக நிரூபிக்கிறது.",
            audioQuestionUrl: nil, // No audio URL in grammar topic model
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createGrammarPatternRecognition(
        grammar: [TamilSupabaseGrammarTopic],
        lessonId: String,
        exerciseNumber: Int
    ) -> TamilSupabasePracticeExercise {

        let firstGrammar = grammar.first!
        let question = "Identify the grammar pattern used in the given sentence"
        let questionTamil = "கொடுக்கப்பட்ட வாக்கியத்தில் பயன்படுத்தப்பட்ட இலக்கண வடிவத்தை அடையாளம் காணவும்"

        let options = grammar.map { $0.titleEnglish }.shuffled()
        let optionsTamil = grammar.map { $0.titleTamil }.shuffled()
        let correctIndex = options.firstIndex(of: firstGrammar.titleEnglish) ?? 0

        var exercise = TamilSupabasePracticeExercise(
            id: "generated-\(lessonId)-\(exerciseNumber)",
            lessonId: lessonId,
            exerciseId: "L\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Grammar Pattern Recognition",
            titleTamil: "இலக்கண வடிவ அடையாளம்",
            instructionsEnglish: "Identify the grammar pattern used in the given sentence",
            instructionsTamil: "கொடுக்கப்பட்ட வாக்கியத்தில் பயன்படுத்தப்பட்ட இலக்கண வடிவத்தை அடையாளம் காணவும்",
            difficultyLevel: 3,
            pointsValue: 25,
            timeLimitSeconds: 90,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: optionsTamil,
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "The sentence follows the '\(firstGrammar.titleEnglish)' pattern.",
            explanationTamil: "வாக்கியம் '\(firstGrammar.titleTamil)' வடிவத்தைப் பின்பற்றுகிறது.",
            audioQuestionUrl: nil, // No audio URL in grammar topic model
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }
}
