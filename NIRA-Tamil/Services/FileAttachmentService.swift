import Foundation
import SwiftUI
import UniformTypeIdentifiers
import Supabase
import Combine

// MARK: - File Attachment Service
@MainActor
class FileAttachmentService: ObservableObject {
    static let shared = FileAttachmentService()
    
    @Published var isUploading = false
    @Published var uploadProgress: Double = 0.0
    @Published var errorMessage: String?
    @Published var recentAttachments: [ChatAttachment] = []
    
    private let supabaseClient = NIRASupabaseClient.shared
    private let maxFileSize: Int64 = 50 * 1024 * 1024 // 50MB
    private let allowedFileTypes: Set<UTType> = [
        .image, .jpeg, .png, .gif, .webP,
        .pdf, .text, .plainText, .rtf,
        .audio, .mp3, .wav, .aiff,
        .video, .zip, .json
    ]
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        loadRecentAttachments()
    }
    
    // MARK: - File Upload
    
    func uploadFile(
        data: Data,
        fileName: String,
        mimeType: String,
        conversationId: UUID
    ) async throws -> ChatAttachment {
        
        guard data.count <= maxFileSize else {
            throw FileAttachmentError.fileTooLarge
        }
        
        guard isValidFileType(mimeType: mimeType) else {
            throw FileAttachmentError.unsupportedFileType
        }
        
        isUploading = true
        uploadProgress = 0.0
        errorMessage = nil
        
        defer {
            isUploading = false
            uploadProgress = 0.0
        }
        
        do {
            // Generate unique file path
            let fileExtension = URL(fileURLWithPath: fileName).pathExtension
            let uniqueFileName = "\(UUID().uuidString).\(fileExtension)"
            let filePath = "chat-attachments/\(conversationId)/\(uniqueFileName)"
            
            // Upload to Supabase Storage
            let uploadedFile = try await uploadToSupabaseStorage(
                data: data,
                path: filePath,
                mimeType: mimeType
            )
            
            // Create attachment record
            let attachment = ChatAttachment(
                id: UUID(),
                fileName: fileName,
                originalFileName: fileName,
                mimeType: mimeType,
                size: Int64(data.count),
                url: uploadedFile.publicURL,
                storagePath: filePath,
                conversationId: conversationId,
                uploadedAt: Date(),
                isProcessed: false
            )
            
            // Save to database
            try await saveAttachmentToDatabase(attachment)
            
            // Process file if needed (extract text, generate thumbnails, etc.)
            Task {
                await processAttachment(attachment)
            }
            
            // Add to recent attachments
            recentAttachments.insert(attachment, at: 0)
            if recentAttachments.count > 20 {
                recentAttachments.removeLast()
            }
            
            return attachment
            
        } catch {
            errorMessage = "Failed to upload file: \(error.localizedDescription)"
            throw error
        }
    }
    
    private func uploadToSupabaseStorage(
        data: Data,
        path: String,
        mimeType: String
    ) async throws -> UploadedFile {
        
        // Simulate upload progress
        let progressTimer = Timer.publish(every: 0.1, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                if self.uploadProgress < 0.9 {
                    self.uploadProgress += 0.1
                }
            }
        
        defer {
            progressTimer.cancel()
        }
        
        // Upload to Supabase Storage
        _ = try await supabaseClient.client.storage
            .from("chat-attachments")
            .upload(
                path,
                data: data,
                options: FileOptions(
                    cacheControl: "3600",
                    contentType: mimeType
                )
            )
        
        uploadProgress = 1.0
        
        // Get public URL
        let publicURL = try supabaseClient.client.storage
            .from("chat-attachments")
            .getPublicURL(path: path)
        
        return UploadedFile(
            path: path,
            publicURL: publicURL.absoluteString
        )
    }
    
    private func saveAttachmentToDatabase(_ attachment: ChatAttachment) async throws {
        struct AttachmentData: Codable {
            let id: String
            let file_name: String
            let original_file_name: String
            let mime_type: String
            let size: Int64
            let url: String
            let storage_path: String
            let conversation_id: String
            let uploaded_at: String
            let is_processed: Bool
        }

        let attachmentData = AttachmentData(
            id: attachment.id.uuidString,
            file_name: attachment.fileName,
            original_file_name: attachment.originalFileName,
            mime_type: attachment.mimeType,
            size: attachment.size,
            url: attachment.url,
            storage_path: attachment.storagePath,
            conversation_id: attachment.conversationId.uuidString,
            uploaded_at: attachment.uploadedAt.ISO8601Format(),
            is_processed: attachment.isProcessed
        )

        try await supabaseClient.client
            .from("chat_attachments")
            .insert(attachmentData)
            .execute()
    }
    
    // MARK: - File Processing
    
    private func processAttachment(_ attachment: ChatAttachment) async {
        do {
            var processedAttachment = attachment
            
            // Process based on file type
            if attachment.mimeType.hasPrefix("image/") {
                processedAttachment = try await processImageAttachment(attachment)
            } else if attachment.mimeType.hasPrefix("text/") || attachment.mimeType == "application/pdf" {
                processedAttachment = try await processTextAttachment(attachment)
            } else if attachment.mimeType.hasPrefix("audio/") {
                processedAttachment = try await processAudioAttachment(attachment)
            }
            
            // Update processed status
            try await updateAttachmentProcessedStatus(processedAttachment.id, isProcessed: true)
            
            // Update local copy
            if let index = recentAttachments.firstIndex(where: { $0.id == attachment.id }) {
                recentAttachments[index] = processedAttachment
            }
            
        } catch {
            print("Failed to process attachment: \(error)")
        }
    }
    
    private func processImageAttachment(_ attachment: ChatAttachment) async throws -> ChatAttachment {
        // Download image data
        guard let url = URL(string: attachment.url),
              let imageData = try? Data(contentsOf: url),
              let image = UIImage(data: imageData) else {
            return attachment
        }
        
        // Generate thumbnail
        let thumbnailSize = CGSize(width: 200, height: 200)
        let thumbnail = image.preparingThumbnail(of: thumbnailSize)
        
        if let thumbnail = thumbnail,
           let thumbnailData = thumbnail.jpegData(compressionQuality: 0.8) {
            
            // Upload thumbnail
            let thumbnailPath = attachment.storagePath.replacingOccurrences(of: ".", with: "_thumb.")
            
            _ = try await uploadToSupabaseStorage(
                data: thumbnailData,
                path: thumbnailPath,
                mimeType: "image/jpeg"
            )
            
            // Update attachment with thumbnail info
            var updatedAttachment = attachment
            updatedAttachment.thumbnailUrl = try supabaseClient.client.storage
                .from("chat-attachments")
                .getPublicURL(path: thumbnailPath)
                .absoluteString
            
            return updatedAttachment
        }
        
        return attachment
    }
    
    private func processTextAttachment(_ attachment: ChatAttachment) async throws -> ChatAttachment {
        // Download and extract text content
        guard let url = URL(string: attachment.url) else {
            return attachment
        }
        
        let extractedText: String
        
        if attachment.mimeType == "application/pdf" {
            extractedText = try await extractTextFromPDF(url: url)
        } else {
            extractedText = try String(contentsOf: url, encoding: .utf8)
        }
        
        // Store extracted text (could be used for search, AI analysis, etc.)
        try await storeExtractedText(
            attachmentId: attachment.id,
            text: extractedText
        )
        
        var updatedAttachment = attachment
        updatedAttachment.extractedText = extractedText
        
        return updatedAttachment
    }
    
    private func processAudioAttachment(_ attachment: ChatAttachment) async throws -> ChatAttachment {
        // For audio files, we could:
        // 1. Generate waveform visualization
        // 2. Extract audio metadata
        // 3. Transcribe speech to text (if it's voice recording)

        // For now, just extract basic metadata
        guard URL(string: attachment.url) != nil else {
            return attachment
        }
        
        // This is a simplified version - in a real app, you'd use AVFoundation
        var updatedAttachment = attachment
        updatedAttachment.metadata = [
            "duration": "0:00", // Would extract actual duration
            "format": attachment.mimeType
        ]
        
        return updatedAttachment
    }
    
    private func extractTextFromPDF(url: URL) async throws -> String {
        // Simplified PDF text extraction
        // In a real app, you'd use PDFKit or similar
        return "PDF content extraction would be implemented here"
    }
    
    private func storeExtractedText(attachmentId: UUID, text: String) async throws {
        struct ExtractedTextData: Codable {
            let attachment_id: String
            let extracted_text: String
            let created_at: String
        }

        let textData = ExtractedTextData(
            attachment_id: attachmentId.uuidString,
            extracted_text: text,
            created_at: Date().ISO8601Format()
        )

        try await supabaseClient.client
            .from("attachment_extracted_text")
            .insert(textData)
            .execute()
    }
    
    private func updateAttachmentProcessedStatus(
        _ attachmentId: UUID,
        isProcessed: Bool
    ) async throws {
        try await supabaseClient.client
            .from("chat_attachments")
            .update(["is_processed": isProcessed])
            .eq("id", value: attachmentId.uuidString)
            .execute()
    }
    
    // MARK: - File Management
    
    func deleteAttachment(_ attachment: ChatAttachment) async throws {
        // Delete from storage
        try await supabaseClient.client.storage
            .from("chat-attachments")
            .remove(paths: [attachment.storagePath])
        
        // Delete thumbnail if exists
        if attachment.thumbnailUrl != nil {
            let thumbnailPath = attachment.storagePath.replacingOccurrences(of: ".", with: "_thumb.")
            _ = try? await supabaseClient.client.storage
                .from("chat-attachments")
                .remove(paths: [thumbnailPath])
        }
        
        // Delete from database
        try await supabaseClient.client
            .from("chat_attachments")
            .delete()
            .eq("id", value: attachment.id.uuidString)
            .execute()
        
        // Remove from recent attachments
        recentAttachments.removeAll { $0.id == attachment.id }
    }
    
    func getAttachmentsForConversation(_ conversationId: UUID) async throws -> [ChatAttachment] {
        _ = try await supabaseClient.client
            .from("chat_attachments")
            .select()
            .eq("conversation_id", value: conversationId.uuidString)
            .order("uploaded_at", ascending: false)
            .execute()

        // Parse response and convert to ChatAttachment objects
        // This is simplified - you'd implement proper JSON decoding
        return []
    }
    
    private func loadRecentAttachments() {
        Task {
            do {
                // Load recent attachments from database
                _ = try await supabaseClient.client
                    .from("chat_attachments")
                    .select()
                    .order("uploaded_at", ascending: false)
                    .limit(20)
                    .execute()

                // Parse and update recentAttachments
                // This is simplified - you'd implement proper JSON decoding

            } catch {
                print("Failed to load recent attachments: \(error)")
            }
        }
    }
    
    // MARK: - Validation
    
    private func isValidFileType(mimeType: String) -> Bool {
        // Check against allowed MIME types
        let allowedMimeTypes = [
            "image/jpeg", "image/png", "image/gif", "image/webp",
            "application/pdf", "text/plain", "text/rtf",
            "audio/mpeg", "audio/wav", "audio/aiff",
            "video/mp4", "video/mov", "video/avi",
            "application/zip", "application/json", "text/csv"
        ]
        
        return allowedMimeTypes.contains(mimeType)
    }
    
    func validateFile(data: Data, fileName: String, mimeType: String) -> FileValidationResult {
        var errors: [String] = []
        
        // Check file size
        if data.count > maxFileSize {
            errors.append("File size exceeds \(maxFileSize / (1024 * 1024))MB limit")
        }
        
        // Check file type
        if !isValidFileType(mimeType: mimeType) {
            errors.append("File type not supported")
        }
        
        // Check file name
        if fileName.isEmpty {
            errors.append("File name cannot be empty")
        }
        
        return FileValidationResult(
            isValid: errors.isEmpty,
            errors: errors
        )
    }
}

// MARK: - Supporting Types

struct UploadedFile {
    let path: String
    let publicURL: String
}

struct FileValidationResult {
    let isValid: Bool
    let errors: [String]
}

enum FileAttachmentError: LocalizedError {
    case fileTooLarge
    case unsupportedFileType
    case uploadFailed
    case processingFailed
    
    var errorDescription: String? {
        switch self {
        case .fileTooLarge:
            return "File size exceeds the maximum allowed limit"
        case .unsupportedFileType:
            return "This file type is not supported"
        case .uploadFailed:
            return "Failed to upload file"
        case .processingFailed:
            return "Failed to process file"
        }
    }
}
