//
//  MicroAssessmentService.swift
//  NIRA
//
//  Continuous Micro-Assessment Framework
//  Evaluates learner progress every 3-5 learning units
//

import Foundation
import Combine
import SwiftUI

@MainActor
class MicroAssessmentService: ObservableObject, @unchecked Sendable {
    @Published var currentAssessment: MicroAssessment?
    @Published var assessmentQueue: [MicroAssessment] = []
    @Published var recentScores: [AssessmentScore] = []
    @Published var skillProgression: [SkillCategory: ProgressionData] = [:]
    
    @MainActor private let supabaseService = NIRASupabaseClient.shared
    private let fsrsService = FSRSService()
    private var cancellables = Set<AnyCancellable>()
    
    // Assessment Configuration
    private let assessmentTriggerInterval = 4 // Every 4 learning units
    private var learningUnitsCompleted = 0
    
    init() {
        setupAssessmentTracking()
        loadRecentAssessments()
    }
    
    // MARK: - Core Assessment Logic
    
    func trackLearningUnitCompletion(unitId: String, unitType: LearningItemType, performance: UnitPerformance) async {
        learningUnitsCompleted += 1
        
        // Store unit performance for analysis
        await recordUnitPerformance(unitId: unitId, unitType: unitType, performance: performance)
        
        // Check if micro-assessment should be triggered
        if shouldTriggerAssessment() {
            await triggerMicroAssessment()
        }
        
        // Update skill progression
        await updateSkillProgression(unitType: unitType, performance: performance)
    }
    
    private func shouldTriggerAssessment() -> Bool {
        return learningUnitsCompleted % assessmentTriggerInterval == 0
    }
    
    private func triggerMicroAssessment() async {
        let assessment = await generateMicroAssessment()
        
        await MainActor.run {
            self.currentAssessment = assessment
            self.assessmentQueue.append(assessment)
        }
    }
    
    private func generateMicroAssessment() async -> MicroAssessment {
        // Analyze recent learning units to determine assessment focus
        let recentUnits = await getRecentLearningUnits(count: assessmentTriggerInterval)
        let weakAreas = await identifyWeakAreas(from: recentUnits)
        let assessmentItems = await createAssessmentItems(targeting: weakAreas)
        
        return MicroAssessment(
            id: UUID().uuidString,
            createdAt: Date(),
            targetSkills: weakAreas,
            items: assessmentItems,
            estimatedDuration: TimeInterval(assessmentItems.count * 30), // 30 seconds per item
            difficultyLevel: calculateOptimalDifficulty(from: recentUnits),
            adaptiveParams: createAdaptiveParameters()
        )
    }
    
    private func identifyWeakAreas(from units: [CompletedLearningUnit]) async -> [SkillCategory] {
        var skillPerformance: [SkillCategory: [Double]] = [:]
        
        // Analyze performance by skill category
        for unit in units {
            let category = mapUnitTypeToSkillCategory(unit.type)
            skillPerformance[category, default: []].append(unit.performance.accuracyScore)
        }
        
        // Identify categories with below-threshold performance
        let threshold = 0.75 // 75% accuracy threshold
        return skillPerformance.compactMap { category, scores in
            let averageScore = scores.reduce(0, +) / Double(scores.count)
            return averageScore < threshold ? category : nil
        }
    }
    
    private func createAssessmentItems(targeting skills: [SkillCategory]) async -> [AssessmentItem] {
        var items: [AssessmentItem] = []
        
        for skill in skills {
            // Create 2-3 items per weak skill area
            let skillItems = await generateItemsForSkill(skill, count: 3)
            items.append(contentsOf: skillItems)
        }
        
        // Ensure minimum 5 items, maximum 12 items
        let targetCount = max(5, min(12, items.count))
        
        if items.count > targetCount {
            items = Array(items.shuffled().prefix(targetCount))
        } else if items.count < 5 {
            // Add general review items to reach minimum
            let generalItems = await generateGeneralReviewItems(count: 5 - items.count)
            items.append(contentsOf: generalItems)
        }
        
        return items.shuffled() // Randomize order
    }
    
    private func generateItemsForSkill(_ skill: SkillCategory, count: Int) async -> [AssessmentItem] {
        var items: [AssessmentItem] = []
        
        for _ in 0..<count {
            switch skill {
            case .vocabulary:
                items.append(await createVocabularyAssessmentItem())
            case .grammar:
                items.append(await createGrammarAssessmentItem())
            case .listening:
                items.append(await createListeningAssessmentItem())
            case .speaking:
                items.append(await createSpeakingAssessmentItem())
            case .reading:
                items.append(await createReadingAssessmentItem())
            case .writing:
                items.append(await createWritingAssessmentItem())
            case .culture:
                items.append(await createCulturalAssessmentItem())
            case .pronunciation:
                items.append(await createPronunciationAssessmentItem())
            }
        }
        
        return items
    }
    
    // MARK: - Assessment Item Creation
    
    private func createVocabularyAssessmentItem() async -> AssessmentItem {
        // Fetch a vocabulary item that hasn't been assessed recently
        let vocabItem = await fetchVocabularyForAssessment()
        
        return AssessmentItem(
            id: UUID().uuidString,
            type: .multipleChoice,
            skill: .vocabulary,
            question: "What does '\(vocabItem.tamilWord)' mean?",
            correctAnswer: vocabItem.englishTranslation,
            options: await generateVocabularyOptions(correct: vocabItem.englishTranslation),
            audioUrl: vocabItem.audioUrl,
            difficultyLevel: vocabItem.difficulty,
            timeLimit: 15,
            metadata: ["word_id": vocabItem.id, "frequency": "\(vocabItem.frequency)"]
        )
    }
    
    private func createGrammarAssessmentItem() async -> AssessmentItem {
        let grammarRule = await fetchGrammarRuleForAssessment()
        
        return AssessmentItem(
            id: UUID().uuidString,
            type: .fillInBlank,
            skill: .grammar,
            question: "Complete the sentence: \(grammarRule.sentenceTemplate)",
            correctAnswer: grammarRule.correctForm,
            options: grammarRule.alternativeOptions,
            audioUrl: grammarRule.audioUrl,
            difficultyLevel: grammarRule.difficulty,
            timeLimit: 20,
            metadata: ["rule_id": grammarRule.id, "rule_type": grammarRule.type]
        )
    }
    
    private func createListeningAssessmentItem() async -> AssessmentItem {
        let audioContent = await fetchAudioContentForAssessment()
        
        return AssessmentItem(
            id: UUID().uuidString,
            type: .multipleChoice,
            skill: .listening,
            question: "Listen to the audio and select what you heard:",
            correctAnswer: audioContent.transcript,
            options: await generateListeningOptions(correct: audioContent.transcript),
            audioUrl: audioContent.audioUrl,
            difficultyLevel: audioContent.difficulty,
            timeLimit: 30,
            metadata: ["content_id": audioContent.id, "speaker": audioContent.speaker]
        )
    }
    
    private func createSpeakingAssessmentItem() async -> AssessmentItem {
        let phrase = await fetchPhraseForSpeakingAssessment()
        
        return AssessmentItem(
            id: UUID().uuidString,
            type: .speaking,
            skill: .speaking,
            question: "Pronounce the following phrase: '\(phrase.tamilText)'",
            correctAnswer: phrase.phoneticTranscription,
            options: [], // No options for speaking assessment
            audioUrl: phrase.referenceAudioUrl,
            difficultyLevel: phrase.difficulty,
            timeLimit: 15,
            metadata: ["phrase_id": phrase.id, "pronunciation_key": phrase.phoneticKey]
        )
    }
    
    // MARK: - Assessment Execution
    
    func startAssessment(_ assessment: MicroAssessment) {
        DispatchQueue.main.async {
            self.currentAssessment = assessment
        }
    }
    
    func submitAssessmentResponse(itemId: String, userAnswer: String, timeSpent: TimeInterval) async {
        guard let assessment = currentAssessment,
              let itemIndex = assessment.items.firstIndex(where: { $0.id == itemId }) else {
            return
        }
        
        let item = assessment.items[itemIndex]
        let isCorrect = evaluateResponse(item: item, userAnswer: userAnswer)
        
        let response = MicroAssessmentResponse(
            id: UUID().uuidString,
            assessmentId: assessment.id,
            itemId: itemId,
            userAnswer: userAnswer,
            correctAnswer: item.correctAnswer,
            isCorrect: isCorrect,
            timeSpent: timeSpent,
            timestamp: Date()
        )
        
        // Store response
        await saveAssessmentResponse(response)
        
        // Update FSRS based on performance
        await updateFSRSFromAssessment(item: item, isCorrect: isCorrect, timeSpent: timeSpent)
        
        // Check if assessment is complete
        await checkAssessmentCompletion(assessment.id)
    }
    
    private func evaluateResponse(item: AssessmentItem, userAnswer: String) -> Bool {
        switch item.type {
        case .multipleChoice, .fillInBlank:
            return userAnswer.lowercased().trimmingCharacters(in: .whitespacesAndNewlines) == 
                   item.correctAnswer.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        case .speaking:
            // Would integrate with speech recognition for pronunciation assessment
            return evaluatePronunciation(userAudio: userAnswer, reference: item.correctAnswer)
        case .writing:
            return evaluateWritingResponse(userText: userAnswer, expected: item.correctAnswer)
        }
    }
    
    private func evaluatePronunciation(userAudio: String, reference: String) -> Bool {
        // Placeholder for speech recognition integration
        // Would use iOS Speech framework or cloud-based pronunciation assessment
        return true // Simplified for demo
    }
    
    private func evaluateWritingResponse(userText: String, expected: String) -> Bool {
        // Implement semantic similarity check for writing responses
        let similarity = calculateTextSimilarity(userText, expected)
        return similarity > 0.8 // 80% similarity threshold
    }
    
    // MARK: - Assessment Analytics
    
    func generateAssessmentReport(_ assessmentId: String) async -> AssessmentReport {
        let responses = await fetchAssessmentResponses(assessmentId)
        let skillPerformance = calculateSkillPerformance(from: responses)
        let recommendations = generateRecommendations(from: skillPerformance)
        
        return AssessmentReport(
            assessmentId: assessmentId,
            completedAt: Date(),
            overallScore: calculateOverallScore(from: responses),
            skillBreakdown: skillPerformance,
            recommendations: recommendations,
            timeSpent: responses.reduce(0) { $0 + $1.timeSpent },
            improvedAreas: identifyImprovedAreas(skillPerformance),
            areasForFocus: identifyAreasForFocus(skillPerformance)
        )
    }
    
    private func calculateSkillPerformance(from responses: [MicroAssessmentResponse]) -> [String: SkillPerformance] {
        var skillGroups: [SkillCategory: [MicroAssessmentResponse]] = [:]
        
        // Group responses by skill
        for response in responses {
            // Would need to fetch the original item to get skill category
            // Simplified for demo
            let skill = SkillCategory.vocabulary // Placeholder
            skillGroups[skill, default: []].append(response)
        }
        
        // Calculate performance for each skill
        return skillGroups.reduce(into: [String: SkillPerformance]()) { result, item in
            let skill = item.key
            let responses = item.value
            let correctCount = responses.filter { $0.isCorrect }.count
            let accuracy = Double(correctCount) / Double(responses.count)
            let averageTime = responses.reduce(0) { $0 + $1.timeSpent } / Double(responses.count)
            
            result[skill.rawValue] = SkillPerformance(
                accuracy: accuracy,
                averageResponseTime: averageTime,
                confidence: calculateConfidence(from: responses),
                trend: .stable // Would calculate based on historical data
            )
        }
    }
    
    // MARK: - Data Persistence
    
    private func saveAssessmentResponse(_ response: MicroAssessmentResponse) async {
        // Mock database operation for compilation
        // try await supabaseService.saveAssessmentResponse(response)
        print("Saving assessment response: \(response.id)")
    }
    
    private func recordUnitPerformance(unitId: String, unitType: LearningItemType, performance: UnitPerformance) async {
        let _ = CompletedLearningUnit(
            id: unitId,
            type: unitType,
            completedAt: Date(),
            performance: performance
        )

        // Mock database operation for compilation
        // try await supabaseService.saveCompletedUnit(record)
        print("Recording unit performance for: \(unitId)")
    }
    
    // MARK: - Helper Methods
    
    private func setupAssessmentTracking() {
        // Reset learning unit counter daily
        Timer.publish(every: 86400, on: .main, in: .common) // 24 hours
            .autoconnect()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.learningUnitsCompleted = 0
            }
            .store(in: &cancellables)
    }
    
    private func loadRecentAssessments() {
        Task {
            // Mock database query for compilation
            let recent: [AssessmentResponse] = []
            // try await supabaseService.fetchRecentAssessmentScores(limit: 10)
            DispatchQueue.main.async {
                // Convert AssessmentResponse to AssessmentScore
                self.recentScores = recent.map { response in
                    AssessmentScore(
                        assessmentId: response.id.uuidString,
                        overallScore: 0.8, // Mock score
                        skillScores: [:],
                        completedAt: Date()
                    )
                }
            }
        }
    }
    
    private func mapUnitTypeToSkillCategory(_ type: LearningItemType) -> SkillCategory {
        switch type {
        case .vocabulary: return .vocabulary
        case .grammar: return .grammar
        case .conversation: return .speaking
        case .phrase: return .vocabulary
        }
    }
    
    private func calculateOptimalDifficulty(from units: [CompletedLearningUnit]) -> Int {
        let averageAccuracy = units.map { $0.performance.accuracyScore }.reduce(0, +) / Double(units.count)
        
        if averageAccuracy > 0.9 {
            return min(10, units.first?.performance.currentDifficulty ?? 5 + 1)
        } else if averageAccuracy < 0.6 {
            return max(1, units.first?.performance.currentDifficulty ?? 5 - 1)
        } else {
            return units.first?.performance.currentDifficulty ?? 5
        }
    }
    
    private func calculateConfidence(from responses: [MicroAssessmentResponse]) -> Double {
        // Calculate confidence based on response time and accuracy patterns
        let avgTime = responses.reduce(0) { $0 + $1.timeSpent } / Double(responses.count)
        let accuracy = Double(responses.filter { $0.isCorrect }.count) / Double(responses.count)
        
        // Quick, accurate responses indicate high confidence
        let timeConfidence = max(0, 1 - (avgTime / 30)) // Normalize by 30 seconds
        return (accuracy + timeConfidence) / 2
    }
    
    private func calculateTextSimilarity(_ text1: String, _ text2: String) -> Double {
        // Simplified similarity calculation
        let words1 = Set(text1.lowercased().components(separatedBy: .whitespacesAndNewlines))
        let words2 = Set(text2.lowercased().components(separatedBy: .whitespacesAndNewlines))
        let intersection = words1.intersection(words2)
        let union = words1.union(words2)
        return Double(intersection.count) / Double(union.count)
    }
    
    // MARK: - Placeholder Methods (To be implemented)
    
    private func getRecentLearningUnits(count: Int) async -> [CompletedLearningUnit] {
        // Placeholder - would fetch from database
        return []
    }
    
    private func updateSkillProgression(unitType: LearningItemType, performance: UnitPerformance) async {
        // Placeholder - would update skill progression
    }
    
    private func generateGeneralReviewItems(count: Int) async -> [AssessmentItem] {
        // Placeholder - would generate general assessment items
        return []
    }
    
    private func createReadingAssessmentItem() async -> AssessmentItem {
        return AssessmentItem(
            id: UUID().uuidString,
            type: AssessmentItemType.multipleChoice,
            skill: SkillCategory.reading,
            question: "Sample reading question",
            correctAnswer: "Sample answer",
            options: ["Option 1", "Option 2", "Option 3", "Sample answer"],
            audioUrl: nil as String?,
            difficultyLevel: 5,
            timeLimit: 30,
            metadata: [:]
        )
    }
    
    private func createWritingAssessmentItem() async -> AssessmentItem {
        return AssessmentItem(
            id: UUID().uuidString,
            type: AssessmentItemType.writing,
            skill: SkillCategory.writing,
            question: "Sample writing prompt",
            correctAnswer: "Sample response",
            options: [],
            audioUrl: nil as String?,
            difficultyLevel: 5,
            timeLimit: 60,
            metadata: [:]
        )
    }
    
    private func createCulturalAssessmentItem() async -> AssessmentItem {
        return AssessmentItem(
            id: UUID().uuidString,
            type: AssessmentItemType.multipleChoice,
            skill: SkillCategory.culture,
            question: "Sample cultural question",
            correctAnswer: "Sample answer",
            options: ["Option 1", "Option 2", "Sample answer", "Option 3"],
            audioUrl: nil as String?,
            difficultyLevel: 5,
            timeLimit: 25,
            metadata: [:]
        )
    }
    
    private func createPronunciationAssessmentItem() async -> AssessmentItem {
        return AssessmentItem(
            id: UUID().uuidString,
            type: AssessmentItemType.speaking,
            skill: SkillCategory.pronunciation,
            question: "Pronounce the following word correctly",
            correctAnswer: "correct pronunciation",
            options: [],
            audioUrl: nil as String?,
            difficultyLevel: 4,
            timeLimit: 15,
            metadata: [:]
        )
    }
    
    private func fetchVocabularyForAssessment() async -> VocabularyItem {
        return VocabularyItem(
            id: UUID().uuidString,
            tamilWord: "வணக்கம்",
            englishTranslation: "Hello",
            audioUrl: nil as String?,
            difficulty: 3,
            frequency: 100
        )
    }
    
    private func generateVocabularyOptions(correct: String) async -> [String] {
        return ["Goodbye", "Thank you", correct, "Please"].shuffled()
    }
    
    private func fetchGrammarRuleForAssessment() async -> GrammarRule {
        return GrammarRule(
            id: UUID().uuidString,
            type: "verb_conjugation",
            sentenceTemplate: "நான் _____ போகிறேன்",
            correctForm: "வீட்டிற்கு",
            alternativeOptions: ["வீட்டிற்கு", "பள்ளிக்கு", "கடைக்கு", "பாரிஸ்"],
            audioUrl: nil as String?,
            difficulty: 4
        )
    }
    
    private func fetchAudioContentForAssessment() async -> AudioContent {
        return AudioContent(
            id: UUID().uuidString,
            transcript: "வணக்கம், எப்படி இருக்கிறீர்கள்?",
            audioUrl: "https://example.com/audio.mp3",
            speaker: "native_speaker_1",
            difficulty: 3
        )
    }
    
    private func generateListeningOptions(correct: String) async -> [String] {
        return ["நீங்கள் எப்படி இருக்கிறீர்கள்?", correct, "நன்றி", "மன்னிக்கவும்"].shuffled()
    }
    
    private func fetchPhraseForSpeakingAssessment() async -> PhraseItem {
        return PhraseItem(
            id: UUID().uuidString,
            tamilText: "நன்றி வணக்கம்",
            phoneticTranscription: "nandri vanakkam",
            phoneticKey: "nɑn̪ɾi ʋɑɳɑkkɑm",
            referenceAudioUrl: "https://example.com/reference.mp3" as String?,
            difficulty: 3
        )
    }
    
    private func createAdaptiveParameters() -> AdaptiveParameters {
        return AdaptiveParameters(
            targetAccuracy: 0.8,
            maxDifficultyIncrease: 2,
            confidenceThreshold: 0.7,
            adaptationRate: 0.1
        )
    }
    
    private func updateFSRSFromAssessment(item: AssessmentItem, isCorrect: Bool, timeSpent: TimeInterval) async {
        // Would update FSRS based on assessment performance
    }
    
    private func checkAssessmentCompletion(_ assessmentId: String) async {
        // Would check if assessment is complete and generate report
    }
    
    private func fetchAssessmentResponses(_ assessmentId: String) async -> [MicroAssessmentResponse] {
        // Placeholder - would fetch responses from database
        return []
    }
    
    private func calculateOverallScore(from responses: [MicroAssessmentResponse]) -> Double {
        guard !responses.isEmpty else { return 0.0 }
        let correctCount = responses.filter { $0.isCorrect }.count
        return Double(correctCount) / Double(responses.count)
    }
    
    private func generateRecommendations(from skillPerformance: [String: SkillPerformance]) -> [LearningRecommendation] {
        return skillPerformance.compactMap { skill, performance in
            if performance.accuracy < 0.7 {
                return LearningRecommendation(
                    id: UUID(),
                    type: RecommendationType.skillReview,
                    title: "Skill Review",
                    description: "Focus on improving \(skill.capitalized) skills",
                    priority: RecommendationPriority.high,
                    targetSkills: [],
                    estimatedTime: 1800, // 30 minutes
                    confidence: 0.8,
                    reasoning: "Performance below 70% threshold",
                    createdAt: Date()
                )
            }
            return nil
        }
    }
    
    private func identifyImprovedAreas(_ skillPerformance: [String: SkillPerformance]) -> [String] {
        return skillPerformance.compactMap { skill, performance in
            performance.trend == .improving ? skill : nil
        }
    }
    
    private func identifyAreasForFocus(_ skillPerformance: [String: SkillPerformance]) -> [String] {
        return skillPerformance.compactMap { skill, performance in
            performance.accuracy < 0.75 ? skill : nil
        }
    }
}

// MARK: - Supporting Models

struct MicroAssessment: Codable, Identifiable {
    let id: String
    let createdAt: Date
    let targetSkills: [SkillCategory]
    let items: [AssessmentItem]
    let estimatedDuration: TimeInterval
    let difficultyLevel: Int
    let adaptiveParams: AdaptiveParameters
}

struct AssessmentItem: Codable, Identifiable {
    let id: String
    let type: AssessmentItemType
    let skill: SkillCategory
    let question: String
    let correctAnswer: String
    let options: [String]
    let audioUrl: String?
    let difficultyLevel: Int
    let timeLimit: TimeInterval
    let metadata: [String: String]
}

struct MicroAssessmentResponse: Codable {
    let id: String
    let assessmentId: String
    let itemId: String
    let userAnswer: String
    let correctAnswer: String
    let isCorrect: Bool
    let timeSpent: TimeInterval
    let timestamp: Date
}

struct AssessmentScore: Codable {
    let assessmentId: String
    let overallScore: Double
    let skillScores: [String: Double] // Changed from [SkillCategory: Double]
    let completedAt: Date
}

struct AssessmentReport: Codable {
    let assessmentId: String
    let completedAt: Date
    let overallScore: Double
    let skillBreakdown: [String: SkillPerformance] // Changed from [SkillCategory: SkillPerformance]
    let recommendations: [LearningRecommendation]
    let timeSpent: TimeInterval
    let improvedAreas: [String] // Changed from [SkillCategory]
    let areasForFocus: [String] // Changed from [SkillCategory]
}

enum AssessmentItemType: String, Codable, CaseIterable {
    case multipleChoice = "multiple_choice"
    case fillInBlank = "fill_in_blank"
    case speaking = "speaking"
    case writing = "writing"
}

// Using existing SkillCategory from AdaptiveCurriculumService
extension SkillCategory {
    var icon: String {
        switch self {
        case .vocabulary: return "book.fill"
        case .grammar: return "textformat.abc"
        case .listening: return "ear.fill"
        case .speaking: return "mic.fill"
        case .reading: return "doc.text.fill"
        case .writing: return "pencil"
        case .culture: return "globe.americas.fill"
        case .pronunciation: return "speaker.wave.3.fill"
        }
    }
}

struct SkillPerformance: Codable {
    let accuracy: Double
    let averageResponseTime: TimeInterval
    let confidence: Double
    let trend: PerformanceTrend
}

enum PerformanceTrend: String, Codable {
    case improving = "improving"
    case stable = "stable"
    case declining = "declining"
}

struct UnitPerformance: Codable {
    let accuracyScore: Double
    let completionTime: TimeInterval
    let attemptsNeeded: Int
    let currentDifficulty: Int
    let engagementScore: Double
}

struct CompletedLearningUnit: Codable {
    let id: String
    let type: LearningItemType
    let completedAt: Date
    let performance: UnitPerformance
}

struct ProgressionData: Codable {
    let currentLevel: Int
    let experience: Double
    let recentPerformance: [Double]
    let lastAssessed: Date
}

// Using existing LearningRecommendation from AdaptiveCurriculumService

// Using existing RecommendationType and RecommendationPriority from AdaptiveCurriculumService

struct AdaptiveParameters: Codable {
    let targetAccuracy: Double
    let maxDifficultyIncrease: Int
    let confidenceThreshold: Double
    let adaptationRate: Double
}

// Placeholder for external data structures
struct VocabularyItem: Codable {
    let id: String
    let tamilWord: String
    let englishTranslation: String
    let audioUrl: String?
    let difficulty: Int
    let frequency: Int
}

struct GrammarRule: Codable {
    let id: String
    let type: String
    let sentenceTemplate: String
    let correctForm: String
    let alternativeOptions: [String]
    let audioUrl: String?
    let difficulty: Int
}

struct AudioContent: Codable {
    let id: String
    let transcript: String
    let audioUrl: String
    let speaker: String
    let difficulty: Int
}

struct PhraseItem: Codable {
    let id: String
    let tamilText: String
    let phoneticTranscription: String
    let phoneticKey: String
    let referenceAudioUrl: String?
    let difficulty: Int
} 