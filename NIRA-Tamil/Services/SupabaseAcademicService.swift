//
//  SupabaseAcademicService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2025-01-19.
//  Copyright © 2025 Securight. All rights reserved.
//

import Foundation
import Supabase
import Combine

@MainActor
class SupabaseAcademicService: ObservableObject {
    static let shared = SupabaseAcademicService()
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var academicModules: [AcademicContentModule] = []
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient

    private init() {
        // Check if Supabase API keys are configured
        if APIKeys.supabaseConfigured {
            // Use real Supabase client
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: APIKeys.supabaseURL)!,
                supabaseKey: APIKeys.supabaseAnonKey
            )
            print("🎓 SupabaseAcademicService initialized with real Supabase client")
        } else {
            // Use mock client for development
            print("⚠️ SupabaseAcademicService using mock client - API keys not configured")
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: "https://mock.supabase.co")!,
                supabaseKey: "mock-key"
            )
        }
    }
    
    // MARK: - Public Methods
    
    /// Fetch academic modules for a specific standard level
    func fetchAcademicModules(for standard: Int) async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        // Check if using mock data
        if !APIKeys.supabaseConfigured {
            print("🔄 Using mock academic modules for standard: \(standard)")
            
            let mockModules = createMockAcademicModules(for: standard)
            print("✅ Loaded \(mockModules.count) mock academic modules for standard \(standard)")
            
            await MainActor.run {
                academicModules = mockModules
                isLoading = false
            }
            return
        }
        
        do {
            print("🔄 Fetching academic modules for standard: \(standard)")
            
            let response: [AcademicContentModule] = try await supabase
                .from("academic_content_modules")
                .select("*")
                .eq("standard_level", value: standard)
                .order("module_type")
                .order("title_english")
                .execute()
                .value
            
            await MainActor.run {
                academicModules = response
            }
            print("✅ Fetched \(response.count) academic modules for standard \(standard)")
            
            // Debug: Show module details
            if response.isEmpty {
                print("⚠️ No academic modules found for standard \(standard) in Supabase")
            } else {
                print("📋 Academic modules found for standard \(standard):")
                let groupedModules = Dictionary(grouping: response, by: { $0.moduleType })
                for (type, modules) in groupedModules {
                    print("  - \(type): \(modules.count) modules")
                }
            }
            
        } catch {
            let mockModules = createMockAcademicModules(for: standard)
            await MainActor.run {
                errorMessage = "Failed to fetch academic modules: \(error.localizedDescription)"
                academicModules = mockModules
            }
            print("❌ Error fetching academic modules: \(error)")
            print("🔄 Using fallback mock modules for standard: \(standard)")
        }
        
        await MainActor.run {
            isLoading = false
        }
    }
    
    /// Fetch all academic modules (for overview)
    func fetchAllAcademicModules() async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        do {
            print("🔄 Fetching all academic modules...")
            
            let response: [AcademicContentModule] = try await supabase
                .from("academic_content_modules")
                .select("*")
                .order("standard_level")
                .order("module_type")
                .execute()
                .value
            
            await MainActor.run {
                academicModules = response
            }
            print("✅ Fetched \(response.count) total academic modules")
            
        } catch {
            await MainActor.run {
                errorMessage = "Failed to fetch academic modules: \(error.localizedDescription)"
                academicModules = []
            }
            print("❌ Error fetching all academic modules: \(error)")
        }
        
        await MainActor.run {
            isLoading = false
        }
    }
    
    /// Get modules by type for a specific standard
    func getModules(for standard: Int, type: String) -> [AcademicContentModule] {
        return academicModules.filter { $0.standardLevel == standard && $0.moduleType == type }
    }
    
    /// Get all available standards
    func getAvailableStandards() -> [Int] {
        let standards = Set(academicModules.map { $0.standardLevel })
        return Array(standards).sorted()
    }
    
    /// Get all available module types for a standard
    func getModuleTypes(for standard: Int) -> [String] {
        let types = Set(academicModules.filter { $0.standardLevel == standard }.map { $0.moduleType })
        return Array(types).sorted()
    }
    
    // MARK: - Mock Data
    
    private func createMockAcademicModules(for standard: Int) -> [AcademicContentModule] {
        return [
            AcademicContentModule(
                id: UUID().uuidString,
                standardLevel: standard,
                moduleType: "story",
                titleEnglish: "Standard \(standard) Tamil Story",
                titleTamil: "தரம் \(standard) தமிழ் கதை",
                contentExcerpt: "A Tamil story for standard \(standard) students...",
                fullContent: "Sample Tamil story content for standard \(standard)...",
                difficultyLevel: standard,
                estimatedReadingTime: 5,
                culturalContext: "Traditional Tamil stories for education",
                learningObjectives: ["Reading comprehension", "Vocabulary building"],
                prerequisiteModules: [],
                tags: ["story", "tamil"],
                audioUrl: nil,
                romanization: nil,
                pronunciation: nil,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            ),
            AcademicContentModule(
                id: UUID().uuidString,
                standardLevel: standard,
                moduleType: "poetry",
                titleEnglish: "Standard \(standard) Tamil Poetry",
                titleTamil: "தரம் \(standard) தமிழ் கவிதை",
                contentExcerpt: "Tamil poetry for standard \(standard) students...",
                fullContent: "Sample Tamil poetry content for standard \(standard)...",
                difficultyLevel: standard,
                estimatedReadingTime: 3,
                culturalContext: "Tamil poetry traditions",
                learningObjectives: ["Poetry appreciation", "Rhythm understanding"],
                prerequisiteModules: [],
                tags: ["poetry", "tamil"],
                audioUrl: nil,
                romanization: nil,
                pronunciation: nil,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            )
        ]
    }
}

// MARK: - Academic Content Module Model

struct AcademicContentModule: Codable, Identifiable {
    let id: String
    let standardLevel: Int
    let moduleType: String
    let titleEnglish: String
    let titleTamil: String
    let contentExcerpt: String?
    let fullContent: String
    let difficultyLevel: Int
    let estimatedReadingTime: Int
    let culturalContext: String?
    let learningObjectives: [String]?
    let prerequisiteModules: [String]?
    let tags: [String]?
    let audioUrl: String?
    let romanization: String?
    let pronunciation: String?
    let createdAt: String
    let updatedAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case standardLevel = "standard_level"
        case moduleType = "module_type"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case contentExcerpt = "content_excerpt"
        case fullContent = "full_content"
        case difficultyLevel = "difficulty_level"
        case estimatedReadingTime = "estimated_reading_time"
        case culturalContext = "cultural_context"
        case learningObjectives = "learning_objectives"
        case prerequisiteModules = "prerequisite_modules"
        case tags
        case audioUrl = "audio_url"
        case romanization
        case pronunciation
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    var moduleTypeDisplayName: String {
        switch moduleType.lowercased() {
        case "story": return "Stories"
        case "poetry": return "Poetry"
        case "literature": return "Literature"
        case "grammar": return "Grammar"
        case "cultural": return "Cultural"
        default: return moduleType.capitalized
        }
    }
    
    var standardDisplayName: String {
        return "Standard \(standardLevel)"
    }
}
