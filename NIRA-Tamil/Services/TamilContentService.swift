//
//  TamilContentService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation
import SwiftUI

@MainActor
class TamilContentService: ObservableObject {
    static let shared = TamilContentService()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var contentMetadata: ContentMetadata?
    
    // Content Storage
    @Published var cefrLessons: [CEFRLevel: [TamilLesson]] = [:]
    @Published var tnTextbooks: [TNTextbook] = []
    @Published var userProgress: LearningProgress?
    
    // Current Session
    @Published var currentLevel: CEFRLevel = .a1
    @Published var currentTrack: ContentTrack = .conversational
    @Published var currentLesson: TamilLesson?

    private let contentLoader = TamilContentLoader()
    private let audioManager = AudioContentManager.shared

    // Track ongoing level loads to prevent concurrent fetches
    private var loadingLevels: Set<CEFRLevel> = []

    private init() {
        loadInitialContent()
    }

    private var progressService: ProgressiveLearningService {
        return ProgressiveLearningService.shared
    }
    
    // MARK: - Content Loading
    
    func loadInitialContent() {
        Task { @MainActor in
            await loadAllContent()
        }
    }
    
    func loadAllContent() async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // Load CEFR lessons
        await loadCEFRLessons()

        // Load TN textbooks
        await loadTNTextbooks()

        // Load user progress
        await loadUserProgress()

        // Update metadata
        await MainActor.run {
            updateContentMetadata()
            isLoading = false
        }
    }
    
    private func loadCEFRLessons() async {
        var loadedLessons: [CEFRLevel: [TamilLesson]] = [:]

        for level in CEFRLevel.allCases {
            do {
                let lessons = try await contentLoader.loadLessonsForLevel(level)
                loadedLessons[level] = lessons
            } catch {
                print("Failed to load lessons for level \(level): \(error)")
                loadedLessons[level] = []
            }
        }

        await MainActor.run {
            cefrLessons = loadedLessons

            // If no lessons were loaded, use sample data
            if cefrLessons.values.allSatisfy({ $0.isEmpty }) {
                loadSampleLessons()
            }
        }
    }
    
    private func loadTNTextbooks() async {
        do {
            let textbooks = try await contentLoader.loadTNTextbooks()
            await MainActor.run {
                tnTextbooks = textbooks
            }
        } catch {
            print("Failed to load TN textbooks: \(error)")
        }
    }
    
    private func loadUserProgress() async {
        // Load from UserDefaults or Core Data
        // For now, create default progress
        userProgress = LearningProgress(
            completedLessons: 0,
            totalLessons: 30,
            currentStreak: 0,
            weeklyGoal: 5,
            weeklyProgress: 0,
            lastStudyDate: Date()
        )
    }
    
    private func updateContentMetadata() {
        let totalLessons = cefrLessons.values.flatMap { $0 }.count
        let totalVocabulary = cefrLessons.values.flatMap { $0 }.flatMap { $0.vocabulary }.count
        let totalConversations = cefrLessons.values.flatMap { $0 }.flatMap { $0.conversations }.count
        
        contentMetadata = ContentMetadata(
            totalLessons: totalLessons,
            totalVocabulary: totalVocabulary,
            totalConversations: totalConversations,
            availableLevels: CEFRLevel.allCases,
            availableTracks: ContentTrack.allCases,
            lastUpdated: Date(),
            contentVersion: "1.0.0"
        )
    }

    // MARK: - Sample Data Loading

    private func loadSampleLessons() {
        // Use Supabase integration instead of hardcoded arrays
        print("🔄 Loading lessons from Supabase with local caching...")

        // Since this is already called from an async context, call directly
        Task { @MainActor in
            await loadLessonsFromSupabase()
        }
    }

    private func loadLessonsFromSupabase() async {
        // Load all levels from Supabase instead of just A1
        print("🔄 Loading all CEFR levels from Supabase...")

        for level in CEFRLevel.allCases {
            await loadLessonsForLevel(level, bypassCache: false)
        }

        print("✅ Completed loading all CEFR levels from Supabase")
    }

    @MainActor
    private func loadLessonsForLevel(_ level: CEFRLevel, bypassCache: Bool = false) async {
        // Prevent concurrent fetches for the same level
        guard !loadingLevels.contains(level) else {
            print("⏳ Already loading lessons for level \(level.rawValue), skipping duplicate request")
            return
        }

        loadingLevels.insert(level)
        defer { loadingLevels.remove(level) }

        // Check local cache first (unless bypassing)
        if !bypassCache && LocalCacheService.shared.isCacheValid(for: level.rawValue) {
            let cachedLessons = LocalCacheService.shared.getCachedLessons(for: level.rawValue)
            if !cachedLessons.isEmpty {
                // Convert Supabase lessons to legacy format for compatibility
                let legacyLessons = cachedLessons.map { $0.toLegacyTamilLesson() }
                await MainActor.run {
                    cefrLessons[level] = legacyLessons
                }
                print("✅ Loaded \(cachedLessons.count) lessons from cache for level \(level.rawValue)")
                return
            }
        }

        if bypassCache {
            print("🚫 Bypassing cache for level \(level.rawValue), fetching fresh from Supabase")
        }

        // Fetch from Supabase if cache is invalid or empty
        await SupabaseContentService.shared.fetchLessons(for: level)

        if let error = SupabaseContentService.shared.errorMessage {
            print("❌ Error loading lessons from Supabase: \(error)")
            // Don't override fallback lessons that are already set
            print("ℹ️ Keeping existing fallback lessons for level \(level.rawValue)")
        } else {
            let supabaseLessons = SupabaseContentService.shared.lessons

            if !supabaseLessons.isEmpty {
                // Cache the lessons on main actor
                await LocalCacheService.shared.cacheLessons(supabaseLessons, for: level.rawValue)

                // Convert to legacy format for compatibility
                let legacyLessons = supabaseLessons.map { $0.toLegacyTamilLesson() }

                await MainActor.run {
                    cefrLessons[level] = legacyLessons
                }

                print("✅ Loaded \(supabaseLessons.count) lessons from Supabase for level \(level.rawValue)")
            } else {
                print("ℹ️ No lessons found in Supabase for level \(level.rawValue), keeping fallback lessons")
            }
        }
    }

    private func createSampleA2Lessons() -> [TamilLesson] {
        return [
            TamilLesson(
                lessonNumber: 1,
                levelCode: "A2",
                titleEnglish: "Family Members",
                titleTamil: "குடும்ப உறுப்பினர்கள்",
                durationMinutes: 25,
                focus: "Family vocabulary and relationships",
                vocabulary: [],
                conversations: [],
                grammar: [],
                practice: []
            )
        ]
    }

    private func createSampleB1Lessons() -> [TamilLesson] {
        return [
            TamilLesson(
                lessonNumber: 1,
                levelCode: "B1",
                titleEnglish: "Shopping and Markets",
                titleTamil: "கடைகள் மற்றும் சந்தைகள்",
                durationMinutes: 30,
                focus: "Shopping vocabulary and transactions",
                vocabulary: [],
                conversations: [],
                grammar: [],
                practice: []
            )
        ]
    }

    private func createSampleB2Lessons() -> [TamilLesson] {
        return [
            TamilLesson(
                lessonNumber: 1,
                levelCode: "B2",
                titleEnglish: "Tamil Literature",
                titleTamil: "தமிழ் இலக்கியம்",
                durationMinutes: 35,
                focus: "Literary Tamil and classical texts",
                vocabulary: [],
                conversations: [],
                grammar: [],
                practice: []
            )
        ]
    }

    private func createSampleC1Lessons() -> [TamilLesson] {
        return [
            TamilLesson(
                lessonNumber: 1,
                levelCode: "C1",
                titleEnglish: "Advanced Grammar",
                titleTamil: "மேம்பட்ட இலக்கணம்",
                durationMinutes: 40,
                focus: "Complex grammar and syntax",
                vocabulary: [],
                conversations: [],
                grammar: [],
                practice: []
            )
        ]
    }

    private func createSampleC2Lessons() -> [TamilLesson] {
        return [
            TamilLesson(
                lessonNumber: 1,
                levelCode: "C2",
                titleEnglish: "Tamil Mastery",
                titleTamil: "தமிழ் தேர்ச்சி",
                durationMinutes: 45,
                focus: "Native-level fluency and cultural nuances",
                vocabulary: [],
                conversations: [],
                grammar: [],
                practice: []
            )
        ]
    }

    private func createSampleA1Lessons() -> [TamilLesson] {
        return [
            TamilLesson(
                lessonNumber: 1,
                levelCode: "A1",
                titleEnglish: "Basic Greetings",
                titleTamil: "அடிப்படை வணக்கங்கள்",
                durationMinutes: 18,
                focus: "Essential Tamil greetings, introductions, and polite expressions",
                vocabulary: Array(1...25).map { i in
                    let vocabData = [
                        ("Hello", "வணக்கம்", "vaṇakkam", "Interjection", "A formal and respectful greeting"),
                        ("Good morning", "காலை வணக்கம்", "kālai vaṇakkam", "Interjection", "Used until noon"),
                        ("Thank you", "நன்றி", "naṉṟi", "Noun", "Expression of gratitude"),
                        ("Please", "தயவுசெய்து", "tayavuceytu", "Adverb", "Polite request word"),
                        ("Sorry", "மன்னிக்கவும்", "maṉṉikkavum", "Interjection", "Apology expression"),
                        ("Yes", "ஆம்", "ām", "Interjection", "Affirmative response"),
                        ("No", "இல்லை", "illai", "Interjection", "Negative response"),
                        ("Water", "தண்ணீர்", "taṇṇīr", "Noun", "Essential liquid"),
                        ("Food", "உணவு", "uṇavu", "Noun", "Nourishment"),
                        ("House", "வீடு", "vīṭu", "Noun", "Place of residence"),
                        ("Mother", "அம்மா", "ammā", "Noun", "Female parent"),
                        ("Father", "அப்பா", "appā", "Noun", "Male parent"),
                        ("Friend", "நண்பன்", "naṇpaṉ", "Noun", "Close companion"),
                        ("School", "பள்ளி", "paḷḷi", "Noun", "Educational institution"),
                        ("Book", "புத்தகம்", "puttakam", "Noun", "Reading material"),
                        ("Time", "நேரம்", "nēram", "Noun", "Duration or moment"),
                        ("Money", "பணம்", "paṇam", "Noun", "Currency"),
                        ("Work", "வேலை", "vēlai", "Noun", "Job or task"),
                        ("Love", "அன்பு", "aṉpu", "Noun", "Deep affection"),
                        ("Happy", "மகிழ்ச்சி", "makiḻcci", "Adjective", "Feeling of joy"),
                        ("Beautiful", "அழகான", "aḻakāṉa", "Adjective", "Pleasing appearance"),
                        ("Big", "பெரிய", "periya", "Adjective", "Large in size"),
                        ("Small", "சிறிய", "ciṟiya", "Adjective", "Little in size"),
                        ("Good", "நல்ல", "nalla", "Adjective", "Positive quality"),
                        ("Come", "வா", "vā", "Verb", "Move toward")
                    ]
                    // Safe array access to prevent index out of range
                    let index = min(i-1, vocabData.count-1)
                    let (english, tamil, roman, pos, cultural) = vocabData[index]
                    return TamilVocabulary(
                        vocabId: "L1V\(String(format: "%02d", i))",
                        englishWord: english,
                        tamilTranslation: tamil,
                        romanization: roman,
                        ipa: "/\(roman)/",
                        audioWordUrl: "lesson_01_vocab_\(String(format: "%02d", i))_word.mp3",
                        exampleSentenceEnglish: "Example with \(english.lowercased()).",
                        exampleSentenceTamil: "\(tamil) உதாரணம்.",
                        exampleSentenceRomanization: "\(roman) utāraṇam.",
                        audioSentenceUrl: "lesson_01_vocab_\(String(format: "%02d", i))_example.mp3",
                        partOfSpeech: pos,
                        culturalNotes: cultural,
                        relatedTerms: "Related to \(english.lowercased())"
                    )
                },
                conversations: Array(1...10).map { i in
                    let conversationData = [
                        ("Meeting a Friend", "நண்பரைச் சந்தித்தல்", "Two friends meet on the street", "Raj & Priya", "informal"),
                        ("At the Shop", "கடையில்", "Buying something at a local shop", "Customer & Shopkeeper", "formal"),
                        ("Family Introduction", "குடும்ப அறிமுகம்", "Introducing family members", "Parent & Child", "informal"),
                        ("Asking for Directions", "வழி கேட்டல்", "Getting directions to a place", "Tourist & Local", "formal"),
                        ("At School", "பள்ளியில்", "Conversation between students", "Student A & Student B", "informal"),
                        ("Ordering Food", "உணவு ஆர்டர்", "Ordering at a restaurant", "Customer & Waiter", "formal"),
                        ("Phone Conversation", "தொலைபேசி உரையாடல்", "Talking on the phone", "Friends", "informal"),
                        ("At the Doctor", "மருத்துவரிடம்", "Medical consultation", "Patient & Doctor", "formal"),
                        ("Weather Talk", "வானிலை பேச்சு", "Discussing the weather", "Neighbors", "informal"),
                        ("Job Interview", "வேலை நேர்காணல்", "Professional interview", "Interviewer & Candidate", "formal")
                    ]
                    // Safe array access to prevent index out of range
                    let index = min(i-1, conversationData.count-1)
                    let (titleEng, titleTam, context, participants, formality) = conversationData[index]
                    return TamilConversation(
                        conversationId: "L1C\(i)",
                        titleEnglish: titleEng,
                        titleTamil: titleTam,
                        context: context,
                        participants: participants,
                        formality: formality,
                        dialogue: {
                            let speakerComponents = participants.components(separatedBy: " & ")
                            let speaker1 = speakerComponents.first ?? "Speaker 1"
                            let speaker2 = speakerComponents.count > 1 ? speakerComponents[1] : "Speaker 2"

                            return [
                                TamilDialogue(
                                    speaker: speaker1,
                                    lineEnglish: "Hello, how are you?",
                                    lineTamil: "வணக்கம், எப்படி இருக்கீங்க?",
                                    lineRomanization: "Vaṇakkam, eppati irukkīṅka?",
                                    audioUrl: "lesson_01_conv_\(String(format: "%02d", i))_line_01.mp3"
                                ),
                                TamilDialogue(
                                    speaker: speaker2,
                                    lineEnglish: "I'm fine, thank you.",
                                    lineTamil: "நான் நலமாக இருக்கிறேன், நன்றி.",
                                    lineRomanization: "Nāṉ nalamāka irukkiṟēṉ, naṉṟi.",
                                    audioUrl: "lesson_01_conv_\(String(format: "%02d", i))_line_02.mp3"
                                )
                            ]
                        }(),
                        educationalIntegration: "Practice \(titleEng.lowercased()) vocabulary and expressions."
                    )
                },
                grammar: Array(1...5).map { i in
                    let grammarData = [
                        ("Basic Sentence Structure", "அடிப்படை வாக்கிய அமைப்பு", "Tamil follows Subject-Object-Verb (SOV) word order", "தமிழில் வாக்கிய அமைப்பு: எழுவாய்-பொருள்-செயல்", "Unlike English, Tamil places the verb at the end of the sentence.", "Tamil verbs change based on who is doing the action."),
                        ("Personal Pronouns", "பெயர்ச்சொற்கள்", "Tamil pronouns change based on respect level and formality", "தமிழில் பெயர்ச்சொற்கள் மரியாதை மற்றும் முறைமையின் அடிப்படையில் மாறும்", "Tamil has different pronouns for formal and informal situations.", "Always use formal pronouns when meeting someone new."),
                        ("Verb Conjugation", "வினை மாற்றம்", "Tamil verbs change form based on tense, person, and number", "தமிழ் வினைகள் காலம், நபர், எண்ணிக்கையின் அடிப்படையில் மாறும்", "Verbs have different endings for past, present, and future tenses.", "Practice with simple present tense first."),
                        ("Noun Cases", "பெயர்ச்சொல் வேற்றுமைகள்", "Tamil nouns change form based on their grammatical function", "தமிழ் பெயர்ச்சொற்கள் இலக்கண செயல்பாட்டின் அடிப்படையில் மாறும்", "There are eight cases in Tamil grammar.", "Start with nominative and accusative cases."),
                        ("Question Formation", "கேள்வி உருவாக்கம்", "Tamil questions use specific question words and sentence patterns", "தமிழ் கேள்விகள் குறிப்பிட்ட கேள்வி சொற்களைப் பயன்படுத்துகின்றன", "Question words usually come at the beginning of sentences.", "Practice with simple yes/no questions first.")
                    ]
                    let index = min(i-1, grammarData.count-1) // Prevent index out of range
                    let (titleEng, titleTam, ruleEng, ruleTam, explanation, tips) = grammarData[index]
                    return TamilGrammar(
                        grammarId: "L1G\(i)",
                        titleEnglish: titleEng,
                        titleTamil: titleTam,
                        ruleEnglish: ruleEng,
                        ruleTamil: ruleTam,
                        explanation: explanation,
                        examples: [
                            TamilGrammarExample(
                                exampleEnglish: "Example sentence in English",
                                exampleTamil: "தமிழில் உதாரண வாக்கியம்",
                                exampleRomanization: "Tamiḻil utāraṇa vākkiyam",
                                audioUrl: "lesson_01_grammar_\(String(format: "%02d", i))_example_01.mp3",
                                breakdown: "Word-by-word breakdown explanation"
                            ),
                            TamilGrammarExample(
                                exampleEnglish: "Another example sentence",
                                exampleTamil: "மற்றொரு உதாரண வாக்கியம்",
                                exampleRomanization: "Maṟṟoru utāraṇa vākkiyam",
                                audioUrl: "lesson_01_grammar_\(String(format: "%02d", i))_example_02.mp3",
                                breakdown: "Second example breakdown"
                            )
                        ],
                        tips: tips,
                        difficulty: "beginner"
                    )
                },
                practice: Array(1...10).map { i in
                    let practiceData = [
                        ("multiple_choice", "Vocabulary Quiz", "சொல்லகராதி வினாடி வினா", "Choose the correct Tamil translation", "சரியான தமிழ் மொழிபெயர்ப்பைத் தேர்ந்தெடுங்கள்"),
                        ("true_false", "Grammar Check", "இலக்கண சரிபார்ப்பு", "Decide if the statement is true or false", "கூற்று உண்மையா பொய்யா என்று முடிவு செய்யுங்கள்"),
                        ("fill_blank", "Complete the Sentence", "வாக்கியத்தை நிறைவு செய்யுங்கள்", "Fill in the missing word", "விடுபட்ட சொல்லை நிரப்புங்கள்"),
                        ("match_following", "Match Words", "சொற்களை இணைக்கவும்", "Match Tamil words with English meanings", "தமிழ் சொற்களை ஆங்கில அர்த்தங்களுடன் இணைக்கவும்"),
                        ("multiple_choice", "Pronunciation Test", "உச்சரிப்பு சோதனை", "Choose the correct pronunciation", "சரியான உச்சரிப்பைத் தேர்ந்தெடுங்கள்"),
                        ("true_false", "Cultural Facts", "கலாச்சார உண்மைகள்", "True or false about Tamil culture", "தமிழ் கலாச்சாரம் பற்றி உண்மை அல்லது பொய்"),
                        ("fill_blank", "Grammar Practice", "இலக்கண பயிற்சி", "Complete with correct verb form", "சரியான வினை வடிவத்துடன் நிறைவு செய்யுங்கள்"),
                        ("multiple_choice", "Sentence Structure", "வாக்கிய அமைப்பு", "Choose the correctly ordered sentence", "சரியாக வரிசைப்படுத்தப்பட்ட வாக்கியத்தைத் தேர்ந்தெடுங்கள்"),
                        ("true_false", "Word Usage", "சொல் பயன்பாடு", "Is this word used correctly?", "இந்த சொல் சரியாகப் பயன்படுத்தப்பட்டுள்ளதா?"),
                        ("match_following", "Family Terms", "குடும்ப சொற்கள்", "Match family relationships", "குடும்ப உறவுகளை இணைக்கவும்")
                    ]
                    // Safe array access to prevent index out of range
                    let index = min(i-1, practiceData.count-1)
                    let (type, titleEng, titleTam, instructEng, instructTam) = practiceData[index]
                    return TamilPractice(
                        practiceId: "L1P\(i)",
                        type: type,
                        titleEnglish: titleEng,
                        titleTamil: titleTam,
                        instructionsEnglish: instructEng,
                        instructionsTamil: instructTam,
                        audioInstructionsUrl: "lesson_01_practice_\(String(format: "%02d", i))_instructions.mp3",
                        exercises: [createPracticeExercise(type: type, practiceIndex: i, exerciseIndex: 1)],
                        difficulty: "beginner"
                    )
                }
            )
        ]
    }

    private func createPracticeExercise(type: String, practiceIndex: Int, exerciseIndex: Int) -> TamilPracticeExercise {
        switch type {
        case "multiple_choice":
            return TamilPracticeExercise(
                exerciseId: "L1P\(practiceIndex)E\(exerciseIndex)",
                exerciseType: "multiple_choice",
                question: "What is the Tamil word for 'Hello'?",
                questionTamil: "'Hello' என்பதற்கான தமிழ் சொல் என்ன?",
                options: ["வணக்கம்", "நன்றி", "மன்னிக்கவும்", "போங்க"],
                optionsTamil: ["வணக்கம்", "நன்றி", "மன்னிக்கவும்", "போங்க"],
                correctAnswer: 0,
                correctAnswers: nil,
                blanks: nil,
                matchPairs: nil,
                explanation: "வணக்கம் is the formal Tamil greeting meaning 'Hello'",
                audioQuestionUrl: "lesson_01_practice_\(String(format: "%02d", practiceIndex))_exercise_\(String(format: "%02d", exerciseIndex)).mp3",
                points: 10
            )
        case "true_false":
            return TamilPracticeExercise(
                exerciseId: "L1P\(practiceIndex)E\(exerciseIndex)",
                exerciseType: "true_false",
                question: "Tamil follows Subject-Verb-Object word order.",
                questionTamil: "தமிழ் எழுவாய்-செயல்-பொருள் வரிசையைப் பின்பற்றுகிறது.",
                options: ["True", "False"],
                optionsTamil: ["உண்மை", "பொய்"],
                correctAnswer: 1,
                correctAnswers: nil,
                blanks: nil,
                matchPairs: nil,
                explanation: "Tamil follows Subject-Object-Verb (SOV) order, not SVO",
                audioQuestionUrl: "lesson_01_practice_\(String(format: "%02d", practiceIndex))_exercise_\(String(format: "%02d", exerciseIndex)).mp3",
                points: 15
            )
        case "fill_blank":
            return TamilPracticeExercise(
                exerciseId: "L1P\(practiceIndex)E\(exerciseIndex)",
                exerciseType: "fill_blank",
                question: "நான் _____ சாப்பிடுகிறேன். (I eat rice)",
                questionTamil: "நான் _____ சாப்பிடுகிறேன். (நான் சாதம் சாப்பிடுகிறேன்)",
                options: ["சாதம்", "தண்ணீர்", "புத்தகம்", "வீடு"],
                optionsTamil: ["சாதம்", "தண்ணீர்", "புத்தகம்", "வீடு"],
                correctAnswer: 0,
                correctAnswers: nil,
                blanks: ["சாதம்"],
                matchPairs: nil,
                explanation: "சாதம் means 'rice' in Tamil",
                audioQuestionUrl: "lesson_01_practice_\(String(format: "%02d", practiceIndex))_exercise_\(String(format: "%02d", exerciseIndex)).mp3",
                points: 12
            )
        case "match_following":
            return TamilPracticeExercise(
                exerciseId: "L1P\(practiceIndex)E\(exerciseIndex)",
                exerciseType: "match_following",
                question: "Match the Tamil words with their English meanings",
                questionTamil: "தமிழ் சொற்களை அவற்றின் ஆங்கில அர்த்தங்களுடன் இணைக்கவும்",
                options: [],
                optionsTamil: [],
                correctAnswer: 0,
                correctAnswers: [0, 1, 2],
                blanks: nil,
                matchPairs: [
                    MatchPair(left: "வணக்கம்", leftTamil: "வணக்கம்", right: "Hello", rightTamil: "Hello"),
                    MatchPair(left: "நன்றி", leftTamil: "நன்றி", right: "Thank you", rightTamil: "Thank you"),
                    MatchPair(left: "தண்ணீர்", leftTamil: "தண்ணீர்", right: "Water", rightTamil: "Water")
                ],
                explanation: "These are basic Tamil words with their English translations",
                audioQuestionUrl: "lesson_01_practice_\(String(format: "%02d", practiceIndex))_exercise_\(String(format: "%02d", exerciseIndex)).mp3",
                points: 20
            )
        default:
            return TamilPracticeExercise(
                exerciseId: "L1P\(practiceIndex)E\(exerciseIndex)",
                exerciseType: "multiple_choice",
                question: "Default question",
                questionTamil: "இயல்புநிலை கேள்வி",
                options: ["Option 1", "Option 2"],
                optionsTamil: ["விருப்பம் 1", "விருப்பம் 2"],
                correctAnswer: 0,
                correctAnswers: nil,
                blanks: nil,
                matchPairs: nil,
                explanation: "Default explanation",
                audioQuestionUrl: "default.mp3",
                points: 5
            )
        }
    }



    // MARK: - Content Access

    func getLessonsForLevel(_ level: CEFRLevel) -> [TamilLesson] {
        // Return cached lessons if available
        if let lessons = cefrLessons[level], !lessons.isEmpty {
            return lessons
        }

        // Create immediate fallback lessons to prevent crashes and empty states
        let fallbackLessons: [TamilLesson]
        switch level {
        case .a1:
            fallbackLessons = createSampleA1Lessons()
        case .a2:
            fallbackLessons = createSampleA2Lessons()
        case .b1:
            fallbackLessons = createSampleB1Lessons()
        case .b2:
            fallbackLessons = createSampleB2Lessons()
        case .c1:
            fallbackLessons = createSampleC1Lessons()
        case .c2:
            fallbackLessons = createSampleC2Lessons()
        }

        // Cache the fallback lessons immediately
        Task { @MainActor in
            cefrLessons[level] = fallbackLessons
        }

        // Load real lessons asynchronously in the background (without blocking UI)
        Task { @MainActor [weak self] in
            await self?.loadLessonsForLevel(level)
        }

        return fallbackLessons
    }

    private func loadLessonsOnDemand(for level: CEFRLevel) {
        // This method is now unused but kept for compatibility
        print("🔄 Loading lessons on demand for level: \(level.rawValue)")
    }

    // MARK: - Debug and Refresh Methods

    /// Force refresh all lessons from Supabase (useful for debugging)
    func refreshAllLessonsFromSupabase() async {
        print("🔄 Force refreshing all lessons from Supabase...")

        // First, check what's actually in Supabase
        await SupabaseContentService.shared.debugLessonCounts()

        // Clear ALL caches first - this is crucial!
        print("🗑️ Clearing all local caches...")
        await LocalCacheService.shared.forceInvalidateAllCaches() // Force clear everything

        // Clear in-memory cache
        await MainActor.run {
            cefrLessons.removeAll()
            loadingLevels.removeAll()
        }

        print("✅ All caches cleared, loading fresh data from Supabase...")

        // Load all levels fresh from Supabase with cache bypass
        for level in CEFRLevel.allCases {
            await loadLessonsForLevel(level, bypassCache: true)
        }
    }

    /// Debug method to show lesson counts for all levels
    func debugLessonCounts() {
        print("📊 Current lesson counts by level:")
        for level in CEFRLevel.allCases {
            let count = cefrLessons[level]?.count ?? 0
            let firstFew = cefrLessons[level]?.prefix(3).map { $0.titleEnglish }.joined(separator: ", ") ?? "None"
            print("  \(level.rawValue): \(count) lessons - [\(firstFew)]")
        }

        // Also show cache info
        let cacheInfo = LocalCacheService.shared.getCacheInfo()
        print("💾 \(cacheInfo)")
    }

    /// Clear cache for a specific level and reload
    func refreshLevel(_ level: CEFRLevel) async {
        print("🔄 Refreshing level \(level.rawValue)...")

        // Clear cache for this level
        await LocalCacheService.shared.clearCache(for: level.rawValue)

        // Clear in-memory cache for this level
        await MainActor.run {
            cefrLessons[level] = nil
            loadingLevels.remove(level)
        }

        // Reload this level with cache bypass
        await loadLessonsForLevel(level, bypassCache: true)

        print("✅ Level \(level.rawValue) refreshed")
    }
    
    func getLesson(level: CEFRLevel, lessonNumber: Int) -> TamilLesson? {
        return cefrLessons[level]?.first { $0.lessonNumber == lessonNumber }
    }
    
    func getNextLesson() -> TamilLesson? {
        let currentLessons = getLessonsForLevel(currentLevel)
        
        if let currentLesson = currentLesson {
            // Find next lesson in current level
            if let nextLesson = currentLessons.first(where: { $0.lessonNumber == currentLesson.lessonNumber + 1 }) {
                return nextLesson
            }
            
            // Move to next level if available
            if let nextLevel = getNextLevel() {
                return getLessonsForLevel(nextLevel).first
            }
        } else {
            // Return first lesson of current level
            return currentLessons.first
        }
        
        return nil
    }
    
    func getNextLevel() -> CEFRLevel? {
        let levels = CEFRLevel.allCases
        guard let currentIndex = levels.firstIndex(of: currentLevel),
              currentIndex < levels.count - 1 else {
            return nil
        }
        return levels[currentIndex + 1]
    }

    // MARK: - Progressive Learning Integration

    func getRecommendedLessons() -> [TamilLesson] {
        var recommendations: [TamilLesson] = []

        // Get next recommended lesson
        if let nextLesson = progressService.getNextRecommendedLesson() {
            recommendations.append(nextLesson)
        }

        // Get review lessons
        let reviewLessons = progressService.getReviewLessons()
        recommendations.append(contentsOf: reviewLessons)

        return recommendations
    }

    func getLessonsForCurrentUserLevel() -> [TamilLesson] {
        return getLessonsForLevel(progressService.currentUserLevel)
    }

    func getUnlockedLessons() -> [TamilLesson] {
        return progressService.learningPath
            .filter { $0.isUnlocked }
            .map { $0.lesson }
    }

    func isLessonUnlocked(_ lesson: TamilLesson) -> Bool {
        // Ensure learning path is generated
        if progressService.learningPath.isEmpty {
            progressService.generateLearningPath()
        }

        let lessonId = "\(lesson.levelCode)-L\(lesson.lessonNumber)"

        // Check learning path first
        if let pathNode = progressService.learningPath.first(where: { $0.id == lessonId }) {
            return pathNode.isUnlocked
        }

        // Fallback: All lessons are unlocked for now (as per ProgressiveLearningService)
        return true
    }

    func isLessonCompleted(_ lesson: TamilLesson) -> Bool {
        let lessonId = "\(lesson.levelCode)-L\(lesson.lessonNumber)"
        return progressService.completedLessons.contains(lessonId)
    }
    
    // MARK: - Progress Management

    func markLessonCompleted(_ lesson: TamilLesson, studyTime: TimeInterval = 0) async {
        guard var progress = userProgress else { return }

        // Update completed lessons count
        progress = LearningProgress(
            completedLessons: progress.completedLessons + 1,
            totalLessons: progress.totalLessons,
            currentStreak: progress.currentStreak + 1,
            weeklyGoal: progress.weeklyGoal,
            weeklyProgress: progress.weeklyProgress + 1,
            lastStudyDate: Date()
        )
        Task { @MainActor in
            userProgress = progress
            saveUserProgress()
        }

        // Update progressive learning service
        await progressService.markLessonCompleted(lesson, studyTime: studyTime)
    }

    func markVocabularyMastered(_ vocabulary: TamilVocabulary) async {
        guard userProgress != nil else { return }

        // For now, just update progressive learning service
        // Widget progress doesn't track individual vocabulary
        await progressService.markVocabularyMastered(vocabulary)
    }
    
    func updateStudyTime(_ minutes: Int) {
        guard let progress = userProgress else { return }
        // Widget progress doesn't track study time
        // Just update last study date
        let updatedProgress = LearningProgress(
            completedLessons: progress.completedLessons,
            totalLessons: progress.totalLessons,
            currentStreak: progress.currentStreak,
            weeklyGoal: progress.weeklyGoal,
            weeklyProgress: progress.weeklyProgress,
            lastStudyDate: Date()
        )
        Task { @MainActor in
            userProgress = updatedProgress
            saveUserProgress()
        }
    }
    
    private func saveUserProgress() {
        // Save to UserDefaults or Core Data
        if let progress = userProgress,
           let data = try? JSONEncoder().encode(progress) {
            UserDefaults.standard.set(data, forKey: "tamil_learning_progress")
        }
    }
    
    // MARK: - Content Search
    
    func searchContent(_ query: String) -> [TamilLesson] {
        let allLessons = cefrLessons.values.flatMap { $0 }
        return allLessons.filter { lesson in
            lesson.titleEnglish.localizedCaseInsensitiveContains(query) ||
            lesson.titleTamil.localizedCaseInsensitiveContains(query) ||
            lesson.focus.localizedCaseInsensitiveContains(query)
        }
    }
    
    func searchVocabulary(_ query: String) -> [TamilVocabulary] {
        let allVocabulary = cefrLessons.values.flatMap { $0 }.flatMap { $0.vocabulary }
        return allVocabulary.filter { vocab in
            vocab.englishWord.localizedCaseInsensitiveContains(query) ||
            vocab.tamilTranslation.localizedCaseInsensitiveContains(query) ||
            vocab.romanization.localizedCaseInsensitiveContains(query)
        }
    }
    
    // MARK: - Audio Integration
    
    func playVocabularyAudio(_ vocabulary: TamilVocabulary) {
        audioManager.playAudio(filename: vocabulary.audioWordUrl)
    }
    
    func playConversationAudio(_ dialogue: TamilDialogue) {
        audioManager.playAudio(filename: dialogue.audioUrl)
    }
    
    // MARK: - Content Statistics
    
    func getProgressStatistics() -> (completed: Int, total: Int, percentage: Double) {
        guard let progress = userProgress else {
            return (0, 0, 0.0)
        }

        let completed = progress.completedLessons
        let total = progress.totalLessons
        let percentage = total > 0 ? Double(completed) / Double(total) * 100 : 0.0

        return (completed, total, percentage)
    }
    
    func getVocabularyStatistics() -> (mastered: Int, total: Int, percentage: Double) {
        // Widget progress doesn't track vocabulary separately
        // Return estimated based on completed lessons
        guard let progress = userProgress else {
            return (0, 0, 0.0)
        }

        let totalVocabulary = contentMetadata?.totalVocabulary ?? 0
        let estimatedMastered = progress.completedLessons * 5 // Estimate 5 vocab per lesson
        let percentage = totalVocabulary > 0 ? Double(estimatedMastered) / Double(totalVocabulary) * 100 : 0.0

        return (estimatedMastered, totalVocabulary, percentage)
    }
}

// MARK: - Extensions for Backward Compatibility

extension TamilSupabaseLesson {
    /// Convert to legacy TamilLesson format for existing UI components
    func toLegacyTamilLesson() -> TamilLesson {
        return TamilLesson(
            lessonNumber: lessonNumber,
            levelCode: levelCode,
            titleEnglish: titleEnglish,
            titleTamil: titleTamil,
            durationMinutes: durationMinutes,
            focus: focus ?? "",
            vocabulary: [], // Will be loaded separately when needed
            conversations: [], // Will be loaded separately when needed
            grammar: [], // Will be loaded separately when needed
            practice: [] // Will be loaded separately when needed
        )
    }
}
