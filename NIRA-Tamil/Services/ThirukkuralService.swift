import Foundation
import Combine

// MARK: - Thirukkural Insight Model (using Thirukkural from TamilCulturalModels)

struct ThirukkuralInsight: Codable {
    let kural: Thirukkural
    let dailyReflection: String
    let practicalApplication: String
    let historicalContext: String
}

// MARK: - Thirukkural Service

@MainActor
class ThirukkuralService: ObservableObject {
    static let shared = ThirukkuralService()
    
    @Published var todayKural: Thirukkural?
    @Published var todayInsight: ThirukkuralInsight?
    @Published var favoriteKurals: [Thirukkural] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private var cancellables = Set<AnyCancellable>()
    private let calendar = Calendar.current
    
    // Enhanced Thirukkural database with comprehensive mock data
    private let thirukkuralDatabase: [Thirukkural] = ThirukkuralService.enhancedThirukkuralDatabase
    
    private init() {
        loadFavoriteKurals()
    }
    
    // MARK: - Public Methods
    
    func loadTodayKural() async {
        isLoading = true
        
        let dayOfYear = calendar.ordinality(of: .day, in: .year, for: Date()) ?? 1
        let kuralIndex = (dayOfYear - 1) % thirukkuralDatabase.count
        let selectedKural = thirukkuralDatabase[kuralIndex]

        todayKural = selectedKural
        todayInsight = generateInsight(for: selectedKural)
        
        isLoading = false
    }
    
    func getRandomKural() -> Thirukkural {
        return thirukkuralDatabase.randomElement() ?? thirukkuralDatabase[0]
    }
    
    func toggleFavorite(_ kural: Thirukkural) {
        if favoriteKurals.contains(where: { $0.id == kural.id }) {
            favoriteKurals.removeAll { $0.id == kural.id }
        } else {
            favoriteKurals.append(kural)
        }
        saveFavoriteKurals()
    }
    
    func isFavorite(_ kural: Thirukkural) -> Bool {
        return favoriteKurals.contains { $0.id == kural.id }
    }
    
    func generateInsight(for kural: Thirukkural) -> ThirukkuralInsight {
        return ThirukkuralInsight(
            kural: kural,
            dailyReflection: generateDailyReflection(for: kural),
            practicalApplication: generatePracticalApplication(for: kural),
            historicalContext: "This verse represents the timeless wisdom of Thiruvalluvar, written over 2000 years ago."
        )
    }
    
    // MARK: - Private Methods
    
    private func generateDailyReflection(for kural: Thirukkural) -> String {
        let reflections = [
            "Today, reflect on how this ancient wisdom applies to your current life situation.",
            "Consider how you can embody this teaching in your daily interactions.",
            "Think about moments when this wisdom could guide your decisions today.",
            "Contemplate how this verse can bring more meaning to your day."
        ]
        return reflections.randomElement() ?? reflections[0]
    }
    
    private func generatePracticalApplication(for kural: Thirukkural) -> String {
        if kural.chapterEnglish.contains("Learning") {
            return "Apply this by dedicating time today to learn something new and then practicing what you've learned."
        } else if kural.chapterEnglish.contains("Love") {
            return "Show genuine care and compassion to someone in your life today."
        } else {
            return "Find a way to incorporate this wisdom into one specific action or decision today."
        }
    }
    
    // MARK: - Persistence
    
    private func saveFavoriteKurals() {
        if let data = try? JSONEncoder().encode(favoriteKurals) {
            UserDefaults.standard.set(data, forKey: "favorite_kurals")
        }
    }
    
    private func loadFavoriteKurals() {
        if let data = UserDefaults.standard.data(forKey: "favorite_kurals"),
           let kurals = try? JSONDecoder().decode([Thirukkural].self, from: data) {
            favoriteKurals = kurals
        }
    }
}
