//
//  PerformanceOptimizationService.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import Foundation
import SwiftUI
import Combine
import Network

// MARK: - Performance Optimization Service with Intelligent Caching

@MainActor
class PerformanceOptimizationService: ObservableObject {
    static let shared = PerformanceOptimizationService()
    
    // MARK: - Published Properties
    @Published var performanceMetrics: PerformanceMetrics = PerformanceMetrics()
    @Published var cacheStatistics: CacheStatistics = CacheStatistics()
    @Published var networkStatus: NetworkStatus = NetworkStatus()
    @Published var memoryUsage: MemoryUsage = MemoryUsage()
    @Published var isOptimizing = false
    @Published var optimizationRecommendations: [OptimizationRecommendation] = []
    
    // MARK: - Private Properties
    private var cacheManager: CacheManager
    private var networkMonitor: NWPathMonitor
    private var performanceTimer: Timer?
    private var memoryTimer: Timer?
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    
    // Cache configurations
    private let maxCacheSize: Int = 100 * 1024 * 1024 // 100MB
    private let maxCacheAge: TimeInterval = 24 * 60 * 60 // 24 hours
    private let preloadThreshold: Double = 0.8 // Preload when 80% through content
    
    // Performance thresholds
    private let responseTimeThreshold: TimeInterval = 2.0
    private let memoryWarningThreshold: Double = 0.8 // 80% of available memory
    private let cacheHitRateThreshold: Double = 0.7 // 70% cache hit rate
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        self.cacheManager = CacheManager(maxSize: maxCacheSize, maxAge: maxCacheAge)
        self.networkMonitor = NWPathMonitor()
        
        setupNetworkMonitoring()
        startPerformanceMonitoring()
        setupMemoryMonitoring()
    }
    
    // MARK: - Public Methods
    
    // MARK: - Caching Operations
    
    func cacheContent<T: Codable>(
        _ content: T,
        forKey key: String,
        category: CacheCategory = .general,
        priority: CachePriority = .normal
    ) {
        let cacheItem = CacheItem(
            key: key,
            data: try? JSONEncoder().encode(content),
            category: category,
            priority: priority,
            createdAt: Date(),
            lastAccessed: Date(),
            accessCount: 0,
            size: MemoryLayout<T>.size
        )
        
        cacheManager.store(cacheItem)
        updateCacheStatistics()
    }
    
    func getCachedContent<T: Codable>(
        forKey key: String,
        type: T.Type
    ) -> T? {
        guard let cacheItem = cacheManager.retrieve(key: key),
              let data = cacheItem.data else {
            recordCacheMiss(for: key)
            return nil
        }
        
        recordCacheHit(for: key)
        
        do {
            let content = try JSONDecoder().decode(type, from: data)
            return content
        } catch {
            print("❌ Failed to decode cached content: \(error)")
            cacheManager.remove(key: key) // Remove corrupted cache
            return nil
        }
    }
    
    func preloadContent(for keys: [String], priority: CachePriority = .high) async {
        isOptimizing = true
        defer { isOptimizing = false }
        
        for key in keys {
            // Check if content is already cached
            if cacheManager.retrieve(key: key) == nil {
                // Preload content based on key pattern
                await preloadContentForKey(key, priority: priority)
            }
        }
    }
    
    func optimizeCache() async {
        isOptimizing = true
        defer { isOptimizing = false }
        
        // Remove expired items
        cacheManager.removeExpiredItems()
        
        // Remove least recently used items if cache is full
        if cacheManager.currentSize > Int(Double(maxCacheSize) * 0.9) {
            cacheManager.evictLRUItems(targetSize: Int(Double(maxCacheSize) * 0.7))
        }
        
        // Compress frequently accessed items
        await compressFrequentlyAccessedItems()
        
        updateCacheStatistics()
        generateOptimizationRecommendations()
    }
    
    func clearCache(category: CacheCategory? = nil) {
        if let category = category {
            cacheManager.removeItems(in: category)
        } else {
            cacheManager.clearAll()
        }
        updateCacheStatistics()
    }
    
    // MARK: - Response Optimization
    
    func optimizeAPIResponse<T: Codable>(
        request: @escaping () async throws -> T,
        cacheKey: String,
        forceRefresh: Bool = false
    ) async throws -> T {
        
        let startTime = Date()
        
        // Check cache first (unless force refresh)
        if !forceRefresh,
           let cachedContent: T = getCachedContent(forKey: cacheKey, type: T.self) {
            recordResponseTime(Date().timeIntervalSince(startTime))
            return cachedContent
        }
        
        // Make API request with optimization
        let response = try await executeOptimizedRequest(request)
        
        // Cache the response
        cacheContent(response, forKey: cacheKey, category: .apiResponse, priority: .normal)
        
        recordResponseTime(Date().timeIntervalSince(startTime))
        return response
    }
    
    func batchOptimizeRequests<T: Codable>(
        requests: [(key: String, request: () async throws -> T)],
        maxConcurrency: Int = 3
    ) async -> [String: Result<T, Error>] {
        
        var results: [String: Result<T, Error>] = [:]
        
        // Process requests in batches to avoid overwhelming the system
        for batch in requests.chunked(into: maxConcurrency) {
            await withTaskGroup(of: (String, Result<T, Error>).self) { group in
                for (key, request) in batch {
                    group.addTask {
                        do {
                            let result = try await self.executeOptimizedRequest(request)
                            return (key, .success(result))
                        } catch {
                            return (key, .failure(error))
                        }
                    }
                }
                
                for await (key, result) in group {
                    results[key] = result
                }
            }
        }
        
        return results
    }
    
    // MARK: - Memory Management
    
    func optimizeMemoryUsage() async {
        isOptimizing = true
        defer { isOptimizing = false }
        
        // Clear low-priority cache items
        cacheManager.removeItems(withPriority: .low)
        
        // Compress images and media
        await compressMediaContent()
        
        // Release unused resources
        releaseUnusedResources()
        
        updateMemoryUsage()
        generateOptimizationRecommendations()
    }
    
    func handleMemoryWarning() {
        // Emergency memory cleanup
        cacheManager.removeItems(withPriority: .low)
        cacheManager.removeItems(withPriority: .normal, olderThan: Date().addingTimeInterval(-3600)) // 1 hour
        
        // Force garbage collection
        autoreleasepool {
            // Trigger memory cleanup
        }
        
        updateMemoryUsage()
    }
    
    // MARK: - Performance Monitoring
    
    func startPerformanceMonitoring() {
        performanceTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updatePerformanceMetrics()
            }
        }
    }
    
    func stopPerformanceMonitoring() {
        performanceTimer?.invalidate()
        performanceTimer = nil
        memoryTimer?.invalidate()
        memoryTimer = nil
    }
    
    func getPerformanceReport() -> PerformanceReport {
        return PerformanceReport(
            metrics: performanceMetrics,
            cacheStats: cacheStatistics,
            memoryUsage: memoryUsage,
            networkStatus: networkStatus,
            recommendations: optimizationRecommendations,
            generatedAt: Date()
        )
    }
    
    // MARK: - Network Optimization
    
    func optimizeForNetworkCondition(_ condition: NetworkCondition) {
        switch condition {
        case .excellent:
            // Enable high-quality content, aggressive preloading
            enableHighQualityMode()
            
        case .good:
            // Standard quality, moderate preloading
            enableStandardMode()
            
        case .poor:
            // Low quality, minimal preloading, aggressive caching
            enableLowBandwidthMode()
            
        case .offline:
            // Offline mode, cache-only
            enableOfflineMode()
            
        case .unknown:
            // Default to standard mode for unknown conditions
            enableStandardMode()
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                self?.updateNetworkStatus(path)
            }
        }
        networkMonitor.start(queue: networkQueue)
    }
    
    private func updateNetworkStatus(_ path: NWPath) {
        let condition = determineNetworkCondition(path)
        let newStatus = NetworkStatus(
            isConnected: path.status == .satisfied,
            condition: condition,
            connectionType: getConnectionType(path),
            lastUpdated: Date()
        )
        
        Task { @MainActor in
            self.networkStatus = newStatus
            // Optimize based on network condition
            self.optimizeForNetworkCondition(condition)
        }
    }
    
    private func determineNetworkCondition(_ path: NWPath) -> NetworkCondition {
        guard path.status == .satisfied else { return .offline }
        
        if path.isExpensive {
            return .poor
        } else if path.usesInterfaceType(.wifi) {
            return .excellent
        } else if path.usesInterfaceType(.cellular) {
            return .good
        } else {
            return .good
        }
    }
    
    private func getConnectionType(_ path: NWPath) -> ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .other
        }
    }
    
    private func setupMemoryMonitoring() {
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMemoryUsage()
            }
        }
        
        // Listen for memory warnings
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleMemoryWarning()
            }
        }
    }
    
    private func updatePerformanceMetrics() {
        var currentMetrics = PerformanceMetrics()
        currentMetrics.averageResponseTime = calculateAverageResponseTime()
        currentMetrics.cacheHitRate = calculateCacheHitRate()
        currentMetrics.memoryUsagePercentage = calculateMemoryUsagePercentage()
        currentMetrics.networkLatency = measureNetworkLatency()
        currentMetrics.appLaunchTime = measureAppLaunchTime()
        currentMetrics.frameRate = measureFrameRate()
        currentMetrics.lastUpdated = Date()
        
        performanceMetrics = currentMetrics
        
        // Check for performance issues
        checkPerformanceThresholds()
    }
    
    private func updateCacheStatistics() {
        cacheStatistics = CacheStatistics(
            totalItems: cacheManager.itemCount,
            totalSize: cacheManager.currentSize,
            hitCount: cacheManager.hitCount,
            missCount: cacheManager.missCount,
            hitRate: cacheManager.hitRate,
            lastCleanup: cacheManager.lastCleanup,
            categorySizes: cacheManager.getCategorySizes()
        )
    }
    
    private func updateMemoryUsage() {
        let usage = MemoryUsage(
            used: getCurrentMemoryUsage(),
            available: getAvailableMemory(),
            peak: getPeakMemoryUsage(),
            lastUpdated: Date()
        )
        
        memoryUsage = usage
        
        // Check memory warning threshold
        if usage.usagePercentage > memoryWarningThreshold {
            Task { @MainActor in
                self.handleMemoryWarning()
            }
        }
    }
    
    private func executeOptimizedRequest<T>(_ request: @escaping () async throws -> T) async throws -> T {
        // Add request optimization logic here
        // - Retry logic
        // - Timeout handling
        // - Request deduplication
        
        return try await withTimeout(seconds: responseTimeThreshold * 2) {
            try await request()
        }
    }
    
    private func preloadContentForKey(_ key: String, priority: CachePriority) async {
        // Implement preloading logic based on key patterns
        // This would be specific to your app's content structure
        print("🔄 Preloading content for key: \(key)")
    }
    
    private func compressFrequentlyAccessedItems() async {
        // Implement compression for frequently accessed cache items
        let frequentItems = cacheManager.getFrequentlyAccessedItems()
        
        for item in frequentItems {
            if let compressedData = compressData(item.data) {
                var compressedItem = item
                compressedItem.data = compressedData
                compressedItem.isCompressed = true
                cacheManager.store(compressedItem)
            }
        }
    }
    
    private func compressMediaContent() async {
        // Implement media compression logic
        print("🗜️ Compressing media content...")
    }
    
    private func releaseUnusedResources() {
        // Release any unused resources
        print("🧹 Releasing unused resources...")
    }
    
    private func compressData(_ data: Data?) -> Data? {
        guard let data = data else { return nil }
        
        // Implement data compression
        return try? (data as NSData).compressed(using: .lzfse) as Data
    }
    
    private func recordCacheHit(for key: String) {
        cacheManager.recordHit(for: key)
    }
    
    private func recordCacheMiss(for key: String) {
        cacheManager.recordMiss(for: key)
    }
    
    private func recordResponseTime(_ time: TimeInterval) {
        performanceMetrics.recordResponseTime(time)
    }
    
    private func checkPerformanceThresholds() {
        var recommendations: [OptimizationRecommendation] = []
        
        if performanceMetrics.averageResponseTime > responseTimeThreshold {
            recommendations.append(OptimizationRecommendation(
                type: .responseTime,
                severity: .high,
                description: "Response times are above threshold",
                suggestion: "Consider optimizing API calls or increasing cache usage"
            ))
        }
        
        if cacheStatistics.hitRate < cacheHitRateThreshold {
            recommendations.append(OptimizationRecommendation(
                type: .cacheEfficiency,
                severity: .medium,
                description: "Cache hit rate is below optimal",
                suggestion: "Review caching strategy and increase cache retention"
            ))
        }
        
        if memoryUsage.usagePercentage > memoryWarningThreshold {
            recommendations.append(OptimizationRecommendation(
                type: .memoryUsage,
                severity: .high,
                description: "Memory usage is high",
                suggestion: "Clear unnecessary cache items and optimize memory usage"
            ))
        }
        
        optimizationRecommendations = recommendations
    }
    
    private func generateOptimizationRecommendations() {
        // Generate additional recommendations based on current state
        checkPerformanceThresholds()
    }
    
    // MARK: - Network Mode Optimizations
    
    private func enableHighQualityMode() {
        // Enable high-quality content and aggressive preloading
        print("📶 Enabling high-quality mode")
    }
    
    private func enableStandardMode() {
        // Standard quality and moderate preloading
        print("📶 Enabling standard mode")
    }
    
    private func enableLowBandwidthMode() {
        // Low quality, minimal preloading, aggressive caching
        print("📶 Enabling low-bandwidth mode")
    }
    
    private func enableOfflineMode() {
        // Cache-only mode
        print("📶 Enabling offline mode")
    }
    
    // MARK: - Performance Measurement Helpers
    
    private func calculateAverageResponseTime() -> TimeInterval {
        // Calculate from stored response times
        return performanceMetrics.averageResponseTime
    }
    
    private func calculateCacheHitRate() -> Double {
        return cacheStatistics.hitRate
    }
    
    private func calculateMemoryUsagePercentage() -> Double {
        return memoryUsage.usagePercentage
    }
    
    private func measureNetworkLatency() -> TimeInterval {
        // Implement network latency measurement
        return 0.1 // Placeholder
    }
    
    private func measureAppLaunchTime() -> TimeInterval {
        // Measure app launch time
        return 2.0 // Placeholder
    }
    
    private func measureFrameRate() -> Double {
        // Measure current frame rate
        return 60.0 // Placeholder
    }
    
    private func getCurrentMemoryUsage() -> Int {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Int(info.resident_size)
        } else {
            return 0
        }
    }
    
    private func getAvailableMemory() -> Int {
        // Get available memory
        return Int(ProcessInfo.processInfo.physicalMemory)
    }
    
    private func getPeakMemoryUsage() -> Int {
        // Get peak memory usage
        return memoryUsage.peak
    }
}

// MARK: - Supporting Models

struct PerformanceMetrics {
    var averageResponseTime: TimeInterval = 0.0
    var cacheHitRate: Double = 0.0
    var memoryUsagePercentage: Double = 0.0
    var networkLatency: TimeInterval = 0.0
    var appLaunchTime: TimeInterval = 0.0
    var frameRate: Double = 60.0
    var lastUpdated: Date = Date()
    
    private var responseTimes: [TimeInterval] = []
    
    mutating func recordResponseTime(_ time: TimeInterval) {
        responseTimes.append(time)
        if responseTimes.count > 100 {
            responseTimes.removeFirst()
        }
        averageResponseTime = responseTimes.reduce(0, +) / Double(responseTimes.count)
    }
}

struct CacheStatistics {
    var totalItems: Int = 0
    var totalSize: Int = 0
    var hitCount: Int = 0
    var missCount: Int = 0
    var hitRate: Double = 0.0
    var lastCleanup: Date?
    var categorySizes: [CacheCategory: Int] = [:]
}

struct NetworkStatus {
    var isConnected: Bool = false
    var condition: NetworkCondition = .unknown
    var connectionType: ConnectionType = .unknown
    var lastUpdated: Date = Date()
}

struct MemoryUsage {
    var used: Int = 0
    var available: Int = 0
    var peak: Int = 0
    var lastUpdated: Date = Date()
    
    var usagePercentage: Double {
        guard available > 0 else { return 0.0 }
        return Double(used) / Double(available)
    }
}

struct PerformanceReport {
    let metrics: PerformanceMetrics
    let cacheStats: CacheStatistics
    let memoryUsage: MemoryUsage
    let networkStatus: NetworkStatus
    let recommendations: [OptimizationRecommendation]
    let generatedAt: Date
}

struct OptimizationRecommendation: Identifiable {
    let id = UUID()
    let type: RecommendationType
    let severity: Severity
    let description: String
    let suggestion: String
    
    enum RecommendationType {
        case responseTime, cacheEfficiency, memoryUsage, networkOptimization
    }
    
    enum Severity {
        case low, medium, high
        
        var color: Color {
            switch self {
            case .low: return .green
            case .medium: return .orange
            case .high: return .red
            }
        }
    }
}

enum NetworkCondition {
    case excellent, good, poor, offline, unknown
}

enum ConnectionType {
    case wifi, cellular, ethernet, other, unknown
}

enum CacheCategory: String, CaseIterable {
    case general = "general"
    case apiResponse = "api_response"
    case media = "media"
    case userContent = "user_content"
    case aiResponse = "ai_response"
}

enum CachePriority: Int, Comparable {
    case low = 1
    case normal = 2
    case high = 3
    case critical = 4
    
    static func < (lhs: CachePriority, rhs: CachePriority) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
}

// MARK: - Cache Manager

class CacheManager {
    private var cache: [String: CacheItem] = [:]
    private let maxSize: Int
    private let maxAge: TimeInterval
    private let queue = DispatchQueue(label: "CacheManager", attributes: .concurrent)
    
    var itemCount: Int { cache.count }
    var currentSize: Int { cache.values.reduce(0) { $0 + $1.size } }
    var hitCount: Int = 0
    var missCount: Int = 0
    var lastCleanup: Date?
    
    var hitRate: Double {
        let total = hitCount + missCount
        return total > 0 ? Double(hitCount) / Double(total) : 0.0
    }
    
    init(maxSize: Int, maxAge: TimeInterval) {
        self.maxSize = maxSize
        self.maxAge = maxAge
    }
    
    func store(_ item: CacheItem) {
        queue.async(flags: .barrier) {
            self.cache[item.key] = item
            self.evictIfNeeded()
        }
    }
    
    func retrieve(key: String) -> CacheItem? {
        return queue.sync {
            guard var item = cache[key] else { return nil }
            
            // Check if expired
            if Date().timeIntervalSince(item.createdAt) > maxAge {
                cache.removeValue(forKey: key)
                return nil
            }
            
            // Update access info
            item.lastAccessed = Date()
            item.accessCount += 1
            cache[key] = item
            
            return item
        }
    }
    
    func remove(key: String) {
        queue.async(flags: .barrier) {
            self.cache.removeValue(forKey: key)
        }
    }
    
    func removeItems(in category: CacheCategory) {
        queue.async(flags: .barrier) {
            self.cache = self.cache.filter { $0.value.category != category }
        }
    }
    
    func removeItems(withPriority priority: CachePriority, olderThan date: Date? = nil) {
        queue.async(flags: .barrier) {
            self.cache = self.cache.filter { item in
                if item.value.priority == priority {
                    if let date = date {
                        return item.value.createdAt > date
                    } else {
                        return false
                    }
                }
                return true
            }
        }
    }
    
    func removeExpiredItems() {
        queue.async(flags: .barrier) {
            let now = Date()
            self.cache = self.cache.filter { item in
                now.timeIntervalSince(item.value.createdAt) <= self.maxAge
            }
            self.lastCleanup = now
        }
    }
    
    func evictLRUItems(targetSize: Int) {
        queue.async(flags: .barrier) {
            while self.currentSize > targetSize && !self.cache.isEmpty {
                // Find least recently used item
                let lruKey = self.cache.min { a, b in
                    a.value.lastAccessed < b.value.lastAccessed
                }?.key
                
                if let key = lruKey {
                    self.cache.removeValue(forKey: key)
                }
            }
        }
    }
    
    func clearAll() {
        queue.async(flags: .barrier) {
            self.cache.removeAll()
            self.hitCount = 0
            self.missCount = 0
        }
    }
    
    func recordHit(for key: String) {
        queue.async(flags: .barrier) {
            self.hitCount += 1
        }
    }
    
    func recordMiss(for key: String) {
        queue.async(flags: .barrier) {
            self.missCount += 1
        }
    }
    
    func getFrequentlyAccessedItems(threshold: Int = 5) -> [CacheItem] {
        return queue.sync {
            return Array(cache.values.filter { $0.accessCount >= threshold })
        }
    }
    
    func getCategorySizes() -> [CacheCategory: Int] {
        return queue.sync {
            var sizes: [CacheCategory: Int] = [:]
            for category in CacheCategory.allCases {
                sizes[category] = cache.values
                    .filter { $0.category == category }
                    .reduce(0) { $0 + $1.size }
            }
            return sizes
        }
    }
    
    private func evictIfNeeded() {
        if currentSize > maxSize {
            evictLRUItems(targetSize: Int(Double(maxSize) * 0.8))
        }
    }
}

struct CacheItem {
    let key: String
    var data: Data?
    let category: CacheCategory
    let priority: CachePriority
    let createdAt: Date
    var lastAccessed: Date
    var accessCount: Int
    let size: Int
    var isCompressed: Bool = false
}

// MARK: - Extensions

extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}

func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
    return try await withThrowingTaskGroup(of: T.self) { group in
        group.addTask {
            try await operation()
        }
        
        group.addTask {
            try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
            throw TimeoutError()
        }
        
        guard let result = try await group.next() else {
            throw TimeoutError()
        }
        
        group.cancelAll()
        return result
    }
}

struct TimeoutError: Error {
    let localizedDescription = "Operation timed out"
} 