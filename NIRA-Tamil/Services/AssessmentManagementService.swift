import Foundation
import Combine
// import Supabase // Disabled for compilation

// MARK: - Data Models

struct Assessment: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: AssessmentType
    let language: Language
    let targetLevel: ProficiencyLevel
    let duration: TimeInterval
    let questions: [AssessmentQuestion]
    let passingScore: Double
    let certificateTemplate: String?
    let createdAt: Date
    let updatedAt: Date
}

enum AssessmentType: String, Codable, CaseIterable {
    case placement = "placement"
    case progress = "progress"
    case proficiency = "proficiency"
    case certification = "certification"
    case diagnostic = "diagnostic"
    case final = "final"
}

struct AssessmentQuestion: Codable, Identifiable {
    let id: UUID
    let type: AssessmentQuestionType
    let content: String
    let options: [String]?
    let correctAnswer: String
    let explanation: String
    let skillArea: SkillCategory
    let difficultyLevel: Double
    let points: Int
    let timeLimit: TimeInterval?
    let audioUrl: String?
    let imageUrl: String?
}

enum AssessmentQuestionType: String, Codable, CaseIterable {
    case multipleChoice = "multiple_choice"
    case fillInBlank = "fill_in_blank"
    case essay = "essay"
    case speaking = "speaking"
    case listening = "listening"
    case translation = "translation"
    case pronunciation = "pronunciation"
    case matching = "matching"
    case ordering = "ordering"
}

struct AssessmentSession: Codable, Identifiable {
    let id: UUID
    let assessmentId: UUID
    let userId: UUID
    let startTime: Date
    var endTime: Date?
    var responses: [AssessmentResponse]
    var currentQuestionIndex: Int
    var status: AssessmentSessionStatus
    var score: Double?
    var feedback: String?
    var certificateId: UUID?
}

enum AssessmentSessionStatus: String, Codable {
    case notStarted = "not_started"
    case inProgress = "in_progress"
    case completed = "completed"
    case abandoned = "abandoned"
    case expired = "expired"
}

struct AssessmentResponse: Codable, Identifiable {
    let id: UUID
    let questionId: UUID
    let userAnswer: String
    let isCorrect: Bool
    let points: Int
    let timeSpent: TimeInterval
    let confidence: Double?
    let timestamp: Date
}

struct AssessmentResult: Codable, Identifiable {
    let id: UUID
    let sessionId: UUID
    let userId: UUID
    let assessmentId: UUID
    let totalScore: Double
    let percentage: Double
    let passed: Bool
    let skillBreakdown: [SkillCategory: Double]
    let strengths: [String]
    let weaknesses: [String]
    let recommendations: [String]
    let estimatedLevel: ProficiencyLevel
    let completionTime: TimeInterval
    let certificateEarned: Bool
    let createdAt: Date
}

struct Certificate: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let assessmentId: UUID
    let title: String
    let description: String
    let language: Language
    let level: ProficiencyLevel
    let score: Double
    let issueDate: Date
    let expiryDate: Date?
    let verificationCode: String
    let certificateUrl: String?
    let isValid: Bool
}

struct PlacementResult: Codable {
    let recommendedLevel: ProficiencyLevel
    let confidence: Double
    let skillGaps: [SkillCategory]
    let strengths: [SkillCategory]
    let recommendedPath: String
    let estimatedTimeToNext: TimeInterval
}

// MARK: - Assessment Errors

enum AssessmentError: Error {
    case assessmentNotFound
    case questionNotFound
    case noActiveSession
    case invalidResponse
    case evaluationFailed
    
    var localizedDescription: String {
        switch self {
        case .assessmentNotFound:
            return "Assessment not found"
        case .questionNotFound:
            return "Question not found"
        case .noActiveSession:
            return "No active assessment session"
        case .invalidResponse:
            return "Invalid response format"
        case .evaluationFailed:
            return "Failed to evaluate answer"
        }
    }
}

// MARK: - Service

@MainActor
class AssessmentManagementService: ObservableObject {
    @Published var availableAssessments: [Assessment] = []
    @Published var currentSession: AssessmentSession?
    @Published var assessmentHistory: [AssessmentResult] = []
    @Published var certificates: [Certificate] = []
    @Published var isLoading = false
    @Published var placementResult: PlacementResult?
    
    private let supabaseClient: NIRASupabaseClient
    private let geminiService: GeminiService
    private let analyticsService: LearningAnalyticsService
    private let pronunciationService: PronunciationAssessmentService
    private var cancellables = Set<AnyCancellable>()
    
    init(
        supabaseClient: NIRASupabaseClient? = nil,
        geminiService: GeminiService? = nil,
        analyticsService: LearningAnalyticsService? = nil,
        pronunciationService: PronunciationAssessmentService? = nil
    ) {
        self.supabaseClient = supabaseClient ?? .shared
        self.geminiService = geminiService ?? GeminiService.shared
        self.analyticsService = analyticsService ?? .shared
        self.pronunciationService = pronunciationService ?? .shared
        
        loadAvailableAssessments()
    }
    
    // MARK: - Assessment Management
    
    func loadAvailableAssessments() {
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                // Load assessments from database
                availableAssessments = try await fetchAssessmentsFromDatabase()
                
                // Generate adaptive assessments if needed
                if availableAssessments.isEmpty {
                    availableAssessments = try await generateDefaultAssessments()
                }
            } catch {
                print("Failed to load assessments: \(error)")
            }
        }
    }
    
    func createCustomAssessment(
        title: String,
        type: AssessmentType,
        language: Language,
        targetLevel: ProficiencyLevel,
        skillAreas: [SkillCategory],
        questionCount: Int
    ) async throws -> Assessment {
        isLoading = true
        defer { isLoading = false }
        
        let assessmentRequest = """
        Create a comprehensive language assessment:
        
        Title: \(title)
        Type: \(type.rawValue)
        Language: \(language.displayName)
        Target Level: \(targetLevel.rawValue)
        Skill Areas: \(skillAreas.map { $0.rawValue }.joined(separator: ", "))
        Question Count: \(questionCount)
        
        Generate diverse questions covering:
        1. Multiple choice for vocabulary and grammar
        2. Fill-in-blank for practical application
        3. Listening comprehension questions
        4. Speaking prompts for oral assessment
        5. Translation exercises
        
        Ensure questions are:
        - Culturally appropriate
        - Level-appropriate difficulty
        - Pedagogically sound
        - Engaging and relevant
        
        Return as structured JSON with questions and metadata.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: assessmentRequest)
        
        let assessment = try parseAssessmentResponse(response, type: type, language: language, targetLevel: targetLevel)
        
        // Save to database
        try await saveAssessmentToDatabase(assessment)
        
        availableAssessments.append(assessment)
        
        // Track assessment creation
        analyticsService.trackInteraction(
            userId: UUID(), // Would get current user ID in real implementation
            interactionType: .exerciseAttempt, // Using closest available type
            contentType: .exercise,
            contentId: assessment.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "type": SupabaseAnyCodable(type.rawValue),
                "language": SupabaseAnyCodable(language.rawValue),
                "question_count": SupabaseAnyCodable(questionCount)
            ]
        )
        
        return assessment
    }
    
    // MARK: - Assessment Sessions
    
    func startAssessment(_ assessmentId: UUID, userId: UUID) async throws -> AssessmentSession {
        guard let assessment = availableAssessments.first(where: { $0.id == assessmentId }) else {
            throw AssessmentError.assessmentNotFound
        }
        
        let session = AssessmentSession(
            id: UUID(),
            assessmentId: assessmentId,
            userId: userId,
            startTime: Date(),
            endTime: nil,
            responses: [],
            currentQuestionIndex: 0,
            status: .inProgress,
            score: nil,
            feedback: nil,
            certificateId: nil
        )
        
        currentSession = session
        
        // Track assessment start
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .lessonStart,
            contentType: .exercise,
            contentId: assessmentId.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "assessment_id": SupabaseAnyCodable(assessmentId.uuidString),
                "type": SupabaseAnyCodable(assessment.type.rawValue)
            ]
        )
        
        return session
    }
    
    func submitAnswer(
        questionId: UUID,
        answer: String,
        timeSpent: TimeInterval,
        confidence: Double? = nil
    ) async throws {
        guard var session = currentSession else {
            throw AssessmentError.noActiveSession
        }
        
        guard let assessment = availableAssessments.first(where: { $0.id == session.assessmentId }) else {
            throw AssessmentError.assessmentNotFound
        }
        
        guard let question = assessment.questions.first(where: { $0.id == questionId }) else {
            throw AssessmentError.questionNotFound
        }
        
        // Evaluate answer
        let isCorrect = try await evaluateAnswer(question: question, userAnswer: answer)
        let points = isCorrect ? question.points : 0
        
        let response = AssessmentResponse(
            id: UUID(),
            questionId: questionId,
            userAnswer: answer,
            isCorrect: isCorrect,
            points: points,
            timeSpent: timeSpent,
            confidence: confidence,
            timestamp: Date()
        )
        
        session.responses.append(response)
        session.currentQuestionIndex += 1
        
        // Check if assessment is complete
        if session.currentQuestionIndex >= assessment.questions.count {
            session.status = .completed
            session.endTime = Date()
            
            // Generate results
            let result = try await generateAssessmentResult(session: session, assessment: assessment)
            assessmentHistory.append(result)
            
            // Generate certificate if passed
            if result.passed && assessment.type == .certification {
                let certificate = try await generateCertificate(result: result, assessment: assessment)
                certificates.append(certificate)
            }
        }
        
        currentSession = session
        
        // Track answer submission
        analyticsService.trackInteraction(
            userId: session.userId,
            interactionType: .exerciseAttempt,
            contentType: .exercise,
            contentId: questionId.uuidString,
            isCorrect: isCorrect,
            responseTime: Int(timeSpent * 1000), // Convert to milliseconds
            metadata: [
                "question_type": SupabaseAnyCodable(question.type.rawValue),
                "is_correct": SupabaseAnyCodable(isCorrect),
                "time_spent": SupabaseAnyCodable(timeSpent)
            ]
        )
    }
    
    func pauseAssessment() async {
        guard var session = currentSession else { return }
        
        session.status = .abandoned
        currentSession = session
        
        analyticsService.trackInteraction(
            userId: session.userId,
            interactionType: .pauseSession,
            contentType: .exercise,
            contentId: session.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: ["session_id": SupabaseAnyCodable(session.id.uuidString)]
        )
    }
    
    func resumeAssessment(_ sessionId: UUID) async throws {
        // Load session from database and resume
        // Implementation would restore session state
    }
    
    // MARK: - Placement Testing
    
    func conductPlacementTest(for language: Language, userId: UUID) async throws -> PlacementResult {
        isLoading = true
        defer { isLoading = false }
        
        // Create adaptive placement assessment
        let placementAssessment = try await createAdaptivePlacementTest(language: language)
        
        // Start assessment session
        _ = try await startAssessment(placementAssessment.id, userId: userId)
        
        // For demo purposes, simulate completion
        let simulatedResult = PlacementResult(
            recommendedLevel: .intermediate,
            confidence: 0.85,
            skillGaps: [.pronunciation, .listening],
            strengths: [.vocabulary, .reading],
            recommendedPath: "Intermediate Conversation Focus",
            estimatedTimeToNext: 30 * 24 * 60 * 60 // 30 days
        )
        
        placementResult = simulatedResult
        
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .lessonComplete,
            contentType: .exercise,
            contentId: placementAssessment.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "language": SupabaseAnyCodable(language.rawValue),
                "recommended_level": SupabaseAnyCodable(simulatedResult.recommendedLevel.rawValue),
                "confidence": SupabaseAnyCodable(simulatedResult.confidence)
            ]
        )
        
        return simulatedResult
    }
    
    private func createAdaptivePlacementTest(language: Language) async throws -> Assessment {
        let placementRequest = """
        Create an adaptive placement test for \(language.displayName):
        
        Requirements:
        1. 20-30 questions covering all skill areas
        2. Progressive difficulty (beginner to advanced)
        3. Quick assessment (15-20 minutes)
        4. Accurate level determination
        5. Identify specific strengths and weaknesses
        
        Include:
        - Vocabulary recognition
        - Grammar understanding
        - Reading comprehension
        - Basic listening (if audio available)
        - Cultural knowledge
        
        Return structured assessment with adaptive scoring.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: placementRequest)
        
        return try parseAssessmentResponse(response, type: .placement, language: language, targetLevel: .intermediate)
    }
    
    // MARK: - Answer Evaluation
    
    private func evaluateAnswer(question: AssessmentQuestion, userAnswer: String) async throws -> Bool {
        switch question.type {
        case .multipleChoice:
            return userAnswer.lowercased() == question.correctAnswer.lowercased()
            
        case .fillInBlank:
            return try await evaluateOpenEndedAnswer(question: question, userAnswer: userAnswer)
            
        case .essay:
            return try await evaluateEssayAnswer(question: question, userAnswer: userAnswer)
            
        case .speaking:
            return try await evaluateSpeakingAnswer(question: question, userAnswer: userAnswer)
            
        case .pronunciation:
            return try await evaluatePronunciationAnswer(question: question, userAnswer: userAnswer)
            
        case .translation:
            return try await evaluateTranslationAnswer(question: question, userAnswer: userAnswer)
            
        default:
            return userAnswer.lowercased().contains(question.correctAnswer.lowercased())
        }
    }
    
    private func evaluateOpenEndedAnswer(question: AssessmentQuestion, userAnswer: String) async throws -> Bool {
        let evaluationRequest = """
        Evaluate this language learning answer:
        
        Question: \(question.content)
        Correct Answer: \(question.correctAnswer)
        User Answer: \(userAnswer)
        
        Consider:
        1. Semantic correctness
        2. Grammar accuracy
        3. Vocabulary appropriateness
        4. Cultural context
        
        Return true if answer demonstrates understanding, false otherwise.
        Provide brief explanation.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: evaluationRequest)
        return response.lowercased().contains("true")
    }
    
    private func evaluateEssayAnswer(question: AssessmentQuestion, userAnswer: String) async throws -> Bool {
        let evaluationRequest = """
        Evaluate this essay response for language proficiency:
        
        Prompt: \(question.content)
        Response: \(userAnswer)
        Target Level: \(question.difficultyLevel)
        
        Assess:
        1. Grammar and syntax
        2. Vocabulary range and accuracy
        3. Coherence and organization
        4. Cultural appropriateness
        5. Content relevance
        
        Score from 0-100 and determine if it meets proficiency threshold.
        """
        
        _ = try await geminiService.makeGeminiRequest(prompt: evaluationRequest)
        // Parse score and determine if it meets threshold
        return true // Simplified for demo
    }
    
    private func evaluateSpeakingAnswer(question: AssessmentQuestion, userAnswer: String) async throws -> Bool {
        // Integration with pronunciation service for speaking evaluation
        // userAnswer would be audio data in real implementation
        return true // Simplified for demo
    }
    
    private func evaluatePronunciationAnswer(question: AssessmentQuestion, userAnswer: String) async throws -> Bool {
        // Use pronunciation assessment service
        // userAnswer would be audio data in real implementation
        return true // Simplified for demo
    }
    
    private func evaluateTranslationAnswer(question: AssessmentQuestion, userAnswer: String) async throws -> Bool {
        let evaluationRequest = """
        Evaluate translation accuracy:
        
        Source: \(question.content)
        Expected: \(question.correctAnswer)
        User Translation: \(userAnswer)
        
        Consider:
        1. Meaning preservation
        2. Grammar correctness
        3. Natural expression
        4. Cultural adaptation
        
        Return evaluation result.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: evaluationRequest)
        return response.lowercased().contains("correct") || response.lowercased().contains("accurate")
    }
    
    // MARK: - Results and Certificates
    
    private func generateAssessmentResult(session: AssessmentSession, assessment: Assessment) async throws -> AssessmentResult {
        let totalPoints = session.responses.reduce(0) { $0 + $1.points }
        let maxPoints = assessment.questions.reduce(0) { $0 + $1.points }
        let percentage = Double(totalPoints) / Double(maxPoints) * 100
        let passed = percentage >= assessment.passingScore
        
        // Generate skill breakdown
        var skillBreakdown: [SkillCategory: Double] = [:]
        for category in SkillCategory.allCases {
            let categoryQuestions = assessment.questions.filter { $0.skillArea == category }
            let categoryResponses = session.responses.filter { response in
                categoryQuestions.contains { $0.id == response.questionId }
            }
            let categoryScore = categoryResponses.reduce(0) { $0 + $1.points }
            let categoryMax = categoryQuestions.reduce(0) { $0 + $1.points }
            skillBreakdown[category] = categoryMax > 0 ? Double(categoryScore) / Double(categoryMax) : 0
        }
        
        // Generate AI feedback
        let feedbackRequest = """
        Generate personalized feedback for language assessment:
        
        Assessment: \(assessment.title)
        Score: \(percentage)%
        Skill Breakdown: \(skillBreakdown)
        
        Provide:
        1. Strengths identified
        2. Areas for improvement
        3. Specific recommendations
        4. Next steps for learning
        
        Be encouraging and constructive.
        """
        
        let feedback = try await geminiService.makeGeminiRequest(prompt: feedbackRequest)
        let (strengths, weaknesses, recommendations) = parseFeedback(feedback)
        
        let result = AssessmentResult(
            id: UUID(),
            sessionId: session.id,
            userId: session.userId,
            assessmentId: assessment.id,
            totalScore: Double(totalPoints),
            percentage: percentage,
            passed: passed,
            skillBreakdown: skillBreakdown,
            strengths: strengths,
            weaknesses: weaknesses,
            recommendations: recommendations,
            estimatedLevel: estimateLevel(from: percentage),
            completionTime: session.endTime?.timeIntervalSince(session.startTime) ?? 0,
            certificateEarned: passed && assessment.type == .certification,
            createdAt: Date()
        )
        
        // Track assessment completion
        analyticsService.trackInteraction(
            userId: result.userId,
            interactionType: .lessonComplete,
            contentType: .exercise,
            contentId: assessment.id.uuidString,
            isCorrect: passed,
            responseTime: Int(result.completionTime * 1000),
            metadata: [
                "score": SupabaseAnyCodable(percentage),
                "passed": SupabaseAnyCodable(passed),
                "duration": SupabaseAnyCodable(result.completionTime)
            ]
        )
        
        return result
    }
    
    private func generateCertificate(result: AssessmentResult, assessment: Assessment) async throws -> Certificate {
        let certificate = Certificate(
            id: UUID(),
            userId: result.userId,
            assessmentId: assessment.id,
            title: "\(assessment.language.displayName) Proficiency Certificate",
            description: "Certified proficiency in \(assessment.language.displayName) at \(assessment.targetLevel.rawValue) level",
            language: assessment.language,
            level: assessment.targetLevel,
            score: result.percentage,
            issueDate: Date(),
            expiryDate: Calendar.current.date(byAdding: .year, value: 2, to: Date()),
            verificationCode: generateVerificationCode(),
            certificateUrl: nil,
            isValid: true
        )
        
        // Generate certificate PDF/image
        // Implementation would create visual certificate
        
        analyticsService.trackInteraction(
            userId: result.userId,
            interactionType: .lessonComplete,
            contentType: .exercise,
            contentId: certificate.id.uuidString,
            isCorrect: true,
            responseTime: nil,
            metadata: [
                "language": SupabaseAnyCodable(assessment.language.rawValue),
                "level": SupabaseAnyCodable(assessment.targetLevel.rawValue),
                "score": SupabaseAnyCodable(result.percentage)
            ]
        )
        
        return certificate
    }
    
    // MARK: - Helper Methods
    
    private func fetchAssessmentsFromDatabase() async throws -> [Assessment] {
        // Fetch from Supabase
        return []
    }
    
    private func saveAssessmentToDatabase(_ assessment: Assessment) async throws {
        // Save to Supabase
    }
    
    private func generateDefaultAssessments() async throws -> [Assessment] {
        var assessments: [Assessment] = []
        
        for language in Language.allCases {
            for level in ProficiencyLevel.allCases {
                let assessment = try await createCustomAssessment(
                    title: "\(language.displayName) \(level.rawValue.capitalized) Assessment",
                    type: .proficiency,
                    language: language,
                    targetLevel: level,
                    skillAreas: SkillCategory.allCases,
                    questionCount: 20
                )
                assessments.append(assessment)
            }
        }
        
        return assessments
    }
    
    private func parseAssessmentResponse(
        _ response: String,
        type: AssessmentType,
        language: Language,
        targetLevel: ProficiencyLevel
    ) throws -> Assessment {
        // Parse AI response and create Assessment
        // For demo, create sample assessment
        
        let questions = (1...20).map { index in
            AssessmentQuestion(
                id: UUID(),
                type: .multipleChoice,
                content: "Sample question \(index)",
                options: ["Option A", "Option B", "Option C", "Option D"],
                correctAnswer: "Option A",
                explanation: "Sample explanation",
                skillArea: SkillCategory.allCases.randomElement()!,
                difficultyLevel: Double.random(in: 1...6),
                points: 5,
                timeLimit: 60,
                audioUrl: nil,
                imageUrl: nil
            )
        }
        
        return Assessment(
            id: UUID(),
            title: "\(language.displayName) \(type.rawValue.capitalized) Test",
            description: "Comprehensive \(language.displayName) assessment",
            type: type,
            language: language,
            targetLevel: targetLevel,
            duration: 30 * 60, // 30 minutes
            questions: questions,
            passingScore: 70.0,
            certificateTemplate: nil,
            createdAt: Date(),
            updatedAt: Date()
        )
    }
    
    private func parseFeedback(_ feedback: String) -> ([String], [String], [String]) {
        // Parse AI feedback into structured components
        let strengths = ["Good vocabulary usage", "Strong grammar foundation"]
        let weaknesses = ["Pronunciation needs work", "Listening comprehension"]
        let recommendations = ["Practice daily conversations", "Focus on audio materials"]
        
        return (strengths, weaknesses, recommendations)
    }
    
    private func estimateLevel(from percentage: Double) -> ProficiencyLevel {
        switch percentage {
        case 90...: return .proficient
        case 80..<90: return .advanced
        case 70..<80: return .upperIntermediate
        case 60..<70: return .intermediate
        case 50..<60: return .elementary
        default: return .beginner
        }
    }
    
    private func generateVerificationCode() -> String {
        let characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return String((0..<8).map { _ in characters.randomElement()! })
    }
} 