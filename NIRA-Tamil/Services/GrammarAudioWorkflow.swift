import Foundation

/// Complete Grammar Audio Workflow
/// Handles generation, upload, and database updates for grammar examples
@MainActor
class GrammarAudioWorkflow: ObservableObject {
    static let shared = GrammarAudioWorkflow()
    
    @Published var isProcessing = false
    @Published var currentStep = ""
    @Published var overallProgress = 0.0
    @Published var statusMessage = ""
    
    // Services
    private let generator = GrammarAudioGenerator.shared
    private let uploader = SupabaseAudioUploader.shared
    private let supabaseService = SupabaseContentService.shared
    
    // Supabase Configuration
    private let supabaseURL = "https://wnsorhbsucjguaoquhvr.supabase.co"
    private let supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"
    private let audioBucket = "audio"
    
    private init() {}
    
    // MARK: - Complete Workflow
    
    /// Execute complete grammar audio workflow
    func executeGrammarAudioWorkflow() async throws -> GrammarWorkflowResult {
        isProcessing = true
        overallProgress = 0.0
        
        do {
            // Step 1: Load grammar examples (10% of progress)
            currentStep = "Loading grammar examples"
            statusMessage = "Fetching grammar examples from database..."
            let grammarExamples = try await loadGrammarExamples()
            overallProgress = 0.1
            
            // Step 2: Generate audio files (50% of progress)
            currentStep = "Generating audio files"
            statusMessage = "Generating MP3 files using Google TTS..."
            let generatedFiles = try await generateAudioFiles(for: grammarExamples)
            overallProgress = 0.6
            
            // Step 3: Upload to Supabase Storage (30% of progress)
            currentStep = "Uploading to Supabase"
            statusMessage = "Uploading audio files to Supabase Storage..."
            let uploadedFiles = try await uploadAudioFiles(generatedFiles)
            overallProgress = 0.9
            
            // Step 4: Update database (10% of progress)
            currentStep = "Updating database"
            statusMessage = "Updating grammar examples with audio URLs..."
            try await updateDatabaseWithAudioURLs(uploadedFiles)
            overallProgress = 1.0
            
            currentStep = "Completed"
            statusMessage = "Grammar audio workflow completed successfully!"
            isProcessing = false
            
            return GrammarWorkflowResult(
                totalExamples: grammarExamples.count,
                generatedFiles: generatedFiles.count,
                uploadedFiles: uploadedFiles.count,
                updatedRecords: uploadedFiles.count
            )
            
        } catch {
            isProcessing = false
            currentStep = "Error"
            statusMessage = "Workflow failed: \(error.localizedDescription)"
            throw error
        }
    }
    
    // MARK: - Step 1: Load Grammar Examples
    
    private func loadGrammarExamples() async throws -> [GrammarExampleRecord] {
        let query = """
        SELECT ge.id, ge.example_tamil, ge.example_english, ge.example_romanization, 
               gt.title_english, gt.title_tamil 
        FROM grammar_examples ge 
        JOIN grammar_topics gt ON ge.grammar_topic_id = gt.id 
        WHERE gt.lesson_id = '7b8c60af-dd2f-4754-9363-ab09a5bcea95' 
        ORDER BY gt.grammar_id, ge.example_order;
        """
        
        let url = URL(string: "\(supabaseURL)/rest/v1/rpc/execute_sql")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue(supabaseKey, forHTTPHeaderField: "apikey")
        
        let requestBody = ["sql": query]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw GrammarWorkflowError.databaseQueryFailed
        }
        
        // Parse the response - this is a simplified version
        // You'll need to implement proper JSON parsing based on your Supabase response format
        let examples = try parseGrammarExamples(from: data)
        
        print("✅ Loaded \(examples.count) grammar examples")
        return examples
    }
    
    private func parseGrammarExamples(from data: Data) throws -> [GrammarExampleRecord] {
        // This is a placeholder - implement actual JSON parsing
        // For now, return the known examples from our previous query
        return [
            GrammarExampleRecord(
                id: "d9b80d40-bb5f-40e4-9984-f3f16ae42826",
                example_tamil: "காலை வணக்கம்!",
                example_english: "Good morning!",
                example_romanization: "kaalai vaNakkam!",
                title_english: "Basic Sentence Structure",
                title_tamil: "அடிப்படை வாக்கிய அமைப்பு"
            )
            // Add more examples here...
        ]
    }
    
    // MARK: - Step 2: Generate Audio Files
    
    private func generateAudioFiles(for examples: [GrammarExampleRecord]) async throws -> [GeneratedGrammarAudioFile] {
        var generatedFiles: [GeneratedGrammarAudioFile] = []
        let totalItems = examples.count
        
        for (index, example) in examples.enumerated() {
            let voice = determineVoice(for: example.example_tamil)
            let filename = "lesson_01_grammar_\(String(format: "%02d", index + 1))_example_\(String(format: "%02d", index + 1)).mp3"
            
            let audioFile = try await generateSingleAudioFile(
                text: example.example_tamil,
                filename: filename,
                voice: voice,
                exampleId: example.id
            )
            
            generatedFiles.append(audioFile)
            
            let progress = Double(index + 1) / Double(totalItems)
            overallProgress = 0.1 + (progress * 0.5) // 10% to 60%
            statusMessage = "Generated \(index + 1)/\(totalItems): \(example.example_english)"
        }
        
        return generatedFiles
    }
    
    private func generateSingleAudioFile(text: String, filename: String, voice: String, exampleId: String) async throws -> GeneratedGrammarAudioFile {
        // Use the existing Google TTS infrastructure
        // This is a simplified version - you'll need to implement the actual TTS call
        
        let audioData = try await callGoogleTTS(text: text, voice: voice)
        
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioDir = documentsPath.appendingPathComponent("Audio")
        try? FileManager.default.createDirectory(at: audioDir, withIntermediateDirectories: true)
        let localURL = audioDir.appendingPathComponent(filename)
        
        try audioData.write(to: localURL)
        
        return GeneratedGrammarAudioFile(
            filename: filename,
            localURL: localURL,
            text: text,
            audioData: audioData,
            exampleId: exampleId,
            voice: voice
        )
    }
    
    private func callGoogleTTS(text: String, voice: String) async throws -> Data {
        // This is a placeholder - implement actual Google TTS call
        // For now, return empty data
        return Data()
    }
    
    private func determineVoice(for text: String) -> String {
        // Use female voice as default for now
        return "ta-IN-Chirp3-HD-Erinome"
    }
    
    // MARK: - Step 3: Upload to Supabase
    
    private func uploadAudioFiles(_ files: [GeneratedGrammarAudioFile]) async throws -> [UploadedGrammarAudioFile] {
        var uploadedFiles: [UploadedGrammarAudioFile] = []
        let totalFiles = files.count
        
        for (index, file) in files.enumerated() {
            let publicURL = try await uploadToSupabaseStorage(file)
            
            let uploadedFile = UploadedGrammarAudioFile(
                filename: file.filename,
                localURL: file.localURL,
                publicURL: publicURL,
                text: file.text,
                exampleId: file.exampleId,
                voice: file.voice
            )
            
            uploadedFiles.append(uploadedFile)
            
            let progress = Double(index + 1) / Double(totalFiles)
            overallProgress = 0.6 + (progress * 0.3) // 60% to 90%
            statusMessage = "Uploaded \(index + 1)/\(totalFiles): \(file.filename)"
        }
        
        return uploadedFiles
    }
    
    private func uploadToSupabaseStorage(_ file: GeneratedGrammarAudioFile) async throws -> String {
        let uploadURL = URL(string: "\(supabaseURL)/storage/v1/object/\(audioBucket)/\(file.filename)")!
        
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        request.setValue("audio/mpeg", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue(supabaseKey, forHTTPHeaderField: "apikey")
        request.httpBody = file.audioData
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw GrammarWorkflowError.uploadFailed
        }
        
        return "\(supabaseURL)/storage/v1/object/public/\(audioBucket)/\(file.filename)"
    }
    
    // MARK: - Step 4: Update Database
    
    private func updateDatabaseWithAudioURLs(_ files: [UploadedGrammarAudioFile]) async throws {
        for file in files {
            try await updateGrammarExampleAudioURL(exampleId: file.exampleId, audioURL: file.publicURL)
        }
    }
    
    private func updateGrammarExampleAudioURL(exampleId: String, audioURL: String) async throws {
        let updateURL = URL(string: "\(supabaseURL)/rest/v1/grammar_examples?id=eq.\(exampleId)")!
        
        var request = URLRequest(url: updateURL)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue(supabaseKey, forHTTPHeaderField: "apikey")
        
        let updateData = ["audio_url": audioURL]
        request.httpBody = try JSONSerialization.data(withJSONObject: updateData)
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 || httpResponse.statusCode == 204 else {
            throw GrammarWorkflowError.databaseUpdateFailed
        }
        
        print("✅ Updated grammar example \(exampleId) with audio URL")
    }
}

// MARK: - Data Models

struct GrammarExampleRecord {
    let id: String
    let example_tamil: String
    let example_english: String
    let example_romanization: String
    let title_english: String
    let title_tamil: String
}

struct UploadedGrammarAudioFile {
    let filename: String
    let localURL: URL
    let publicURL: String
    let text: String
    let exampleId: String
    let voice: String
}

struct GrammarWorkflowResult {
    let totalExamples: Int
    let generatedFiles: Int
    let uploadedFiles: Int
    let updatedRecords: Int
}

// MARK: - Errors

enum GrammarWorkflowError: Error {
    case databaseQueryFailed
    case audioGenerationFailed
    case uploadFailed
    case databaseUpdateFailed
}
