//
//  SupabaseTestService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import Supabase
import Combine

/// Service for testing Supabase integration and database connectivity
class SupabaseTestService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = SupabaseTestService()
    
    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var testResults: [String] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        // Initialize Supabase client
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co")!,
            supabaseKey: APIKeys.supabaseAnonKey
        )
        
        print("🧪 SupabaseTestService initialized")
    }
    
    // MARK: - Test Methods
    
    /// Run comprehensive Supabase integration tests
    func runIntegrationTests() async {
        isLoading = true
        testResults = []
        errorMessage = nil
        
        await testDatabaseConnection()
        await testLessonsFetch()
        await testLocalCaching()
        await testFallbackMechanism()
        
        isLoading = false
        
        let successCount = testResults.filter { $0.contains("✅") }.count
        let totalTests = testResults.count
        
        print("🧪 Integration tests completed: \(successCount)/\(totalTests) passed")
    }
    
    /// Test basic database connectivity
    func testDatabaseConnection() async {
        do {
            // Simple query to test connection
            let _: [TamilSupabaseLesson] = try await supabase
                .from("lessons")
                .select("id, title_english")
                .limit(1)
                .execute()
                .value
            
            isConnected = true
            testResults.append("✅ Database connection successful")
            print("✅ Database connection test passed")
            
        } catch {
            isConnected = false
            testResults.append("❌ Database connection failed: \(error.localizedDescription)")
            print("❌ Database connection test failed: \(error)")
        }
    }
    
    /// Test fetching lessons from database
    private func testLessonsFetch() async {
        do {
            let response: [TamilSupabaseLesson] = try await supabase
                .from("lessons")
                .select("*")
                .eq("level_code", value: "A1")
                .eq("is_active", value: true)
                .execute()
                .value
            
            if response.count > 0 {
                testResults.append("✅ Lessons fetch successful: \(response.count) lessons found")
                print("✅ Lessons fetch test passed: \(response.count) lessons")
            } else {
                testResults.append("⚠️ Lessons fetch returned no results")
                print("⚠️ Lessons fetch test: no results")
            }
            
        } catch {
            testResults.append("❌ Lessons fetch failed: \(error.localizedDescription)")
            print("❌ Lessons fetch test failed: \(error)")
        }
    }
    
    /// Test local caching functionality
    func testLocalCaching() async {
        let cacheService = await LocalCacheService.shared

        // Test cache with actual lesson data
        let testLesson = TamilSupabaseLesson(
            id: "test-lesson-id",
            lessonNumber: 1,
            levelCode: "TEST",
            titleEnglish: "Test Lesson",
            titleTamil: "சோதனை பாடம்",
            titleRomanization: "Sōtaṉai pāṭam",
            descriptionEnglish: "Test lesson for caching",
            descriptionTamil: "கேச்சிங்கிற்கான சோதனை பாடம்",
            focus: "Testing",
            durationMinutes: 15,
            difficultyScore: 1,
            prerequisites: [],
            tags: ["test"],
            culturalContext: "Test context",
            isActive: true,
            createdAt: ISO8601DateFormatter().string(from: Date()),
            updatedAt: ISO8601DateFormatter().string(from: Date())
        )

        // Test cache write
        await cacheService.cacheLessons([testLesson], for: "TEST")

        // Test cache read
        let cachedLessons = await cacheService.getCachedLessons(for: "TEST")

        await MainActor.run {
            if cachedLessons.count > 0 && cachedLessons.first?.id == "test-lesson-id" {
                testResults.append("✅ Local caching working correctly")
                print("✅ Local caching test passed")
            } else {
                testResults.append("❌ Local caching failed")
                print("❌ Local caching test failed")
            }
        }

        // Clean up test data
        await cacheService.clearCache(for: "TEST")
    }
    
    /// Test fallback to hardcoded data
    func testFallbackMechanism() async {
        await MainActor.run {
            let contentService = TamilContentService.shared

            // This should work even if Supabase fails
            let lessons = contentService.getLessonsForLevel(.a1)

            if lessons.count > 0 {
                testResults.append("✅ Fallback mechanism working: \(lessons.count) fallback lessons")
                print("✅ Fallback mechanism test passed")
            } else {
                testResults.append("❌ Fallback mechanism failed")
                print("❌ Fallback mechanism test failed")
            }
        }
    }
    
    /// Test vocabulary fetching for a specific lesson
    func testVocabularyFetch(lessonId: String) async {
        do {
            let response: [TamilSupabaseVocabulary] = try await supabase
                .from("vocabulary")
                .select("*")
                .eq("lesson_id", value: lessonId)
                .execute()
                .value
            
            testResults.append("✅ Vocabulary fetch successful: \(response.count) items")
            print("✅ Vocabulary fetch test passed: \(response.count) items")
            
        } catch {
            testResults.append("❌ Vocabulary fetch failed: \(error.localizedDescription)")
            print("❌ Vocabulary fetch test failed: \(error)")
        }
    }
    
    /// Get formatted test results for display
    func getFormattedResults() -> String {
        return testResults.joined(separator: "\n")
    }
    
    /// Clear test results
    func clearResults() {
        testResults = []
        errorMessage = nil
    }
}
