//
//  UserProgressService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import Supabase
import Combine

/// Service for managing user progress tracking and synchronization
@MainActor
class UserProgressService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = UserProgressService()
    
    // MARK: - Published Properties
    @Published var userProgress: SupabaseUserProgressData?
    @Published var lessonProgress: [String: LessonProgress] = [:]
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var syncStatus: SyncStatus = .idle
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    private let userId: String
    
    // MARK: - Initialization
    private init() {
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co")!,
            supabaseKey: APIKeys.supabaseAnonKey
        )
        
        // For now, use a default user ID. In production, this would come from authentication
        self.userId = "default-user-\(UUID().uuidString)"
        
        print("📊 UserProgressService initialized for user: \(userId)")
        
        loadLocalProgress()
        
        // Start periodic sync
        startPeriodicSync()
    }
    
    // MARK: - Progress Management
    
    /// Initialize user progress if not exists
    func initializeUserProgress() async {
        if userProgress == nil {
            userProgress = SupabaseUserProgressData(
                userId: userId,
                totalLessonsCompleted: 0,
                currentLevel: CEFRLevel.a1,
                currentStreak: 0,
                longestStreak: 0,
                totalStudyTimeMinutes: 0,
                weeklyGoal: 5,
                weeklyProgress: 0,
                lastStudyDate: nil,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            )
            
            await saveProgressToSupabase()
            saveProgressLocally()
        }
    }
    
    /// Mark a lesson as completed
    func completeLessonProgress(lessonId: String, score: Int, timeSpentMinutes: Int) async {
        // Update lesson progress
        let lessonProgress = LessonProgress(
            id: UUID().uuidString,
            userId: userId,
            lessonId: lessonId,
            isCompleted: true,
            score: score,
            timeSpentMinutes: timeSpentMinutes,
            attemptsCount: 1,
            lastAttemptDate: ISO8601DateFormatter().string(from: Date()),
            createdAt: ISO8601DateFormatter().string(from: Date()),
            updatedAt: ISO8601DateFormatter().string(from: Date())
        )
        
        self.lessonProgress[lessonId] = lessonProgress
        
        // Update overall user progress
        if var progress = userProgress {
            progress.totalLessonsCompleted += 1
            progress.totalStudyTimeMinutes += timeSpentMinutes
            progress.weeklyProgress += 1
            progress.lastStudyDate = ISO8601DateFormatter().string(from: Date())
            progress.updatedAt = ISO8601DateFormatter().string(from: Date())
            
            // Update streak
            updateStreak(&progress)
            
            userProgress = progress
        }
        
        // Save to both local and remote
        saveProgressLocally()
        await saveProgressToSupabase()
        await saveLessonProgressToSupabase(lessonProgress)
        
        print("✅ Lesson \(lessonId) completed with score \(score)")
    }
    
    /// Update user's study streak
    private func updateStreak(_ progress: inout SupabaseUserProgressData) {
        let calendar = Calendar.current
        let today = Date()
        
        if let lastStudyDateString = progress.lastStudyDate,
           let lastStudyDate = ISO8601DateFormatter().date(from: lastStudyDateString) {
            
            let daysBetween = calendar.dateComponents([.day], from: lastStudyDate, to: today).day ?? 0
            
            if daysBetween == 1 {
                // Consecutive day - increment streak
                progress.currentStreak += 1
                progress.longestStreak = max(progress.longestStreak, progress.currentStreak)
            } else if daysBetween > 1 {
                // Streak broken - reset
                progress.currentStreak = 1
            }
            // If daysBetween == 0, it's the same day, don't change streak
        } else {
            // First study session
            progress.currentStreak = 1
            progress.longestStreak = 1
        }
    }
    
    /// Get progress for a specific lesson
    func getLessonProgress(lessonId: String) -> LessonProgress? {
        return lessonProgress[lessonId]
    }
    
    /// Check if lesson is completed
    func isLessonCompleted(lessonId: String) -> Bool {
        return lessonProgress[lessonId]?.isCompleted ?? false
    }
    
    /// Get completion percentage for current level
    func getCompletionPercentage(for level: CEFRLevel) -> Double {
        let completedLessons = lessonProgress.values.filter { $0.isCompleted }.count
        let totalLessons = 10 // This should come from lesson count for the level
        return Double(completedLessons) / Double(totalLessons)
    }
}

// MARK: - Data Persistence

extension UserProgressService {
    
    /// Save progress to Supabase
    private func saveProgressToSupabase() async {
        guard let progress = userProgress else { return }
        
        do {
            let _: SupabaseUserProgressData = try await supabase
                .from("user_progress")
                .upsert(progress)
                .execute()
                .value
            
            print("✅ User progress saved to Supabase")
            
        } catch {
            print("❌ Failed to save user progress to Supabase: \(error)")
            errorMessage = "Failed to sync progress: \(error.localizedDescription)"
        }
    }
    
    /// Save lesson progress to Supabase
    private func saveLessonProgressToSupabase(_ progress: LessonProgress) async {
        do {
            let _: LessonProgress = try await supabase
                .from("lesson_progress")
                .upsert(progress)
                .execute()
                .value
            
            print("✅ Lesson progress saved to Supabase")
            
        } catch {
            print("❌ Failed to save lesson progress to Supabase: \(error)")
            errorMessage = "Failed to sync lesson progress: \(error.localizedDescription)"
        }
    }
    
    /// Load progress from Supabase
    func loadProgressFromSupabase() async {
        isLoading = true
        syncStatus = .syncing
        
        do {
            // Load user progress
            let userProgressResponse: [SupabaseUserProgressData] = try await supabase
                .from("user_progress")
                .select("*")
                .eq("user_id", value: userId)
                .execute()
                .value
            
            if let progress = userProgressResponse.first {
                userProgress = progress
            }
            
            // Load lesson progress
            let lessonProgressResponse: [LessonProgress] = try await supabase
                .from("lesson_progress")
                .select("*")
                .eq("user_id", value: userId)
                .execute()
                .value
            
            lessonProgress = Dictionary(uniqueKeysWithValues: lessonProgressResponse.map { ($0.lessonId, $0) })
            
            syncStatus = .synced
            print("✅ Progress loaded from Supabase")
            
        } catch {
            syncStatus = .failed
            errorMessage = "Failed to load progress: \(error.localizedDescription)"
            print("❌ Failed to load progress from Supabase: \(error)")
        }
        
        isLoading = false
    }
    
    /// Save progress locally using UserDefaults
    private func saveProgressLocally() {
        if let progress = userProgress,
           let encoded = try? JSONEncoder().encode(progress) {
            UserDefaults.standard.set(encoded, forKey: "user_progress_\(userId)")
        }
        
        if let encoded = try? JSONEncoder().encode(lessonProgress) {
            UserDefaults.standard.set(encoded, forKey: "lesson_progress_\(userId)")
        }
        
        UserDefaults.standard.synchronize()
    }
    
    /// Load progress from local storage
    private func loadLocalProgress() {
        // Load user progress
        if let data = UserDefaults.standard.data(forKey: "user_progress_\(userId)"),
           let decoded = try? JSONDecoder().decode(SupabaseUserProgressData.self, from: data) {
            userProgress = decoded
        }
        
        // Load lesson progress
        if let data = UserDefaults.standard.data(forKey: "lesson_progress_\(userId)"),
           let decoded = try? JSONDecoder().decode([String: LessonProgress].self, from: data) {
            lessonProgress = decoded
        }
        
        print("📱 Progress loaded from local storage")
    }
    
    /// Start periodic sync with Supabase
    private func startPeriodicSync() {
        Timer.publish(every: 300, on: .main, in: .common) // Sync every 5 minutes
            .autoconnect()
            .sink { _ in
                Task { @MainActor in
                    await self.syncWithSupabase()
                }
            }
            .store(in: &cancellables)
    }
    
    /// Sync local progress with Supabase
    func syncWithSupabase() async {
        await saveProgressToSupabase()
        
        for progress in lessonProgress.values {
            await saveLessonProgressToSupabase(progress)
        }
        
        print("🔄 Progress synced with Supabase")
    }
}

// MARK: - Supporting Types

enum SyncStatus {
    case idle
    case syncing
    case synced
    case failed
}

struct SupabaseUserProgressData: Codable, Identifiable {
    let id = UUID()
    let userId: String
    var totalLessonsCompleted: Int
    var currentLevel: CEFRLevel
    var currentStreak: Int
    var longestStreak: Int
    var totalStudyTimeMinutes: Int
    var weeklyGoal: Int
    var weeklyProgress: Int
    var lastStudyDate: String?
    let createdAt: String
    var updatedAt: String
    
    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case totalLessonsCompleted = "total_lessons_completed"
        case currentLevel = "current_level"
        case currentStreak = "current_streak"
        case longestStreak = "longest_streak"
        case totalStudyTimeMinutes = "total_study_time_minutes"
        case weeklyGoal = "weekly_goal"
        case weeklyProgress = "weekly_progress"
        case lastStudyDate = "last_study_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct LessonProgress: Codable, Identifiable {
    let id: String
    let userId: String
    let lessonId: String
    var isCompleted: Bool
    var score: Int
    var timeSpentMinutes: Int
    var attemptsCount: Int
    var lastAttemptDate: String
    let createdAt: String
    var updatedAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case lessonId = "lesson_id"
        case isCompleted = "is_completed"
        case score
        case timeSpentMinutes = "time_spent_minutes"
        case attemptsCount = "attempts_count"
        case lastAttemptDate = "last_attempt_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}
