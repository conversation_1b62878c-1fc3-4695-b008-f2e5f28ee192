//
//  TamilContentLoader.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation

class TamilContentLoader {
    private let fileManager = FileManager.default
    private let cache = NSCache<NSString, NSData>()
    
    // MARK: - Content Paths
    
    private var documentsPath: URL {
        return fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }

    private var bundlePath: URL {
        // Get the main bundle path and navigate to the project root
        let bundleURL = Bundle.main.bundleURL.deletingLastPathComponent().deletingLastPathComponent()
        return bundleURL
    }

    private var lessonsBasePath: URL {
        return bundlePath.appendingPathComponent("docs/Lessons/Tamil/Lessons")
    }

    private var textbooksBasePath: URL {
        return bundlePath.appendingPathComponent("docs/Lessons/TN Books_json")
    }

    private var audioBasePath: URL {
        return bundlePath.appendingPathComponent("docs/Lessons/Tamil/Audio")
    }
    
    // MARK: - CEFR Lessons Loading
    
    func loadLessonsForLevel(_ level: CEFRLevel) async throws -> [TamilLesson] {
        let levelPath = lessonsBasePath.appendingPathComponent(level.rawValue)
        
        guard fileManager.fileExists(atPath: levelPath.path) else {
            print("Level directory not found: \(levelPath.path)")
            return []
        }
        
        var lessons: [TamilLesson] = []
        
        do {
            let files = try fileManager.contentsOfDirectory(at: levelPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" }
            
            for file in jsonFiles.sorted(by: { $0.lastPathComponent < $1.lastPathComponent }) {
                if let lesson = try await loadLessonFromFile(file) {
                    lessons.append(lesson)
                }
            }
        } catch {
            throw ContentLoadingError.directoryReadError(error)
        }
        
        return lessons
    }
    
    private func loadLessonFromFile(_ fileURL: URL) async throws -> TamilLesson? {
        let cacheKey = fileURL.path as NSString
        
        // Check cache first
        if let cachedData = cache.object(forKey: cacheKey) {
            return try JSONDecoder().decode(TamilLesson.self, from: cachedData as Data)
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            
            // Cache the data
            cache.setObject(data as NSData, forKey: cacheKey)
            
            let lesson = try JSONDecoder().decode(TamilLesson.self, from: data)
            return lesson
        } catch {
            print("Failed to load lesson from \(fileURL.lastPathComponent): \(error)")
            throw ContentLoadingError.jsonParsingError(error)
        }
    }
    
    // MARK: - Tamil Nadu Textbooks Loading
    
    func loadTNTextbooks() async throws -> [TNTextbook] {
        guard fileManager.fileExists(atPath: textbooksBasePath.path) else {
            print("Textbooks directory not found: \(textbooksBasePath.path)")
            return []
        }
        
        var textbooks: [TNTextbook] = []
        
        do {
            let files = try fileManager.contentsOfDirectory(at: textbooksBasePath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" && $0.lastPathComponent.hasPrefix("Std") }
            
            for file in jsonFiles.sorted(by: { $0.lastPathComponent < $1.lastPathComponent }) {
                if let textbook = try await loadTextbookFromFile(file) {
                    textbooks.append(textbook)
                }
            }
        } catch {
            throw ContentLoadingError.directoryReadError(error)
        }
        
        return textbooks
    }
    
    private func loadTextbookFromFile(_ fileURL: URL) async throws -> TNTextbook? {
        let cacheKey = fileURL.path as NSString
        
        // Check cache first
        if let cachedData = cache.object(forKey: cacheKey) {
            let pages = try JSONDecoder().decode([String: String].self, from: cachedData as Data)
            return createTextbookFromPages(pages, filename: fileURL.lastPathComponent)
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            
            // Cache the data
            cache.setObject(data as NSData, forKey: cacheKey)
            
            let pages = try JSONDecoder().decode([String: String].self, from: data)
            return createTextbookFromPages(pages, filename: fileURL.lastPathComponent)
        } catch {
            print("Failed to load textbook from \(fileURL.lastPathComponent): \(error)")
            throw ContentLoadingError.jsonParsingError(error)
        }
    }
    
    private func createTextbookFromPages(_ pages: [String: String], filename: String) -> TNTextbook? {
        // Extract standard number from filename (e.g., "Std01-Tamil.json" -> 1)
        let components = filename.replacingOccurrences(of: ".json", with: "").components(separatedBy: "-")
        guard let stdComponent = components.first else {
            return nil
        }

        let standardString = stdComponent.replacingOccurrences(of: "Std", with: "")
        guard let standard = Int(standardString) else {
            return nil
        }

        return TNTextbook(
            standard: standard,
            subject: "Tamil",
            pages: pages
        )
    }
    
    // MARK: - Audio Content Management
    
    func getAudioURL(for filename: String, level: CEFRLevel) -> URL? {
        let audioLevelPath = audioBasePath.appendingPathComponent(level.rawValue)
        let audioURL = audioLevelPath.appendingPathComponent(filename)
        
        guard fileManager.fileExists(atPath: audioURL.path) else {
            print("Audio file not found: \(audioURL.path)")
            return nil
        }
        
        return audioURL
    }
    
    func preloadAudioForLesson(_ lesson: TamilLesson) async {
        // Preload vocabulary audio
        for vocabulary in lesson.vocabulary {
            _ = getAudioURL(for: vocabulary.audioWordUrl, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
            _ = getAudioURL(for: vocabulary.audioSentenceUrl, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
        }
        
        // Preload conversation audio
        for conversation in lesson.conversations {
            for dialogue in conversation.dialogue {
                _ = getAudioURL(for: dialogue.audioUrl, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
            }
        }
    }
    
    // MARK: - Content Validation
    
    func validateContentIntegrity() async -> ContentValidationResult {
        var result = ContentValidationResult()
        
        // Validate CEFR lessons
        for level in CEFRLevel.allCases {
            do {
                let lessons = try await loadLessonsForLevel(level)
                result.cefrLessonsCount[level] = lessons.count
                
                // Validate audio files for each lesson
                for lesson in lessons {
                    let audioValidation = await validateAudioForLesson(lesson)
                    result.audioValidation[lesson.titleEnglish] = audioValidation
                }
            } catch {
                result.errors.append("Failed to validate \(level.rawValue): \(error.localizedDescription)")
            }
        }
        
        // Validate TN textbooks
        do {
            let textbooks = try await loadTNTextbooks()
            result.tnTextbooksCount = textbooks.count
        } catch {
            result.errors.append("Failed to validate TN textbooks: \(error.localizedDescription)")
        }
        
        return result
    }
    
    private func validateAudioForLesson(_ lesson: TamilLesson) async -> AudioValidationResult {
        var result = AudioValidationResult()
        let level = CEFRLevel(rawValue: lesson.levelCode) ?? .a1
        
        // Check vocabulary audio
        for vocabulary in lesson.vocabulary {
            let wordAudioExists = getAudioURL(for: vocabulary.audioWordUrl, level: level) != nil
            let sentenceAudioExists = getAudioURL(for: vocabulary.audioSentenceUrl, level: level) != nil
            
            result.vocabularyAudio[vocabulary.vocabId] = (wordAudioExists, sentenceAudioExists)
        }
        
        // Check conversation audio
        for conversation in lesson.conversations {
            for dialogue in conversation.dialogue {
                let audioExists = getAudioURL(for: dialogue.audioUrl, level: level) != nil
                result.conversationAudio[dialogue.speaker] = audioExists
            }
        }
        
        return result
    }
    
    // MARK: - Cache Management
    
    func clearCache() {
        cache.removeAllObjects()
    }
    
    func getCacheSize() -> Int {
        return cache.totalCostLimit
    }
}

// MARK: - Error Types

enum ContentLoadingError: Error, LocalizedError {
    case directoryReadError(Error)
    case jsonParsingError(Error)
    case fileNotFound(String)
    case invalidFormat(String)
    
    var errorDescription: String? {
        switch self {
        case .directoryReadError(let error):
            return "Failed to read directory: \(error.localizedDescription)"
        case .jsonParsingError(let error):
            return "Failed to parse JSON: \(error.localizedDescription)"
        case .fileNotFound(let filename):
            return "File not found: \(filename)"
        case .invalidFormat(let description):
            return "Invalid format: \(description)"
        }
    }
}

// MARK: - Validation Result Types

struct ContentValidationResult {
    var cefrLessonsCount: [CEFRLevel: Int] = [:]
    var tnTextbooksCount: Int = 0
    var audioValidation: [String: AudioValidationResult] = [:]
    var errors: [String] = []
    
    var isValid: Bool {
        return errors.isEmpty && !cefrLessonsCount.isEmpty
    }
}

struct AudioValidationResult {
    var vocabularyAudio: [String: (wordExists: Bool, sentenceExists: Bool)] = [:]
    var conversationAudio: [String: Bool] = [:]
    
    var totalAudioFiles: Int {
        let vocabCount = vocabularyAudio.values.reduce(0) { count, audio in
            count + (audio.wordExists ? 1 : 0) + (audio.sentenceExists ? 1 : 0)
        }
        let convCount = conversationAudio.values.filter { $0 }.count
        return vocabCount + convCount
    }
}
