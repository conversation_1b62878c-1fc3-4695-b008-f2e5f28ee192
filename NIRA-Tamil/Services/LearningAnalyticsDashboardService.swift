import Foundation
import Combine

// MARK: - Data Models

struct LearningDashboard: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let overviewMetrics: OverviewMetrics
    let skillProgress: [SkillProgressData]
    let learningTrends: LearningTrends
    let achievements: [AchievementSummary]
    let recommendations: [LearningRecommendation]
    let goals: [DashboardLearningGoal]
    let lastUpdated: Date
}

struct OverviewMetrics: Codable {
    let totalXP: Int
    let currentStreak: Int
    let longestStreak: Int
    let lessonsCompleted: Int
    let vocabularyMastered: Int
    let conversationMinutes: Double
    let averageSessionTime: TimeInterval
    let weeklyProgress: Double
    let monthlyProgress: Double
}

struct SkillProgressData: Codable, Identifiable {
    let id: UUID
    let skill: SkillCategory
    let currentLevel: Double
    let targetLevel: Double
    let progress: Double
    let recentImprovement: Double
    let timeSpent: TimeInterval
    let exercisesCompleted: Int
    let accuracy: Double
    let trend: ProgressTrend
}

enum ProgressTrend: String, Codable, CaseIterable {
    case improving = "improving"
    case stable = "stable"
    case declining = "declining"
    case stagnant = "stagnant"
}

struct LearningTrends: Codable {
    let dailyActivity: [DailyActivityData]
    let weeklyProgress: [WeeklyProgressData]
    let monthlyStats: [MonthlyStatsData]
    let skillDistribution: [SkillDistributionData]
    let performancePatterns: [PerformancePattern]
}

struct DailyActivityData: Codable, Identifiable {
    let id: UUID
    let date: Date
    let xpEarned: Int
    let timeSpent: TimeInterval
    let lessonsCompleted: Int
    let exercisesCompleted: Int
    let accuracy: Double
}

struct WeeklyProgressData: Codable, Identifiable {
    let id: UUID
    let weekStart: Date
    let totalXP: Int
    let averageAccuracy: Double
    let streakDays: Int
    let skillsImproved: [SkillCategory]
    let goalsAchieved: Int
}

struct MonthlyStatsData: Codable, Identifiable {
    let id: UUID
    let month: Date
    let totalXP: Int
    let lessonsCompleted: Int
    let vocabularyLearned: Int
    let conversationTime: TimeInterval
    let achievementsUnlocked: Int
    let averagePerformance: Double
}

struct SkillDistributionData: Codable, Identifiable {
    let id: UUID
    let skill: SkillCategory
    let percentage: Double
    let timeSpent: TimeInterval
    let proficiencyLevel: Double
}

struct PerformancePattern: Codable, Identifiable {
    let id: UUID
    let pattern: PatternType
    let description: String
    let confidence: Double
    let recommendation: String
    let timeframe: TimeInterval
}

enum PatternType: String, Codable {
    case peakPerformance = "peak_performance"
    case learningPlateau = "learning_plateau"
    case rapidImprovement = "rapid_improvement"
    case consistentProgress = "consistent_progress"
    case irregularStudy = "irregular_study"
}

struct AchievementSummary: Codable, Identifiable {
    let id: UUID
    let title: String
    let category: AchievementCategory
    let tier: AchievementTier
    let earnedDate: Date
    let xpReward: Int
}

// Note: LearningGoal is defined in AdaptiveLearningModels.swift
// Using typealias to resolve ambiguity
typealias DashboardLearningGoal = LearningGoal

enum DashboardGoalType: String, Codable {
    case xpTarget = "xp_target"
    case streakTarget = "streak_target"
    case skillMastery = "skill_mastery"
    case lessonCompletion = "lesson_completion"
    case vocabularyGoal = "vocabulary_goal"
    case conversationTime = "conversation_time"
}

enum DashboardGoalPriority: String, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
}

// MARK: - Service

@MainActor
class LearningAnalyticsDashboardService: ObservableObject {
    @Published var dashboard: LearningDashboard?
    @Published var isLoading = false
    @Published var chartData: [String: [ChartDataPoint]] = [:]
    @Published var insights: [DashboardInsight] = []
    
    private let analyticsService: LearningAnalyticsService
    private let supabaseClient: NIRASupabaseClient
    private let geminiService: GeminiService
    private var cancellables = Set<AnyCancellable>()
    
    init(
        analyticsService: LearningAnalyticsService? = nil,
        supabaseClient: NIRASupabaseClient? = nil,
        geminiService: GeminiService? = nil
    ) {
        self.analyticsService = analyticsService ?? .shared
        self.supabaseClient = supabaseClient ?? .shared
        self.geminiService = geminiService ?? GeminiService.shared
        
        setupObservers()
    }
    
    // MARK: - Dashboard Generation
    
    func generateDashboard(for userId: UUID) async throws {
        isLoading = true
        defer { isLoading = false }
        
        // Collect all user data
        let userProgress = try await collectUserProgress(userId)
        let analyticsData = try await collectAnalyticsData(userId)
        let achievements = try await collectAchievements(userId)
        let goals = try await collectGoals(userId)
        
        // Generate AI insights
        let insights = try await generateAIInsights(
            progress: userProgress,
            analytics: analyticsData
        )
        
        // Create dashboard
        let dashboard = LearningDashboard(
            id: UUID(),
            userId: userId,
            overviewMetrics: generateOverviewMetrics(from: userProgress),
            skillProgress: generateSkillProgress(from: userProgress),
            learningTrends: generateLearningTrends(from: analyticsData),
            achievements: achievements,
            recommendations: insights.recommendations,
            goals: goals,
            lastUpdated: Date()
        )
        
        self.dashboard = dashboard
        self.insights = insights.insights
        
        // Generate chart data
        await generateChartData(from: dashboard)
    }
    
    // MARK: - Data Collection
    
    private func collectUserProgress(_ userId: UUID) async throws -> [String: Any] {
        // Collect user progress from various services
        return [
            "total_xp": 15000,
            "current_streak": 7,
            "lessons_completed": 45,
            "vocabulary_mastered": 250
        ]
    }
    
    private func collectAnalyticsData(_ userId: UUID) async throws -> [[String: Any]] {
        // Get analytics data for the user
        // Note: InteractionEvent is not available, using generic data structure
        return []
    }
    
    private func collectAchievements(_ userId: UUID) async throws -> [AchievementSummary] {
        // Collect user achievements
        return []
    }
    
    private func collectGoals(_ userId: UUID) async throws -> [DashboardLearningGoal] {
        // Collect user goals
        return []
    }
    
    // MARK: - AI Insights Generation
    
    private func generateAIInsights(
        progress: [String: Any],
        analytics: [[String: Any]]
    ) async throws -> (insights: [DashboardInsight], recommendations: [LearningRecommendation]) {
        
        let insightRequest = """
        Analyze learning data and generate insights:
        
        Progress Data: \(progress)
        Recent Activity: \(analytics.count) interactions
        
        Generate:
        1. Key learning insights
        2. Performance patterns
        3. Personalized recommendations
        4. Areas for improvement
        5. Strengths to leverage
        
        Focus on actionable insights that help improve learning outcomes.
        """
        
        let response = try await geminiService.makeGeminiRequest(prompt: insightRequest)
        
        // Parse AI response into insights and recommendations
        let insights = parseInsights(from: response)
        let recommendations = parseRecommendations(from: response)
        
        return (insights, recommendations)
    }
    
    // MARK: - Data Generation
    
    private func generateOverviewMetrics(from progress: [String: Any]) -> OverviewMetrics {
        return OverviewMetrics(
            totalXP: progress["total_xp"] as? Int ?? 0,
            currentStreak: progress["current_streak"] as? Int ?? 0,
            longestStreak: progress["longest_streak"] as? Int ?? 0,
            lessonsCompleted: progress["lessons_completed"] as? Int ?? 0,
            vocabularyMastered: progress["vocabulary_mastered"] as? Int ?? 0,
            conversationMinutes: progress["conversation_minutes"] as? Double ?? 0,
            averageSessionTime: progress["average_session_time"] as? TimeInterval ?? 0,
            weeklyProgress: progress["weekly_progress"] as? Double ?? 0,
            monthlyProgress: progress["monthly_progress"] as? Double ?? 0
        )
    }
    
    private func generateSkillProgress(from progress: [String: Any]) -> [SkillProgressData] {
        return SkillCategory.allCases.map { skill in
            SkillProgressData(
                id: UUID(),
                skill: skill,
                currentLevel: Double.random(in: 1...5),
                targetLevel: 5.0,
                progress: Double.random(in: 0.3...0.9),
                recentImprovement: Double.random(in: 0...0.2),
                timeSpent: TimeInterval.random(in: 3600...18000),
                exercisesCompleted: Int.random(in: 10...50),
                accuracy: Double.random(in: 0.7...0.95),
                trend: ProgressTrend.allCases.randomElement()!
            )
        }
    }
    
    private func generateLearningTrends(from analytics: [[String: Any]]) -> LearningTrends {
        return LearningTrends(
            dailyActivity: generateDailyActivity(),
            weeklyProgress: generateWeeklyProgress(),
            monthlyStats: generateMonthlyStats(),
            skillDistribution: generateSkillDistribution(),
            performancePatterns: generatePerformancePatterns()
        )
    }
    
    private func generateDailyActivity() -> [DailyActivityData] {
        return (0..<30).map { dayOffset in
            DailyActivityData(
                id: UUID(),
                date: Calendar.current.date(byAdding: .day, value: -dayOffset, to: Date())!,
                xpEarned: Int.random(in: 100...800),
                timeSpent: TimeInterval.random(in: 900...3600),
                lessonsCompleted: Int.random(in: 1...5),
                exercisesCompleted: Int.random(in: 5...20),
                accuracy: Double.random(in: 0.7...0.95)
            )
        }
    }
    
    private func generateWeeklyProgress() -> [WeeklyProgressData] {
        return (0..<12).map { weekOffset in
            WeeklyProgressData(
                id: UUID(),
                weekStart: Calendar.current.date(byAdding: .weekOfYear, value: -weekOffset, to: Date())!,
                totalXP: Int.random(in: 2000...5000),
                averageAccuracy: Double.random(in: 0.75...0.92),
                streakDays: Int.random(in: 3...7),
                skillsImproved: SkillCategory.allCases.shuffled().prefix(Int.random(in: 2...5)).map { $0 },
                goalsAchieved: Int.random(in: 1...3)
            )
        }
    }
    
    private func generateMonthlyStats() -> [MonthlyStatsData] {
        return (0..<6).map { monthOffset in
            MonthlyStatsData(
                id: UUID(),
                month: Calendar.current.date(byAdding: .month, value: -monthOffset, to: Date())!,
                totalXP: Int.random(in: 8000...20000),
                lessonsCompleted: Int.random(in: 20...60),
                vocabularyLearned: Int.random(in: 50...150),
                conversationTime: TimeInterval.random(in: 3600...10800),
                achievementsUnlocked: Int.random(in: 2...8),
                averagePerformance: Double.random(in: 0.75...0.9)
            )
        }
    }
    
    private func generateSkillDistribution() -> [SkillDistributionData] {
        let total: Double = 100.0
        var remaining = total
        
        return SkillCategory.allCases.enumerated().map { index, skill in
            let percentage = index == SkillCategory.allCases.count - 1 ? 
                remaining : Double.random(in: 5...min(25, remaining))
            remaining -= percentage
            
            return SkillDistributionData(
                id: UUID(),
                skill: skill,
                percentage: percentage,
                timeSpent: TimeInterval(percentage * 100),
                proficiencyLevel: Double.random(in: 1...5)
            )
        }
    }
    
    private func generatePerformancePatterns() -> [PerformancePattern] {
        return [
            PerformancePattern(
                id: UUID(),
                pattern: .peakPerformance,
                description: "Best performance occurs in morning sessions",
                confidence: 0.85,
                recommendation: "Schedule challenging lessons in the morning",
                timeframe: 7 * 24 * 3600
            ),
            PerformancePattern(
                id: UUID(),
                pattern: .consistentProgress,
                description: "Steady improvement across all skills",
                confidence: 0.92,
                recommendation: "Continue current learning routine",
                timeframe: 30 * 24 * 3600
            )
        ]
    }
    
    // MARK: - Chart Data Generation
    
    private func generateChartData(from dashboard: LearningDashboard) async {
        chartData["daily_xp"] = dashboard.learningTrends.dailyActivity.map { activity in
            ChartDataPoint(
                x: activity.date.timeIntervalSince1970,
                y: Double(activity.xpEarned),
                label: DateFormatter.shortDate.string(from: activity.date)
            )
        }
        
        chartData["skill_progress"] = dashboard.skillProgress.map { skill in
            ChartDataPoint(
                x: Double(skill.skill.hashValue),
                y: skill.progress * 100,
                label: skill.skill.rawValue.capitalized
            )
        }
        
        chartData["weekly_accuracy"] = dashboard.learningTrends.weeklyProgress.map { week in
            ChartDataPoint(
                x: week.weekStart.timeIntervalSince1970,
                y: week.averageAccuracy * 100,
                label: DateFormatter.shortDate.string(from: week.weekStart)
            )
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupObservers() {
        // Note: LearningAnalyticsService doesn't have published properties
        // This would be implemented when the analytics service is updated
    }
    
    private func refreshDashboard() async {
        guard let dashboard = dashboard else { return }
        
        // Refresh dashboard data periodically
        try? await generateDashboard(for: dashboard.userId)
    }
    
    private func parseInsights(from response: String) -> [DashboardInsight] {
        // Parse AI response into insights
        return [
            DashboardInsight(
                id: UUID(),
                title: "Strong Vocabulary Progress",
                description: "Your vocabulary acquisition rate is above average",
                type: .strength,
                confidence: 0.9,
                actionable: true,
                recommendation: "Continue focusing on vocabulary exercises"
            )
        ]
    }
    
    private func parseRecommendations(from response: String) -> [LearningRecommendation] {
        // Parse AI response into recommendations
        return [
            LearningRecommendation(
                id: UUID(),
                type: .skillReview,
                title: "Practice Pronunciation",
                description: "Focus on pronunciation exercises to improve speaking skills",
                priority: .medium,
                targetSkills: [],
                estimatedTime: 15 * 60,
                confidence: 0.8,
                reasoning: "Pronunciation scores show room for improvement",
                createdAt: Date()
            )
        ]
    }
}

// MARK: - Supporting Types

struct ChartDataPoint: Codable, Identifiable {
    let id: UUID
    let x: Double
    let y: Double
    let label: String
    
    init(x: Double, y: Double, label: String) {
        self.id = UUID()
        self.x = x
        self.y = y
        self.label = label
    }
}

struct DashboardInsight: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: InsightType
    let confidence: Double
    let actionable: Bool
    let recommendation: String
}

enum InsightType: String, Codable {
    case strength = "strength"
    case weakness = "weakness"
    case opportunity = "opportunity"
    case pattern = "pattern"
    case achievement = "achievement"
}

// MARK: - Extensions

extension DateFormatter {
    static let shortDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter
    }()
} 