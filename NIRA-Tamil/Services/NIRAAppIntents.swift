// //
// //  NIRA// AppIntents.swift
// //  NIRA
// //
// //  Created by NIRA Team on 1/22/25.
// //  iOS 18 App Intents Integration for Siri and Spotlight
// //
// 
// import Foundation
// import SwiftUI
// 
// // iOS 18 App Intents - will be available when entitlements are enabled
// // import // AppIntents
// 
// // MARK: - App Shortcuts Provider
// 
// // struct NIRAAppShortcutsProvider: AppShortcutsProvider {
//     static var appShortcuts: [// AppShortcut] {
//         // AppShortcut(
//             intent: StartLessonIntent(),
//             phrases: [
//                 "Start a Tamil lesson in \(.applicationName)",
//                 "Begin learning Tamil with \(.applicationName)",
//                 "Practice Tamil in \(.applicationName)"
//             ],
//             shortTitle: "Start Lesson",
//             systemImageName: "book.fill"
//         )
//         
//         // AppShortcut(
//             intent: PronunciationPracticeIntent(),
//             phrases: [
//                 "Practice pronunciation in \(.applicationName)",
//                 "Check my Tamil pronunciation",
//                 "Start pronunciation practice"
//             ],
//             shortTitle: "Pronunciation",
//             systemImageName: "mic.fill"
//         )
//         
//         // AppShortcut(
//             intent: VocabularyReviewIntent(),
//             phrases: [
//                 "Review vocabulary in \(.applicationName)",
//                 "Practice Tamil words",
//                 "Show me today's vocabulary"
//             ],
//             shortTitle: "Vocabulary",
//             systemImageName: "text.book.closed.fill"
//         )
//         
//         // AppShortcut(
//             intent: CulturalExplorationIntent(),
//             phrases: [
//                 "Explore Tamil culture in \(.applicationName)",
//                 "Learn about Tamil traditions",
//                 "Show cultural content"
//             ],
//             shortTitle: "Culture",
//             systemImageName: "globe.asia.australia.fill"
//         )
//         
//         // AppShortcut(
//             intent: ProgressCheckIntent(),
//             phrases: [
//                 "Check my progress in \(.applicationName)",
//                 "Show my learning stats",
//                 "How am I doing with Tamil?"
//             ],
//             shortTitle: "Progress",
//             systemImageName: "chart.line.uptrend.xyaxis"
//         )
//     }
// }
// 
// // MARK: - Start Lesson Intent
// 
// struct StartLessonIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Start Tamil Lesson"
//     static var description = IntentDescription("Start a new Tamil language lesson")
//     static var openAppWhenRun: Bool = true
//     
//     @Parameter(title: "Lesson Type", description: "Type of lesson to start")
//     var lessonType: LessonTypeEntity?
//     
//     @Parameter(title: "Difficulty Level", description: "Difficulty level for the lesson")
//     var difficulty: DifficultyEntity?
//     
//     static var parameterSummary: some ParameterSummary {
//         Summary("Start a \(\.$lessonType) lesson at \(\.$difficulty) level")
//     }
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
//         let selectedLessonType = lessonType?.type ?? .vocabulary
//         let selectedDifficulty = difficulty?.level ?? .beginner
//         
//         // Update app state to start the lesson
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .startLessonFromIntent,
//                 object: nil,
//                 userInfo: [
//                     "lessonType": selectedLessonType,
//                     "difficulty": selectedDifficulty
//                 ]
//             )
//         }
//         
//         return .result(
//             dialog: "Starting your \(selectedLessonType.displayName) lesson at \(selectedDifficulty.displayName) level!",
//             view: LessonStartSnippetView(
//                 lessonType: selectedLessonType,
//                 difficulty: selectedDifficulty
//             )
//         )
//     }
// }
// 
// // MARK: - Pronunciation Practice Intent
// 
// struct PronunciationPracticeIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Practice Pronunciation"
//     static var description = IntentDescription("Start pronunciation practice session")
//     static var openAppWhenRun: Bool = true
//     
//     @Parameter(title: "Word or Phrase", description: "Specific word or phrase to practice")
//     var targetText: String?
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog {
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .startPronunciationPractice,
//                 object: nil,
//                 userInfo: ["targetText": targetText ?? ""]
//             )
//         }
//         
//         let message = if let text = targetText {
//             "Starting pronunciation practice for '\(text)'"
//         } else {
//             "Starting pronunciation practice session"
//         }
//         
//         return .result(dialog: message)
//     }
// }
// 
// // MARK: - Vocabulary Review Intent
// 
// struct VocabularyReviewIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Review Vocabulary"
//     static var description = IntentDescription("Review Tamil vocabulary words")
//     static var openAppWhenRun: Bool = true
//     
//     @Parameter(title: "Category", description: "Vocabulary category to review")
//     var category: VocabCategoryEntity?
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
//         let selectedCategory = category?.category ?? .daily
//         
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .startVocabularyReview,
//                 object: nil,
//                 userInfo: ["category": selectedCategory]
//             )
//         }
//         
//         return .result(
//             dialog: "Starting vocabulary review for \(selectedCategory.displayName)",
//             view: VocabularySnippetView(category: selectedCategory)
//         )
//     }
// }
// 
// // MARK: - Cultural Exploration Intent
// 
// struct CulturalExplorationIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Explore Tamil Culture"
//     static var description = IntentDescription("Explore Tamil cultural content")
//     static var openAppWhenRun: Bool = true
//     
//     @Parameter(title: "Cultural Topic", description: "Specific cultural topic to explore")
//     var topic: CulturalTopicEntity?
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog {
//         let selectedTopic = topic?.topic ?? .festivals
//         
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .startCulturalExploration,
//                 object: nil,
//                 userInfo: ["topic": selectedTopic]
//             )
//         }
//         
//         return .result(dialog: "Exploring Tamil \(selectedTopic.displayName)")
//     }
// }
// 
// // MARK: - Progress Check Intent
// 
// struct ProgressCheckIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Check Learning Progress"
//     static var description = IntentDescription("Check your Tamil learning progress")
//     static var openAppWhenRun: Bool = false
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
//         // Get current progress data
//         let progressData = await getCurrentProgress()
//         
//         return .result(
//             dialog: "You've completed \(progressData.completedLessons) lessons with a \(progressData.averageScore)% average score. Keep up the great work!",
//             view: ProgressSnippetView(progress: progressData)
//         )
//     }
//     
//     private func getCurrentProgress() async -> ProgressData {
//         // Mock progress data - replace with actual data fetching
//         return ProgressData(
//             completedLessons: 15,
//             totalLessons: 30,
//             averageScore: 87,
//             currentStreak: 7,
//             totalStudyTime: 1250 // minutes
//         )
//     }
// }
// 
// // MARK: - Entity Definitions
// 
// struct LessonTypeEntity: // AppEntity {
//     static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Lesson Type")
//     static var defaultQuery = LessonTypeQuery()
//     
//     let id: String
//     let type: LessonType
//     
//     var displayRepresentation: DisplayRepresentation {
//         DisplayRepresentation(title: "\(type.displayName)")
//     }
//     
//     enum LessonType: String, CaseIterable {
//         case vocabulary = "vocabulary"
//         case grammar = "grammar"
//         case conversation = "conversation"
//         case pronunciation = "pronunciation"
//         case cultural = "cultural"
//         
//         var displayName: String {
//             switch self {
//             case .vocabulary: return "Vocabulary"
//             case .grammar: return "Grammar"
//             case .conversation: return "Conversation"
//             case .pronunciation: return "Pronunciation"
//             case .cultural: return "Cultural"
//             }
//         }
//     }
// }
// 
// struct LessonTypeQuery: EntityQuery {
//     func entities(for identifiers: [String]) async throws -> [LessonTypeEntity] {
//         return identifiers.compactMap { id in
//             guard let type = LessonTypeEntity.LessonType(rawValue: id) else { return nil }
//             return LessonTypeEntity(id: id, type: type)
//         }
//     }
//     
//     func suggestedEntities() async throws -> [LessonTypeEntity] {
//         return LessonTypeEntity.LessonType.allCases.map { type in
//             LessonTypeEntity(id: type.rawValue, type: type)
//         }
//     }
// }
// 
// struct DifficultyEntity: // AppEntity {
//     static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Difficulty Level")
//     static var defaultQuery = DifficultyQuery()
//     
//     let id: String
//     let level: DifficultyLevel
//     
//     var displayRepresentation: DisplayRepresentation {
//         DisplayRepresentation(title: "\(level.displayName)")
//     }
//     
//     enum DifficultyLevel: String, CaseIterable {
//         case beginner = "beginner"
//         case intermediate = "intermediate"
//         case advanced = "advanced"
//         
//         var displayName: String {
//             switch self {
//             case .beginner: return "Beginner"
//             case .intermediate: return "Intermediate"
//             case .advanced: return "Advanced"
//             }
//         }
//     }
// }
// 
// struct DifficultyQuery: EntityQuery {
//     func entities(for identifiers: [String]) async throws -> [DifficultyEntity] {
//         return identifiers.compactMap { id in
//             guard let level = DifficultyEntity.DifficultyLevel(rawValue: id) else { return nil }
//             return DifficultyEntity(id: id, level: level)
//         }
//     }
//     
//     func suggestedEntities() async throws -> [DifficultyEntity] {
//         return DifficultyEntity.DifficultyLevel.allCases.map { level in
//             DifficultyEntity(id: level.rawValue, level: level)
//         }
//     }
// }
// 
// struct VocabCategoryEntity: // AppEntity {
//     static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Vocabulary Category")
//     static var defaultQuery = VocabCategoryQuery()
//     
//     let id: String
//     let category: VocabCategory
//     
//     var displayRepresentation: DisplayRepresentation {
//         DisplayRepresentation(title: "\(category.displayName)")
//     }
//     
//     enum VocabCategory: String, CaseIterable {
//         case daily = "daily"
//         case family = "family"
//         case food = "food"
//         case travel = "travel"
//         case business = "business"
//         case cultural = "cultural"
//         
//         var displayName: String {
//             switch self {
//             case .daily: return "Daily Life"
//             case .family: return "Family"
//             case .food: return "Food"
//             case .travel: return "Travel"
//             case .business: return "Business"
//             case .cultural: return "Cultural"
//             }
//         }
//     }
// }
// 
// struct VocabCategoryQuery: EntityQuery {
//     func entities(for identifiers: [String]) async throws -> [VocabCategoryEntity] {
//         return identifiers.compactMap { id in
//             guard let category = VocabCategoryEntity.VocabCategory(rawValue: id) else { return nil }
//             return VocabCategoryEntity(id: id, category: category)
//         }
//     }
//     
//     func suggestedEntities() async throws -> [VocabCategoryEntity] {
//         return VocabCategoryEntity.VocabCategory.allCases.map { category in
//             VocabCategoryEntity(id: category.rawValue, category: category)
//         }
//     }
// }
// 
// struct CulturalTopicEntity: // AppEntity {
//     static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Cultural Topic")
//     static var defaultQuery = CulturalTopicQuery()
//     
//     let id: String
//     let topic: CulturalTopic
//     
//     var displayRepresentation: DisplayRepresentation {
//         DisplayRepresentation(title: "\(topic.displayName)")
//     }
//     
//     enum CulturalTopic: String, CaseIterable {
//         case festivals = "festivals"
//         case traditions = "traditions"
//         case food = "food"
//         case music = "music"
//         case dance = "dance"
//         case history = "history"
//         
//         var displayName: String {
//             switch self {
//             case .festivals: return "Festivals"
//             case .traditions: return "Traditions"
//             case .food: return "Food Culture"
//             case .music: return "Music"
//             case .dance: return "Dance"
//             case .history: return "History"
//             }
//         }
//     }
// }
// 
// struct CulturalTopicQuery: EntityQuery {
//     func entities(for identifiers: [String]) async throws -> [CulturalTopicEntity] {
//         return identifiers.compactMap { id in
//             guard let topic = CulturalTopicEntity.CulturalTopic(rawValue: id) else { return nil }
//             return CulturalTopicEntity(id: id, topic: topic)
//         }
//     }
//     
//     func suggestedEntities() async throws -> [CulturalTopicEntity] {
//         return CulturalTopicEntity.CulturalTopic.allCases.map { topic in
//             CulturalTopicEntity(id: topic.rawValue, topic: topic)
//         }
//     }
// }
// 
// // MARK: - Supporting Models
// 
// struct ProgressData {
//     let completedLessons: Int
//     let totalLessons: Int
//     let averageScore: Int
//     let currentStreak: Int
//     let totalStudyTime: Int // in minutes
//     
//     var progressPercentage: Double {
//         return Double(completedLessons) / Double(totalLessons) * 100
//     }
// }
// 
// // MARK: - Notification Names
// 
// extension Notification.Name {
//     static let startLessonFromIntent = Notification.Name("startLessonFromIntent")
//     static let startPronunciationPractice = Notification.Name("startPronunciationPractice")
//     static let startVocabularyReview = Notification.Name("startVocabularyReview")
//     static let startCulturalExploration = Notification.Name("startCulturalExploration")
// }
