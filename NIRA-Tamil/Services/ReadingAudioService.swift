import Foundation
import AVFoundation
import Supabase

// MARK: - Reading Audio Service

@MainActor
class ReadingAudioService: NSObject, ObservableObject {
    
    // MARK: - Singleton
    static let shared = ReadingAudioService()
    
    // MARK: - Published Properties
    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var statusMessage = ""
    @Published var generatedAudioCount = 0
    @Published var currentlyPlaying: String?
    @Published var isPlaying = false
    @Published var playbackError: String?
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var audioPlayer: AVAudioPlayer?
    private let fileManager = FileManager.default
    
    // Google TTS Configuration - Using approved voices from memories
    private let femaleVoice = "ta-IN-Chirp3-HD-Erinome" // Premium Tamil Female
    private let maleVoice = "ta-IN-Chirp3-HD-Iapetus"   // Premium Tamil Male
    private let languageCode = "ta-IN"
    private let audioFormat = "MP3"
    private let speakingRate = 0.9 // Slightly slower for learning
    private let pitch = 0.0
    
    // Service Account Management - Multiple keys to avoid throttling
    private var currentServiceAccountIndex = 0
    private let serviceAccountPaths = [
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-7e3f3c2b36fa.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-95832b0001f9.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-df136a7cd82d.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-e5a76dc745e2.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-e791986c718e.json"
    ]
    
    // Local storage
    private let localAudioDirectory: URL
    private let cacheDirectory: URL
    
    // MARK: - Initialization
    private override init() {
        // Initialize Supabase client
        if APIKeys.supabaseConfigured {
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: APIKeys.supabaseURL)!,
                supabaseKey: APIKeys.supabaseAnonKey
            )
        } else {
            // Mock client for development
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: "https://mock.supabase.co")!,
                supabaseKey: "mock-key"
            )
        }
        
        // Setup local directories
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        localAudioDirectory = documentsPath.appendingPathComponent("ReadingAudio")
        cacheDirectory = documentsPath.appendingPathComponent("AudioCache")
        
        // Create directories if they don't exist
        try? fileManager.createDirectory(at: localAudioDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        
        print("📖 ReadingAudioService initialized")
    }
    
    // MARK: - Audio Generation
    
    /// Generate audio for all reading content
    func generateAllReadingAudio() async {
        isGenerating = true
        generationProgress = 0.0
        generatedAudioCount = 0
        statusMessage = "Loading reading content..."
        
        do {
            // Fetch all reading content from database
            let readingContent = try await fetchReadingContent()
            
            statusMessage = "Generating audio for \(readingContent.count) reading items..."
            
            for (index, content) in readingContent.enumerated() {
                do {
                    // Generate audio for this content
                    let audioResult = try await generateReadingAudio(for: content)
                    
                    // Update content with audio URL in database
                    try await updateContentWithAudioURL(contentId: content.contentId, audioURL: audioResult.audioURL)
                    
                    generatedAudioCount += 1
                    generationProgress = Double(index + 1) / Double(readingContent.count)
                    statusMessage = "Generated \(index + 1)/\(readingContent.count): \(content.titleEnglish)"
                    
                    // Small delay to avoid rate limiting
                    try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                    
                } catch {
                    print("❌ Failed to generate audio for \(content.titleEnglish): \(error)")
                    // Continue with next item
                }
            }
            
            statusMessage = "Audio generation completed! Generated \(generatedAudioCount) files."
            
        } catch {
            statusMessage = "Failed to generate audio: \(error.localizedDescription)"
            print("❌ Audio generation failed: \(error)")
        }
        
        isGenerating = false
    }
    
    /// Generate audio for a single reading content item
    func generateReadingAudio(for content: ReadingContent) async throws -> ReadingAudioResult {
        statusMessage = "Generating audio for: \(content.titleEnglish)"
        
        // Determine voice based on content type or use female as default
        let voice = determineVoice(for: content)
        
        // Generate audio data
        let audioData = try await generateAudioData(
            text: content.contentTamil,
            voice: voice,
            filename: "reading_\(content.contentId)"
        )
        
        // Upload to Supabase Storage
        let audioURL = try await uploadAudioToSupabase(
            audioData: audioData,
            filename: "reading_\(content.contentId).mp3"
        )
        
        print("✅ Generated reading audio for: \(content.titleEnglish)")
        
        return ReadingAudioResult(
            contentId: content.contentId,
            audioURL: audioURL,
            voice: voice,
            audioData: audioData
        )
    }
    
    // MARK: - Audio Playback
    
    /// Play audio for reading content
    func playReadingAudio(for content: ReadingContent) async {
        // Stop any currently playing audio
        stopAudio()
        
        // Reset error state
        playbackError = nil
        currentlyPlaying = content.contentId
        
        // Check if we have audio URL
        guard let audioURL = content.audioUrl, !audioURL.isEmpty else {
            // Generate audio on demand if not available
            do {
                statusMessage = "Generating audio..."
                let audioResult = try await generateReadingAudio(for: content)
                await playAudioFromURL(audioResult.audioURL)
            } catch {
                playbackError = "Failed to generate audio: \(error.localizedDescription)"
                currentlyPlaying = nil
            }
            return
        }
        
        // Play existing audio
        await playAudioFromURL(audioURL)
    }
    
    /// Play audio from URL (local or remote)
    private func playAudioFromURL(_ urlString: String) async {
        if urlString.hasPrefix("http://") || urlString.hasPrefix("https://") {
            // Remote URL - download and cache
            await playRemoteAudio(from: urlString)
        } else {
            // Local file
            if let localURL = getLocalAudioURL(from: urlString) {
                playLocalAudio(url: localURL)
            } else {
                playbackError = "Audio file not found"
                currentlyPlaying = nil
            }
        }
    }
    
    /// Stop audio playback
    func stopAudio() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        currentlyPlaying = nil
    }
    
    // MARK: - Private Methods
    
    private func fetchReadingContent() async throws -> [ReadingContent] {
        guard APIKeys.supabaseConfigured else {
            // Return mock data for development
            return []
        }
        
        let response: [ReadingContent] = try await supabase
            .from("reading_content")
            .select()
            .eq("is_active", value: true)
            .execute()
            .value
        
        return response
    }
    
    private func generateAudioData(text: String, voice: String, filename: String) async throws -> Data {
        guard let serviceAccount = getCurrentServiceAccount() else {
            throw ReadingAudioError.noServiceAccount
        }
        
        // Get access token (simplified for now)
        let accessToken = try await getAccessToken(serviceAccount: serviceAccount)
        
        // Call Google Cloud TTS API
        let audioData = try await callGoogleTTSAPI(text: text, voice: voice, accessToken: accessToken)
        
        // Save to local file for caching
        let localURL = localAudioDirectory.appendingPathComponent("\(filename).mp3")
        try audioData.write(to: localURL)
        
        return audioData
    }
    
    private func callGoogleTTSAPI(text: String, voice: String, accessToken: String) async throws -> Data {
        let url = URL(string: "https://texttospeech.googleapis.com/v1/text:synthesize")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody: [String: Any] = [
            "input": ["text": text],
            "voice": [
                "languageCode": languageCode,
                "name": voice
            ],
            "audioConfig": [
                "audioEncoding": audioFormat,
                "speakingRate": speakingRate,
                "pitch": pitch
            ]
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw ReadingAudioError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 429 {
                // Rate limited, rotate account and retry
                rotateServiceAccount()
                return try await callGoogleTTSAPI(text: text, voice: voice, accessToken: accessToken)
            }
            throw ReadingAudioError.apiError(httpResponse.statusCode)
        }
        
        let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        guard let audioContent = jsonResponse?["audioContent"] as? String,
              let audioData = Data(base64Encoded: audioContent) else {
            throw ReadingAudioError.invalidAudioData
        }
        
        return audioData
    }
    
    private func uploadAudioToSupabase(audioData: Data, filename: String) async throws -> String {
        guard APIKeys.supabaseConfigured else {
            // Return mock URL for development
            return "https://mock.supabase.co/storage/v1/object/public/audio/\(filename)"
        }
        
        let filePath = "reading_content/\(filename)"
        
        try await supabase.storage
            .from("audio")
            .upload(filePath, data: audioData, options: FileOptions(contentType: "audio/mpeg"))
        
        let publicURL = try supabase.storage
            .from("audio")
            .getPublicURL(path: filePath)
        
        return publicURL.absoluteString
    }
    
    private func updateContentWithAudioURL(contentId: String, audioURL: String) async throws {
        guard APIKeys.supabaseConfigured else { return }
        
        try await supabase
            .from("reading_content")
            .update(["audio_url": audioURL])
            .eq("content_id", value: contentId)
            .execute()
    }
    
    private func determineVoice(for content: ReadingContent) -> String {
        // Use female voice as default, could add logic for male/female selection
        return femaleVoice
    }
    
    private func getCurrentServiceAccount() -> [String: Any]? {
        let currentPath = serviceAccountPaths[currentServiceAccountIndex]
        
        guard fileManager.fileExists(atPath: currentPath),
              let data = try? Data(contentsOf: URL(fileURLWithPath: currentPath)),
              let serviceAccount = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }
        
        return serviceAccount
    }
    
    private func rotateServiceAccount() {
        currentServiceAccountIndex = (currentServiceAccountIndex + 1) % serviceAccountPaths.count
        print("🔄 Rotated to service account \(currentServiceAccountIndex + 1)")
    }
    
    private func getAccessToken(serviceAccount: [String: Any]) async throws -> String {
        // Simplified token generation - in production, implement proper JWT
        return "demo_access_token"
    }
    
    private func playRemoteAudio(from urlString: String) async {
        // Implementation similar to existing AudioPlayerService
        // Download, cache, and play
        print("🌐 Playing remote audio: \(urlString)")
    }
    
    private func playLocalAudio(url: URL) {
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            if audioPlayer?.play() == true {
                isPlaying = true
                playbackError = nil
                print("🔊 Playing reading audio: \(url.lastPathComponent)")
            }
        } catch {
            playbackError = "Failed to play audio: \(error.localizedDescription)"
            currentlyPlaying = nil
        }
    }
    
    private func getLocalAudioURL(from urlString: String) -> URL? {
        // Check local audio directory
        let filename = URL(string: urlString)?.lastPathComponent ?? urlString
        let localURL = localAudioDirectory.appendingPathComponent(filename)
        
        if fileManager.fileExists(atPath: localURL.path) {
            return localURL
        }
        
        return nil
    }
}

// MARK: - AVAudioPlayerDelegate

extension ReadingAudioService: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            isPlaying = false
            currentlyPlaying = nil

            if flag {
                print("✅ Audio playback completed successfully")
            } else {
                playbackError = "Audio playback was interrupted"
            }
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            isPlaying = false
            currentlyPlaying = nil
            playbackError = "Audio decode error: \(error?.localizedDescription ?? "Unknown error")"
            print("❌ Audio decode error: \(error?.localizedDescription ?? "Unknown")")
        }
    }
}

// MARK: - Data Models

struct ReadingAudioResult {
    let contentId: String
    let audioURL: String
    let voice: String
    let audioData: Data
}

enum ReadingAudioError: LocalizedError {
    case noServiceAccount
    case invalidResponse
    case apiError(Int)
    case invalidAudioData
    case uploadFailed
    
    var errorDescription: String? {
        switch self {
        case .noServiceAccount:
            return "No Google TTS service account available"
        case .invalidResponse:
            return "Invalid response from Google TTS API"
        case .apiError(let code):
            return "Google TTS API error: \(code)"
        case .invalidAudioData:
            return "Invalid audio data received"
        case .uploadFailed:
            return "Failed to upload audio to storage"
        }
    }
}
