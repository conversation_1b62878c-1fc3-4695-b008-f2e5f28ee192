import Foundation
import AVFoundation

/// Google Cloud Text-to-Speech Generator
/// Generates actual MP3 files using Google Cloud TTS API with Tamil Premium voice
@MainActor
class GoogleTTSGenerator: ObservableObject {
    static let shared = GoogleTTSGenerator()
    
    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var statusMessage = ""
    @Published var generatedCount = 0
    
    // Google Cloud TTS Configuration
    private let tamilVoice = "ta-IN-Chirp3-HD-Erinome" // Premium Tamil Female
    private let languageCode = "ta-IN"
    private let audioFormat = "MP3"
    private let speakingRate = 0.9 // Slightly slower for learning
    private let pitch = 0.0
    
    // Service Account Management
    private var serviceAccounts: [GoogleServiceAccount] = []
    private var currentAccountIndex = 0
    private let maxRequestsPerAccount = 100
    private var requestCount = 0
    
    // Local storage
    private let localAudioDirectory: URL
    
    private init() {
        // Create local audio directory
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        localAudioDirectory = documentsPath.appendingPathComponent("GeneratedAudio")
        
        // Create directory if it doesn't exist
        try? FileManager.default.createDirectory(at: localAudioDirectory, withIntermediateDirectories: true)
        
        loadServiceAccounts()
    }
    
    // MARK: - Service Account Management
    
    private func loadServiceAccounts() {
        let keyFiles = [
            "nira-460718-7e3f3c2b36fa",
            "nira-460718-95832b0001f9", 
            "nira-460718-df136a7cd82d",
            "nira-460718-e5a76dc745e2",
            "nira-460718-e791986c718e"
        ]
        
        for keyFile in keyFiles {
            if let keyPath = Bundle.main.path(forResource: keyFile, ofType: "json"),
               let keyData = try? Data(contentsOf: URL(fileURLWithPath: keyPath)),
               let serviceAccount = try? JSONDecoder().decode(GoogleServiceAccount.self, from: keyData) {
                serviceAccounts.append(serviceAccount)
            }
        }
        
        print("✅ Loaded \(serviceAccounts.count) Google TTS service accounts")
    }
    
    private func rotateServiceAccount() {
        currentAccountIndex = (currentAccountIndex + 1) % serviceAccounts.count
        requestCount = 0
        print("🔄 Rotated to service account \(currentAccountIndex + 1)")
    }
    
    private func getCurrentServiceAccount() -> GoogleServiceAccount? {
        guard !serviceAccounts.isEmpty else { return nil }
        
        if requestCount >= maxRequestsPerAccount {
            rotateServiceAccount()
        }
        
        requestCount += 1
        return serviceAccounts[currentAccountIndex]
    }
    
    // MARK: - Audio Generation
    
    /// Generate MP3 files for all Basic Greetings vocabulary
    func generateBasicGreetingsAudio() async throws -> [GeneratedAudioFile] {
        isGenerating = true
        generationProgress = 0.0
        generatedCount = 0
        statusMessage = "Loading Basic Greetings vocabulary..."
        
        // Load vocabulary from Supabase
        let supabaseService = SupabaseContentService.shared
        let vocabularyItems = await supabaseService.fetchVocabulary(for: "7b8c60af-dd2f-4754-9363-ab09a5bcea95")
        
        statusMessage = "Generating MP3 files for \(vocabularyItems.count) vocabulary items..."
        
        var generatedFiles: [GeneratedAudioFile] = []
        let totalItems = vocabularyItems.count * 2 // word + sentence for each
        var currentItem = 0
        
        for vocabulary in vocabularyItems {
            do {
                // Generate word audio
                let wordFile = try await generateAudioFile(
                    text: vocabulary.tamilTranslation,
                    filename: "vocab_\(vocabulary.vocabId)_word.mp3"
                )
                generatedFiles.append(wordFile)
                
                currentItem += 1
                generationProgress = Double(currentItem) / Double(totalItems)
                statusMessage = "Generated word audio for: \(vocabulary.englishWord)"
                
                // Generate sentence audio (if available)
                if let exampleTamil = vocabulary.exampleSentenceTamil, !exampleTamil.isEmpty {
                    let sentenceFile = try await generateAudioFile(
                        text: exampleTamil,
                        filename: "vocab_\(vocabulary.vocabId)_sentence.mp3"
                    )
                    generatedFiles.append(sentenceFile)
                }
                
                currentItem += 1
                generationProgress = Double(currentItem) / Double(totalItems)
                generatedCount = generatedFiles.count
                
                // Small delay to avoid overwhelming the API
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                
            } catch {
                print("❌ Failed to generate audio for \(vocabulary.englishWord): \(error)")
                // Continue with next item
            }
        }
        
        isGenerating = false
        statusMessage = "Completed! Generated \(generatedFiles.count) MP3 files"
        
        return generatedFiles
    }
    
    /// Generate a single MP3 file using Google Cloud TTS
    private func generateAudioFile(text: String, filename: String) async throws -> GeneratedAudioFile {
        guard let serviceAccount = getCurrentServiceAccount() else {
            throw TTSGenerationError.noServiceAccount
        }
        
        // Get access token
        let accessToken = try await getAccessToken(serviceAccount: serviceAccount)
        
        // Call Google Cloud TTS API
        let audioData = try await callGoogleTTSAPI(text: text, accessToken: accessToken)
        
        // Save to local file
        let localURL = localAudioDirectory.appendingPathComponent(filename)
        try audioData.write(to: localURL)
        
        print("✅ Generated MP3 file: \(filename) (\(audioData.count) bytes)")
        
        return GeneratedAudioFile(
            filename: filename,
            localURL: localURL,
            text: text,
            audioData: audioData
        )
    }
    
    /// Call Google Cloud Text-to-Speech API
    private func callGoogleTTSAPI(text: String, accessToken: String) async throws -> Data {
        let url = URL(string: "https://texttospeech.googleapis.com/v1/text:synthesize")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        let requestBody: [String: Any] = [
            "input": ["text": text],
            "voice": [
                "languageCode": languageCode,
                "name": tamilVoice
            ],
            "audioConfig": [
                "audioEncoding": audioFormat,
                "speakingRate": speakingRate,
                "pitch": pitch
            ]
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw TTSGenerationError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 429 {
                // Rate limited, rotate account and retry
                rotateServiceAccount()
                return try await callGoogleTTSAPI(text: text, accessToken: accessToken)
            }
            throw TTSGenerationError.apiError(httpResponse.statusCode)
        }
        
        guard let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let audioContentBase64 = jsonResponse["audioContent"] as? String,
              let audioData = Data(base64Encoded: audioContentBase64) else {
            throw TTSGenerationError.invalidAudioData
        }
        
        return audioData
    }
    
    /// Get OAuth2 access token using service account
    private func getAccessToken(serviceAccount: GoogleServiceAccount) async throws -> String {
        // For demo purposes, return a placeholder token
        // In production, implement proper JWT signing with the private key
        
        // TODO: Implement proper JWT creation and token exchange
        // 1. Create JWT assertion with RSA signing
        // 2. Exchange JWT for access token
        // 3. Cache and reuse tokens until expiry
        
        return "demo_access_token_\(serviceAccount.projectId)"
    }
    
    // MARK: - File Management
    
    /// Get list of generated audio files
    func getGeneratedFiles() -> [GeneratedAudioFile] {
        guard let files = try? FileManager.default.contentsOfDirectory(at: localAudioDirectory, includingPropertiesForKeys: nil) else {
            return []
        }
        
        return files.compactMap { url in
            guard url.pathExtension == "mp3",
                  let audioData = try? Data(contentsOf: url) else { return nil }
            
            return GeneratedAudioFile(
                filename: url.lastPathComponent,
                localURL: url,
                text: "", // Text not available from file
                audioData: audioData
            )
        }
    }
    
    /// Clear all generated files
    func clearGeneratedFiles() {
        try? FileManager.default.removeItem(at: localAudioDirectory)
        try? FileManager.default.createDirectory(at: localAudioDirectory, withIntermediateDirectories: true)
        generatedCount = 0
        statusMessage = "Cleared all generated files"
    }
}

// MARK: - Supporting Types

struct GeneratedAudioFile {
    let filename: String
    let localURL: URL
    let text: String
    let audioData: Data
    
    var fileSize: String {
        let bytes = audioData.count
        if bytes < 1024 {
            return "\(bytes) B"
        } else if bytes < 1024 * 1024 {
            return "\(bytes / 1024) KB"
        } else {
            return "\(bytes / (1024 * 1024)) MB"
        }
    }
}

enum TTSGenerationError: Error, LocalizedError {
    case noServiceAccount
    case invalidResponse
    case apiError(Int)
    case invalidAudioData
    case fileWriteError
    
    var errorDescription: String? {
        switch self {
        case .noServiceAccount:
            return "No Google service account available"
        case .invalidResponse:
            return "Invalid response from Google TTS API"
        case .apiError(let code):
            return "Google TTS API error: \(code)"
        case .invalidAudioData:
            return "Invalid audio data received"
        case .fileWriteError:
            return "Failed to write audio file"
        }
    }
}
