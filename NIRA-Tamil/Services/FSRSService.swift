//
//  FSRSService.swift
//  NIRA
//
//  Free Spaced Repetition Scheduler (FSRS) Implementation
//  Optimizes learning intervals based on memory science
//

import Foundation
import Combine
import SwiftUI

@MainActor
class FSRSService: ObservableObject, @unchecked Sendable {
    @Published var reviewSchedule: [ReviewItem] = []
    @Published var dailyReviewCount: Int = 0
    @Published var streakCount: Int = 0
    
    @MainActor private let supabaseService = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    // FSRS Algorithm Parameters (optimized for language learning)
    private let fsrsParameters = FSRSParameters(
        requestRetention: 0.9,      // Target 90% retention rate
        maximumInterval: 36500,     // ~100 years maximum
        weights: [
            0.4072, 1.1829, 3.1262, 15.4722, 7.2102,
            0.5316, 1.0651, 0.0234, 1.616, 0.1544,
            1.0824, 1.9813, 0.0953, 0.2975, 2.2042,
            0.2407, 2.9466, 0.5034, 0.6567
        ]
    )
    
    init() {
        loadReviewSchedule()
        setupDailyReviewTracking()
    }
    
    // MARK: - Core FSRS Algorithm
    
    func scheduleReview(for item: LearningItem, rating: ReviewRating) async -> ReviewItem {
        let currentCard = await getOrCreateCard(for: item)
        let updatedCard = calculateNextReview(card: currentCard, rating: rating)
        
        await saveCard(updatedCard)
        await logReview(card: updatedCard, rating: rating)
        
        return ReviewItem(
            id: UUID().uuidString,
            learningItemId: item.id,
            cardId: updatedCard.id.uuidString,
            nextReviewDate: updatedCard.due,
            difficulty: updatedCard.difficulty,
            stability: updatedCard.stability,
            retrievability: updatedCard.retrievability
        )
    }
    
    private func calculateNextReview(card: FSRSCard, rating: ReviewRating) -> FSRSCard {
        let currentTime = Date()
        var updatedCard = card
        
        // Calculate elapsed days since last review
        let elapsedDays = max(0, currentTime.timeIntervalSince(card.lastReview) / 86400)
        
        // Update retrievability based on elapsed time
        updatedCard.retrievability = calculateRetrievability(
            elapsedDays: elapsedDays,
            stability: card.stability
        )
        
        // Apply FSRS algorithm based on rating
        switch rating {
        case .again:
            updatedCard = handleAgainRating(card: updatedCard)
        case .hard:
            updatedCard = handleHardRating(card: updatedCard)
        case .good:
            updatedCard = handleGoodRating(card: updatedCard)
        case .easy:
            updatedCard = handleEasyRating(card: updatedCard)
        }
        
        // Update review metadata
        updatedCard.lastReview = currentTime
        updatedCard.reps += 1
        updatedCard.lapses += (rating == .again) ? 1 : 0
        
        return updatedCard
    }
    
    private func calculateRetrievability(elapsedDays: Double, stability: Double) -> Double {
        return pow(1 + elapsedDays / (9 * stability), -1)
    }
    
    private func handleAgainRating(card: FSRSCard) -> FSRSCard {
        var updatedCard = card
        
        // Increase difficulty significantly
        updatedCard.difficulty = min(10.0, card.difficulty + 0.2)
        
        // Reset stability to initial value
        updatedCard.stability = calculateInitialStability(difficulty: updatedCard.difficulty)
        
        // Schedule for immediate re-study (1 minute)
        updatedCard.due = Date().addingTimeInterval(60)
        updatedCard.state = .learning
        
        return updatedCard
    }
    
    private func handleHardRating(card: FSRSCard) -> FSRSCard {
        var updatedCard = card
        
        // Slight difficulty increase
        updatedCard.difficulty = min(10.0, card.difficulty + 0.15)
        
        // Moderate stability increase
        let stabilityIncrease = calculateStabilityIncrease(
            currentStability: card.stability,
            retrievability: card.retrievability,
            rating: .hard
        )
        updatedCard.stability = card.stability * stabilityIncrease
        
        // Schedule review with reduced interval
        let interval = max(1, Int(updatedCard.stability * 0.6))
        updatedCard.due = Calendar.current.date(byAdding: .day, value: interval, to: Date()) ?? Date()
        updatedCard.state = .review
        
        return updatedCard
    }
    
    private func handleGoodRating(card: FSRSCard) -> FSRSCard {
        var updatedCard = card
        
        // Slight difficulty decrease
        updatedCard.difficulty = max(1.0, card.difficulty - 0.15)
        
        // Standard stability increase
        let stabilityIncrease = calculateStabilityIncrease(
            currentStability: card.stability,
            retrievability: card.retrievability,
            rating: .good
        )
        updatedCard.stability = card.stability * stabilityIncrease
        
        // Schedule review with normal interval
        let interval = max(1, Int(updatedCard.stability))
        updatedCard.due = Calendar.current.date(byAdding: .day, value: interval, to: Date()) ?? Date()
        updatedCard.state = .review
        
        return updatedCard
    }
    
    private func handleEasyRating(card: FSRSCard) -> FSRSCard {
        var updatedCard = card
        
        // Decrease difficulty more significantly
        updatedCard.difficulty = max(1.0, card.difficulty - 0.2)
        
        // Larger stability increase
        let stabilityIncrease = calculateStabilityIncrease(
            currentStability: card.stability,
            retrievability: card.retrievability,
            rating: .easy
        )
        updatedCard.stability = card.stability * stabilityIncrease * 1.3
        
        // Schedule review with extended interval
        let interval = max(1, Int(updatedCard.stability * 1.3))
        updatedCard.due = Calendar.current.date(byAdding: .day, value: interval, to: Date()) ?? Date()
        updatedCard.state = .review
        
        return updatedCard
    }
    
    private func calculateStabilityIncrease(currentStability: Double, retrievability: Double, rating: ReviewRating) -> Double {
        let factor = exp(fsrsParameters.weights[8] * 
            (Double(rating.rawValue) - 3) + 
            fsrsParameters.weights[9] * (retrievability - 0.9))
        return max(0.1, factor)
    }
    
    private func calculateInitialStability(difficulty: Double) -> Double {
        return max(0.1, fsrsParameters.weights[0] + fsrsParameters.weights[1] * difficulty)
    }
    
    // MARK: - Review Management
    
    func getDueReviews() async -> [ReviewItem] {
        let currentDate = Date()
        return reviewSchedule.filter { $0.nextReviewDate <= currentDate }
    }
    
    func getUpcomingReviews(days: Int = 7) async -> [ReviewItem] {
        let endDate = Calendar.current.date(byAdding: .day, value: days, to: Date()) ?? Date()
        return reviewSchedule.filter { 
            $0.nextReviewDate > Date() && $0.nextReviewDate <= endDate 
        }.sorted { $0.nextReviewDate < $1.nextReviewDate }
    }
    
    func markReviewCompleted(reviewId: String, rating: ReviewRating) async {
        guard let reviewIndex = reviewSchedule.firstIndex(where: { $0.id == reviewId }) else { return }
        
        // Update daily review count
        DispatchQueue.main.async {
            self.dailyReviewCount += 1
        }
        
        // Remove completed review and reschedule if needed
        reviewSchedule.remove(at: reviewIndex)
        
        // Update streak tracking
        await updateStreakCount()
    }
    
    // MARK: - Data Persistence
    
    private func loadReviewSchedule() {
        Task {
            do {
                let reviews = try await supabaseService.fetchReviewSchedule()
                DispatchQueue.main.async {
                    // Convert SupabaseReviewLog to ReviewItem
                    self.reviewSchedule = reviews.map { log in
                        ReviewItem(
                            id: log.id.uuidString,
                            learningItemId: log.cardId.uuidString,
                            cardId: log.cardId.uuidString,
                            nextReviewDate: log.due,
                            difficulty: log.difficulty,
                            stability: log.stability,
                            retrievability: 0.9
                        )
                    }
                }
            } catch {
                print("Failed to load review schedule: \(error)")
            }
        }
    }
    
    private func getOrCreateCard(for item: LearningItem) async -> FSRSCard {
        if let existingCard = await supabaseService.fetchCard(for: UUID(uuidString: item.id) ?? UUID()) {
            // Convert SupabaseCard to FSRSCard
            return FSRSCard(
                id: UUID(),
                learningItemId: UUID(uuidString: item.id) ?? UUID(),
                due: existingCard.due,
                stability: existingCard.stability,
                difficulty: existingCard.difficulty,
                elapsedDays: existingCard.elapsedDays,
                scheduledDays: existingCard.scheduledDays,
                reps: existingCard.reps,
                lapses: existingCard.lapses,
                state: CardState(rawValue: String(existingCard.state)) ?? .new,
                lastReview: existingCard.lastReview ?? Date(),
                retrievability: 0.9
            )
        }
        
        // Create new card with initial values
        let newCard = FSRSCard(
            id: UUID(),
            learningItemId: UUID(),
            due: Date(),
            stability: calculateInitialStability(difficulty: 5.0),
            difficulty: 5.0,
            elapsedDays: 0,
            scheduledDays: 0,
            reps: 0,
            lapses: 0,
            state: .new,
            lastReview: Date(),
            retrievability: 1.0
        )
        
        await saveCard(newCard)
        return newCard
    }
    
    private func saveCard(_ card: FSRSCard) async {
        do {
            // Convert FSRSCard to SupabaseCard
            let supabaseCard = SupabaseCard(
                itemId: card.learningItemId,
                state: String(card.state.rawValue),
                stability: card.stability,
                difficulty: card.difficulty,
                elapsedDays: card.elapsedDays,
                scheduledDays: card.scheduledDays,
                reps: card.reps,
                lapses: card.lapses,
                lastReview: card.lastReview,
                due: card.due
            )
            try await supabaseService.saveCard(supabaseCard)
        } catch {
            print("Failed to save FSRS card: \(error)")
        }
    }
    
    private func logReview(card: FSRSCard, rating: ReviewRating) async {
        let reviewLog = ReviewLog(
            id: UUID().uuidString,
            cardId: card.id.uuidString,
            rating: rating,
            state: card.state,
            due: card.due,
            stability: card.stability,
            difficulty: card.difficulty,
            elapsedDays: card.elapsedDays,
            lastElapsedDays: card.elapsedDays,
            scheduledDays: card.scheduledDays,
            review: Date()
        )
        
        do {
            // Convert ReviewLog to SupabaseReviewLog
            let supabaseReviewLog = SupabaseReviewLog(
                cardId: card.learningItemId,
                rating: reviewLog.rating.rawValue,
                state: String(reviewLog.state.rawValue),
                due: reviewLog.due,
                stability: reviewLog.stability,
                difficulty: reviewLog.difficulty,
                elapsedDays: reviewLog.elapsedDays,
                lastElapsedDays: reviewLog.lastElapsedDays,
                scheduledDays: reviewLog.scheduledDays,
                reviewTime: Date()
            )
            try await supabaseService.saveReviewLog(supabaseReviewLog)
        } catch {
            print("Failed to log review: \(error)")
        }
    }
    
    // MARK: - Analytics and Insights
    
    func getRetentionRate(days: Int = 30) async -> Double {
        do {
            let logs = try await supabaseService.fetchReviewLogs(lastDays: days)
            let totalReviews = logs.count
            let successfulReviews = logs.filter { $0.rating != ReviewRating.again.rawValue }.count
            
            return totalReviews > 0 ? Double(successfulReviews) / Double(totalReviews) : 0.0
        } catch {
            print("Failed to calculate retention rate: \(error)")
            return 0.0
        }
    }
    
    func getAverageInterval() async -> Double {
        let dueReviews = await getDueReviews()
        let intervals = dueReviews.map { $0.nextReviewDate.timeIntervalSince(Date()) / 86400 }
        return intervals.isEmpty ? 0 : intervals.reduce(0, +) / Double(intervals.count)
    }
    
    func getStudyLoad(days: Int = 7) async -> [Date: Int] {
        let upcoming = await getUpcomingReviews(days: days)
        var loadByDate: [Date: Int] = [:]
        
        let calendar = Calendar.current
        for review in upcoming {
            let dateKey = calendar.startOfDay(for: review.nextReviewDate)
            loadByDate[dateKey, default: 0] += 1
        }
        
        return loadByDate
    }
    
    // MARK: - Helper Methods
    
    private func setupDailyReviewTracking() {
        // Reset daily count at midnight
        Timer.publish(every: 60, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await MainActor.run {
                        let calendar = Calendar.current
                        if calendar.component(.hour, from: Date()) == 0 &&
                           calendar.component(.minute, from: Date()) == 0 {
                            self?.dailyReviewCount = 0
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func updateStreakCount() async {
        // Implementation for streak tracking
        // Would check if user has completed reviews for consecutive days
    }
}

// MARK: - Supporting Models

struct FSRSParameters {
    let requestRetention: Double
    let maximumInterval: Int
    let weights: [Double]
}

struct FSRSCard: Codable {
    let id: UUID
    let learningItemId: UUID
    var due: Date
    var stability: Double
    var difficulty: Double
    var elapsedDays: Int
    var scheduledDays: Int
    var reps: Int
    var lapses: Int
    var state: CardState
    var lastReview: Date
    var retrievability: Double
}

struct ReviewItem: Codable, Identifiable {
    let id: String
    let learningItemId: String
    let cardId: String
    let nextReviewDate: Date
    let difficulty: Double
    let stability: Double
    let retrievability: Double
}

struct ReviewLog: Codable {
    let id: String
    let cardId: String
    let rating: ReviewRating
    let state: CardState
    let due: Date
    let stability: Double
    let difficulty: Double
    let elapsedDays: Int
    let lastElapsedDays: Int
    let scheduledDays: Int
    let review: Date
}

enum ReviewRating: Int, CaseIterable, Codable {
    case again = 1  // Forgot completely
    case hard = 2   // Remembered with difficulty
    case good = 3   // Remembered correctly
    case easy = 4   // Remembered very easily
}

enum CardState: String, CaseIterable, Codable {
    case new = "New"
    case learning = "Learning"
    case review = "Review"
    case relearning = "Relearning"
}

struct LearningItem: Codable {
    let id: String
    let type: LearningItemType
    let content: String
    let translation: String?
    let lessonId: String
    let difficulty: Int
}

enum LearningItemType: String, Codable, CaseIterable {
    case vocabulary = "vocabulary"
    case phrase = "phrase"
    case grammar = "grammar"
    case conversation = "conversation"
} 