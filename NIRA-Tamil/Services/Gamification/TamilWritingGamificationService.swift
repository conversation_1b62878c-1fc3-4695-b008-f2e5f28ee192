//
//  TamilWritingGamificationService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Combine
import GameKit
import Swift<PERSON>

@MainActor
class TamilWritingGamificationService: ObservableObject {
    static let shared = TamilWritingGamificationService()
    
    @Published var userProfile: GamificationProfile?
    @Published var achievements: [Achievement] = []
    @Published var activeChallenges: [Challenge] = []
    @Published var leaderboards: [Leaderboard] = []
    @Published var streakData: StreakData?
    @Published var recentRewards: [Reward] = []
    @Published var socialFeed: [SocialActivity] = []
    @Published var isGameCenterAuthenticated = false
    
    // private let supabase = SupabaseService.shared.client // TODO: Add when SupabaseService is available
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Core Gamification Models
    
    struct GamificationProfile: Codable, Identifiable {
        let id = UUID()
        let userId: String
        let username: String
        var level: Int
        var experience: Int
        var experienceToNextLevel: Int
        var totalPoints: Int
        let rank: String
        let badges: [Badge]
        let statistics: UserStatistics
        let preferences: GamificationPreferences
        let joinedDate: Date
        let lastActiveDate: Date
        
        var progressToNextLevel: Double {
            let currentLevelExp = experience - (level * 1000) // Simplified calculation
            let expNeeded = experienceToNextLevel
            return expNeeded > 0 ? Double(currentLevelExp) / Double(expNeeded) : 1.0
        }
        
        struct Badge: Codable, Identifiable {
            let id = UUID()
            let badgeId: String
            let name: String
            let description: String
            let icon: String
            let rarity: Rarity
            let unlockedDate: Date
            let category: BadgeCategory
            
            enum Rarity: String, Codable, CaseIterable {
                case common = "common"
                case uncommon = "uncommon"
                case rare = "rare"
                case epic = "epic"
                case legendary = "legendary"
                
                var color: Color {
                    switch self {
                    case .common: return .gray
                    case .uncommon: return .green
                    case .rare: return .blue
                    case .epic: return .purple
                    case .legendary: return .orange
                    }
                }
                
                var displayName: String {
                    return rawValue.capitalized
                }
            }
            
            enum BadgeCategory: String, Codable, CaseIterable {
                case writing = "writing"
                case accuracy = "accuracy"
                case speed = "speed"
                case consistency = "consistency"
                case social = "social"
                case special = "special"
                case milestone = "milestone"
                
                var icon: String {
                    switch self {
                    case .writing: return "pencil"
                    case .accuracy: return "target"
                    case .speed: return "speedometer"
                    case .consistency: return "calendar"
                    case .social: return "person.2"
                    case .special: return "star"
                    case .milestone: return "flag"
                    }
                }
            }
        }
        
        struct UserStatistics: Codable {
            let totalCharactersWritten: Int
            let totalPracticeTime: Int // minutes
            let averageAccuracy: Double
            let bestAccuracy: Double
            let longestStreak: Int
            let currentStreak: Int
            let challengesCompleted: Int
            let achievementsUnlocked: Int
            let socialInteractions: Int
            let favoriteCharacters: [String]
            let weakestCharacters: [String]
            let practiceFrequency: Double
            let improvementRate: Double
        }
        
        struct GamificationPreferences: Codable {
            let enableNotifications: Bool
            let enableSocialFeatures: Bool
            let enableCompetitiveMode: Bool
            let preferredChallengeTypes: [Challenge.ChallengeType]
            let privacyLevel: PrivacyLevel
            let motivationStyle: MotivationStyle
            
            enum PrivacyLevel: String, Codable {
                case publicLevel = "public"
                case friends = "friends"
                case privateLevel = "private"
            }
            
            enum MotivationStyle: String, Codable {
                case achievement = "achievement"
                case progress = "progress"
                case competition = "competition"
                case collaboration = "collaboration"
            }
        }
    }
    
    struct Achievement: Codable, Identifiable {
        let id = UUID()
        let achievementId: String
        let title: String
        let description: String
        let icon: String
        let category: Category
        let difficulty: Difficulty
        let points: Int
        let requirements: [Requirement]
        let rewards: [Reward]
        let isUnlocked: Bool
        let unlockedDate: Date?
        let progress: Double
        let isSecret: Bool
        
        enum Category: String, Codable, CaseIterable {
            case firstSteps = "first_steps"
            case mastery = "mastery"
            case consistency = "consistency"
            case speed = "speed"
            case accuracy = "accuracy"
            case social = "social"
            case exploration = "exploration"
            case dedication = "dedication"
            
            var displayName: String {
                switch self {
                case .firstSteps: return "First Steps"
                case .mastery: return "Mastery"
                case .consistency: return "Consistency"
                case .speed: return "Speed"
                case .accuracy: return "Accuracy"
                case .social: return "Social"
                case .exploration: return "Exploration"
                case .dedication: return "Dedication"
                }
            }
            
            var color: Color {
                switch self {
                case .firstSteps: return .green
                case .mastery: return .purple
                case .consistency: return .blue
                case .speed: return .red
                case .accuracy: return .orange
                case .social: return .pink
                case .exploration: return .teal
                case .dedication: return .indigo
                }
            }
        }
        
        enum Difficulty: String, Codable, CaseIterable {
            case bronze = "bronze"
            case silver = "silver"
            case gold = "gold"
            case platinum = "platinum"
            case diamond = "diamond"
            
            var color: Color {
                switch self {
                case .bronze: return .brown
                case .silver: return .gray
                case .gold: return .yellow
                case .platinum: return .cyan
                case .diamond: return .blue
                }
            }
            
            var pointMultiplier: Int {
                switch self {
                case .bronze: return 1
                case .silver: return 2
                case .gold: return 3
                case .platinum: return 5
                case .diamond: return 10
                }
            }
        }
        
        struct Requirement: Codable, Identifiable {
            let id = UUID()
            let type: RequirementType
            let target: Double
            let current: Double
            let description: String
            
            enum RequirementType: String, Codable {
                case charactersWritten = "characters_written"
                case accuracyAchieved = "accuracy_achieved"
                case streakMaintained = "streak_maintained"
                case practiceTime = "practice_time"
                case challengesCompleted = "challenges_completed"
                case socialInteractions = "social_interactions"
                case specificCharacter = "specific_character"
                case levelReached = "level_reached"
            }
            
            var isCompleted: Bool {
                return current >= target
            }
            
            var progress: Double {
                return min(1.0, current / target)
            }
        }
    }
    
    struct Challenge: Codable, Identifiable {
        let id = UUID()
        let challengeId: String
        let title: String
        let description: String
        let icon: String
        let challengeType: ChallengeType
        let difficulty: Difficulty
        let duration: TimeInterval
        let startDate: Date
        let endDate: Date
        let requirements: [ChallengeRequirement]
        let rewards: [Reward]
        let participants: [Participant]
        let isActive: Bool
        let isCompleted: Bool
        let completedDate: Date?
        let leaderboard: [LeaderboardEntry]
        
        enum ChallengeType: String, Codable, CaseIterable {
            case daily = "daily"
            case weekly = "weekly"
            case monthly = "monthly"
            case special = "special"
            case community = "community"
            case personal = "personal"
            
            var displayName: String {
                return rawValue.capitalized
            }
            
            var icon: String {
                switch self {
                case .daily: return "sun.max"
                case .weekly: return "calendar"
                case .monthly: return "calendar.badge.clock"
                case .special: return "star"
                case .community: return "person.3"
                case .personal: return "person"
                }
            }
        }
        
        enum Difficulty: String, Codable, CaseIterable {
            case easy = "easy"
            case medium = "medium"
            case hard = "hard"
            case expert = "expert"
            
            var color: Color {
                switch self {
                case .easy: return .green
                case .medium: return .yellow
                case .hard: return .orange
                case .expert: return .red
                }
            }
            
            var pointMultiplier: Double {
                switch self {
                case .easy: return 1.0
                case .medium: return 1.5
                case .hard: return 2.0
                case .expert: return 3.0
                }
            }
        }
        
        struct ChallengeRequirement: Codable, Identifiable {
            let id = UUID()
            let description: String
            let target: Double
            let current: Double
            let unit: String
            let isCompleted: Bool
            
            var progress: Double {
                return min(1.0, current / target)
            }
        }
        
        struct Participant: Codable, Identifiable {
            let id = UUID()
            let userId: String
            let username: String
            let joinedDate: Date
            let progress: Double
            let isCompleted: Bool
            let rank: Int?
        }
        
        struct LeaderboardEntry: Codable, Identifiable {
            let id = UUID()
            let userId: String
            let username: String
            let score: Double
            let rank: Int
            let completedDate: Date?
        }
    }
    
    struct Reward: Codable, Identifiable {
        let id = UUID()
        let rewardId: String
        let type: RewardType
        let title: String
        let description: String
        let value: Int
        let icon: String
        let rarity: Rarity
        let isCollected: Bool
        let collectedDate: Date?
        let expiryDate: Date?
        
        enum RewardType: String, Codable, CaseIterable {
            case experience = "experience"
            case points = "points"
            case badge = "badge"
            case title = "title"
            case avatar = "avatar"
            case theme = "theme"
            case feature = "feature"
            case currency = "currency"
            
            var displayName: String {
                return rawValue.capitalized
            }
            
            var icon: String {
                switch self {
                case .experience: return "star.fill"
                case .points: return "diamond.fill"
                case .badge: return "shield.fill"
                case .title: return "crown.fill"
                case .avatar: return "person.circle.fill"
                case .theme: return "paintbrush.fill"
                case .feature: return "sparkles"
                case .currency: return "dollarsign.circle.fill"
                }
            }
        }
        
        enum Rarity: String, Codable, CaseIterable {
            case common = "common"
            case uncommon = "uncommon"
            case rare = "rare"
            case epic = "epic"
            case legendary = "legendary"
            
            var color: Color {
                switch self {
                case .common: return .gray
                case .uncommon: return .green
                case .rare: return .blue
                case .epic: return .purple
                case .legendary: return .orange
                }
            }
        }
    }
    
    struct Leaderboard: Codable, Identifiable {
        let id = UUID()
        let leaderboardId: String
        let title: String
        let description: String
        let category: Category
        let timeframe: Timeframe
        let entries: [LeaderboardEntry]
        let lastUpdated: Date
        let isGlobal: Bool
        
        enum Category: String, Codable, CaseIterable {
            case overall = "overall"
            case accuracy = "accuracy"
            case speed = "speed"
            case consistency = "consistency"
            case characters = "characters"
            case challenges = "challenges"
            
            var displayName: String {
                return rawValue.capitalized
            }
            
            var icon: String {
                switch self {
                case .overall: return "trophy"
                case .accuracy: return "target"
                case .speed: return "speedometer"
                case .consistency: return "calendar"
                case .characters: return "textformat.abc"
                case .challenges: return "flag"
                }
            }
        }
        
        enum Timeframe: String, Codable, CaseIterable {
            case daily = "daily"
            case weekly = "weekly"
            case monthly = "monthly"
            case allTime = "all_time"
            
            var displayName: String {
                switch self {
                case .daily: return "Today"
                case .weekly: return "This Week"
                case .monthly: return "This Month"
                case .allTime: return "All Time"
                }
            }
        }
        
        struct LeaderboardEntry: Codable, Identifiable {
            let id = UUID()
            let userId: String
            let username: String
            let score: Double
            let rank: Int
            let change: RankChange
            let avatar: String?
            let title: String?
            let badges: [String]
            
            enum RankChange: String, Codable {
                case up = "up"
                case down = "down"
                case same = "same"
                case new = "new"
                
                var icon: String {
                    switch self {
                    case .up: return "arrow.up"
                    case .down: return "arrow.down"
                    case .same: return "minus"
                    case .new: return "star"
                    }
                }
                
                var color: Color {
                    switch self {
                    case .up: return .green
                    case .down: return .red
                    case .same: return .gray
                    case .new: return .blue
                    }
                }
            }
        }
    }
    
    struct StreakData: Codable {
        let currentStreak: Int
        let longestStreak: Int
        let streakStartDate: Date
        let lastPracticeDate: Date
        let streakType: StreakType
        let milestones: [StreakMilestone]
        let isActive: Bool
        
        enum StreakType: String, Codable {
            case daily = "daily"
            case writing = "writing"
            case accuracy = "accuracy"
            case challenge = "challenge"
        }
        
        struct StreakMilestone: Codable, Identifiable {
            let id = UUID()
            let days: Int
            let title: String
            let reward: Reward
            let isAchieved: Bool
            let achievedDate: Date?
        }
    }
    
    struct SocialActivity: Codable, Identifiable {
        let id = UUID()
        let activityId: String
        let userId: String
        let username: String
        let activityType: ActivityType
        let title: String
        let description: String
        let timestamp: Date
        let data: [String: String]
        var likes: Int
        let comments: [Comment]
        var isLiked: Bool
        
        enum ActivityType: String, Codable {
            case achievement = "achievement"
            case challenge = "challenge"
            case milestone = "milestone"
            case streak = "streak"
            case level = "level"
            case badge = "badge"
            
            var icon: String {
                switch self {
                case .achievement: return "trophy"
                case .challenge: return "flag"
                case .milestone: return "star"
                case .streak: return "flame"
                case .level: return "arrow.up"
                case .badge: return "shield"
                }
            }
        }
        
        struct Comment: Codable, Identifiable {
            let id = UUID()
            let userId: String
            let username: String
            let text: String
            let timestamp: Date
        }
    }
    
    private init() {
        setupGamificationSystem()
        authenticateGameCenter()
    }
    
    // MARK: - Gamification System Setup
    
    private func setupGamificationSystem() {
        loadUserProfile()
        loadAchievements()
        loadActiveChallenges()
        loadLeaderboards()
        loadStreakData()
        loadSocialFeed()
    }
    
    private func authenticateGameCenter() {
        #if !targetEnvironment(simulator)
        GKLocalPlayer.local.authenticateHandler = { viewController, error in
            if let error = error {
                print("Game Center authentication failed: \(error)")
                return
            }
            
            if GKLocalPlayer.local.isAuthenticated {
                self.isGameCenterAuthenticated = true
                print("Game Center authenticated successfully")
            }
        }
        #endif
    }
    
    // MARK: - Achievement System
    
    /// Check and unlock achievements based on user activity
    func checkAchievements(for activity: UserActivity) async {
        let potentialAchievements = achievements.filter { !$0.isUnlocked }
        
        for achievement in potentialAchievements {
            let updatedAchievement = await evaluateAchievement(achievement, for: activity)
            
            if updatedAchievement.isUnlocked && !achievement.isUnlocked {
                await unlockAchievement(updatedAchievement)
            } else if updatedAchievement.progress != achievement.progress {
                await updateAchievementProgress(updatedAchievement)
            }
        }
    }
    
    /// Award experience points and handle level progression
    func awardExperience(_ points: Int, for activity: UserActivity) async {
        guard var profile = userProfile else { return }
        
        let oldLevel = profile.level
        profile.experience += points
        profile.totalPoints += points
        
        // Check for level up
        while profile.experience >= profile.experienceToNextLevel {
            profile.experience -= profile.experienceToNextLevel
            profile.level += 1
            profile.experienceToNextLevel = calculateExperienceForLevel(profile.level + 1)
        }
        
        userProfile = profile
        
        // Handle level up rewards
        if profile.level > oldLevel {
            await handleLevelUp(from: oldLevel, to: profile.level)
        }
        
        // Save to database
        await saveUserProfile(profile)
    }
    
    // MARK: - Challenge System
    
    /// Generate daily challenges based on user's progress and preferences
    func generateDailyChallenges() async -> [Challenge] {
        guard let profile = userProfile else { return [] }
        
        var challenges: [Challenge] = []
        
        // Accuracy challenge
        let accuracyChallenge = Challenge(
            challengeId: "daily_accuracy_\(Date().timeIntervalSince1970)",
            title: "Accuracy Master",
            description: "Achieve 85% accuracy in 10 character attempts",
            icon: "target",
            challengeType: .daily,
            difficulty: .medium,
            duration: 86400, // 24 hours
            startDate: Date(),
            endDate: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date(),
            requirements: [
                Challenge.ChallengeRequirement(
                    description: "Achieve 85% accuracy",
                    target: 0.85,
                    current: 0.0,
                    unit: "accuracy",
                    isCompleted: false
                ),
                Challenge.ChallengeRequirement(
                    description: "Complete 10 attempts",
                    target: 10,
                    current: 0,
                    unit: "attempts",
                    isCompleted: false
                )
            ],
            rewards: [
                Reward(
                    rewardId: "daily_accuracy_reward",
                    type: .experience,
                    title: "Accuracy Bonus",
                    description: "50 experience points",
                    value: 50,
                    icon: "star.fill",
                    rarity: .common,
                    isCollected: false,
                    collectedDate: nil,
                    expiryDate: Calendar.current.date(byAdding: .day, value: 1, to: Date())
                )
            ],
            participants: [],
            isActive: true,
            isCompleted: false,
            completedDate: nil,
            leaderboard: []
        )
        
        challenges.append(accuracyChallenge)
        
        // Speed challenge
        let speedChallenge = Challenge(
            challengeId: "daily_speed_\(Date().timeIntervalSince1970)",
            title: "Speed Writer",
            description: "Write 5 characters in under 2 minutes",
            icon: "speedometer",
            challengeType: .daily,
            difficulty: .easy,
            duration: 86400,
            startDate: Date(),
            endDate: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date(),
            requirements: [
                Challenge.ChallengeRequirement(
                    description: "Complete in under 2 minutes",
                    target: 120,
                    current: 0,
                    unit: "seconds",
                    isCompleted: false
                )
            ],
            rewards: [
                Reward(
                    rewardId: "daily_speed_reward",
                    type: .points,
                    title: "Speed Bonus",
                    description: "25 points",
                    value: 25,
                    icon: "diamond.fill",
                    rarity: .common,
                    isCollected: false,
                    collectedDate: nil,
                    expiryDate: Calendar.current.date(byAdding: .day, value: 1, to: Date())
                )
            ],
            participants: [],
            isActive: true,
            isCompleted: false,
            completedDate: nil,
            leaderboard: []
        )
        
        challenges.append(speedChallenge)
        
        activeChallenges = challenges
        return challenges
    }
    
    // MARK: - Social Features
    
    /// Share achievement or milestone to social feed
    func shareToSocialFeed(_ activity: SocialActivity) async {
        socialFeed.insert(activity, at: 0)
        
        // Keep only recent 50 activities
        if socialFeed.count > 50 {
            socialFeed = Array(socialFeed.prefix(50))
        }
        
        // Save to database
        await saveSocialActivity(activity)
    }
    
    /// Like a social activity
    func likeActivity(_ activityId: String) async {
        if let index = socialFeed.firstIndex(where: { $0.activityId == activityId }) {
            socialFeed[index].likes += 1
            socialFeed[index].isLiked = true
            
            // Save to database
            await updateActivityLikes(activityId, likes: socialFeed[index].likes)
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadUserProfile() {
        // Load from database or create default
        userProfile = createDefaultProfile()
    }
    
    private func loadAchievements() {
        achievements = createDefaultAchievements()
    }
    
    private func loadActiveChallenges() {
        // Load active challenges from database
    }
    
    private func loadLeaderboards() {
        leaderboards = createDefaultLeaderboards()
    }
    
    private func loadStreakData() {
        streakData = StreakData(
            currentStreak: 5,
            longestStreak: 12,
            streakStartDate: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
            lastPracticeDate: Date(),
            streakType: .daily,
            milestones: [],
            isActive: true
        )
    }
    
    private func loadSocialFeed() {
        // Load social feed from database
    }
    
    // Mock data creation methods
    private func createDefaultProfile() -> GamificationProfile {
        return GamificationProfile(
            userId: "current-user",
            username: "TamilLearner",
            level: 5,
            experience: 1250,
            experienceToNextLevel: 1500,
            totalPoints: 2750,
            rank: "Intermediate Writer",
            badges: [],
            statistics: GamificationProfile.UserStatistics(
                totalCharactersWritten: 247,
                totalPracticeTime: 450,
                averageAccuracy: 0.78,
                bestAccuracy: 0.95,
                longestStreak: 12,
                currentStreak: 5,
                challengesCompleted: 8,
                achievementsUnlocked: 6,
                socialInteractions: 15,
                favoriteCharacters: ["அ", "க", "த"],
                weakestCharacters: ["ழ", "ற", "ன"],
                practiceFrequency: 0.75,
                improvementRate: 0.15
            ),
            preferences: GamificationProfile.GamificationPreferences(
                enableNotifications: true,
                enableSocialFeatures: true,
                enableCompetitiveMode: true,
                preferredChallengeTypes: [.daily, .weekly],
                privacyLevel: .friends,
                motivationStyle: .achievement
            ),
            joinedDate: Calendar.current.date(byAdding: .month, value: -2, to: Date()) ?? Date(),
            lastActiveDate: Date()
        )
    }
    
    private func createDefaultAchievements() -> [Achievement] {
        return [
            Achievement(
                achievementId: "first_character",
                title: "First Steps",
                description: "Write your first Tamil character",
                icon: "star.fill",
                category: .firstSteps,
                difficulty: .bronze,
                points: 10,
                requirements: [
                    Achievement.Requirement(
                        type: .charactersWritten,
                        target: 1,
                        current: 1,
                        description: "Write 1 character"
                    )
                ],
                rewards: [
                    Reward(
                        rewardId: "first_character_badge",
                        type: .badge,
                        title: "Beginner Writer",
                        description: "Your first Tamil writing badge",
                        value: 1,
                        icon: "shield.fill",
                        rarity: .common,
                        isCollected: true,
                        collectedDate: Date(),
                        expiryDate: nil
                    )
                ],
                isUnlocked: true,
                unlockedDate: Date(),
                progress: 1.0,
                isSecret: false
            )
        ]
    }
    
    private func createDefaultLeaderboards() -> [Leaderboard] {
        return [
            Leaderboard(
                leaderboardId: "weekly_accuracy",
                title: "Weekly Accuracy Leaders",
                description: "Top performers in writing accuracy this week",
                category: .accuracy,
                timeframe: .weekly,
                entries: [],
                lastUpdated: Date(),
                isGlobal: true
            )
        ]
    }
    
    // Additional helper methods would be implemented here...
    private func evaluateAchievement(_ achievement: Achievement, for activity: UserActivity) async -> Achievement { return achievement }
    private func unlockAchievement(_ achievement: Achievement) async {}
    private func updateAchievementProgress(_ achievement: Achievement) async {}
    private func calculateExperienceForLevel(_ level: Int) -> Int { return level * 1000 }
    private func handleLevelUp(from oldLevel: Int, to newLevel: Int) async {}
    private func saveUserProfile(_ profile: GamificationProfile) async {}
    private func saveSocialActivity(_ activity: SocialActivity) async {}
    private func updateActivityLikes(_ activityId: String, likes: Int) async {}
}

// MARK: - User Activity Model

struct UserActivity {
    let activityType: ActivityType
    let data: [String: Any]
    let timestamp: Date
    
    enum ActivityType {
        case characterWritten
        case accuracyAchieved
        case challengeCompleted
        case streakMaintained
        case levelUp
        case socialInteraction
    }
}
