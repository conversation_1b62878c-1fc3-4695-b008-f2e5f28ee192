// //
// //  NIRAControlsProvider.swift
// //  NIRA
// //
// //  Created by NIRA Team on 1/22/25.
// //  iOS 18 Controls for Control Center and Lock Screen
// //
// 
// import Foundation
// import SwiftUI
// 
// // iOS 18 Controls - will be available when entitlements are enabled
// // import ControlWidget
// // import // AppIntents
// 
// // MARK: - Quick Practice Control
// 
// struct QuickPracticeControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.quick-practice"
//         ) {
//             ControlWidgetButton(action: QuickPracticeIntent()) {
//                 Label("Quick Practice", systemImage: "brain.head.profile")
//             }
//         }
//         .displayName("Quick Practice")
//         .description("Start a 5-minute Tamil practice session")
//         .supportedFamilies([.systemSmall])
//     }
// }
// 
// // MARK: - Pronunciation Check Control
// 
// struct PronunciationControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.pronunciation"
//         ) {
//             ControlWidgetButton(action: PronunciationCheckIntent()) {
//                 Label("Pronunciation", systemImage: "mic.circle.fill")
//             }
//         }
//         .displayName("Pronunciation Check")
//         .description("Quick pronunciation assessment")
//         .supportedFamilies([.systemSmall])
//     }
// }
// 
// // MARK: - Daily Word Control
// 
// struct DailyWordControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.daily-word"
//         ) {
//             ControlWidgetButton(action: DailyWordIntent()) {
//                 Label("Daily Word", systemImage: "text.book.closed")
//             }
//         }
//         .displayName("Daily Word")
//         .description("Learn today's Tamil word")
//         .supportedFamilies([.systemSmall, .systemMedium])
//     }
// }
// 
// // MARK: - Progress Control
// 
// struct ProgressControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.progress"
//         ) {
//             ControlWidgetButton(action: ViewProgressIntent()) {
//                 Label("Progress", systemImage: "chart.line.uptrend.xyaxis")
//             }
//         }
//         .displayName("Learning Progress")
//         .description("View your Tamil learning progress")
//         .supportedFamilies([.systemSmall, .systemMedium])
//     }
// }
// 
// // MARK: - Study Timer Control
// 
// struct StudyTimerControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.study-timer"
//         ) {
//             ControlWidgetButton(action: StartStudyTimerIntent()) {
//                 Label("Study Timer", systemImage: "timer")
//             }
//         }
//         .displayName("Study Timer")
//         .description("Start a focused study session")
//         .supportedFamilies([.systemSmall])
//     }
// }
// 
// // MARK: - Cultural Exploration Control
// 
// struct CulturalControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.cultural"
//         ) {
//             ControlWidgetButton(action: CulturalExplorationIntent()) {
//                 Label("Culture", systemImage: "globe.asia.australia.fill")
//             }
//         }
//         .displayName("Tamil Culture")
//         .description("Explore Tamil cultural content")
//         .supportedFamilies([.systemSmall, .systemMedium])
//     }
// }
// 
// // MARK: - Control Intents
// 
// struct QuickPracticeIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Quick Practice"
//     static var description = IntentDescription("Start a quick 5-minute practice session")
//     static var openAppWhenRun: Bool = true
//     
//     func perform() async throws -> some IntentResult {
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .startQuickPractice,
//                 object: nil,
//                 userInfo: ["duration": 300] // 5 minutes
//             )
//         }
//         
//         return .result()
//     }
// }
// 
// struct PronunciationCheckIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Pronunciation Check"
//     static var description = IntentDescription("Quick pronunciation assessment")
//     static var openAppWhenRun: Bool = true
//     
//     func perform() async throws -> some IntentResult {
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .startPronunciationCheck,
//                 object: nil
//             )
//         }
//         
//         return .result()
//     }
// }
// 
// struct DailyWordIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Daily Word"
//     static var description = IntentDescription("Learn today's Tamil word")
//     static var openAppWhenRun: Bool = true
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog {
//         let dailyWord = await getDailyWord()
//         
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .showDailyWord,
//                 object: nil,
//                 userInfo: ["word": dailyWord]
//             )
//         }
//         
//         return .result(
//             dialog: "Today's Tamil word is '\(dailyWord.tamil)' which means '\(dailyWord.english)'"
//         )
//     }
//     
//     private func getDailyWord() async -> DailyWord {
//         // Mock daily word - replace with actual implementation
//         let words = [
//             DailyWord(tamil: "வணக்கம்", english: "Hello", pronunciation: "vanakkam"),
//             DailyWord(tamil: "நன்றி", english: "Thank you", pronunciation: "nandri"),
//             DailyWord(tamil: "மன்னிக்கவும்", english: "Sorry", pronunciation: "mannikkavum"),
//             DailyWord(tamil: "உணவு", english: "Food", pronunciation: "unavu"),
//             DailyWord(tamil: "நீர்", english: "Water", pronunciation: "neer")
//         ]
//         
//         let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: Date()) ?? 1
//         return words[dayOfYear % words.count]
//     }
// }
// 
// struct ViewProgressIntent: // AppIntent {
//     static var title: LocalizedStringResource = "View Progress"
//     static var description = IntentDescription("View your learning progress")
//     static var openAppWhenRun: Bool = true
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog {
//         let progress = await getCurrentProgress()
//         
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .showProgress,
//                 object: nil,
//                 userInfo: ["progress": progress]
//             )
//         }
//         
//         return .result(
//             dialog: "You've completed \(progress.completedLessons) out of \(progress.totalLessons) lessons. \(progress.currentStreak) day streak!"
//         )
//     }
//     
//     private func getCurrentProgress() async -> ProgressData {
//         // Mock progress - replace with actual data
//         return ProgressData(
//             completedLessons: 15,
//             totalLessons: 30,
//             averageScore: 87,
//             currentStreak: 7,
//             totalStudyTime: 1250
//         )
//     }
// }
// 
// struct StartStudyTimerIntent: // AppIntent {
//     static var title: LocalizedStringResource = "Start Study Timer"
//     static var description = IntentDescription("Start a focused study session")
//     static var openAppWhenRun: Bool = true
//     
//     @Parameter(title: "Duration", description: "Study session duration in minutes")
//     var duration: Int?
//     
//     func perform() async throws -> some IntentResult & ProvidesDialog {
//         let sessionDuration = duration ?? 25 // Default to 25 minutes (Pomodoro)
//         
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .startStudyTimer,
//                 object: nil,
//                 userInfo: ["duration": sessionDuration * 60] // Convert to seconds
//             )
//         }
//         
//         return .result(
//             dialog: "Starting a \(sessionDuration)-minute focused study session"
//         )
//     }
// }
// 
// // MARK: - Supporting Models
// 
// struct DailyWord {
//     let tamil: String
//     let english: String
//     let pronunciation: String
// }
// 
// // MARK: - Additional Notification Names
// 
// extension Notification.Name {
//     static let startQuickPractice = Notification.Name("startQuickPractice")
//     static let startPronunciationCheck = Notification.Name("startPronunciationCheck")
//     static let showDailyWord = Notification.Name("showDailyWord")
//     static let showProgress = Notification.Name("showProgress")
//     static let startStudyTimer = Notification.Name("startStudyTimer")
// }
// 
// // MARK: - Control Widget Bundle
// 
// // @main
// struct NIRAControlWidgetBundle: WidgetBundle {
//     var body: some Widget {
//         QuickPracticeControl()
//         PronunciationControl()
//         DailyWordControl()
//         ProgressControl()
//         StudyTimerControl()
//         CulturalControl()
//     }
// }
// 
// // MARK: - Enhanced Controls with State
// 
// struct AdvancedProgressControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.advanced-progress"
//         ) {
//             ControlWidgetButton(action: ViewProgressIntent()) {
//                 VStack(spacing: 4) {
//                     HStack {
//                         Image(systemName: "chart.line.uptrend.xyaxis")
//                             .font(.caption)
//                         Text("Progress")
//                             .font(.caption2)
//                             .fontWeight(.medium)
//                     }
//                     
//                     // Progress indicator
//                     ProgressView(value: 0.6) // Mock progress
//                         .progressViewStyle(LinearProgressViewStyle())
//                         .scaleEffect(x: 1, y: 0.5)
//                     
//                     Text("18/30 Lessons")
//                         .font(.caption2)
//                         .foregroundColor(.secondary)
//                 }
//             }
//         }
//         .displayName("Detailed Progress")
//         .description("View detailed learning progress")
//         .supportedFamilies([.systemMedium])
//     }
// }
// 
// struct StreakControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.streak"
//         ) {
//             ControlWidgetButton(action: ViewProgressIntent()) {
//                 VStack(spacing: 2) {
//                     Image(systemName: "flame.fill")
//                         .font(.title3)
//                         .foregroundColor(.orange)
//                     
//                     Text("7")
//                         .font(.headline)
//                         .fontWeight(.bold)
//                     
//                     Text("Day Streak")
//                         .font(.caption2)
//                         .foregroundColor(.secondary)
//                 }
//             }
//         }
//         .displayName("Learning Streak")
//         .description("View your current learning streak")
//         .supportedFamilies([.systemSmall])
//     }
// }
// 
// // MARK: - Interactive Controls (iOS 18.2+)
// 
// @available(iOS 18.2, *)
// struct InteractiveVocabularyControl: ControlWidget {
//     var body: some ControlWidgetConfiguration {
//         StaticControlConfiguration(
//             kind: "com.nira.interactive-vocab"
//         ) {
//             ControlWidgetToggle(
//                 "Practice Mode",
//                 isOn: false,
//                 action: TogglePracticeModeIntent()
//             ) { isOn in
//                 Label(
//                     isOn ? "Practice On" : "Practice Off",
//                     systemImage: isOn ? "brain.head.profile.fill" : "brain.head.profile"
//                 )
//             }
//         }
//         .displayName("Practice Mode")
//         .description("Toggle vocabulary practice mode")
//         .supportedFamilies([.systemSmall, .systemMedium])
//     }
// }
// 
// @available(iOS 18.2, *)
// struct TogglePracticeModeIntent: SetValueIntent {
//     static var title: LocalizedStringResource = "Toggle Practice Mode"
//     
//     @Parameter(title: "Practice Mode")
//     var value: Bool
//     
//     func perform() async throws -> some IntentResult {
//         await MainActor.run {
//             NotificationCenter.default.post(
//                 name: .togglePracticeMode,
//                 object: nil,
//                 userInfo: ["enabled": value]
//             )
//         }
//         
//         return .result()
//     }
// }
// 
// extension Notification.Name {
//     static let togglePracticeMode = Notification.Name("togglePracticeMode")
// }
