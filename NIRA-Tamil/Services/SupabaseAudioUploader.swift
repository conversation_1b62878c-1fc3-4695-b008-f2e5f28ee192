import Foundation

/// Supabase Storage Audio Uploader
/// Uploads generated MP3 files to Supabase Storage and updates database URLs
@MainActor
class SupabaseAudioUploader: ObservableObject {
    static let shared = SupabaseAudioUploader()
    
    @Published var isUploading = false
    @Published var uploadProgress = 0.0
    @Published var statusMessage = ""
    @Published var uploadedCount = 0
    
    // Supabase Configuration
    private let supabaseURL = "https://wnsorhbsucjguaoquhvr.supabase.co"
    private let supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"
    private let audioBucket = "audio"
    
    private let supabaseService = SupabaseContentService.shared
    
    private init() {}
    
    // MARK: - Upload Process
    
    /// Upload generated audio files to Supabase Storage and update database
    func uploadGeneratedAudio(_ generatedFiles: [GeneratedAudioFile]) async throws -> [UploadedAudioFile] {
        isUploading = true
        uploadProgress = 0.0
        uploadedCount = 0
        statusMessage = "Starting upload to Supabase Storage..."
        
        var uploadedFiles: [UploadedAudioFile] = []
        let totalFiles = generatedFiles.count
        
        for (index, generatedFile) in generatedFiles.enumerated() {
            do {
                // Upload to Supabase Storage
                let publicURL = try await uploadToSupabaseStorage(generatedFile)
                
                let uploadedFile = UploadedAudioFile(
                    filename: generatedFile.filename,
                    localURL: generatedFile.localURL,
                    publicURL: publicURL,
                    text: generatedFile.text,
                    fileSize: generatedFile.fileSize
                )
                
                uploadedFiles.append(uploadedFile)
                uploadedCount += 1
                
                uploadProgress = Double(index + 1) / Double(totalFiles)
                statusMessage = "Uploaded \(index + 1)/\(totalFiles): \(generatedFile.filename)"
                
                print("✅ Uploaded: \(generatedFile.filename) -> \(publicURL)")
                
                // Small delay to avoid overwhelming Supabase
                try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
                
            } catch {
                print("❌ Failed to upload \(generatedFile.filename): \(error)")
                // Continue with next file
            }
        }
        
        isUploading = false
        statusMessage = "Upload completed! \(uploadedFiles.count)/\(totalFiles) files uploaded"
        
        return uploadedFiles
    }
    
    /// Upload a single file to Supabase Storage
    private func uploadToSupabaseStorage(_ file: GeneratedAudioFile) async throws -> String {
        let uploadURL = URL(string: "\(supabaseURL)/storage/v1/object/\(audioBucket)/\(file.filename)")!
        
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        request.setValue("audio/mpeg", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue(supabaseKey, forHTTPHeaderField: "apikey")
        request.httpBody = file.audioData
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw UploadError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 || httpResponse.statusCode == 201 else {
            throw UploadError.uploadFailed(httpResponse.statusCode)
        }
        
        // Return public URL
        return "\(supabaseURL)/storage/v1/object/public/\(audioBucket)/\(file.filename)"
    }
    
    // MARK: - Database Updates
    
    /// Update vocabulary records with uploaded audio URLs
    func updateVocabularyAudioURLs(_ uploadedFiles: [UploadedAudioFile]) async throws {
        statusMessage = "Updating database with audio URLs..."
        
        // Group files by vocabulary ID
        var vocabularyUpdates: [String: VocabularyAudioUpdate] = [:]
        
        for file in uploadedFiles {
            let vocabId = extractVocabId(from: file.filename)
            
            if vocabularyUpdates[vocabId] == nil {
                vocabularyUpdates[vocabId] = VocabularyAudioUpdate(vocabId: vocabId)
            }
            
            if file.filename.contains("_word.") {
                vocabularyUpdates[vocabId]?.wordAudioURL = file.publicURL
            } else if file.filename.contains("_sentence.") {
                vocabularyUpdates[vocabId]?.sentenceAudioURL = file.publicURL
            }
        }
        
        // Update database records
        for update in vocabularyUpdates.values {
            try await updateVocabularyRecord(update)
        }
        
        statusMessage = "Database updated with \(vocabularyUpdates.count) vocabulary audio URLs"
    }
    
    /// Extract vocabulary ID from filename (e.g., "vocab_L1V01_word.mp3" -> "L1V01")
    private func extractVocabId(from filename: String) -> String {
        let components = filename.components(separatedBy: "_")
        if components.count >= 2 {
            return components[1] // vocab_L1V01_word.mp3 -> L1V01
        }
        return filename
    }
    
    /// Update a single vocabulary record in the database
    private func updateVocabularyRecord(_ update: VocabularyAudioUpdate) async throws {
        let updateQuery = """
        UPDATE vocabulary 
        SET audio_word_url = '\(update.wordAudioURL ?? "")',
            audio_sentence_url = '\(update.sentenceAudioURL ?? "")'
        WHERE vocab_id = '\(update.vocabId)'
        """
        
        let url = URL(string: "\(supabaseURL)/rest/v1/rpc/execute_sql")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue(supabaseKey, forHTTPHeaderField: "apikey")
        
        let requestBody = ["sql": updateQuery]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw UploadError.databaseUpdateFailed
        }
        
        guard httpResponse.statusCode == 200 || httpResponse.statusCode == 204 else {
            throw UploadError.databaseUpdateFailed
        }
        
        print("✅ Updated vocabulary \(update.vocabId) with audio URLs")
    }
    
    // MARK: - Complete Workflow
    
    /// Complete workflow: Generate -> Upload -> Update Database
    func generateUploadAndUpdateBasicGreetings() async throws -> WorkflowResult {
        statusMessage = "Starting complete audio generation workflow..."
        
        // Step 1: Generate MP3 files
        let generator = GoogleTTSGenerator.shared
        let generatedFiles = try await generator.generateBasicGreetingsAudio()
        
        // Step 2: Upload to Supabase Storage
        let uploadedFiles = try await uploadGeneratedAudio(generatedFiles)
        
        // Step 3: Update database
        try await updateVocabularyAudioURLs(uploadedFiles)
        
        statusMessage = "Workflow completed successfully!"
        
        return WorkflowResult(
            generatedFiles: generatedFiles.count,
            uploadedFiles: uploadedFiles.count,
            updatedRecords: uploadedFiles.count
        )
    }
    
    // MARK: - Validation
    
    /// Test uploaded audio URLs to ensure they work
    func validateUploadedAudio() async -> ValidationResult {
        statusMessage = "Validating uploaded audio URLs..."
        
        // Load vocabulary with updated URLs
        let vocabularyItems = await supabaseService.fetchVocabulary(for: "7b8c60af-dd2f-4754-9363-ab09a5bcea95")
        
        var validURLs = 0
        var invalidURLs = 0
        var testResults: [String] = []
        
        for vocabulary in vocabularyItems {
            // Test word URL
            if let wordURL = vocabulary.audioWordUrl {
                let isValid = await testAudioURL(wordURL)
                if isValid {
                    validURLs += 1
                    testResults.append("✅ \(vocabulary.englishWord) - Word audio")
                } else {
                    invalidURLs += 1
                    testResults.append("❌ \(vocabulary.englishWord) - Word audio")
                }
            }
            
            // Test sentence URL
            if let sentenceURL = vocabulary.audioSentenceUrl {
                let isValid = await testAudioURL(sentenceURL)
                if isValid {
                    validURLs += 1
                    testResults.append("✅ \(vocabulary.englishWord) - Sentence audio")
                } else {
                    invalidURLs += 1
                    testResults.append("❌ \(vocabulary.englishWord) - Sentence audio")
                }
            }
        }
        
        statusMessage = "Validation completed: \(validURLs) valid, \(invalidURLs) invalid"
        
        return ValidationResult(
            totalTested: validURLs + invalidURLs,
            validURLs: validURLs,
            invalidURLs: invalidURLs,
            testResults: testResults
        )
    }
    
    /// Test if an audio URL returns a valid response
    private func testAudioURL(_ urlString: String) async -> Bool {
        guard let url = URL(string: urlString) else { return false }
        
        do {
            let (_, response) = try await URLSession.shared.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                return httpResponse.statusCode == 200
            }
            return false
        } catch {
            return false
        }
    }
}

// MARK: - Supporting Types

struct UploadedAudioFile {
    let filename: String
    let localURL: URL
    let publicURL: String
    let text: String
    let fileSize: String
}

struct VocabularyAudioUpdate {
    let vocabId: String
    var wordAudioURL: String?
    var sentenceAudioURL: String?
}

struct WorkflowResult {
    let generatedFiles: Int
    let uploadedFiles: Int
    let updatedRecords: Int
    
    var summary: String {
        return "Generated \(generatedFiles) files, uploaded \(uploadedFiles), updated \(updatedRecords) records"
    }
}

struct ValidationResult {
    let totalTested: Int
    let validURLs: Int
    let invalidURLs: Int
    let testResults: [String]
    
    var successRate: Double {
        guard totalTested > 0 else { return 0 }
        return Double(validURLs) / Double(totalTested)
    }
    
    var summary: String {
        return "Tested \(totalTested) URLs: \(validURLs) valid (\(Int(successRate * 100))%)"
    }
}

enum UploadError: Error, LocalizedError {
    case invalidResponse
    case uploadFailed(Int)
    case databaseUpdateFailed
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse:
            return "Invalid response from server"
        case .uploadFailed(let code):
            return "Upload failed with HTTP code: \(code)"
        case .databaseUpdateFailed:
            return "Failed to update database"
        case .networkError:
            return "Network error occurred"
        }
    }
}
