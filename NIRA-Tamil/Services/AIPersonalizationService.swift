import Foundation
import SwiftUI
import Combine
import CoreML

@MainActor
class AIPersonalizationService: ObservableObject {
    static let shared = AIPersonalizationService()
    
    @Published var learningStyleProfile: LearningStyleProfile?
    @Published var personalizedContent: [PersonalizedContent] = []
    @Published var adaptiveDifficulty: [SkillArea: DifficultyLevel] = [:]
    @Published var learningPathOptimization: LearningPathOptimization?
    @Published var isAnalyzing: Bool = false
    
    private let userPreferencesService = UserPreferencesService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private let curriculumService = CurriculumService.shared
    private let recommendationEngine = EnhancedRecommendationEngine.shared
    
    private var cancellables = Set<AnyCancellable>()
    private var behavioralData: [BehavioralDataPoint] = []
    private var learningPatterns: [LearningPattern] = []
    
    private init() {
        setupSubscriptions()
        initializePersonalization()
    }
    
    // MARK: - Public Methods
    
    func analyzeUserBehavior() async {
        isAnalyzing = true
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.detectLearningStyle() }
            group.addTask { await self.analyzeContentPreferences() }
            group.addTask { await self.optimizeDifficultyLevels() }
            group.addTask { await self.generatePersonalizedContent() }
        }
        
        await MainActor.run {
            self.isAnalyzing = false
        }
    }
    
    func getLearningStyleRecommendations() -> [LearningStyleRecommendation] {
        guard let profile = learningStyleProfile else { return [] }
        
        var recommendations: [LearningStyleRecommendation] = []
        
        // Visual learner recommendations
        if profile.visualScore > 0.7 {
            recommendations.append(LearningStyleRecommendation(
                type: .visual,
                title: "Visual Learning Boost",
                description: "You learn best with visual aids. Try lessons with images, diagrams, and visual vocabulary cards.",
                actionItems: [
                    "Use flashcards with images",
                    "Watch video lessons",
                    "Practice with visual grammar charts"
                ],
                confidenceScore: profile.visualScore
            ))
        }
        
        // Auditory learner recommendations
        if profile.auditoryScore > 0.7 {
            recommendations.append(LearningStyleRecommendation(
                type: .auditory,
                title: "Audio-First Learning",
                description: "You excel with audio content. Focus on listening exercises and pronunciation practice.",
                actionItems: [
                    "Listen to native speaker recordings",
                    "Practice pronunciation daily",
                    "Use audio-only lessons"
                ],
                confidenceScore: profile.auditoryScore
            ))
        }
        
        // Kinesthetic learner recommendations
        if profile.kinestheticScore > 0.7 {
            recommendations.append(LearningStyleRecommendation(
                type: .kinesthetic,
                title: "Interactive Learning",
                description: "You learn through interaction and practice. Engage with hands-on exercises and real conversations.",
                actionItems: [
                    "Practice with interactive exercises",
                    "Use gesture-based learning",
                    "Engage in conversation practice"
                ],
                confidenceScore: profile.kinestheticScore
            ))
        }
        
        return recommendations.sorted { $0.confidenceScore > $1.confidenceScore }
    }
    
    func adaptContentForUser(_ content: CurriculumLesson) -> AdaptedContent {
        guard let profile = learningStyleProfile else {
            return AdaptedContent(
                originalContent: content, 
                adaptations: [],
                adaptationReason: "No learning style profile available",
                estimatedEffectivenessBoost: 0.0
            )
        }
        
        var adaptations: [ContentAdaptation] = []
        
        // Adapt based on learning style
        if profile.primaryStyle == .visual {
            adaptations.append(.addVisualAids)
            adaptations.append(.enhanceImageContent)
            adaptations.append(.useColorCoding)
        }
        
        if profile.primaryStyle == .auditory {
            adaptations.append(.addAudioNarration)
            adaptations.append(.enhancePronunciationGuides)
            adaptations.append(.includeRhythmsAndSongs)
        }
        
        if profile.primaryStyle == .kinesthetic {
            adaptations.append(.addInteractiveElements)
            adaptations.append(.includeGestureBasedLearning)
            adaptations.append(.enhanceHandsOnPractice)
        }
        
        // Adapt based on difficulty preferences
        let currentDifficulty = adaptiveDifficulty[content.skillArea] ?? .medium
        if currentDifficulty != .medium {
            adaptations.append(.adjustDifficulty(currentDifficulty))
        }
        
        // Adapt based on pacing preferences
        if profile.preferredPacing == .slow {
            adaptations.append(.addExtraExplanations)
            adaptations.append(.includeMoreExamples)
        } else if profile.preferredPacing == .fast {
            adaptations.append(.removeRedundantContent)
            adaptations.append(.addAdvancedChallenges)
        }
        
        return AdaptedContent(
            originalContent: content,
            adaptations: adaptations,
            adaptationReason: generateAdaptationReason(profile: profile, content: content),
            estimatedEffectivenessBoost: calculateEffectivenessBoost(adaptations: adaptations)
        )
    }
    
    func getOptimalLearningPath() -> OptimalLearningPath? {
        guard let optimization = learningPathOptimization else { return nil }
        
        let currentSkillLevels = curriculumService.userSkillLevels
        let weakestSkills = currentSkillLevels.sorted { $0.value.rawValue < $1.value.rawValue }.prefix(3)
        let strongestSkills = currentSkillLevels.sorted { $0.value.rawValue > $1.value.rawValue }.prefix(2)
        
        var pathNodes: [OptimalPathNode] = []
        
        // Focus on weakest skills first
        for (skillArea, _) in weakestSkills {
            let recommendations = curriculumService.getNextRecommendedLessons(count: 2)
                // Filter by skill area would require additional metadata
            
            for recommendation in recommendations {
                pathNodes.append(OptimalPathNode(
                    lessonId: UUID(uuidString: recommendation.id) ?? UUID(),
                    title: recommendation.title,
                    skillArea: skillArea,
                    priority: .high,
                    estimatedDuration: 30,
                    difficultyAdjustment: optimization.difficultyAdjustments[skillArea] ?? 0.0,
                    personalizedReason: "Strengthen your weakest skill area"
                ))
            }
        }
        
        // Add balanced practice from stronger skills
        for (skillArea, _) in strongestSkills {
            let recommendations = curriculumService.getNextRecommendedLessons(count: 1)
                // Filter by skill area would require additional metadata
            
            for recommendation in recommendations {
                pathNodes.append(OptimalPathNode(
                    lessonId: UUID(uuidString: recommendation.id) ?? UUID(),
                    title: recommendation.title,
                    skillArea: skillArea,
                    priority: .medium,
                    estimatedDuration: 30,
                    difficultyAdjustment: optimization.difficultyAdjustments[skillArea] ?? 0.0,
                    personalizedReason: "Maintain your strong skills"
                ))
            }
        }
        
        return OptimalLearningPath(
            nodes: pathNodes,
            totalEstimatedTime: pathNodes.reduce(0) { $0 + $1.estimatedDuration },
            expectedImprovementScore: optimization.expectedImprovementScore,
            personalizationFactors: optimization.personalizationFactors
        )
    }
    
    func updateLearningPreferences(from interaction: UserInteraction) {
        behavioralData.append(BehavioralDataPoint(
            timestamp: Date(),
            interactionType: interaction.type,
            duration: interaction.duration,
            accuracy: interaction.accuracy,
            hintsUsed: interaction.hintsUsed,
            skipCount: interaction.skipCount,
            engagementLevel: interaction.engagementLevel
        ))
        
        // Trigger re-analysis if we have enough new data
        if behavioralData.count % 10 == 0 {
            Task {
                await analyzeUserBehavior()
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupSubscriptions() {
        // Listen for user progress updates
        analyticsService.$userProgress
            .debounce(for: .seconds(2), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                Task {
                    await self?.analyzeUserBehavior()
                }
            }
            .store(in: &cancellables)
        
        // Listen for curriculum changes
        curriculumService.$userSkillLevels
            .debounce(for: .seconds(1), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                Task {
                    await self?.optimizeDifficultyLevels()
                }
            }
            .store(in: &cancellables)
    }
    
    private func initializePersonalization() {
        Task {
            await analyzeUserBehavior()
        }
    }
    
    private func detectLearningStyle() async {
        // Analyze behavioral patterns to detect learning style
        let recentBehavior = behavioralData.suffix(50)
        
        var visualScore: Double = 0.0
        var auditoryScore: Double = 0.0
        var kinestheticScore: Double = 0.0
        var readingScore: Double = 0.0
        
        for dataPoint in recentBehavior {
            switch dataPoint.interactionType {
            case .visualContent:
                visualScore += dataPoint.engagementLevel * dataPoint.accuracy
            case .audioContent:
                auditoryScore += dataPoint.engagementLevel * dataPoint.accuracy
            case .interactiveContent:
                kinestheticScore += dataPoint.engagementLevel * dataPoint.accuracy
            case .textContent:
                readingScore += dataPoint.engagementLevel * dataPoint.accuracy
            default:
                break
            }
        }
        
        // Normalize scores
        let totalScore = visualScore + auditoryScore + kinestheticScore + readingScore
        if totalScore > 0 {
            visualScore /= totalScore
            auditoryScore /= totalScore
            kinestheticScore /= totalScore
            readingScore /= totalScore
        }
        
        // Determine primary and secondary styles
        let scores = [
            (LearningStyle.visual, visualScore),
            (LearningStyle.auditory, auditoryScore),
            (LearningStyle.kinesthetic, kinestheticScore),
            (LearningStyle.reading, readingScore)
        ].sorted { $0.1 > $1.1 }
        
        let primaryStyle = scores.first?.0 ?? .mixed
        let secondaryStyle = scores.count > 1 ? scores[1].0 : nil
        
        // Analyze pacing preferences
        let averageDuration = recentBehavior.reduce(0.0) { $0 + Double($1.duration) } / Double(max(recentBehavior.count, 1))
        let averageHints = recentBehavior.reduce(0.0) { $0 + Double($1.hintsUsed) } / Double(max(recentBehavior.count, 1))
        
        let preferredPacing: LearningPacing
        if averageDuration > 300 && averageHints > 2 { // 5+ minutes, many hints
            preferredPacing = .slow
        } else if averageDuration < 120 && averageHints < 1 { // <2 minutes, few hints
            preferredPacing = .fast
        } else {
            preferredPacing = .medium
        }
        
        await MainActor.run {
            self.learningStyleProfile = LearningStyleProfile(
                primaryStyle: primaryStyle,
                secondaryStyle: secondaryStyle,
                visualScore: visualScore,
                auditoryScore: auditoryScore,
                kinestheticScore: kinestheticScore,
                readingScore: readingScore,
                preferredPacing: preferredPacing,
                confidenceLevel: min(1.0, Double(recentBehavior.count) / 50.0),
                lastUpdated: Date()
            )
        }
    }
    
    private func analyzeContentPreferences() async {
        // Analyze which types of content the user engages with most
        let contentAnalysis = behavioralData.reduce(into: [String: (engagement: Double, accuracy: Double, count: Int)]()) { result, dataPoint in
            let key = dataPoint.interactionType.rawValue
            let current = result[key] ?? (0.0, 0.0, 0)
            result[key] = (
                current.engagement + dataPoint.engagementLevel,
                current.accuracy + dataPoint.accuracy,
                current.count + 1
            )
        }
        
        var preferences: [ContentPreference] = []
        
        for (contentType, data) in contentAnalysis {
            let avgEngagement = data.engagement / Double(data.count)
            let avgAccuracy = data.accuracy / Double(data.count)
            let preferenceScore = (avgEngagement + avgAccuracy) / 2.0
            
            preferences.append(ContentPreference(
                contentType: contentType,
                preferenceScore: preferenceScore,
                engagementLevel: avgEngagement,
                successRate: avgAccuracy,
                sampleSize: data.count
            ))
        }
        
        await MainActor.run {
            // Update personalized content based on preferences
            self.generatePersonalizedContentFromPreferences(preferences)
        }
    }
    
    private func optimizeDifficultyLevels() async {
        let skillLevels = curriculumService.userSkillLevels
        var optimizedDifficulty: [SkillArea: DifficultyLevel] = [:]
        
        for (skillArea, _) in skillLevels {
            // Analyze recent performance in this skill area
            let recentPerformance = behavioralData.suffix(20).filter { dataPoint in
                // This would need to be enhanced to track skill area per interaction
                return true // Placeholder
            }
            
            let averageAccuracy = recentPerformance.reduce(0.0) { $0 + $1.accuracy } / Double(max(recentPerformance.count, 1))
            let averageEngagement = recentPerformance.reduce(0.0) { $0 + $1.engagementLevel } / Double(max(recentPerformance.count, 1))
            
            // Adjust difficulty based on performance
            let difficultyLevel: DifficultyLevel
            if averageAccuracy > 0.9 && averageEngagement > 0.8 {
                difficultyLevel = .hard // User is excelling, increase challenge
            } else if averageAccuracy < 0.6 || averageEngagement < 0.5 {
                difficultyLevel = .easy // User is struggling, reduce difficulty
            } else {
                difficultyLevel = .medium // Maintain current level
            }
            
            optimizedDifficulty[skillArea] = difficultyLevel
        }
        
        await MainActor.run {
            self.adaptiveDifficulty = optimizedDifficulty
        }
    }
    
    private func generatePersonalizedContent() async {
        guard let profile = learningStyleProfile else { return }
        
        let recommendations = curriculumService.getNextRecommendedLessons(count: 10)
        var personalizedItems: [PersonalizedContent] = []
        
        for recommendation in recommendations {
            let adaptedContent = adaptContentForUser(CurriculumLesson(
                id: UUID(uuidString: recommendation.id) ?? UUID(),
                title: recommendation.title,
                difficulty: .intermediate, // Default difficulty
                skillArea: .vocabulary, // Default skill area
                prerequisites: [], // LessonRecommendation doesn't have prerequisites array
                estimatedDuration: 30, // Default duration
                topics: [],
                learningObjectives: []
            ))
            
            personalizedItems.append(PersonalizedContent(
                id: UUID(),
                originalLessonId: UUID(uuidString: recommendation.id) ?? UUID(),
                title: recommendation.title,
                adaptedTitle: generateAdaptedTitle(recommendation.title, for: profile),
                skillArea: .vocabulary, // Default skill area
                adaptations: adaptedContent.adaptations,
                personalizedDescription: adaptedContent.adaptationReason,
                estimatedEffectiveness: adaptedContent.estimatedEffectivenessBoost,
                createdAt: Date()
            ))
        }
        
        await MainActor.run {
            self.personalizedContent = personalizedItems
        }
    }
    
    private func generatePersonalizedContentFromPreferences(_ preferences: [ContentPreference]) {
        // Implementation for generating content based on preferences
        // This would create personalized content recommendations
    }
    
    private func generateAdaptationReason(profile: LearningStyleProfile, content: CurriculumLesson) -> String {
        var reasons: [String] = []
        
        if profile.primaryStyle == .visual {
            reasons.append("Enhanced with visual elements for your visual learning style")
        }
        
        if profile.preferredPacing == .slow {
            reasons.append("Extended explanations to match your preferred learning pace")
        }
        
        if let difficulty = adaptiveDifficulty[content.skillArea] {
            switch difficulty {
            case .easy:
                reasons.append("Simplified to build confidence in \(content.skillArea.displayName)")
            case .hard:
                reasons.append("Enhanced challenge to accelerate your \(content.skillArea.displayName) progress")
            case .medium:
                break
            }
        }
        
        return reasons.joined(separator: ". ")
    }
    
    private func calculateEffectivenessBoost(adaptations: [ContentAdaptation]) -> Double {
        // Calculate expected effectiveness boost from adaptations
        let baseBoost = 0.1 // 10% base improvement
        let adaptationBoost = Double(adaptations.count) * 0.05 // 5% per adaptation
        return min(0.5, baseBoost + adaptationBoost) // Cap at 50% improvement
    }
    
    private func generateAdaptedTitle(_ originalTitle: String, for profile: LearningStyleProfile) -> String {
        switch profile.primaryStyle {
        case .visual:
            return "🎨 \(originalTitle)"
        case .auditory:
            return "🎵 \(originalTitle)"
        case .kinesthetic:
            return "🤲 \(originalTitle)"
        case .reading:
            return "📖 \(originalTitle)"
        case .mixed:
            return originalTitle
        }
    }
}

// MARK: - Supporting Models

struct LearningStyleProfile {
    let primaryStyle: LearningStyle
    let secondaryStyle: LearningStyle?
    let visualScore: Double
    let auditoryScore: Double
    let kinestheticScore: Double
    let readingScore: Double
    let preferredPacing: LearningPacing
    let confidenceLevel: Double
    let lastUpdated: Date
}

enum LearningStyle {
    case visual
    case auditory
    case kinesthetic
    case reading
    case mixed
    
    var displayName: String {
        switch self {
        case .visual: return "Visual"
        case .auditory: return "Auditory"
        case .kinesthetic: return "Kinesthetic"
        case .reading: return "Reading/Writing"
        case .mixed: return "Mixed"
        }
    }
    
    var icon: String {
        switch self {
        case .visual: return "eye.fill"
        case .auditory: return "ear.fill"
        case .kinesthetic: return "hand.raised.fill"
        case .reading: return "book.fill"
        case .mixed: return "brain.head.profile"
        }
    }
}

enum LearningPacing {
    case slow
    case medium
    case fast
    
    var displayName: String {
        switch self {
        case .slow: return "Methodical"
        case .medium: return "Balanced"
        case .fast: return "Accelerated"
        }
    }
}

enum DifficultyLevel {
    case easy
    case medium
    case hard
    
    var displayName: String {
        switch self {
        case .easy: return "Gentle"
        case .medium: return "Balanced"
        case .hard: return "Challenging"
        }
    }
}

struct LearningStyleRecommendation: Identifiable {
    let id = UUID()
    let type: LearningStyle
    let title: String
    let description: String
    let actionItems: [String]
    let confidenceScore: Double
}

struct AdaptedContent {
    let originalContent: CurriculumLesson
    let adaptations: [ContentAdaptation]
    let adaptationReason: String
    let estimatedEffectivenessBoost: Double
}

enum ContentAdaptation {
    case addVisualAids
    case enhanceImageContent
    case useColorCoding
    case addAudioNarration
    case enhancePronunciationGuides
    case includeRhythmsAndSongs
    case addInteractiveElements
    case includeGestureBasedLearning
    case enhanceHandsOnPractice
    case adjustDifficulty(DifficultyLevel)
    case addExtraExplanations
    case includeMoreExamples
    case removeRedundantContent
    case addAdvancedChallenges
    
    var displayName: String {
        switch self {
        case .addVisualAids: return "Visual Aids"
        case .enhanceImageContent: return "Enhanced Images"
        case .useColorCoding: return "Color Coding"
        case .addAudioNarration: return "Audio Narration"
        case .enhancePronunciationGuides: return "Pronunciation Guides"
        case .includeRhythmsAndSongs: return "Rhythms & Songs"
        case .addInteractiveElements: return "Interactive Elements"
        case .includeGestureBasedLearning: return "Gesture Learning"
        case .enhanceHandsOnPractice: return "Hands-on Practice"
        case .adjustDifficulty(let level): return "Difficulty: \(level.displayName)"
        case .addExtraExplanations: return "Extra Explanations"
        case .includeMoreExamples: return "More Examples"
        case .removeRedundantContent: return "Streamlined Content"
        case .addAdvancedChallenges: return "Advanced Challenges"
        }
    }
}

struct PersonalizedContent: Identifiable {
    let id: UUID
    let originalLessonId: UUID
    let title: String
    let adaptedTitle: String
    let skillArea: SkillArea
    let adaptations: [ContentAdaptation]
    let personalizedDescription: String
    let estimatedEffectiveness: Double
    let createdAt: Date
}

struct BehavioralDataPoint {
    let timestamp: Date
    let interactionType: InteractionType
    let duration: Int // seconds
    let accuracy: Double
    let hintsUsed: Int
    let skipCount: Int
    let engagementLevel: Double // 0.0 to 1.0
}

enum InteractionType: String, CaseIterable {
    case visualContent = "visual"
    case audioContent = "audio"
    case interactiveContent = "interactive"
    case textContent = "text"
    case videoContent = "video"
    case gameContent = "game"
    case conversationContent = "conversation"
}

struct UserInteraction {
    let type: InteractionType
    let duration: Int
    let accuracy: Double
    let hintsUsed: Int
    let skipCount: Int
    let engagementLevel: Double
}

struct ContentPreference {
    let contentType: String
    let preferenceScore: Double
    let engagementLevel: Double
    let successRate: Double
    let sampleSize: Int
}

struct LearningPathOptimization {
    let difficultyAdjustments: [SkillArea: Double]
    let expectedImprovementScore: Double
    let personalizationFactors: [String]
}

struct OptimalLearningPath {
    let nodes: [OptimalPathNode]
    let totalEstimatedTime: Int
    let expectedImprovementScore: Double
    let personalizationFactors: [String]
}

struct OptimalPathNode: Identifiable {
    let id = UUID()
    let lessonId: UUID
    let title: String
    let skillArea: SkillArea
    let priority: LearningPriority
    let estimatedDuration: Int
    let difficultyAdjustment: Double
    let personalizedReason: String
}

enum LearningPriority {
    case low
    case medium
    case high
    case critical
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        case .critical: return "Critical"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .blue
        case .high: return .orange
        case .critical: return .red
        }
    }
}

struct LearningPattern {
    let id = UUID()
    let patternType: PatternType
    let description: String
    let frequency: Double
    let impact: PatternImpact
    let detectedAt: Date
}

// Note: PatternType is defined in LearningAnalyticsDashboardService.swift

enum PatternImpact {
    case positive
    case negative
    case neutral
} 