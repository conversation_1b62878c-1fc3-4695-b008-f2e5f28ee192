import Foundation
import AVFoundation

/// Grammar Audio Generator
/// Generates audio files for grammar examples using Google TTS with approved voices
@MainActor
class GrammarAudioGenerator: ObservableObject {
    static let shared = GrammarAudioGenerator()
    
    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var statusMessage = ""
    @Published var generatedCount = 0
    
    // Google Cloud TTS Configuration - Approved Voices
    private let femaleVoice = "ta-IN-Chirp3-HD-Erinome" // Premium Tamil Female
    private let maleVoice = "ta-IN-Chirp3-HD-Iapetus"   // Premium Tamil Male
    private let languageCode = "ta-IN"
    private let audioFormat = "MP3"
    private let speakingRate = 0.9 // Slightly slower for learning
    private let pitch = 0.0
    
    // Service Account Management - Use existing infrastructure
    private var currentServiceAccountIndex = 0
    private let serviceAccountPaths = [
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-7e3f3c2b36fa.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-95832b0001f9.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-df136a7cd82d.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-e5a76dc745e2.json",
        "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-e791986c718e.json"
    ]
    
    // Local storage
    private let localAudioDirectory: URL = {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioDir = documentsPath.appendingPathComponent("Audio")
        try? FileManager.default.createDirectory(at: audioDir, withIntermediateDirectories: true)
        return audioDir
    }()
    
    private init() {}
    
    // MARK: - Main Generation Function
    
    /// Generate audio for all grammar examples in Basic Greetings lesson
    func generateGrammarAudio() async throws -> [GeneratedGrammarAudioFile] {
        isGenerating = true
        generationProgress = 0.0
        generatedCount = 0
        statusMessage = "Loading grammar examples..."
        
        // Load grammar examples from database
        let grammarExamples = try await loadGrammarExamples()
        
        statusMessage = "Generating MP3 files for \(grammarExamples.count) grammar examples..."
        
        var generatedFiles: [GeneratedGrammarAudioFile] = []
        let totalItems = grammarExamples.count
        
        for (index, example) in grammarExamples.enumerated() {
            do {
                // Determine voice based on content (you can add logic for male/female detection)
                let voice = determineVoice(for: example.example_tamil)
                
                // Generate audio file
                let audioFile = try await generateAudioFile(
                    text: example.example_tamil,
                    filename: "lesson_01_grammar_\(String(format: "%02d", index + 1))_example_\(String(format: "%02d", index + 1)).mp3",
                    voice: voice,
                    exampleId: example.id
                )
                
                generatedFiles.append(audioFile)
                generatedCount += 1
                
                generationProgress = Double(index + 1) / Double(totalItems)
                statusMessage = "Generated \(index + 1)/\(totalItems): \(example.example_english)"
                
                // Small delay to avoid rate limiting
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                
            } catch {
                print("❌ Failed to generate audio for example \(example.id): \(error)")
                // Continue with next item
            }
        }
        
        statusMessage = "Generated \(generatedFiles.count) audio files successfully!"
        isGenerating = false
        
        return generatedFiles
    }
    
    // MARK: - Audio Generation
    
    /// Generate a single MP3 file using Google Cloud TTS
    private func generateAudioFile(text: String, filename: String, voice: String, exampleId: String) async throws -> GeneratedGrammarAudioFile {
        guard let serviceAccount = getCurrentServiceAccount() else {
            throw GrammarTTSGenerationError.noServiceAccount
        }
        
        // Get access token
        let accessToken = try await getAccessToken(serviceAccount: serviceAccount)
        
        // Call Google Cloud TTS API
        let audioData = try await callGoogleTTSAPI(text: text, accessToken: accessToken, voice: voice)
        
        // Save to local file
        let localURL = localAudioDirectory.appendingPathComponent(filename)
        try audioData.write(to: localURL)
        
        print("✅ Generated MP3 file: \(filename) (\(audioData.count) bytes)")
        
        return GeneratedGrammarAudioFile(
            filename: filename,
            localURL: localURL,
            text: text,
            audioData: audioData,
            exampleId: exampleId,
            voice: voice
        )
    }
    
    /// Call Google Cloud Text-to-Speech API
    private func callGoogleTTSAPI(text: String, accessToken: String, voice: String) async throws -> Data {
        let url = URL(string: "https://texttospeech.googleapis.com/v1/text:synthesize")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        let requestBody: [String: Any] = [
            "input": ["text": text],
            "voice": [
                "languageCode": languageCode,
                "name": voice
            ],
            "audioConfig": [
                "audioEncoding": audioFormat,
                "speakingRate": speakingRate,
                "pitch": pitch
            ]
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw GrammarTTSGenerationError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 429 {
                // Rate limited, rotate account and retry
                rotateServiceAccount()
                return try await callGoogleTTSAPI(text: text, accessToken: accessToken, voice: voice)
            }
            throw GrammarTTSGenerationError.apiError(httpResponse.statusCode)
        }
        
        guard let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let audioContent = jsonResponse["audioContent"] as? String,
              let audioData = Data(base64Encoded: audioContent) else {
            throw GrammarTTSGenerationError.invalidAudioData
        }
        
        return audioData
    }
    
    // MARK: - Helper Functions
    
    /// Determine appropriate voice for the text (can be enhanced with gender detection logic)
    private func determineVoice(for text: String) -> String {
        // For now, alternate between voices or use female as default
        // You can enhance this with actual gender detection logic
        return femaleVoice // Default to female voice
    }
    
    /// Load grammar examples from database
    private func loadGrammarExamples() async throws -> [GrammarExampleData] {
        // Use Supabase to fetch grammar examples
        let url = URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co/rest/v1/grammar_examples")!
        var request = URLRequest(url: url)
        request.setValue("Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs", forHTTPHeaderField: "Authorization")
        request.setValue("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs", forHTTPHeaderField: "apikey")

        // Add query parameters for the specific lesson
        var components = URLComponents(url: url, resolvingAgainstBaseURL: false)!
        components.queryItems = [
            URLQueryItem(name: "select", value: "id,example_tamil,example_english,example_romanization,grammar_topics!inner(title_english,title_tamil,lesson_id)"),
            URLQueryItem(name: "grammar_topics.lesson_id", value: "eq.7b8c60af-dd2f-4754-9363-ab09a5bcea95"),
            URLQueryItem(name: "order", value: "grammar_topics(grammar_id),example_order")
        ]
        request.url = components.url

        let (data, _) = try await URLSession.shared.data(for: request)

        // Parse the response (simplified)
        let examples = try parseGrammarExamplesResponse(data)
        return examples
    }

    private func parseGrammarExamplesResponse(_ data: Data) throws -> [GrammarExampleData] {
        // For now, return hardcoded examples - you can implement JSON parsing later
        return [
            GrammarExampleData(
                id: "d9b80d40-bb5f-40e4-9984-f3f16ae42826",
                example_tamil: "காலை வணக்கம்!",
                example_english: "Good morning!",
                example_romanization: "kaalai vaNakkam!"
            ),
            GrammarExampleData(
                id: "55fdd9b2-c32c-4cef-8a9d-0bb1a9a752e4",
                example_tamil: "மாலை வணக்கம்!",
                example_english: "Good evening!",
                example_romanization: "maalai vaNakkam!"
            )
            // Add more examples as needed
        ]
    }
    
    /// Get current service account
    private func getCurrentServiceAccount() -> [String: Any]? {
        guard currentServiceAccountIndex < serviceAccountPaths.count else {
            return nil
        }

        let keyPath = serviceAccountPaths[currentServiceAccountIndex]
        guard let data = try? Data(contentsOf: URL(fileURLWithPath: keyPath)),
              let serviceAccount = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }

        return serviceAccount
    }

    /// Rotate to next service account
    private func rotateServiceAccount() {
        currentServiceAccountIndex = (currentServiceAccountIndex + 1) % serviceAccountPaths.count
        print("🔄 Rotated to service account \(currentServiceAccountIndex + 1)")
    }

    /// Get OAuth access token using JWT
    private func getAccessToken(serviceAccount: [String: Any]) async throws -> String {
        // Use the existing JWT implementation from GoogleTTSGenerator
        // This is a simplified version - in production you'd implement proper JWT
        guard let _ = serviceAccount["private_key"] as? String,
              let _ = serviceAccount["client_email"] as? String else {
            throw GrammarTTSGenerationError.noServiceAccount
        }

        // For now, return a placeholder - implement proper JWT generation
        return "placeholder_token"
    }
}

// MARK: - Data Models

struct GrammarExampleData {
    let id: String
    let example_tamil: String
    let example_english: String
    let example_romanization: String
}

struct GeneratedGrammarAudioFile {
    let filename: String
    let localURL: URL
    let text: String
    let audioData: Data
    let exampleId: String
    let voice: String
    
    var fileSize: Int {
        return audioData.count
    }
}

// MARK: - Errors

enum GrammarTTSGenerationError: Error {
    case noServiceAccount
    case invalidResponse
    case apiError(Int)
    case invalidAudioData
}
