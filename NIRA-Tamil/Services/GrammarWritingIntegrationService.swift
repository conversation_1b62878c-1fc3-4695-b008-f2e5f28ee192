//
//  GrammarWritingIntegrationService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Combine

@MainActor
class GrammarWritingIntegrationService: ObservableObject {
    static let shared = GrammarWritingIntegrationService()
    
    @Published var grammarWritingExercises: [GrammarWritingExercise] = []
    @Published var isGeneratingContent = false
    
    private let scriptService = TamilScriptService.shared
    // private let supabase = SupabaseService.shared.client // TODO: Add when SupabaseService is available
    
    private init() {}
    
    // MARK: - Grammar Writing Exercise Models
    
    struct GrammarWritingExercise: Identifiable, Codable {
        let id: UUID
        let grammarTopic: GrammarTopic
        let lessonId: String
        let cefrLevel: CEFRLevel
        let exerciseType: ExerciseType
        let title: String
        let description: String
        let targetSentences: [TargetSentence]
        let grammarFocus: [GrammarFocus]
        let writingSteps: [GrammarWritingStep]
        let culturalNotes: [String]
        let difficultyLevel: Int
        let estimatedTime: Int // minutes
        let successCriteria: GrammarSuccessCriteria
        
        struct TargetSentence: Identifiable, Codable {
            let id: UUID
            let tamil: String
            let romanization: String
            let english: String
            let grammarPattern: String
            let keyCharacters: [String]
            let grammarHighlights: [GrammarHighlight]
        }
        
        struct GrammarHighlight: Codable {
            let text: String
            let grammarFunction: String
            let explanation: String
            let writingTips: [String]
        }
        
        struct GrammarWritingStep: Identifiable, Codable {
            let id: UUID
            let stepNumber: Int
            let stepType: StepType
            let instruction: String
            let targetText: String
            let grammarExplanation: String
            let writingGuidance: [String]
            let expectedDuration: Int // seconds
            
            enum StepType: String, Codable, CaseIterable {
                case grammarIntroduction = "grammar_introduction"
                case characterPractice = "character_practice"
                case wordFormation = "word_formation"
                case sentenceConstruction = "sentence_construction"
                case grammarApplication = "grammar_application"
                case freeWriting = "free_writing"
            }
        }
        
        enum ExerciseType: String, Codable, CaseIterable {
            case nounDeclension = "noun_declension"
            case verbConjugation = "verb_conjugation"
            case sentenceStructure = "sentence_structure"
            case questionFormation = "question_formation"
            case negation = "negation"
            case pluralization = "pluralization"
            case caseMarkers = "case_markers"
            case tenseFormation = "tense_formation"
            
            var displayName: String {
                switch self {
                case .nounDeclension: return "Noun Declension"
                case .verbConjugation: return "Verb Conjugation"
                case .sentenceStructure: return "Sentence Structure"
                case .questionFormation: return "Question Formation"
                case .negation: return "Negation"
                case .pluralization: return "Pluralization"
                case .caseMarkers: return "Case Markers"
                case .tenseFormation: return "Tense Formation"
                }
            }
        }
        
        struct GrammarSuccessCriteria: Codable {
            let minimumAccuracy: Double
            let requiredSentences: Int
            let grammarAccuracy: Double
            let timeLimit: Int? // seconds
            let allowedAttempts: Int
        }
    }
    
    struct GrammarTopic: Codable {
        let id: String
        let name: String
        let description: String
        let level: CEFRLevel
        let prerequisites: [String]
        let keyPatterns: [String]
        let examples: [String]
    }
    
    struct GrammarFocus: Codable {
        let concept: String
        let explanation: String
        let writingApplication: String
        let commonMistakes: [String]
        let practicePoints: [String]
    }
    
    // MARK: - Grammar Exercise Generation
    
    /// Generate grammar-focused writing exercises for a lesson
    func generateGrammarWritingExercises(for lessonId: String, level: CEFRLevel) async {
        isGeneratingContent = true
        defer { isGeneratingContent = false }
        
        do {
            let grammarTopics = getGrammarTopicsForLevel(level)
            var exercises: [GrammarWritingExercise] = []
            
            for topic in grammarTopics {
                let exercise = await createGrammarWritingExercise(
                    topic: topic,
                    lessonId: lessonId,
                    level: level
                )
                exercises.append(exercise)
            }
            
            grammarWritingExercises.append(contentsOf: exercises)
            
            // Save to database
            await saveGrammarExercises(exercises)
            
            print("✅ Generated \(exercises.count) grammar writing exercises")
            
        } catch {
            print("❌ Error generating grammar exercises: \(error)")
        }
    }
    
    /// Create a comprehensive grammar writing exercise
    private func createGrammarWritingExercise(
        topic: GrammarTopic,
        lessonId: String,
        level: CEFRLevel
    ) async -> GrammarWritingExercise {
        
        let exerciseType = determineExerciseType(for: topic)
        let targetSentences = generateTargetSentences(for: topic, level: level)
        let grammarFocus = createGrammarFocus(for: topic)
        let writingSteps = createGrammarWritingSteps(
            topic: topic,
            sentences: targetSentences,
            exerciseType: exerciseType
        )
        
        let culturalNotes = generateCulturalNotes(for: topic)
        let difficultyLevel = calculateDifficultyLevel(topic: topic, level: level)
        let estimatedTime = calculateEstimatedTime(steps: writingSteps, difficulty: difficultyLevel)
        
        let successCriteria = GrammarWritingExercise.GrammarSuccessCriteria(
            minimumAccuracy: level == .a1 ? 70.0 : level == .a2 ? 75.0 : 80.0,
            requiredSentences: targetSentences.count,
            grammarAccuracy: 85.0,
            timeLimit: estimatedTime * 60,
            allowedAttempts: 3
        )
        
        return GrammarWritingExercise(
            id: UUID(),
            grammarTopic: topic,
            lessonId: lessonId,
            cefrLevel: level,
            exerciseType: exerciseType,
            title: "Writing Practice: \(topic.name)",
            description: "Practice writing Tamil sentences while learning \(topic.name.lowercased())",
            targetSentences: targetSentences,
            grammarFocus: grammarFocus,
            writingSteps: writingSteps,
            culturalNotes: culturalNotes,
            difficultyLevel: difficultyLevel,
            estimatedTime: estimatedTime,
            successCriteria: successCriteria
        )
    }
    
    // MARK: - Grammar Topics by Level
    
    private func getGrammarTopicsForLevel(_ level: CEFRLevel) -> [GrammarTopic] {
        switch level {
        case .a1:
            return [
                GrammarTopic(
                    id: "basic_sentence_structure",
                    name: "Basic Sentence Structure",
                    description: "Subject + Object + Verb pattern in Tamil",
                    level: .a1,
                    prerequisites: [],
                    keyPatterns: ["நான் + object + verb", "அவர் + object + verb"],
                    examples: ["நான் சாப்பிடுகிறேன்", "அவர் படிக்கிறார்"]
                ),
                GrammarTopic(
                    id: "present_tense",
                    name: "Present Tense",
                    description: "Present tense verb conjugations",
                    level: .a1,
                    prerequisites: ["basic_sentence_structure"],
                    keyPatterns: ["-கிறேன்", "-கிறார்", "-கிறது"],
                    examples: ["வருகிறேன்", "செல்கிறார்", "இருக்கிறது"]
                ),
                GrammarTopic(
                    id: "basic_questions",
                    name: "Basic Questions",
                    description: "Simple question formation with எங்கே, என்ன, யார்",
                    level: .a1,
                    prerequisites: ["present_tense"],
                    keyPatterns: ["எங்கே + verb?", "என்ன + verb?", "யார் + verb?"],
                    examples: ["எங்கே போகிறீர்கள்?", "என்ன செய்கிறீர்கள்?"]
                )
            ]
        case .a2:
            return [
                GrammarTopic(
                    id: "past_tense",
                    name: "Past Tense",
                    description: "Past tense formations and usage",
                    level: .a2,
                    prerequisites: ["present_tense"],
                    keyPatterns: ["-ினேன்", "-ினார்", "-ியது"],
                    examples: ["வந்தேன்", "சென்றார்", "இருந்தது"]
                ),
                GrammarTopic(
                    id: "case_markers",
                    name: "Case Markers",
                    description: "Basic case markers: -ஐ, -இல், -உடன்",
                    level: .a2,
                    prerequisites: ["basic_sentence_structure"],
                    keyPatterns: ["object + ஐ", "place + இல்", "person + உடன்"],
                    examples: ["புத்தகத்தை", "வீட்டில்", "நண்பனுடன்"]
                )
            ]
        default:
            return []
        }
    }
    
    // MARK: - Target Sentence Generation
    
    private func generateTargetSentences(for topic: GrammarTopic, level: CEFRLevel) -> [GrammarWritingExercise.TargetSentence] {
        switch topic.id {
        case "basic_sentence_structure":
            return [
                GrammarWritingExercise.TargetSentence(
                    id: UUID(),
                    tamil: "நான் சாப்பிடுகிறேன்",
                    romanization: "naan saappidukiren",
                    english: "I am eating",
                    grammarPattern: "Subject + Verb",
                    keyCharacters: ["நான்", "சாப்பிடுகிறேன்"],
                    grammarHighlights: [
                        GrammarWritingExercise.GrammarHighlight(
                            text: "நான்",
                            grammarFunction: "First person pronoun",
                            explanation: "Subject of the sentence",
                            writingTips: ["Write நான் with proper spacing", "நான் always comes at the beginning"]
                        ),
                        GrammarWritingExercise.GrammarHighlight(
                            text: "சாப்பிடுகிறேன்",
                            grammarFunction: "Present tense verb",
                            explanation: "First person present tense ending -கிறேன்",
                            writingTips: ["Pay attention to the compound characters", "End with -கிறேன் for first person"]
                        )
                    ]
                ),
                GrammarWritingExercise.TargetSentence(
                    id: UUID(),
                    tamil: "அவர் படிக்கிறார்",
                    romanization: "avar padikkiraar",
                    english: "He/She is studying",
                    grammarPattern: "Subject + Verb",
                    keyCharacters: ["அவர்", "படிக்கிறார்"],
                    grammarHighlights: [
                        GrammarWritingExercise.GrammarHighlight(
                            text: "அவர்",
                            grammarFunction: "Third person pronoun (respectful)",
                            explanation: "Respectful way to refer to someone",
                            writingTips: ["அவர் shows respect", "Used for both male and female"]
                        )
                    ]
                )
            ]
            
        case "present_tense":
            return [
                GrammarWritingExercise.TargetSentence(
                    id: UUID(),
                    tamil: "நான் வீட்டிற்கு வருகிறேன்",
                    romanization: "naan veettirku varukiren",
                    english: "I am coming home",
                    grammarPattern: "Subject + Destination + Verb",
                    keyCharacters: ["நான்", "வீட்டிற்கு", "வருகிறேன்"],
                    grammarHighlights: [
                        GrammarWritingExercise.GrammarHighlight(
                            text: "வீட்டிற்கு",
                            grammarFunction: "Destination marker",
                            explanation: "To/towards the house",
                            writingTips: ["வீடு + இற்கு = வீட்டிற்கு", "Shows direction or destination"]
                        )
                    ]
                )
            ]
            
        case "basic_questions":
            return [
                GrammarWritingExercise.TargetSentence(
                    id: UUID(),
                    tamil: "நீங்கள் எங்கே போகிறீர்கள்?",
                    romanization: "neengal engae pokireerkal?",
                    english: "Where are you going?",
                    grammarPattern: "Subject + Question word + Verb + ?",
                    keyCharacters: ["நீங்கள்", "எங்கே", "போகிறீர்கள்"],
                    grammarHighlights: [
                        GrammarWritingExercise.GrammarHighlight(
                            text: "எங்கே",
                            grammarFunction: "Question word for place",
                            explanation: "Asks about location or destination",
                            writingTips: ["எங்கே always comes before the verb in questions"]
                        )
                    ]
                )
            ]
            
        default:
            return []
        }
    }
    
    // MARK: - Grammar Writing Steps Creation
    
    private func createGrammarWritingSteps(
        topic: GrammarTopic,
        sentences: [GrammarWritingExercise.TargetSentence],
        exerciseType: GrammarWritingExercise.ExerciseType
    ) -> [GrammarWritingExercise.GrammarWritingStep] {
        
        var steps: [GrammarWritingExercise.GrammarWritingStep] = []
        var stepNumber = 1
        
        // Step 1: Grammar Introduction
        steps.append(GrammarWritingExercise.GrammarWritingStep(
            id: UUID(),
            stepNumber: stepNumber,
            stepType: .grammarIntroduction,
            instruction: "Learn about \(topic.name)",
            targetText: topic.description,
            grammarExplanation: topic.description,
            writingGuidance: [
                "Understand the grammar pattern before writing",
                "Pay attention to word order in Tamil",
                "Notice how Tamil sentence structure differs from English"
            ],
            expectedDuration: 60
        ))
        stepNumber += 1
        
        // Step 2: Character Practice for key grammar elements
        for sentence in sentences.prefix(2) {
            for highlight in sentence.grammarHighlights {
                steps.append(GrammarWritingExercise.GrammarWritingStep(
                    id: UUID(),
                    stepNumber: stepNumber,
                    stepType: .characterPractice,
                    instruction: "Practice writing '\(highlight.text)'",
                    targetText: highlight.text,
                    grammarExplanation: highlight.explanation,
                    writingGuidance: highlight.writingTips,
                    expectedDuration: 45
                ))
                stepNumber += 1
            }
        }
        
        // Step 3: Sentence Construction
        for sentence in sentences {
            steps.append(GrammarWritingExercise.GrammarWritingStep(
                id: UUID(),
                stepNumber: stepNumber,
                stepType: .sentenceConstruction,
                instruction: "Write the complete sentence",
                targetText: sentence.tamil,
                grammarExplanation: "Pattern: \(sentence.grammarPattern)",
                writingGuidance: [
                    "Follow the Tamil word order",
                    "Pay attention to verb endings",
                    "Maintain proper spacing between words"
                ],
                expectedDuration: 90
            ))
            stepNumber += 1
        }
        
        // Step 4: Grammar Application
        steps.append(GrammarWritingExercise.GrammarWritingStep(
            id: UUID(),
            stepNumber: stepNumber,
            stepType: .grammarApplication,
            instruction: "Create your own sentence using this grammar pattern",
            targetText: "",
            grammarExplanation: "Apply the \(topic.name) pattern in a new sentence",
            writingGuidance: [
                "Use the same grammar pattern",
                "Choose different vocabulary words",
                "Maintain correct Tamil sentence structure"
            ],
            expectedDuration: 120
        ))
        
        return steps
    }
    
    // MARK: - Helper Methods
    
    private func determineExerciseType(for topic: GrammarTopic) -> GrammarWritingExercise.ExerciseType {
        switch topic.id {
        case "basic_sentence_structure": return .sentenceStructure
        case "present_tense", "past_tense": return .verbConjugation
        case "basic_questions": return .questionFormation
        case "case_markers": return .caseMarkers
        default: return .sentenceStructure
        }
    }
    
    private func createGrammarFocus(for topic: GrammarTopic) -> [GrammarFocus] {
        return [
            GrammarFocus(
                concept: topic.name,
                explanation: topic.description,
                writingApplication: "Practice writing sentences that demonstrate \(topic.name.lowercased())",
                commonMistakes: [
                    "Incorrect word order",
                    "Wrong verb endings",
                    "Missing case markers"
                ],
                practicePoints: [
                    "Focus on Tamil sentence structure",
                    "Pay attention to verb conjugations",
                    "Practice character combinations in grammar words"
                ]
            )
        ]
    }
    
    private func generateCulturalNotes(for topic: GrammarTopic) -> [String] {
        switch topic.id {
        case "basic_sentence_structure":
            return [
                "Tamil follows Subject-Object-Verb (SOV) order, unlike English",
                "Respect levels are built into Tamil grammar through verb endings",
                "Tamil sentences can be very expressive with minimal words"
            ]
        case "present_tense":
            return [
                "Tamil present tense often implies ongoing action",
                "Different endings show respect levels and relationships",
                "Present tense is used for habitual actions and current states"
            ]
        case "basic_questions":
            return [
                "Tamil questions maintain the same word order as statements",
                "Question words usually come before the verb",
                "Tone and context are important in Tamil questions"
            ]
        default:
            return ["Tamil grammar reflects the rich cultural heritage of the language"]
        }
    }
    
    private func calculateDifficultyLevel(topic: GrammarTopic, level: CEFRLevel) -> Int {
        let baseLevel = level == .a1 ? 1 : level == .a2 ? 2 : 3
        let topicComplexity = topic.prerequisites.count
        return min(5, baseLevel + topicComplexity)
    }
    
    private func calculateEstimatedTime(steps: [GrammarWritingExercise.GrammarWritingStep], difficulty: Int) -> Int {
        let totalSeconds = steps.map { $0.expectedDuration }.reduce(0, +)
        let baseMinutes = totalSeconds / 60
        let difficultyMultiplier = 1.0 + (Double(difficulty - 1) * 0.2)
        return Int(Double(baseMinutes) * difficultyMultiplier)
    }
    
    private func saveGrammarExercises(_ exercises: [GrammarWritingExercise]) async {
        // Save to database - implementation would convert to TamilWritingContent format
        for exercise in exercises {
            print("Saving grammar exercise: \(exercise.title)")
        }
    }
    
    // MARK: - Public Interface
    
    /// Get grammar writing exercises for a specific lesson
    func getGrammarExercisesForLesson(_ lessonId: String) -> [GrammarWritingExercise] {
        return grammarWritingExercises.filter { $0.lessonId == lessonId }
    }
    
    /// Get exercises by grammar topic
    func getExercisesByTopic(_ topicId: String) -> [GrammarWritingExercise] {
        return grammarWritingExercises.filter { $0.grammarTopic.id == topicId }
    }
    
    /// Generate all grammar exercises for A1 level
    func generateA1GrammarExercises() async {
        await generateGrammarWritingExercises(for: "A1_GRAMMAR_FOUNDATION", level: .a1)
    }
}
