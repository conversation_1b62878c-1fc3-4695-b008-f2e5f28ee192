//
//  SecureErrorHandlingService.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation
import os.log

// MARK: - Secure Error Handling Service

class SecureErrorHandlingService {
    static let shared = SecureErrorHandlingService()

    private let logger = Logger(subsystem: "com.nira.app", category: "security")
    private let secureStorage = SecureStorageService.shared

    // Error tracking for security monitoring
    private var errorCounts: [String: Int] = [:]
    private var lastErrors: [SecureErrorEvent] = []
    private let maxStoredErrors = 50

    private init() {}

    // MARK: - Public Error Handling Methods

    /// Handle errors securely without exposing sensitive information
    func handleError(_ error: Error, context: String = "", userFacing: Bool = true) -> String {
        let errorEvent = SecureErrorEvent(
            error: error,
            context: context,
            timestamp: Date(),
            userFacing: userFacing
        )

        // Log error securely
        logErrorSecurely(errorEvent)

        // Track error for monitoring
        trackError(errorEvent)

        // Return user-safe error message
        return createUserSafeMessage(for: error, context: context)
    }

    /// Handle authentication errors with specific security considerations
    func handleAuthenticationError(_ error: Error, context: String = "") -> String {
        let errorEvent = SecureErrorEvent(
            error: error,
            context: "auth_\(context)",
            timestamp: Date(),
            userFacing: true
        )

        // Log with high security priority
        logSecurityEvent(errorEvent)

        // Track for potential security threats
        trackSecurityError(errorEvent)

        // Return generic authentication error message
        return createAuthenticationErrorMessage(for: error)
    }

    /// Handle network errors securely
    func handleNetworkError(_ error: Error, context: String = "") -> String {
        let errorEvent = SecureErrorEvent(
            error: error,
            context: "network_\(context)",
            timestamp: Date(),
            userFacing: true
        )

        logErrorSecurely(errorEvent)
        trackError(errorEvent)

        return createNetworkErrorMessage(for: error)
    }

    /// Handle AI/LLM service errors with OWASP LLM considerations
    func handleAIServiceError(_ error: Error, context: String = "") -> String {
        let errorEvent = SecureErrorEvent(
            error: error,
            context: "ai_\(context)",
            timestamp: Date(),
            userFacing: true
        )

        // AI errors may contain sensitive information
        logAIErrorSecurely(errorEvent)
        trackError(errorEvent)

        return createAIServiceErrorMessage(for: error)
    }

    // MARK: - Private Logging Methods

    private func logErrorSecurely(_ errorEvent: SecureErrorEvent) {
        // Create sanitized log message
        let sanitizedMessage = sanitizeForLogging(errorEvent.errorDescription)
        let sanitizedContext = sanitizeForLogging(errorEvent.context)

        // Log with appropriate level
        if errorEvent.userFacing {
            logger.info("User error in \(sanitizedContext): \(sanitizedMessage)")
        } else {
            logger.debug("Internal error in \(sanitizedContext): \(sanitizedMessage)")
        }

        // Store error event
        storeErrorEvent(errorEvent)
    }

    private func logSecurityEvent(_ errorEvent: SecureErrorEvent) {
        let sanitizedMessage = sanitizeForLogging(errorEvent.errorDescription)
        let sanitizedContext = sanitizeForLogging(errorEvent.context)

        // Security events get higher priority logging
        logger.error("Security event in \(sanitizedContext): \(sanitizedMessage)")

        // Store with security flag
        var securityEvent = errorEvent
        securityEvent.isSecurityRelated = true
        storeErrorEvent(securityEvent)
    }

    private func logAIErrorSecurely(_ errorEvent: SecureErrorEvent) {
        // AI errors need special handling to prevent prompt injection logging
        let sanitizedMessage = sanitizeAIErrorForLogging(errorEvent.errorDescription)
        let sanitizedContext = sanitizeForLogging(errorEvent.context)

        logger.info("AI service error in \(sanitizedContext): \(sanitizedMessage)")

        storeErrorEvent(errorEvent)
    }

    // MARK: - Error Message Creation

    private func createUserSafeMessage(for error: Error, context: String) -> String {
        // Never expose technical details to users
        switch error {
        case is ValidationError:
            return error.localizedDescription // Validation errors are safe to show
        case is NetworkSecurityError:
            return "Connection error. Please check your internet connection and try again."
        case is SecureStorageError:
            return "A storage error occurred. Please try again."
        case is SecureGeminiError:
            return "AI service is temporarily unavailable. Please try again later."
        default:
            return "An unexpected error occurred. Please try again."
        }
    }

    private func createAuthenticationErrorMessage(for error: Error) -> String {
        // Authentication errors should be generic to prevent user enumeration
        switch error {
        case let authError as AuthenticationError:
            switch authError {
            case .signInFailed:
                return "Invalid email or password. Please try again."
            case .signUpFailed:
                return "Unable to create account. Please try again."
            case .passwordResetFailed:
                return "Unable to reset password. Please try again."
            default:
                return "Authentication error. Please try again."
            }
        default:
            return "Authentication error. Please try again."
        }
    }

    private func createNetworkErrorMessage(for error: Error) -> String {
        if let networkError = error as? NetworkSecurityError {
            switch networkError {
            case .insecureScheme, .untrustedDomain:
                return "Connection security error. Please try again."
            case .httpError(let code) where code >= 500:
                return "Server is temporarily unavailable. Please try again later."
            case .httpError(let code) where code >= 400:
                return "Request error. Please check your input and try again."
            default:
                return "Network error. Please check your connection and try again."
            }
        }

        return "Network error. Please check your connection and try again."
    }

    private func createAIServiceErrorMessage(for error: Error) -> String {
        if let aiError = error as? SecureGeminiError {
            switch aiError {
            case .rateLimitExceeded, .requestTooSoon:
                return "AI service is busy. Please wait a moment and try again."
            case .blockedContent, .sensitiveDataDetected:
                return "Unable to process request. Please modify your input and try again."
            case .promptTooLong, .responseTooLong:
                return "Request is too long. Please shorten your input and try again."
            case .noNetworkConnection:
                return "No internet connection. Please check your connection and try again."
            default:
                return "AI service is temporarily unavailable. Please try again later."
            }
        }

        return "AI service error. Please try again later."
    }

    // MARK: - Error Tracking and Monitoring

    private func trackError(_ errorEvent: SecureErrorEvent) {
        let errorKey = "\(errorEvent.errorType)_\(errorEvent.context)"
        errorCounts[errorKey, default: 0] += 1

        // Store recent errors for analysis
        lastErrors.append(errorEvent)
        if lastErrors.count > maxStoredErrors {
            lastErrors.removeFirst(lastErrors.count - maxStoredErrors)
        }

        // Check for suspicious patterns
        checkForSuspiciousPatterns()
    }

    private func trackSecurityError(_ errorEvent: SecureErrorEvent) {
        trackError(errorEvent)

        // Additional security monitoring
        let securityKey = "security_\(errorEvent.context)"
        let securityCount = errorCounts[securityKey, default: 0] + 1
        errorCounts[securityKey] = securityCount

        // Alert if too many security errors
        if securityCount > 10 {
            logger.critical("High number of security errors detected: \(securityCount)")
        }
    }

    private func checkForSuspiciousPatterns() {
        let recentErrors = lastErrors.suffix(10)
        let recentSecurityErrors = recentErrors.filter { $0.isSecurityRelated }

        // Check for rapid security errors (potential attack)
        if recentSecurityErrors.count >= 5 {
            let timeSpan = recentSecurityErrors.last!.timestamp.timeIntervalSince(recentSecurityErrors.first!.timestamp)
            if timeSpan < 60 { // 5 security errors in 1 minute
                logger.critical("Potential security attack detected: \(recentSecurityErrors.count) security errors in \(timeSpan) seconds")
            }
        }
    }

    // MARK: - Data Sanitization

    private func sanitizeForLogging(_ message: String) -> String {
        var sanitized = message

        // Remove potential sensitive data patterns
        let sensitivePatterns = [
            ("password", "***"),
            ("token", "***"),
            ("key", "***"),
            ("secret", "***"),
            ("api_key", "***"),
            ("bearer", "***"),
            ("authorization", "***")
        ]

        for (pattern, replacement) in sensitivePatterns {
            sanitized = sanitized.replacingOccurrences(
                of: pattern,
                with: replacement,
                options: .caseInsensitive
            )
        }

        // Remove email addresses
        let emailRegex = try! NSRegularExpression(pattern: #"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"#)
        sanitized = emailRegex.stringByReplacingMatches(
            in: sanitized,
            range: NSRange(location: 0, length: sanitized.count),
            withTemplate: "***@***.***"
        )

        return sanitized
    }

    private func sanitizeAIErrorForLogging(_ message: String) -> String {
        var sanitized = sanitizeForLogging(message)

        // Additional AI-specific sanitization
        let aiPatterns = [
            ("prompt", "***"),
            ("instruction", "***"),
            ("system", "***"),
            ("model", "***"),
            ("training", "***")
        ]

        for (pattern, replacement) in aiPatterns {
            sanitized = sanitized.replacingOccurrences(
                of: pattern,
                with: replacement,
                options: .caseInsensitive
            )
        }

        return sanitized
    }

    private func storeErrorEvent(_ errorEvent: SecureErrorEvent) {
        // Store error events securely for analysis
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(errorEvent)

            let key = "error_\(UUID().uuidString)"
            try secureStorage.storeSecurely(data, forKey: key)
        } catch {
            // If we can't store the error, at least log it
            logger.error("Failed to store error event: \(error.localizedDescription)")
        }
    }

    // MARK: - Error Analytics

    func getErrorStatistics() -> ErrorStatistics {
        let recentErrors = lastErrors.filter { Date().timeIntervalSince($0.timestamp) < 3600 }
        return ErrorStatistics(
            totalErrors: lastErrors.count,
            networkErrors: lastErrors.filter { $0.errorType.contains("Network") }.count,
            authenticationErrors: lastErrors.filter { $0.errorType.contains("Authentication") }.count,
            apiErrors: lastErrors.filter { $0.errorType.contains("API") }.count,
            dataErrors: lastErrors.filter { $0.errorType.contains("Data") }.count,
            recentErrors: recentErrors.count,
            lastErrorTime: lastErrors.last?.timestamp
        )
    }

    private func getMostCommonErrors() -> [String: Int] {
        let sortedErrors = errorCounts.sorted { $0.value > $1.value }.prefix(5)
        var result: [String: Int] = [:]
        for (key, value) in sortedErrors {
            result[key] = value
        }
        return result
    }

    private func getRecentErrorRate() -> Double {
        let recentErrors = lastErrors.filter { Date().timeIntervalSince($0.timestamp) < 3600 } // Last hour
        return Double(recentErrors.count) / 60.0 // Errors per minute
    }
}

// MARK: - Supporting Types

struct SecureErrorEvent: Codable {
    let errorType: String
    let errorDescription: String
    let context: String
    let timestamp: Date
    let userFacing: Bool
    var isSecurityRelated: Bool = false

    init(error: Error, context: String, timestamp: Date, userFacing: Bool) {
        self.errorType = String(describing: type(of: error))
        self.errorDescription = error.localizedDescription
        self.context = context
        self.timestamp = timestamp
        self.userFacing = userFacing
    }
}

// Note: ErrorStatistics is defined in SecurityModels.swift
