import Foundation
import Combine

// MARK: - Cultural Insights Models

struct TamilCulturalInsight: Codable, Identifiable {
    let id: UUID
    let title: String
    let tamilTitle: String
    let description: String
    let category: CulturalCategory
    let content: String
    let tags: [String]
    let difficulty: InsightDifficulty
    let readingTime: Int
    let culturalSignificance: String
    let modernRelevance: String

    init(title: String, tamilTitle: String, description: String, category: CulturalCategory, content: String, tags: [String], difficulty: InsightDifficulty, readingTime: Int, culturalSignificance: String, modernRelevance: String) {
        self.id = UUID()
        self.title = title
        self.tamilTitle = tamilTitle
        self.description = description
        self.category = category
        self.content = content
        self.tags = tags
        self.difficulty = difficulty
        self.readingTime = readingTime
        self.culturalSignificance = culturalSignificance
        self.modernRelevance = modernRelevance
    }
}

// Using enums from TamilCulturalModels.swift

// MARK: - Cultural Insights Service

@MainActor
class CulturalInsightsService: ObservableObject {
    static let shared = CulturalInsightsService()

    @Published var todayInsight: TamilCulturalInsight
    @Published var featuredInsights: [TamilCulturalInsight] = []
    @Published var favoriteInsights: [TamilCulturalInsight] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private var cancellables = Set<AnyCancellable>()
    private let calendar = Calendar.current
    
    // Enhanced cultural insights database with comprehensive Tamil cultural content
    private let culturalInsights: [TamilCulturalInsight] = CulturalInsightsService.enhancedCulturalInsights + [
        TamilCulturalInsight(
            title: "The Art of Kolam",
            tamilTitle: "கோலம் கலை",
            description: "Discover the mathematical beauty and spiritual significance of Tamil floor art",
            category: .arts,
            content: "Kolam is a traditional Tamil art form where intricate patterns are drawn on the ground using rice flour. These geometric designs carry deep cultural and spiritual meanings, representing the Tamil philosophy of impermanence. Every morning, Tamil women create these ephemeral artworks that welcome prosperity and ward off evil. The patterns follow complex mathematical principles, often featuring symmetry, fractals, and geometric progressions.",
            tags: ["kolam", "art", "mathematics", "spirituality", "tradition"],
            difficulty: .intermediate,
            readingTime: 4,
            culturalSignificance: "Kolam represents the Tamil worldview of harmony between art, mathematics, and spirituality",
            modernRelevance: "Today, kolam patterns inspire modern art, architecture, and computer graphics algorithms"
        ),
        TamilCulturalInsight(
            title: "Tamil Poetry Traditions",
            tamilTitle: "தமிழ் கவிதை மரபு",
            description: "Journey through the evolution of Tamil poetry from Sangam to modern times",
            category: .literature,
            content: "Tamil poetry has a continuous tradition spanning over 2,000 years. From the ancient Sangam poems that celebrated love and war to the devotional hymns of the Bhakti movement, Tamil poetry has always been the voice of the people. The tradition includes various forms like Venba, Kali, and Aasiriyappa, each with distinct meters and purposes.",
            tags: ["poetry", "sangam", "literature", "bhakti", "tradition"],
            difficulty: .advanced,
            readingTime: 8,
            culturalSignificance: "Tamil poetry preserves the emotional and philosophical heritage of Tamil civilization",
            modernRelevance: "Contemporary Tamil poets continue to innovate while honoring traditional forms"
        )
    ]
    
    private init() {
        todayInsight = culturalInsights[0]
        loadFavoriteInsights()
        setupDailyRotation()

        // Load today's insight asynchronously
        Task {
            await loadTodayInsight()
        }
    }
    
    // MARK: - Public Methods
    
    func loadTodayInsight() async {
        isLoading = true
        
        let dayOfYear = calendar.ordinality(of: .day, in: .year, for: Date()) ?? 1
        let insightIndex = (dayOfYear - 1) % culturalInsights.count
        todayInsight = culturalInsights[insightIndex]
        
        await loadFeaturedInsights()
        
        isLoading = false
    }
    
    func getInsightsByCategory(_ category: CulturalCategory) -> [TamilCulturalInsight] {
        return culturalInsights.filter { $0.category == category }
    }

    func toggleFavorite(_ insight: TamilCulturalInsight) {
        if favoriteInsights.contains(where: { $0.id == insight.id }) {
            favoriteInsights.removeAll { $0.id == insight.id }
        } else {
            favoriteInsights.append(insight)
        }
        saveFavoriteInsights()
    }

    func isFavorite(_ insight: TamilCulturalInsight) -> Bool {
        return favoriteInsights.contains { $0.id == insight.id }
    }
    
    // MARK: - Private Methods
    
    private func loadFeaturedInsights() async {
        var featured: [TamilCulturalInsight] = []
        let categories = CulturalCategory.allCases.shuffled()

        for category in categories.prefix(4) {
            if let insight = culturalInsights.filter({ $0.category == category }).randomElement() {
                featured.append(insight)
            }
        }

        featuredInsights = featured
    }
    
    private func setupDailyRotation() {
        Timer.publish(every: 3600, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await MainActor.run {
                        let hour = Calendar.current.component(.hour, from: Date())
                        if hour == 0 {
                            Task {
                                await self?.loadTodayInsight()
                            }
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Persistence
    
    private func saveFavoriteInsights() {
        if let data = try? JSONEncoder().encode(favoriteInsights) {
            UserDefaults.standard.set(data, forKey: "favorite_cultural_insights")
        }
    }
    
    private func loadFavoriteInsights() {
        if let data = UserDefaults.standard.data(forKey: "favorite_cultural_insights"),
           let insights = try? JSONDecoder().decode([TamilCulturalInsight].self, from: data) {
            favoriteInsights = insights
        }
    }
}
