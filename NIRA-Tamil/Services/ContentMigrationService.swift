//
//  ContentMigrationService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import Supabase
import Combine

/// Service for migrating content from local files to Supabase database
class ContentMigrationService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ContentMigrationService()
    
    // MARK: - Published Properties
    @Published var migrationProgress: Double = 0.0
    @Published var currentOperation = ""
    @Published var isLoading = false
    @Published var migrationResults: [String] = []
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co")!,
            supabaseKey: APIKeys.supabaseAnonKey
        )
        
        print("📦 ContentMigrationService initialized")
    }
    
    // MARK: - Migration Methods
    
    /// Run complete content migration
    func runContentMigration() async {
        isLoading = true
        migrationProgress = 0.0
        migrationResults = []
        errorMessage = nil
        
        await migrateA1Lessons()
        await migrateA2Lessons()
        await migrateVocabularyContent()
        await migrateConversationContent()
        await migrateGrammarContent()
        await migratePracticeContent()
        
        migrationProgress = 1.0
        isLoading = false
        
        let successCount = migrationResults.filter { $0.contains("✅") }.count
        let totalOperations = migrationResults.count
        
        print("📦 Content migration completed: \(successCount)/\(totalOperations) successful")
    }
    
    /// Migrate A1 lessons to database
    private func migrateA1Lessons() async {
        currentOperation = "Migrating A1 Lessons..."
        migrationProgress = 0.1
        
        let a1Lessons = createA1LessonData()
        
        for (index, lesson) in a1Lessons.enumerated() {
            do {
                let _: TamilSupabaseLesson = try await supabase
                    .from("lessons")
                    .upsert(lesson)
                    .execute()
                    .value
                
                migrationResults.append("✅ A1 Lesson \(index + 1) migrated: \(lesson.titleEnglish)")
                print("✅ Migrated A1 lesson: \(lesson.titleEnglish)")
                
            } catch {
                migrationResults.append("❌ A1 Lesson \(index + 1) failed: \(error.localizedDescription)")
                print("❌ Failed to migrate A1 lesson: \(error)")
            }
        }
        
        migrationProgress = 0.25
    }
    
    /// Migrate A2 lessons to database
    private func migrateA2Lessons() async {
        currentOperation = "Migrating A2 Lessons..."
        migrationProgress = 0.3
        
        let a2Lessons = createA2LessonData()
        
        for (index, lesson) in a2Lessons.enumerated() {
            do {
                let _: TamilSupabaseLesson = try await supabase
                    .from("lessons")
                    .upsert(lesson)
                    .execute()
                    .value
                
                migrationResults.append("✅ A2 Lesson \(index + 1) migrated: \(lesson.titleEnglish)")
                print("✅ Migrated A2 lesson: \(lesson.titleEnglish)")
                
            } catch {
                migrationResults.append("❌ A2 Lesson \(index + 1) failed: \(error.localizedDescription)")
                print("❌ Failed to migrate A2 lesson: \(error)")
            }
        }
        
        migrationProgress = 0.45
    }
    
    /// Migrate vocabulary content
    private func migrateVocabularyContent() async {
        currentOperation = "Migrating Vocabulary..."
        migrationProgress = 0.5
        
        let vocabularyData = createVocabularyData()
        
        for (index, vocab) in vocabularyData.enumerated() {
            do {
                let _: TamilSupabaseVocabulary = try await supabase
                    .from("vocabulary")
                    .upsert(vocab)
                    .execute()
                    .value
                
                migrationResults.append("✅ Vocabulary \(index + 1) migrated: \(vocab.tamilTranslation)")
                
            } catch {
                migrationResults.append("❌ Vocabulary \(index + 1) failed: \(error.localizedDescription)")
                print("❌ Failed to migrate vocabulary: \(error)")
            }
        }
        
        migrationProgress = 0.65
    }
    
    /// Migrate conversation content
    private func migrateConversationContent() async {
        currentOperation = "Migrating Conversations..."
        migrationProgress = 0.7
        
        let conversationData = createConversationData()
        
        for (index, conversation) in conversationData.enumerated() {
            do {
                let _: TamilSupabaseConversation = try await supabase
                    .from("conversations")
                    .upsert(conversation)
                    .execute()
                    .value
                
                migrationResults.append("✅ Conversation \(index + 1) migrated")
                
            } catch {
                migrationResults.append("❌ Conversation \(index + 1) failed: \(error.localizedDescription)")
                print("❌ Failed to migrate conversation: \(error)")
            }
        }
        
        migrationProgress = 0.8
    }
    
    /// Migrate grammar content
    private func migrateGrammarContent() async {
        currentOperation = "Migrating Grammar Topics..."
        migrationProgress = 0.85
        
        let grammarData = createGrammarData()
        
        for (index, grammar) in grammarData.enumerated() {
            do {
                let _: TamilSupabaseGrammarTopic = try await supabase
                    .from("grammar_topics")
                    .upsert(grammar)
                    .execute()
                    .value
                
                migrationResults.append("✅ Grammar topic \(index + 1) migrated: \(grammar.titleEnglish)")
                
            } catch {
                migrationResults.append("❌ Grammar topic \(index + 1) failed: \(error.localizedDescription)")
                print("❌ Failed to migrate grammar: \(error)")
            }
        }
        
        migrationProgress = 0.9
    }
    
    /// Migrate practice content
    private func migratePracticeContent() async {
        currentOperation = "Migrating Practice Exercises..."
        migrationProgress = 0.95
        
        let practiceData = createPracticeData()
        
        for (index, practice) in practiceData.enumerated() {
            do {
                let _: TamilSupabasePracticeExercise = try await supabase
                    .from("practice_exercises")
                    .upsert(practice)
                    .execute()
                    .value
                
                migrationResults.append("✅ Practice exercise \(index + 1) migrated")
                
            } catch {
                migrationResults.append("❌ Practice exercise \(index + 1) failed: \(error.localizedDescription)")
                print("❌ Failed to migrate practice: \(error)")
            }
        }
    }
    
    /// Clear migration results
    func clearResults() {
        migrationResults = []
        errorMessage = nil
        migrationProgress = 0.0
        currentOperation = ""
    }
}

// MARK: - Data Creation Methods

extension ContentMigrationService {

    /// Create A1 lesson data for migration
    private func createA1LessonData() -> [TamilSupabaseLesson] {
        return [
            TamilSupabaseLesson(
                id: "a1-lesson-1",
                lessonNumber: 1,
                levelCode: "A1",
                titleEnglish: "Basic Greetings",
                titleTamil: "அடிப்படை வாழ்த்துகள்",
                titleRomanization: "Aṭippaṭai vāḻttukkaḷ",
                descriptionEnglish: "Learn essential Tamil greetings for daily interactions",
                descriptionTamil: "தினசரி உரையாடலுக்கான அத்தியாவசிய தமிழ் வாழ்த்துகளைக் கற்றுக்கொள்ளுங்கள்",
                focus: "Greetings and basic courtesy",
                durationMinutes: 20,
                difficultyScore: 1,
                prerequisites: [],
                tags: ["greetings", "basic", "courtesy"],
                culturalContext: "Tamil greetings reflect respect and cultural values",
                isActive: true,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            ),
            TamilSupabaseLesson(
                id: "a1-lesson-2",
                lessonNumber: 2,
                levelCode: "A1",
                titleEnglish: "Family Members",
                titleTamil: "குடும்ப உறுப்பினர்கள்",
                titleRomanization: "Kuṭumpa uṟuppiṉarkaḷ",
                descriptionEnglish: "Learn to identify and talk about family relationships",
                descriptionTamil: "குடும்ப உறவுகளை அடையாளம் காணவும் பேசவும் கற்றுக்கொள்ளுங்கள்",
                focus: "Family vocabulary and relationships",
                durationMinutes: 25,
                difficultyScore: 2,
                prerequisites: ["a1-lesson-1"],
                tags: ["family", "relationships", "vocabulary"],
                culturalContext: "Family is central to Tamil culture and society",
                isActive: true,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            ),
            TamilSupabaseLesson(
                id: "a1-lesson-3",
                lessonNumber: 3,
                levelCode: "A1",
                titleEnglish: "Numbers and Counting",
                titleTamil: "எண்கள் மற்றும் எண்ணுதல்",
                titleRomanization: "Eṇkaḷ maṟṟum eṇṇutal",
                descriptionEnglish: "Master Tamil numbers from 1 to 100",
                descriptionTamil: "1 முதல் 100 வரையிலான தமிழ் எண்களில் தேர்ச்சி பெறுங்கள்",
                focus: "Numerical system and counting",
                durationMinutes: 30,
                difficultyScore: 2,
                prerequisites: ["a1-lesson-1", "a1-lesson-2"],
                tags: ["numbers", "counting", "mathematics"],
                culturalContext: "Tamil has a unique numerical system with cultural significance",
                isActive: true,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            )
        ]
    }

    /// Create A2 lesson data for migration
    private func createA2LessonData() -> [TamilSupabaseLesson] {
        return [
            TamilSupabaseLesson(
                id: "a2-lesson-1",
                lessonNumber: 1,
                levelCode: "A2",
                titleEnglish: "Shopping and Markets",
                titleTamil: "கடைகள் மற்றும் சந்தைகள்",
                titleRomanization: "Kaṭaikaḷ maṟṟum cantaikaḷ",
                descriptionEnglish: "Learn to navigate Tamil markets and make purchases",
                descriptionTamil: "தமிழ் சந்தைகளில் செல்லவும் வாங்கவும் கற்றுக்கொள்ளுங்கள்",
                focus: "Shopping vocabulary and bargaining",
                durationMinutes: 35,
                difficultyScore: 3,
                prerequisites: ["a1-lesson-1", "a1-lesson-2", "a1-lesson-3"],
                tags: ["shopping", "markets", "money"],
                culturalContext: "Traditional Tamil markets are vibrant social spaces",
                isActive: true,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            ),
            TamilSupabaseLesson(
                id: "a2-lesson-2",
                lessonNumber: 2,
                levelCode: "A2",
                titleEnglish: "Food and Cooking",
                titleTamil: "உணவு மற்றும் சமையல்",
                titleRomanization: "Uṇavu maṟṟum camayal",
                descriptionEnglish: "Explore Tamil cuisine vocabulary and cooking terms",
                descriptionTamil: "தமிழ் உணவு வகைகள் மற்றும் சமையல் சொற்களை ஆராயுங்கள்",
                focus: "Food vocabulary and cooking methods",
                durationMinutes: 40,
                difficultyScore: 3,
                prerequisites: ["a2-lesson-1"],
                tags: ["food", "cooking", "cuisine"],
                culturalContext: "Tamil cuisine reflects regional diversity and traditions",
                isActive: true,
                createdAt: ISO8601DateFormatter().string(from: Date()),
                updatedAt: ISO8601DateFormatter().string(from: Date())
            )
        ]
    }

    /// Create vocabulary data for migration
    private func createVocabularyData() -> [TamilSupabaseVocabulary] {
        return [
            TamilSupabaseVocabulary(
                id: "vocab-1",
                lessonId: "a1-lesson-1",
                vocabId: "1",
                englishWord: "Hello/Greetings",
                tamilTranslation: "வணக்கம்",
                romanization: "Vaṇakkam",
                ipaPronunciation: "ʋaɳakkam",
                partOfSpeech: "Interjection",
                difficultyLevel: 1,
                frequencyRank: 1,
                culturalNotes: "Universal Tamil greeting showing respect",
                relatedTerms: ["greeting", "respect"],
                exampleSentenceEnglish: "Hello, how are you?",
                exampleSentenceTamil: "வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?",
                exampleSentenceRomanization: "Vaṇakkam, nīṅkaḷ eppaṭi irukkiṟīrkaḷ?",
                audioWordUrl: nil,
                audioSentenceUrl: nil,
                imageUrl: nil,
                createdAt: ISO8601DateFormatter().string(from: Date())
            ),
            TamilSupabaseVocabulary(
                id: "vocab-2",
                lessonId: "a1-lesson-1",
                vocabId: "2",
                englishWord: "Thank you",
                tamilTranslation: "நன்றி",
                romanization: "Naṉṟi",
                ipaPronunciation: "nanri",
                partOfSpeech: "Interjection",
                difficultyLevel: 1,
                frequencyRank: 2,
                culturalNotes: "Essential expression of gratitude in Tamil culture",
                relatedTerms: ["gratitude", "thanks"],
                exampleSentenceEnglish: "Thank you for your help",
                exampleSentenceTamil: "உங்கள் உதவிக்கு நன்றி",
                exampleSentenceRomanization: "Uṅkaḷ utavikku naṉṟi",
                audioWordUrl: nil,
                audioSentenceUrl: nil,
                imageUrl: nil,
                createdAt: ISO8601DateFormatter().string(from: Date())
            )
        ]
    }

    /// Create conversation data for migration
    private func createConversationData() -> [TamilSupabaseConversation] {
        return [
            TamilSupabaseConversation(
                id: "conv-1",
                lessonId: "a1-lesson-1",
                conversationId: "1",
                titleEnglish: "Meeting Someone New",
                titleTamil: "புதிய நபரை சந்திப்பது",
                contextDescription: "Two people meeting for the first time",
                participants: "Two adults",
                formalityLevel: "Formal",
                culturalSetting: "Traditional Tamil greeting context",
                audioFullUrl: nil,
                createdAt: ISO8601DateFormatter().string(from: Date())
            )
        ]
    }

    /// Create grammar data for migration
    private func createGrammarData() -> [TamilSupabaseGrammarTopic] {
        return [
            TamilSupabaseGrammarTopic(
                id: "grammar-1",
                lessonId: "a1-lesson-1",
                grammarId: "1",
                titleEnglish: "Basic Sentence Structure",
                titleTamil: "அடிப்படை வாக்கிய அமைப்பு",
                titleRomanization: "adippadai vaakkiya amaippu",
                ruleEnglish: "Tamil follows Subject-Object-Verb (SOV) word order",
                ruleTamil: "தமிழ் மொழி எழுவாய்-செயப்படுபொருள்-செயல் (SOV) வரிசையைப் பின்பற்றுகிறது",
                explanation: "Tamil sentence structure reflects logical thinking patterns",
                difficultyLevel: 1,
                tips: ["Start with subject", "Place object before verb", "Verb comes at the end"],
                commonMistakes: ["Placing verb before object", "Forgetting subject"],
                createdAt: ISO8601DateFormatter().string(from: Date())
            )
        ]
    }

    /// Create practice exercise data for migration
    private func createPracticeData() -> [TamilSupabasePracticeExercise] {
        return [
            TamilSupabasePracticeExercise(
                id: "practice-1",
                lessonId: "a1-lesson-1",
                exerciseId: "1",
                exerciseType: "multiple_choice",
                titleEnglish: "Tamil Greetings Quiz",
                titleTamil: "தமிழ் வாழ்த்துகள் வினாடி வினா",
                instructionsEnglish: "Choose the correct Tamil word for 'Hello'",
                instructionsTamil: "'வணக்கம்' என்ற சொல்லின் சரியான பொருளைத் தேர்ந்தெடுக்கவும்",
                difficultyLevel: 1,
                pointsValue: 10,
                timeLimitSeconds: 30,
                createdAt: ISO8601DateFormatter().string(from: Date())
            )
        ]
    }
}
