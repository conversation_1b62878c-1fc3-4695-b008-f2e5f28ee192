//
//  AuthenticationService.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import Foundation
import SwiftUI
import Supabase
import Combine
import AuthenticationServices

// MARK: - Authentication Service

@MainActor
class AuthenticationService: ObservableObject {
    static let shared = AuthenticationService()

    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: Supabase.User?
    @Published var userProfile: SupabaseUserProfile?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var authState: AuthState = .unauthenticated

    // MARK: - Private Properties
    private let supabaseClient = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        setupAuthStateListener()
        checkInitialAuthState()
    }
    
    // MARK: - Public Methods
    
    /// Sign up a new user with email and password
    func signUp(email: String, password: String, firstName: String? = nil, lastName: String? = nil) async throws {
        isLoading = true
        errorMessage = nil

        do {
            print("📝 Attempting email sign-up for: \(email)")
            let session = try await supabaseClient.client.auth.signUp(
                email: email,
                password: password
            )

            self.currentUser = session.user
            self.isAuthenticated = true
            authState = .authenticated
            print("✅ Email sign-up successful: \(session.user.id)")

            // Create user profile if sign up successful
            if let firstName = firstName, let lastName = lastName {
                try await createUserProfile(firstName: firstName, lastName: lastName)
            }

        } catch {
            print("❌ Email sign-up failed: \(error)")
            errorMessage = handleAuthError(error)
            throw AuthenticationError.signUpFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Sign in with email and password
    func signIn(email: String, password: String) async throws {
        isLoading = true
        errorMessage = nil

        do {
            print("🔑 Attempting email sign-in for: \(email)")
            let session = try await supabaseClient.client.auth.signIn(
                email: email,
                password: password
            )

            self.currentUser = session.user
            self.isAuthenticated = true
            authState = .authenticated
            print("✅ Email sign-in successful: \(session.user.id)")

            // Sync NIRASupabaseClient with the new session
            await NIRASupabaseClient.shared.syncWithCurrentSession()

            // Load user profile
            await loadUserProfile()

        } catch {
            print("❌ Email sign-in failed: \(error)")
            errorMessage = handleAuthError(error)
            throw AuthenticationError.signInFailed(error.localizedDescription)
        }

        isLoading = false
    }
    
    /// Sign out the current user
    func signOut() async throws {
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseClient.signOut()
            
            // Clear local state
            currentUser = nil
            userProfile = nil
            isAuthenticated = false
            authState = .unauthenticated
            
        } catch {
            errorMessage = handleAuthError(error)
            throw AuthenticationError.signOutFailed(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    /// Reset password for email
    func resetPassword(email: String) async throws {
        isLoading = true
        errorMessage = nil

        do {
            print("🔄 Sending password reset for: \(email)")
            try await supabaseClient.client.auth.resetPasswordForEmail(email)
            print("✅ Password reset email sent")

        } catch {
            print("❌ Password reset failed: \(error)")
            errorMessage = handleAuthError(error)
            throw AuthenticationError.passwordResetFailed(error.localizedDescription)
        }

        isLoading = false
    }

    // MARK: - Social Authentication

    /// Sign in with Apple
    func signInWithApple(_ result: Result<ASAuthorization, Error>) async throws {
        isLoading = true
        errorMessage = nil

        do {
            switch result {
            case .success(let authorization):
                if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
                    print("🍎 Attempting Apple Sign In")

                    guard let identityToken = appleIDCredential.identityToken,
                          let identityTokenString = String(data: identityToken, encoding: .utf8) else {
                        throw AuthenticationError.invalidAppleCredentials
                    }

                    let session = try await supabaseClient.client.auth.signInWithIdToken(
                        credentials: OpenIDConnectCredentials(
                            provider: .apple,
                            idToken: identityTokenString
                        )
                    )

                    self.currentUser = session.user
                    self.isAuthenticated = true
                    authState = .authenticated
                    print("✅ Apple Sign In successful: \(session.user.id)")

                    // Load user profile
                    await loadUserProfile()
                }

            case .failure(let error):
                print("❌ Apple Sign In failed: \(error)")
                throw error
            }

        } catch {
            print("❌ Apple Sign In error: \(error)")
            errorMessage = handleAuthError(error)
            throw AuthenticationError.appleSignInFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Sign in with Google
    func signInWithGoogle() async throws {
        isLoading = true
        errorMessage = nil

        do {
            print("🔍 Google Sign In not yet implemented")
            // TODO: Implement Google Sign In
            // This would require Google Sign In SDK
            throw AuthenticationError.providerNotImplemented("Google Sign In")

        } catch {
            print("❌ Google Sign In failed: \(error)")
            errorMessage = handleAuthError(error)
            throw AuthenticationError.googleSignInFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Sign in with Facebook
    func signInWithFacebook() async throws {
        isLoading = true
        errorMessage = nil

        do {
            print("📘 Facebook Sign In not yet implemented")
            // TODO: Implement Facebook Sign In
            // This would require Facebook SDK
            throw AuthenticationError.providerNotImplemented("Facebook Sign In")

        } catch {
            print("❌ Facebook Sign In failed: \(error)")
            errorMessage = handleAuthError(error)
            throw AuthenticationError.facebookSignInFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Sign in with Twitter
    func signInWithTwitter() async throws {
        isLoading = true
        errorMessage = nil

        do {
            print("🐦 Twitter Sign In not yet implemented")
            // TODO: Implement Twitter Sign In
            // This would require Twitter SDK
            throw AuthenticationError.providerNotImplemented("Twitter Sign In")

        } catch {
            print("❌ Twitter Sign In failed: \(error)")
            errorMessage = handleAuthError(error)
            throw AuthenticationError.twitterSignInFailed(error.localizedDescription)
        }

        isLoading = false
    }
    
    /// Continue as guest (limited functionality)
    func continueAsGuest() async {
        print("👤 Continuing as guest")
        isLoading = true

        do {
            // Create anonymous session with Supabase
            let session = try await supabaseClient.client.auth.signInAnonymously()
            self.currentUser = session.user
            self.isAuthenticated = true
            authState = .guest
            print("✅ Guest access granted: \(session.user.id)")

            // Create a temporary guest profile
            userProfile = SupabaseUserProfile(
                id: session.user.id.uuidString,
                email: "<EMAIL>",
                firstName: "Guest",
                lastName: "User",
                preferredLanguages: ["english"],
                createdAt: Date(),
                lastActiveDate: Date(),
                isEmailVerified: false,
                subscriptionTier: "free",
                totalLessonsCompleted: 0,
                currentStreak: 0,
                longestStreak: 0,
                totalStudyTimeMinutes: 0
            )

        } catch {
            print("❌ Guest access failed: \(error)")
            errorMessage = "Failed to continue as guest"
            // Fallback to local guest mode
            authState = .guest
            isAuthenticated = true
            userProfile = SupabaseUserProfile(
                id: UUID().uuidString,
                email: "<EMAIL>",
                firstName: "Guest",
                lastName: "User",
                preferredLanguages: ["english"],
                createdAt: Date(),
                lastActiveDate: Date(),
                isEmailVerified: false,
                subscriptionTier: "free",
                totalLessonsCompleted: 0,
                currentStreak: 0,
                longestStreak: 0,
                totalStudyTimeMinutes: 0
            )
        }

        isLoading = false
    }
    
    /// Update user profile
    func updateProfile(firstName: String? = nil, lastName: String? = nil, preferredLanguages: [String]? = nil) async throws {
        guard authState != .guest else {
            throw AuthenticationError.guestLimitation
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseClient.updateUserProfile(
                userId: UUID(),
                profileData: [
                    "first_name": firstName as Any,
                    "last_name": lastName as Any,
                    "preferred_languages": (preferredLanguages ?? []).joined(separator: ",") as Any
                ]
            )
            
            // Reload user profile
            await loadUserProfile()
            
        } catch {
            errorMessage = handleAuthError(error)
            throw AuthenticationError.profileUpdateFailed(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    /// Check if user has required permissions
    func hasPermission(for feature: AppFeature) -> Bool {
        switch authState {
        case .authenticated:
            return true
        case .guest:
            return feature.isAvailableForGuests
        case .unauthenticated, .emailVerificationRequired:
            return false
        }
    }
    
    /// Upgrade guest to full account
    func upgradeGuestAccount(email: String, password: String) async throws {
        guard authState == .guest else {
            throw AuthenticationError.notGuest
        }
        
        try await signUp(email: email, password: password)
    }
    
    // MARK: - Private Methods
    
    private func setupAuthStateListener() {
        // Listen to Supabase auth state changes
        Task {
            for await state in supabaseClient.client.auth.authStateChanges {
                await MainActor.run {
                    switch state.event {
                    case .signedIn:
                        if let user = state.session?.user {
                            print("🔐 Auth state changed: User signed in - \(user.id)")
                            self.currentUser = user
                            self.isAuthenticated = true
                            self.authState = .authenticated

                            // Sync NIRASupabaseClient with the new session
                            Task {
                                await NIRASupabaseClient.shared.syncWithCurrentSession()
                                await self.loadUserProfile()
                            }
                        }
                    case .signedOut:
                        print("🔐 Auth state changed: User signed out")
                        self.currentUser = nil
                        self.isAuthenticated = false
                        self.authState = .unauthenticated
                        self.userProfile = nil
                    case .tokenRefreshed:
                        print("🔐 Auth state changed: Token refreshed")
                        if let user = state.session?.user {
                            self.currentUser = user
                            Task {
                                await NIRASupabaseClient.shared.syncWithCurrentSession()
                            }
                        }
                    default:
                        break
                    }
                }
            }
        }

        // Also listen to NIRASupabaseClient session changes for fallback
        supabaseClient.$session
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (session: Session?) in
                if session == nil && self?.authState != .guest {
                    self?.currentUser = nil
                    self?.userProfile = nil
                    self?.isAuthenticated = false
                    self?.authState = .unauthenticated
                }
            }
            .store(in: &cancellables)
    }
    
    private func checkInitialAuthState() {
        // Check if user is already authenticated
        Task {
            do {
                let session = try await supabaseClient.client.auth.session
                await MainActor.run {
                    print("🔐 Found existing session for user: \(session.user.id)")
                    self.currentUser = session.user
                    self.isAuthenticated = true
                    self.authState = .authenticated
                }

                // Sync NIRASupabaseClient with the existing session
                await NIRASupabaseClient.shared.syncWithCurrentSession()
                await loadUserProfile()
            } catch {
                await MainActor.run {
                    print("🔐 No existing session found: \(error)")
                    self.currentUser = nil
                    self.isAuthenticated = false
                    self.authState = .unauthenticated
                }
            }
        }
    }
    
    private func createUserProfile(firstName: String, lastName: String) async throws {
        guard let userId = currentUser?.id else {
            throw AuthenticationError.noCurrentUser
        }
        
        let profile = SupabaseUserProfile(
            id: userId.uuidString,
            email: currentUser?.email ?? "",
            firstName: firstName,
            lastName: lastName,
            preferredLanguages: ["english"],
            createdAt: Date(),
            lastActiveDate: Date(),
            isEmailVerified: false,
            subscriptionTier: "free",
            totalLessonsCompleted: 0,
            currentStreak: 0,
            longestStreak: 0,
            totalStudyTimeMinutes: 0
        )
        
        // Save to database
        try await supabaseClient.createUserProfile(profile)
        userProfile = profile
    }
    
    private func loadUserProfile() async {
        guard authState == .authenticated else { return }
        
        do {
            userProfile = try await supabaseClient.getUserProfile()
        } catch {
            print("❌ Failed to load user profile: \(error)")
            // Don't throw error here, just log it
        }
    }
    
    private func handleAuthError(_ error: Error) -> String {
        if let authError = error as? AuthError {
            // Handle known Supabase AuthError cases
            let errorMessage = authError.localizedDescription.lowercased()
            
            if errorMessage.contains("invalid") && errorMessage.contains("credentials") {
                return "Invalid email or password. Please try again."
            } else if errorMessage.contains("email") && errorMessage.contains("confirm") {
                return "Please check your email and click the confirmation link."
            } else if errorMessage.contains("too many") || errorMessage.contains("rate limit") {
                return "Too many attempts. Please wait a moment and try again."
            } else if errorMessage.contains("weak") && errorMessage.contains("password") {
                return "Password is too weak. Please choose a stronger password."
            } else if errorMessage.contains("already") && errorMessage.contains("registered") {
                return "An account with this email already exists."
            } else {
                return "Authentication error: \(authError.localizedDescription)"
            }
        }
        
        return "An unexpected error occurred. Please try again."
    }
}

// MARK: - Supporting Types

enum AuthState {
    case unauthenticated
    case authenticated
    case guest
    case emailVerificationRequired
    
    var description: String {
        switch self {
        case .unauthenticated:
            return "Not signed in"
        case .authenticated:
            return "Signed in"
        case .guest:
            return "Guest mode"
        case .emailVerificationRequired:
            return "Email verification required"
        }
    }
}

enum AuthenticationError: LocalizedError {
    case signUpFailed(String)
    case signInFailed(String)
    case signOutFailed(String)
    case passwordResetFailed(String)
    case profileUpdateFailed(String)
    case appleSignInFailed(String)
    case googleSignInFailed(String)
    case facebookSignInFailed(String)
    case twitterSignInFailed(String)
    case invalidAppleCredentials
    case providerNotImplemented(String)
    case noCurrentUser
    case guestLimitation
    case notGuest

    var errorDescription: String? {
        switch self {
        case .signUpFailed(let message):
            return "Sign up failed: \(message)"
        case .signInFailed(let message):
            return "Sign in failed: \(message)"
        case .signOutFailed(let message):
            return "Sign out failed: \(message)"
        case .passwordResetFailed(let message):
            return "Password reset failed: \(message)"
        case .profileUpdateFailed(let message):
            return "Profile update failed: \(message)"
        case .appleSignInFailed(let message):
            return "Apple Sign In failed: \(message)"
        case .googleSignInFailed(let message):
            return "Google Sign In failed: \(message)"
        case .facebookSignInFailed(let message):
            return "Facebook Sign In failed: \(message)"
        case .twitterSignInFailed(let message):
            return "Twitter Sign In failed: \(message)"
        case .invalidAppleCredentials:
            return "Invalid Apple credentials received"
        case .providerNotImplemented(let provider):
            return "\(provider) is not yet implemented"
        case .noCurrentUser:
            return "No current user found"
        case .guestLimitation:
            return "This feature requires a full account. Please sign up to continue."
        case .notGuest:
            return "This action is only available for guest users"
        }
    }
}

enum AppFeature {
    case aiChat
    case lessons
    case simulations
    case progress
    case profile
    case voiceRecording
    case fileUpload
    case socialFeatures
    case advancedAnalytics
    
    var isAvailableForGuests: Bool {
        switch self {
        case .aiChat, .lessons, .simulations:
            return true // Basic features available for guests
        case .progress, .profile, .voiceRecording, .fileUpload, .socialFeatures, .advancedAnalytics:
            return false // Advanced features require account
        }
    }
    
    var description: String {
        switch self {
        case .aiChat:
            return "AI Chat"
        case .lessons:
            return "Lessons"
        case .simulations:
            return "Simulations"
        case .progress:
            return "Progress Tracking"
        case .profile:
            return "Profile Management"
        case .voiceRecording:
            return "Voice Recording"
        case .fileUpload:
            return "File Upload"
        case .socialFeatures:
            return "Social Features"
        case .advancedAnalytics:
            return "Advanced Analytics"
        }
    }
}

// MARK: - Extensions

extension NIRASupabaseClient {
    func createUserProfile(_ profile: SupabaseUserProfile) async throws {
        let _: [String: Any] = [
            "id": profile.id,
            "email": profile.email,
            "first_name": profile.firstName ?? "",
            "last_name": profile.lastName ?? "",
            "preferred_languages": profile.preferredLanguages.joined(separator: ","),
            "created_at": ISO8601DateFormatter().string(from: profile.createdAt),
            "last_active_date": ISO8601DateFormatter().string(from: profile.lastActiveDate),
            "is_email_verified": profile.isEmailVerified,
            "subscription_tier": profile.subscriptionTier,
            "total_lessons_completed": profile.totalLessonsCompleted,
            "current_streak": profile.currentStreak,
            "longest_streak": profile.longestStreak,
            "total_study_time_minutes": profile.totalStudyTimeMinutes
        ]

        // Mock database operation for compilation
        // _ = try await client.from("users")
        //     .insert(profileData)
        //     .execute()
    }
} 