import Foundation
import SwiftUI
import Combine

// MARK: - Reading Statistics Service

@MainActor
class ReadingStatisticsService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ReadingStatisticsService()
    
    // MARK: - Published Properties
    @Published var dailyStats: DailyReadingStats = DailyReadingStats()
    @Published var weeklyStats: WeeklyReadingStats = WeeklyReadingStats()
    @Published var overallStats: OverallReadingStats = OverallReadingStats()
    @Published var achievements: [ReadingAchievement] = []
    @Published var currentStreak: Int = 0
    @Published var longestStreak: Int = 0
    @Published var isLoading = false
    
    // MARK: - Private Properties
    private let readingService = ReadingContentService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        setupObservers()
        loadStatistics()
    }
    
    // MARK: - Setup
    
    private func setupObservers() {
        // Observe reading progress changes
        readingService.$userProgress
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.updateStatistics()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Statistics Calculation
    
    func updateStatistics() {
        calculateDailyStats()
        calculateWeeklyStats()
        calculateOverallStats()
        updateStreaks()
        checkForNewAchievements()
        saveStatistics()
    }
    
    private func calculateDailyStats() {
        let today = Calendar.current.startOfDay(for: Date())
        let todayProgress = readingService.userProgress.filter { progress in
            Calendar.current.isDate(progress.lastReadAt, inSameDayAs: today)
        }
        
        dailyStats = DailyReadingStats(
            date: today,
            contentRead: todayProgress.count,
            totalReadingTime: todayProgress.reduce(0) { $0 + $1.readingTimeSeconds },
            completedContent: todayProgress.filter { $0.isCompleted }.count,
            averageCompletionRate: calculateAverageCompletion(for: todayProgress),
            categoriesExplored: Set(todayProgress.compactMap { progress in
                readingService.readingContent.first { $0.contentId == progress.contentId }?.category
            }).count
        )
    }
    
    private func calculateWeeklyStats() {
        let calendar = Calendar.current
        let today = Date()
        let weekStart = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today
        
        let weekProgress = readingService.userProgress.filter { progress in
            progress.lastReadAt >= weekStart
        }
        
        // Calculate daily breakdown for the week
        var dailyBreakdown: [Date: Int] = [:]
        for i in 0..<7 {
            if let day = calendar.date(byAdding: .day, value: i, to: weekStart) {
                let dayProgress = weekProgress.filter { progress in
                    calendar.isDate(progress.lastReadAt, inSameDayAs: day)
                }
                dailyBreakdown[day] = dayProgress.count
            }
        }
        
        weeklyStats = WeeklyReadingStats(
            weekStart: weekStart,
            totalContentRead: weekProgress.count,
            totalReadingTime: weekProgress.reduce(0) { $0 + $1.readingTimeSeconds },
            dailyBreakdown: dailyBreakdown,
            averageDailyReading: Double(weekProgress.count) / 7.0,
            bestDay: dailyBreakdown.max(by: { $0.value < $1.value })?.key,
            consistencyScore: calculateConsistencyScore(dailyBreakdown)
        )
    }
    
    private func calculateOverallStats() {
        let allProgress = readingService.userProgress
        let allContent = readingService.readingContent
        
        // Calculate category breakdown
        var categoryStats: [ReadingCategory: CategoryStats] = [:]
        for category in ReadingCategory.allCases {
            let categoryContent = allContent.filter { $0.category == category }
            let categoryProgress = allProgress.filter { progress in
                categoryContent.contains { $0.contentId == progress.contentId }
            }
            
            categoryStats[category] = CategoryStats(
                totalContent: categoryContent.count,
                completedContent: categoryProgress.filter { $0.isCompleted }.count,
                totalReadingTime: categoryProgress.reduce(0) { $0 + $1.readingTimeSeconds },
                averageScore: calculateAverageScore(for: categoryProgress)
            )
        }
        
        // Calculate level breakdown
        var levelStats: [CEFRLevel: LevelStats] = [:]
        for level in CEFRLevel.allCases {
            let levelContent = allContent.filter { $0.difficultyLevel == level }
            let levelProgress = allProgress.filter { progress in
                levelContent.contains { $0.contentId == progress.contentId }
            }
            
            levelStats[level] = LevelStats(
                totalContent: levelContent.count,
                completedContent: levelProgress.filter { $0.isCompleted }.count,
                averageReadingTime: calculateAverageReadingTime(for: levelProgress),
                masteryLevel: calculateMasteryLevel(for: levelProgress, totalContent: levelContent.count)
            )
        }
        
        overallStats = OverallReadingStats(
            totalContentRead: allProgress.count,
            totalContentCompleted: allProgress.filter { $0.isCompleted }.count,
            totalReadingTime: allProgress.reduce(0) { $0 + $1.readingTimeSeconds },
            averageReadingSpeed: calculateAverageReadingSpeed(),
            categoryStats: categoryStats,
            levelStats: levelStats,
            favoriteCategory: findFavoriteCategory(categoryStats),
            strongestLevel: findStrongestLevel(levelStats),
            improvementAreas: identifyImprovementAreas(categoryStats, levelStats)
        )
    }
    
    private func updateStreaks() {
        let sortedProgress = readingService.userProgress
            .filter { $0.isCompleted }
            .sorted { $0.lastReadAt > $1.lastReadAt }
        
        // Calculate current streak
        var streak = 0
        var currentDate = Calendar.current.startOfDay(for: Date())
        
        for progress in sortedProgress {
            let progressDate = Calendar.current.startOfDay(for: progress.lastReadAt)
            
            if Calendar.current.isDate(progressDate, inSameDayAs: currentDate) ||
               Calendar.current.isDate(progressDate, inSameDayAs: Calendar.current.date(byAdding: .day, value: -1, to: currentDate)!) {
                streak += 1
                currentDate = Calendar.current.date(byAdding: .day, value: -1, to: progressDate)!
            } else {
                break
            }
        }
        
        currentStreak = streak
        
        // Calculate longest streak
        var maxStreak = 0
        var tempStreak = 0
        var lastDate: Date?
        
        for progress in sortedProgress.reversed() {
            let progressDate = Calendar.current.startOfDay(for: progress.lastReadAt)
            
            if let last = lastDate {
                let daysDiff = Calendar.current.dateComponents([.day], from: last, to: progressDate).day ?? 0
                if daysDiff <= 1 {
                    tempStreak += 1
                } else {
                    maxStreak = max(maxStreak, tempStreak)
                    tempStreak = 1
                }
            } else {
                tempStreak = 1
            }
            
            lastDate = progressDate
        }
        
        longestStreak = max(maxStreak, tempStreak)
    }
    
    private func checkForNewAchievements() {
        var newAchievements: [ReadingAchievement] = []

        // Check various achievement criteria
        if dailyStats.contentRead >= 5 && !hasAchievement(.dailyReader) {
            newAchievements.append(.dailyReader)
        }

        if currentStreak >= 7 && !hasAchievement(.weekStreak) {
            newAchievements.append(.weekStreak)
        }

        if currentStreak >= 30 && !hasAchievement(.monthStreak) {
            newAchievements.append(.monthStreak)
        }

        if overallStats.totalContentCompleted >= 10 && !hasAchievement(.tenCompleted) {
            newAchievements.append(.tenCompleted)
        }

        if overallStats.totalContentCompleted >= 50 && !hasAchievement(.fiftyCompleted) {
            newAchievements.append(.fiftyCompleted)
        }

        if overallStats.totalContentCompleted >= 100 && !hasAchievement(.hundredCompleted) {
            newAchievements.append(.hundredCompleted)
        }

        if overallStats.totalReadingTime >= 3600 && !hasAchievement(.oneHourTotal) {
            newAchievements.append(.oneHourTotal)
        }

        if overallStats.totalReadingTime >= 36000 && !hasAchievement(.tenHoursTotal) {
            newAchievements.append(.tenHoursTotal)
        }

        if overallStats.averageReadingSpeed >= 200 && !hasAchievement(.speedReader) {
            newAchievements.append(.speedReader)
        }

        // Check category mastery
        let completedCategories = overallStats.categoryStats.filter { $0.value.completionRate >= 80 }.count
        if completedCategories >= ReadingCategory.allCases.count && !hasAchievement(.categoryMaster) {
            newAchievements.append(.categoryMaster)
        }

        // Check level mastery
        let masteredLevels = overallStats.levelStats.filter { $0.value.masteryLevel >= 80 }.count
        if masteredLevels >= CEFRLevel.allCases.count && !hasAchievement(.levelMaster) {
            newAchievements.append(.levelMaster)
        }

        // Add new achievements with haptic feedback
        for achievement in newAchievements {
            achievements.append(achievement)
            HapticFeedbackManager.shared.achievementUnlocked()
            print("🏆 Achievement unlocked: \(achievement.title)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func calculateAverageCompletion(for progress: [ReadingProgress]) -> Double {
        guard !progress.isEmpty else { return 0.0 }
        return progress.reduce(0) { $0 + $1.completionPercentage } / Double(progress.count)
    }
    
    private func calculateConsistencyScore(_ dailyBreakdown: [Date: Int]) -> Double {
        let values = Array(dailyBreakdown.values)
        guard !values.isEmpty else { return 0.0 }
        
        let average = Double(values.reduce(0, +)) / Double(values.count)
        let variance = values.reduce(0.0) { result, value in
            result + pow(Double(value) - average, 2)
        } / Double(values.count)
        
        // Convert variance to consistency score (0-100)
        return max(0, 100 - sqrt(variance) * 10)
    }
    
    private func calculateAverageScore(for progress: [ReadingProgress]) -> Double {
        let scores = progress.compactMap { $0.comprehensionScore }
        guard !scores.isEmpty else { return 0.0 }
        return Double(scores.reduce(0, +)) / Double(scores.count)
    }
    
    private func calculateAverageReadingTime(for progress: [ReadingProgress]) -> Double {
        guard !progress.isEmpty else { return 0.0 }
        return Double(progress.reduce(0) { $0 + $1.readingTimeSeconds }) / Double(progress.count)
    }
    
    private func calculateMasteryLevel(for progress: [ReadingProgress], totalContent: Int) -> Double {
        guard totalContent > 0 else { return 0.0 }
        let completed = progress.filter { $0.isCompleted }.count
        return Double(completed) / Double(totalContent) * 100.0
    }
    
    private func calculateAverageReadingSpeed() -> Double {
        let allProgress = readingService.userProgress.filter { $0.readingTimeSeconds > 0 }
        guard !allProgress.isEmpty else { return 0.0 }
        
        let totalCharacters = allProgress.compactMap { progress in
            readingService.readingContent.first { $0.contentId == progress.contentId }?.contentTamil.count
        }.reduce(0, +)
        
        let totalTime = allProgress.reduce(0) { $0 + $1.readingTimeSeconds }
        
        guard totalTime > 0 else { return 0.0 }
        return Double(totalCharacters) / Double(totalTime) * 60.0 // Characters per minute
    }
    
    private func findFavoriteCategory(_ categoryStats: [ReadingCategory: CategoryStats]) -> ReadingCategory? {
        return categoryStats.max(by: { $0.value.completedContent < $1.value.completedContent })?.key
    }
    
    private func findStrongestLevel(_ levelStats: [CEFRLevel: LevelStats]) -> CEFRLevel? {
        return levelStats.max(by: { $0.value.masteryLevel < $1.value.masteryLevel })?.key
    }
    
    private func identifyImprovementAreas(_ categoryStats: [ReadingCategory: CategoryStats], _ levelStats: [CEFRLevel: LevelStats]) -> [String] {
        var areas: [String] = []
        
        // Find categories with low completion rates
        for (category, stats) in categoryStats {
            let completionRate = stats.totalContent > 0 ? Double(stats.completedContent) / Double(stats.totalContent) : 0.0
            if completionRate < 0.3 && stats.totalContent > 0 {
                areas.append("Improve \(category.rawValue) reading")
            }
        }
        
        // Find levels with low mastery
        for (level, stats) in levelStats {
            if stats.masteryLevel < 50.0 && stats.totalContent > 0 {
                areas.append("Focus on \(level.rawValue.uppercased()) level content")
            }
        }
        
        return areas
    }
    
    private func hasAchievement(_ achievement: ReadingAchievement) -> Bool {
        return achievements.contains(achievement)
    }
    
    // MARK: - Persistence
    
    private func saveStatistics() {
        // Save to UserDefaults for local persistence
        if let encoded = try? JSONEncoder().encode(achievements) {
            UserDefaults.standard.set(encoded, forKey: "reading_achievements")
        }
        
        UserDefaults.standard.set(currentStreak, forKey: "current_reading_streak")
        UserDefaults.standard.set(longestStreak, forKey: "longest_reading_streak")
    }
    
    private func loadStatistics() {
        // Load from UserDefaults
        if let data = UserDefaults.standard.data(forKey: "reading_achievements"),
           let loadedAchievements = try? JSONDecoder().decode([ReadingAchievement].self, from: data) {
            achievements = loadedAchievements
        }
        
        currentStreak = UserDefaults.standard.integer(forKey: "current_reading_streak")
        longestStreak = UserDefaults.standard.integer(forKey: "longest_reading_streak")
    }
}

// MARK: - Extensions

// MARK: - Statistics Data Models

struct DailyReadingStats: Codable {
    let date: Date
    let contentRead: Int
    let totalReadingTime: Int // seconds
    let completedContent: Int
    let averageCompletionRate: Double
    let categoriesExplored: Int

    init() {
        self.date = Date()
        self.contentRead = 0
        self.totalReadingTime = 0
        self.completedContent = 0
        self.averageCompletionRate = 0.0
        self.categoriesExplored = 0
    }

    init(date: Date, contentRead: Int, totalReadingTime: Int, completedContent: Int, averageCompletionRate: Double, categoriesExplored: Int) {
        self.date = date
        self.contentRead = contentRead
        self.totalReadingTime = totalReadingTime
        self.completedContent = completedContent
        self.averageCompletionRate = averageCompletionRate
        self.categoriesExplored = categoriesExplored
    }

    var readingTimeDisplay: String {
        let minutes = totalReadingTime / 60
        let seconds = totalReadingTime % 60
        return minutes > 0 ? "\(minutes)m \(seconds)s" : "\(seconds)s"
    }
}

struct WeeklyReadingStats: Codable {
    let weekStart: Date
    let totalContentRead: Int
    let totalReadingTime: Int
    let dailyBreakdown: [Date: Int]
    let averageDailyReading: Double
    let bestDay: Date?
    let consistencyScore: Double

    init() {
        self.weekStart = Date()
        self.totalContentRead = 0
        self.totalReadingTime = 0
        self.dailyBreakdown = [:]
        self.averageDailyReading = 0.0
        self.bestDay = nil
        self.consistencyScore = 0.0
    }

    init(weekStart: Date, totalContentRead: Int, totalReadingTime: Int, dailyBreakdown: [Date: Int], averageDailyReading: Double, bestDay: Date?, consistencyScore: Double) {
        self.weekStart = weekStart
        self.totalContentRead = totalContentRead
        self.totalReadingTime = totalReadingTime
        self.dailyBreakdown = dailyBreakdown
        self.averageDailyReading = averageDailyReading
        self.bestDay = bestDay
        self.consistencyScore = consistencyScore
    }
}

struct OverallReadingStats: Codable {
    let totalContentRead: Int
    let totalContentCompleted: Int
    let totalReadingTime: Int
    let averageReadingSpeed: Double // characters per minute
    let categoryStats: [ReadingCategory: CategoryStats]
    let levelStats: [CEFRLevel: LevelStats]
    let favoriteCategory: ReadingCategory?
    let strongestLevel: CEFRLevel?
    let improvementAreas: [String]

    init() {
        self.totalContentRead = 0
        self.totalContentCompleted = 0
        self.totalReadingTime = 0
        self.averageReadingSpeed = 0.0
        self.categoryStats = [:]
        self.levelStats = [:]
        self.favoriteCategory = nil
        self.strongestLevel = nil
        self.improvementAreas = []
    }

    init(totalContentRead: Int, totalContentCompleted: Int, totalReadingTime: Int, averageReadingSpeed: Double, categoryStats: [ReadingCategory: CategoryStats], levelStats: [CEFRLevel: LevelStats], favoriteCategory: ReadingCategory?, strongestLevel: CEFRLevel?, improvementAreas: [String]) {
        self.totalContentRead = totalContentRead
        self.totalContentCompleted = totalContentCompleted
        self.totalReadingTime = totalReadingTime
        self.averageReadingSpeed = averageReadingSpeed
        self.categoryStats = categoryStats
        self.levelStats = levelStats
        self.favoriteCategory = favoriteCategory
        self.strongestLevel = strongestLevel
        self.improvementAreas = improvementAreas
    }

    var completionRate: Double {
        guard totalContentRead > 0 else { return 0.0 }
        return Double(totalContentCompleted) / Double(totalContentRead) * 100.0
    }
}

struct CategoryStats: Codable {
    let totalContent: Int
    let completedContent: Int
    let totalReadingTime: Int
    let averageScore: Double

    var completionRate: Double {
        guard totalContent > 0 else { return 0.0 }
        return Double(completedContent) / Double(totalContent) * 100.0
    }
}

struct LevelStats: Codable {
    let totalContent: Int
    let completedContent: Int
    let averageReadingTime: Double
    let masteryLevel: Double

    var completionRate: Double {
        guard totalContent > 0 else { return 0.0 }
        return Double(completedContent) / Double(totalContent) * 100.0
    }
}

enum ReadingAchievement: String, Codable, CaseIterable {
    case dailyReader = "daily_reader"
    case weekStreak = "week_streak"
    case monthStreak = "month_streak"
    case tenCompleted = "ten_completed"
    case fiftyCompleted = "fifty_completed"
    case hundredCompleted = "hundred_completed"
    case oneHourTotal = "one_hour_total"
    case tenHoursTotal = "ten_hours_total"
    case speedReader = "speed_reader"
    case categoryMaster = "category_master"
    case levelMaster = "level_master"

    var title: String {
        switch self {
        case .dailyReader: return "Daily Reader"
        case .weekStreak: return "Week Streak"
        case .monthStreak: return "Month Streak"
        case .tenCompleted: return "First Ten"
        case .fiftyCompleted: return "Half Century"
        case .hundredCompleted: return "Century"
        case .oneHourTotal: return "One Hour"
        case .tenHoursTotal: return "Ten Hours"
        case .speedReader: return "Speed Reader"
        case .categoryMaster: return "Category Master"
        case .levelMaster: return "Level Master"
        }
    }

    var description: String {
        switch self {
        case .dailyReader: return "Read 5 pieces of content in one day"
        case .weekStreak: return "Read for 7 consecutive days"
        case .monthStreak: return "Read for 30 consecutive days"
        case .tenCompleted: return "Complete 10 reading exercises"
        case .fiftyCompleted: return "Complete 50 reading exercises"
        case .hundredCompleted: return "Complete 100 reading exercises"
        case .oneHourTotal: return "Spend 1 hour total reading"
        case .tenHoursTotal: return "Spend 10 hours total reading"
        case .speedReader: return "Achieve high reading speed"
        case .categoryMaster: return "Master all content categories"
        case .levelMaster: return "Master all difficulty levels"
        }
    }

    var icon: String {
        switch self {
        case .dailyReader: return "book.fill"
        case .weekStreak: return "flame.fill"
        case .monthStreak: return "star.fill"
        case .tenCompleted: return "10.circle.fill"
        case .fiftyCompleted: return "50.circle.fill"
        case .hundredCompleted: return "100.circle.fill"
        case .oneHourTotal: return "clock.fill"
        case .tenHoursTotal: return "timer.fill"
        case .speedReader: return "bolt.fill"
        case .categoryMaster: return "crown.fill"
        case .levelMaster: return "trophy.fill"
        }
    }

    var color: Color {
        switch self {
        case .dailyReader: return .blue
        case .weekStreak: return .orange
        case .monthStreak: return .red
        case .tenCompleted: return .green
        case .fiftyCompleted: return .purple
        case .hundredCompleted: return .gold
        case .oneHourTotal: return .cyan
        case .tenHoursTotal: return .indigo
        case .speedReader: return .yellow
        case .categoryMaster: return .pink
        case .levelMaster: return .gold
        }
    }
}

extension Color {
    static let gold = Color(red: 1.0, green: 0.84, blue: 0.0)
    static let cyan = Color(red: 0.0, green: 1.0, blue: 1.0)
}
