//
//  InputValidationService.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation
import CryptoKit
import Combine

// MARK: - Input Validation Service

@MainActor
class InputValidationService: ObservableObject {
    static let shared = InputValidationService()

    private var cancellables = Set<AnyCancellable>()
    private let validationQueue = DispatchQueue(label: "validation", qos: .userInitiated)

    private init() {}

    // MARK: - Debounced Validation Methods (Performance Optimized)

    func validateEmailDebounced(_ email: String, completion: @escaping (Result<String, ValidationError>) -> Void) {
        Just(email)
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { emailValue in
                Task.detached(priority: .userInitiated) {
                    do {
                        let validatedEmail = try await self.validateEmail(emailValue)
                        await MainActor.run {
                            completion(.success(validatedEmail))
                        }
                    } catch let error as ValidationError {
                        await MainActor.run {
                            completion(.failure(error))
                        }
                    } catch {
                        await MainActor.run {
                            completion(.failure(.invalidEmailFormat))
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }

    func validateTextContentDebounced(_ text: String, maxLength: Int = 1000, completion: @escaping (Result<String, ValidationError>) -> Void) {
        Just(text)
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { textValue in
                Task.detached(priority: .userInitiated) {
                    do {
                        let validatedText = try await self.validateTextContent(textValue, maxLength: maxLength)
                        await MainActor.run {
                            completion(.success(validatedText))
                        }
                    } catch let error as ValidationError {
                        await MainActor.run {
                            completion(.failure(error))
                        }
                    } catch {
                        await MainActor.run {
                            completion(.failure(.textTooLong))
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }

    func validateAIPromptDebounced(_ prompt: String, completion: @escaping (Result<String, ValidationError>) -> Void) {
        Just(prompt)
            .debounce(for: .milliseconds(800), scheduler: RunLoop.main)
            .sink { promptValue in
                Task.detached(priority: .userInitiated) {
                    do {
                        let validatedPrompt = try await self.validateAIPrompt(promptValue)
                        await MainActor.run {
                            completion(.success(validatedPrompt))
                        }
                    } catch let error as ValidationError {
                        await MainActor.run {
                            completion(.failure(error))
                        }
                    } catch {
                        await MainActor.run {
                            completion(.failure(.promptTooLong))
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Email Validation

    func validateEmail(_ email: String) async throws -> String {
        let trimmedEmail = email.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        // Check length
        guard trimmedEmail.count >= 5 && trimmedEmail.count <= 254 else {
            throw ValidationError.invalidEmailLength
        }

        // Check format using RFC 5322 compliant regex
        let emailRegex = #"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"#

        guard trimmedEmail.range(of: emailRegex, options: .regularExpression) != nil else {
            throw ValidationError.invalidEmailFormat
        }

        // Check for common security issues
        guard !containsSQLInjectionPatterns(trimmedEmail) else {
            throw ValidationError.suspiciousInput
        }

        return trimmedEmail
    }

    // MARK: - Password Validation

    func validatePassword(_ password: String) throws -> String {
        // Check length
        guard password.count >= 8 else {
            throw ValidationError.passwordTooShort
        }

        guard password.count <= 128 else {
            throw ValidationError.passwordTooLong
        }

        // Check complexity requirements
        let hasUppercase = password.rangeOfCharacter(from: .uppercaseLetters) != nil
        let hasLowercase = password.rangeOfCharacter(from: .lowercaseLetters) != nil
        let hasNumbers = password.rangeOfCharacter(from: .decimalDigits) != nil
        let hasSpecialChars = password.rangeOfCharacter(from: CharacterSet(charactersIn: "!@#$%^&*()_+-=[]{}|;:,.<>?")) != nil

        let complexityCount = [hasUppercase, hasLowercase, hasNumbers, hasSpecialChars].filter { $0 }.count

        guard complexityCount >= 3 else {
            throw ValidationError.passwordTooWeak
        }

        // Check against common passwords
        guard !isCommonPassword(password) else {
            throw ValidationError.passwordTooCommon
        }

        // Check for personal information patterns
        guard !containsPersonalInfoPatterns(password) else {
            throw ValidationError.passwordContainsPersonalInfo
        }

        return password
    }

    // MARK: - Name Validation

    func validateName(_ name: String) throws -> String {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check length
        guard trimmedName.count >= 1 && trimmedName.count <= 50 else {
            throw ValidationError.invalidNameLength
        }

        // Check for valid characters (letters, spaces, hyphens, apostrophes)
        let nameRegex = #"^[a-zA-ZÀ-ÿ\s\-'\.]+$"#
        guard trimmedName.range(of: nameRegex, options: .regularExpression) != nil else {
            throw ValidationError.invalidNameFormat
        }

        // Check for security issues
        guard !containsSQLInjectionPatterns(trimmedName) else {
            throw ValidationError.suspiciousInput
        }

        guard !containsXSSPatterns(trimmedName) else {
            throw ValidationError.suspiciousInput
        }

        return trimmedName
    }

    // MARK: - Text Content Validation

    func validateTextContent(_ text: String, maxLength: Int = 1000) async throws -> String {
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check length
        guard trimmedText.count <= maxLength else {
            throw ValidationError.textTooLong
        }

        // Check for malicious patterns
        guard !containsSQLInjectionPatterns(trimmedText) else {
            throw ValidationError.suspiciousInput
        }

        guard !containsXSSPatterns(trimmedText) else {
            throw ValidationError.suspiciousInput
        }

        guard !containsCommandInjectionPatterns(trimmedText) else {
            throw ValidationError.suspiciousInput
        }

        return trimmedText
    }

    // MARK: - AI Prompt Validation (OWASP LLM Security)

    func validateAIPrompt(_ prompt: String) async throws -> String {
        let trimmedPrompt = prompt.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check length (prevent token exhaustion attacks)
        guard trimmedPrompt.count <= 4000 else {
            throw ValidationError.promptTooLong
        }

        // Check for prompt injection patterns
        let suspiciousPatterns = [
            "ignore previous instructions",
            "forget everything above",
            "system:",
            "assistant:",
            "user:",
            "\\n\\n",
            "---",
            "```",
            "<script>",
            "javascript:",
            "data:",
            "vbscript:"
        ]

        let lowercasePrompt = trimmedPrompt.lowercased()
        for pattern in suspiciousPatterns {
            if lowercasePrompt.contains(pattern) {
                throw ValidationError.promptInjectionDetected
            }
        }

        // Check for attempts to extract training data
        let dataExtractionPatterns = [
            "repeat",
            "memorize",
            "training data",
            "dataset",
            "model weights",
            "parameters"
        ]

        for pattern in dataExtractionPatterns {
            if lowercasePrompt.contains(pattern) {
                throw ValidationError.dataExtractionAttempt
            }
        }

        return trimmedPrompt
    }

    // MARK: - URL Validation

    func validateURL(_ urlString: String) throws -> URL {
        let trimmedURL = urlString.trimmingCharacters(in: .whitespacesAndNewlines)

        guard let url = URL(string: trimmedURL) else {
            throw ValidationError.invalidURL
        }

        // Only allow HTTPS URLs
        guard url.scheme == "https" else {
            throw ValidationError.insecureURL
        }

        // Check for valid host
        guard let host = url.host, !host.isEmpty else {
            throw ValidationError.invalidURL
        }

        // Prevent local network access
        let localPatterns = ["localhost", "127.0.0.1", "0.0.0.0", "::1", "10.", "192.168.", "172."]
        for pattern in localPatterns {
            if host.contains(pattern) {
                throw ValidationError.localNetworkAccess
            }
        }

        return url
    }

    // MARK: - Private Security Checks

    private func containsSQLInjectionPatterns(_ input: String) -> Bool {
        let sqlPatterns = [
            "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_",
            "union", "select", "insert", "update", "delete", "drop",
            "exec", "execute", "script", "declare", "cast"
        ]

        let lowercaseInput = input.lowercased()
        return sqlPatterns.contains { lowercaseInput.contains($0) }
    }

    private func containsXSSPatterns(_ input: String) -> Bool {
        let xssPatterns = [
            "<script", "</script>", "javascript:", "vbscript:", "onload=",
            "onerror=", "onclick=", "onmouseover=", "onfocus=", "onblur=",
            "eval(", "alert(", "confirm(", "prompt("
        ]

        let lowercaseInput = input.lowercased()
        return xssPatterns.contains { lowercaseInput.contains($0) }
    }

    private func containsCommandInjectionPatterns(_ input: String) -> Bool {
        let commandPatterns = [
            "|", "&", ";", "$", "`", "$(", "${", "&&", "||",
            "rm ", "del ", "format ", "shutdown", "reboot"
        ]

        return commandPatterns.contains { input.contains($0) }
    }

    private func isCommonPassword(_ password: String) -> Bool {
        let commonPasswords = [
            "password", "123456", "password123", "admin", "qwerty",
            "letmein", "welcome", "monkey", "dragon", "master"
        ]

        let lowercasePassword = password.lowercased()
        return commonPasswords.contains { lowercasePassword.contains($0) }
    }

    private func containsPersonalInfoPatterns(_ password: String) -> Bool {
        // Check for common personal info patterns
        let patterns = [
            "birthday", "name", "email", "phone", "address",
            "123456", "abcdef", "qwerty", "password"
        ]

        let lowercasePassword = password.lowercased()
        return patterns.contains { lowercasePassword.contains($0) }
    }
}

// MARK: - Validation Errors

enum ValidationError: LocalizedError {
    case invalidEmailLength
    case invalidEmailFormat
    case passwordTooShort
    case passwordTooLong
    case passwordTooWeak
    case passwordTooCommon
    case passwordContainsPersonalInfo
    case invalidNameLength
    case invalidNameFormat
    case textTooLong
    case suspiciousInput
    case promptTooLong
    case promptInjectionDetected
    case dataExtractionAttempt
    case invalidURL
    case insecureURL
    case localNetworkAccess

    var errorDescription: String? {
        switch self {
        case .invalidEmailLength:
            return "Email must be between 5 and 254 characters."
        case .invalidEmailFormat:
            return "Please enter a valid email address."
        case .passwordTooShort:
            return "Password must be at least 8 characters long."
        case .passwordTooLong:
            return "Password must be less than 128 characters."
        case .passwordTooWeak:
            return "Password must contain at least 3 of: uppercase, lowercase, numbers, special characters."
        case .passwordTooCommon:
            return "This password is too common. Please choose a more unique password."
        case .passwordContainsPersonalInfo:
            return "Password should not contain personal information."
        case .invalidNameLength:
            return "Name must be between 1 and 50 characters."
        case .invalidNameFormat:
            return "Name contains invalid characters."
        case .textTooLong:
            return "Text content is too long."
        case .suspiciousInput:
            return "Input contains potentially harmful content."
        case .promptTooLong:
            return "Prompt is too long. Please shorten your message."
        case .promptInjectionDetected:
            return "Message contains suspicious patterns and cannot be processed."
        case .dataExtractionAttempt:
            return "Request appears to be attempting data extraction."
        case .invalidURL:
            return "Invalid URL format."
        case .insecureURL:
            return "Only HTTPS URLs are allowed."
        case .localNetworkAccess:
            return "Local network access is not permitted."
        }
    }
}
