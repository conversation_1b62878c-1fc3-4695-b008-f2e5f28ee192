//
//  AudioFileManagementService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import AVFoundation
import Supabase
import Combine

/// Service for managing audio files with Supabase Storage and Google TTS integration
@MainActor
class AudioFileManagementService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = AudioFileManagementService()
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var downloadProgress: [String: Double] = [:]
    @Published var cachedAudioFiles: Set<String> = []
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var audioPlayer: AVAudioPlayer?
    private var cancellables = Set<AnyCancellable>()
    private let cacheDirectory: URL
    private let googleTTSService = MockGoogleTTSService.shared
    
    // MARK: - Initialization
    private init() {
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co")!,
            supabaseKey: APIKeys.supabaseAnonKey
        )
        
        // Create cache directory
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        self.cacheDirectory = documentsPath.appendingPathComponent("AudioCache")
        
        createCacheDirectory()
        loadCachedFilesList()
        
        print("🎵 AudioFileManagementService initialized")
    }
    
    // MARK: - Audio File Management
    
    /// Play audio from URL or generate with TTS
    func playAudio(text: String, language: String = "ta", audioUrl: String? = nil) async {
        do {
            var audioData: Data
            
            if let url = audioUrl, !url.isEmpty {
                // Try to play from cached file or download
                audioData = try await getAudioData(from: url)
            } else {
                // Generate audio using Google TTS
                audioData = try await googleTTSService.generateAudio(text: text, language: language)
            }
            
            // Play the audio
            try await playAudioData(audioData)
            
        } catch {
            errorMessage = "Failed to play audio: \(error.localizedDescription)"
            print("❌ Audio playback failed: \(error)")
        }
    }
    
    /// Get audio data from URL (cached or download)
    private func getAudioData(from urlString: String) async throws -> Data {
        let cacheKey = urlString.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? urlString
        let cachedFilePath = cacheDirectory.appendingPathComponent("\(cacheKey).mp3")
        
        // Check if file is cached
        if FileManager.default.fileExists(atPath: cachedFilePath.path) {
            print("🎵 Playing cached audio: \(urlString)")
            return try Data(contentsOf: cachedFilePath)
        }
        
        // Download from Supabase Storage
        print("⬇️ Downloading audio: \(urlString)")
        let audioData = try await downloadAudioFromSupabase(urlString)
        
        // Cache the file
        try audioData.write(to: cachedFilePath)
        cachedAudioFiles.insert(cacheKey)
        saveCachedFilesList()
        
        return audioData
    }
    
    /// Download audio file from Supabase Storage
    private func downloadAudioFromSupabase(_ fileName: String) async throws -> Data {
        do {
            let data = try await supabase.storage
                .from("audio-files")
                .download(path: fileName)
            
            print("✅ Downloaded audio file: \(fileName)")
            return data
            
        } catch {
            print("❌ Failed to download audio from Supabase: \(error)")
            throw AudioError.downloadFailed(error.localizedDescription)
        }
    }
    
    /// Upload audio file to Supabase Storage
    func uploadAudioToSupabase(audioData: Data, fileName: String) async throws -> String {
        do {
            _ = try await supabase.storage
                .from("audio-files")
                .upload(fileName, data: audioData)

            print("✅ Uploaded audio file: \(fileName)")
            return fileName

        } catch {
            print("❌ Failed to upload audio to Supabase: \(error)")
            throw AudioError.uploadFailed(error.localizedDescription)
        }
    }
    
    /// Play audio data using AVAudioPlayer
    private func playAudioData(_ data: Data) async throws {
        do {
            audioPlayer = try AVAudioPlayer(data: data)
            audioPlayer?.prepareToPlay()
            audioPlayer?.play()
            
            print("🎵 Audio playback started")
            
        } catch {
            throw AudioError.playbackFailed(error.localizedDescription)
        }
    }
    
    /// Stop current audio playback
    func stopAudio() {
        audioPlayer?.stop()
        audioPlayer = nil
        print("⏹️ Audio playback stopped")
    }
    
    /// Preload audio files for a lesson
    func preloadLessonAudio(lessonId: String, audioUrls: [String]) async {
        isLoading = true
        
        for (index, url) in audioUrls.enumerated() {
            do {
                _ = try await getAudioData(from: url)
                
                // Update progress
                let progress = Double(index + 1) / Double(audioUrls.count)
                downloadProgress[lessonId] = progress
                
            } catch {
                print("❌ Failed to preload audio: \(url)")
            }
        }
        
        downloadProgress.removeValue(forKey: lessonId)
        isLoading = false
        
        print("✅ Preloaded audio for lesson: \(lessonId)")
    }
    
    /// Generate and cache TTS audio for vocabulary
    func generateVocabularyAudio(vocabulary: [TamilSupabaseVocabulary]) async {
        isLoading = true
        
        for (index, vocab) in vocabulary.enumerated() {
            do {
                // Generate audio for Tamil word
                let tamilAudio = try await googleTTSService.generateAudio(
                    text: vocab.tamilTranslation,
                    language: "ta"
                )
                
                // Cache the audio
                let cacheKey = "vocab_\(vocab.id)_tamil"
                let cachedFilePath = cacheDirectory.appendingPathComponent("\(cacheKey).mp3")
                try tamilAudio.write(to: cachedFilePath)
                cachedAudioFiles.insert(cacheKey)
                
                // Generate audio for example sentence if available
                if let exampleTamil = vocab.exampleSentenceTamil {
                    let exampleAudio = try await googleTTSService.generateAudio(
                        text: exampleTamil,
                        language: "ta"
                    )
                    
                    let exampleCacheKey = "vocab_\(vocab.id)_example"
                    let exampleCachedFilePath = cacheDirectory.appendingPathComponent("\(exampleCacheKey).mp3")
                    try exampleAudio.write(to: exampleCachedFilePath)
                    cachedAudioFiles.insert(exampleCacheKey)
                }
                
                // Update progress
                let progress = Double(index + 1) / Double(vocabulary.count)
                downloadProgress["vocabulary_generation"] = progress
                
            } catch {
                print("❌ Failed to generate audio for vocabulary: \(vocab.id)")
            }
        }
        
        downloadProgress.removeValue(forKey: "vocabulary_generation")
        saveCachedFilesList()
        isLoading = false
        
        print("✅ Generated TTS audio for \(vocabulary.count) vocabulary items")
    }
    
    /// Play vocabulary word audio
    func playVocabularyAudio(vocabularyId: String, type: VocabularyAudioType) async {
        let cacheKey = "vocab_\(vocabularyId)_\(type.rawValue)"
        let cachedFilePath = cacheDirectory.appendingPathComponent("\(cacheKey).mp3")
        
        if FileManager.default.fileExists(atPath: cachedFilePath.path) {
            do {
                let audioData = try Data(contentsOf: cachedFilePath)
                try await playAudioData(audioData)
            } catch {
                errorMessage = "Failed to play vocabulary audio: \(error.localizedDescription)"
            }
        } else {
            errorMessage = "Audio not available for this vocabulary item"
        }
    }
    
    /// Clear audio cache
    func clearAudioCache() {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for file in files {
                try FileManager.default.removeItem(at: file)
            }
            
            cachedAudioFiles.removeAll()
            saveCachedFilesList()
            
            print("🗑️ Audio cache cleared")
            
        } catch {
            errorMessage = "Failed to clear cache: \(error.localizedDescription)"
            print("❌ Failed to clear audio cache: \(error)")
        }
    }
    
    /// Get cache size in MB
    func getCacheSize() -> Double {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            let totalSize = files.reduce(0) { total, url in
                let fileSize = (try? url.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0
                return total + fileSize
            }
            return Double(totalSize) / (1024 * 1024) // Convert to MB
        } catch {
            return 0.0
        }
    }
}

// MARK: - Cache Management

extension AudioFileManagementService {
    
    /// Create cache directory if it doesn't exist
    private func createCacheDirectory() {
        if !FileManager.default.fileExists(atPath: cacheDirectory.path) {
            do {
                try FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
                print("📁 Created audio cache directory")
            } catch {
                print("❌ Failed to create cache directory: \(error)")
            }
        }
    }
    
    /// Load list of cached files
    private func loadCachedFilesList() {
        if let data = UserDefaults.standard.data(forKey: "cached_audio_files"),
           let decoded = try? JSONDecoder().decode(Set<String>.self, from: data) {
            cachedAudioFiles = decoded
        }
    }
    
    /// Save list of cached files
    private func saveCachedFilesList() {
        if let encoded = try? JSONEncoder().encode(cachedAudioFiles) {
            UserDefaults.standard.set(encoded, forKey: "cached_audio_files")
            UserDefaults.standard.synchronize()
        }
    }
}

// MARK: - Mock Google TTS Service

class MockGoogleTTSService {
    static let shared = MockGoogleTTSService()
    
    private init() {}
    
    /// Generate audio using Google TTS (mock implementation)
    func generateAudio(text: String, language: String) async throws -> Data {
        // This is a mock implementation
        // In a real app, you would integrate with Google Cloud Text-to-Speech API
        
        print("🎤 Generating TTS audio for: \(text)")
        
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Return mock audio data (empty MP3 header)
        let mockAudioData = Data([
            0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
        ])
        
        return mockAudioData
    }
}

// MARK: - Supporting Types

enum AudioError: LocalizedError {
    case downloadFailed(String)
    case uploadFailed(String)
    case playbackFailed(String)
    case generationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .downloadFailed(let message):
            return "Download failed: \(message)"
        case .uploadFailed(let message):
            return "Upload failed: \(message)"
        case .playbackFailed(let message):
            return "Playback failed: \(message)"
        case .generationFailed(let message):
            return "Audio generation failed: \(message)"
        }
    }
}

enum VocabularyAudioType: String {
    case tamil = "tamil"
    case example = "example"
}
