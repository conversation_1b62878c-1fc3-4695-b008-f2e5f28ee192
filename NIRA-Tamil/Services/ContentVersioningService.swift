//
//  ContentVersioningService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import Foundation
import Supabase
import Combine

/// Service for managing content versions, updates, and schema migrations
@MainActor
class ContentVersioningService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ContentVersioningService()
    
    // MARK: - Published Properties
    @Published var currentVersion = "1.0.0"
    @Published var latestVersion = "1.0.0"
    @Published var isUpdateAvailable = false
    @Published var isUpdating = false
    @Published var updateProgress: Double = 0.0
    @Published var updateStatus = ""
    @Published var migrationHistory: [SchemaMigration] = []
    
    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    private let versionKey = "content_version"
    private let migrationKey = "schema_migrations"
    
    // MARK: - Initialization
    private init() {
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co")!,
            supabaseKey: APIKeys.supabaseAnonKey
        )
        
        loadCurrentVersion()
        loadMigrationHistory()
        
        print("📦 ContentVersioningService initialized")

        // Check for updates on startup
        Task { @MainActor in
            await checkForUpdates()
        }

        // Start periodic update checks
        startPeriodicUpdateChecks()
    }
    
    // MARK: - Version Management
    
    /// Check for content updates
    func checkForUpdates() async {
        do {
            let versionInfo: [ContentVersion] = try await supabase
                .from("content_versions")
                .select("*")
                .order("version_number", ascending: false)
                .limit(1)
                .execute()
                .value
            
            if let latest = versionInfo.first {
                await MainActor.run {
                    latestVersion = latest.versionNumber
                    isUpdateAvailable = isVersionNewer(latest.versionNumber, than: currentVersion)
                }

                if isUpdateAvailable {
                    print("🆕 Update available: \(currentVersion) → \(latestVersion)")
                } else {
                    print("✅ Content is up to date: \(currentVersion)")
                }
            }
            
        } catch {
            print("❌ Failed to check for updates: \(error)")
        }
    }
    
    /// Perform content update
    func performUpdate() async {
        guard isUpdateAvailable else { return }

        await MainActor.run {
            isUpdating = true
            updateProgress = 0.0
            updateStatus = "Preparing update..."
        }

        do {
            // Get update details
            let updateInfo = try await getUpdateInfo(from: currentVersion, to: latestVersion)

            // Perform migrations
            await performMigrations(updateInfo.migrations)

            // Update content
            await updateContent(updateInfo.contentUpdates)

            // Update version
            await MainActor.run {
                currentVersion = latestVersion
                isUpdateAvailable = false
                updateStatus = "Update completed successfully"
            }
            saveCurrentVersion()

            print("✅ Content updated to version \(latestVersion)")

        } catch {
            await MainActor.run {
                updateStatus = "Update failed: \(error.localizedDescription)"
            }
            print("❌ Content update failed: \(error)")
        }
        
        isUpdating = false
        updateProgress = 1.0
    }
    
    /// Get update information between versions
    private func getUpdateInfo(from: String, to: String) async throws -> UpdateInfo {
        let updates: [ContentUpdate] = try await supabase
            .from("content_updates")
            .select("*")
            .gte("from_version", value: from)
            .lte("to_version", value: to)
            .order("sequence_number")
            .execute()
            .value
        
        let migrations: [SchemaMigration] = try await supabase
            .from("schema_migrations")
            .select("*")
            .gte("from_version", value: from)
            .lte("to_version", value: to)
            .order("sequence_number")
            .execute()
            .value
        
        return UpdateInfo(contentUpdates: updates, migrations: migrations)
    }
    
    // MARK: - Schema Migrations
    
    /// Perform schema migrations
    private func performMigrations(_ migrations: [SchemaMigration]) async {
        updateStatus = "Running database migrations..."
        updateProgress = 0.2
        
        for (index, migration) in migrations.enumerated() {
            do {
                try await performMigration(migration)
            } catch {
                print("❌ Migration failed: \(migration.name) - \(error)")
                // Continue with other migrations
            }

            let progress = 0.2 + (0.3 * Double(index + 1) / Double(migrations.count))
            updateProgress = progress
        }
        
        // Save migration history
        migrationHistory.append(contentsOf: migrations)
        saveMigrationHistory()
    }
    
    /// Perform individual migration
    private func performMigration(_ migration: SchemaMigration) async throws {
        print("🔄 Running migration: \(migration.name)")
        
        do {
            // Execute migration SQL
            if let sql = migration.migrationSql {
                // In a real implementation, you would execute the SQL
                // For now, we'll simulate the migration
                try await simulateMigration(sql)
            }
            
            // Run any custom migration logic
            await runCustomMigrationLogic(migration)
            
            print("✅ Migration completed: \(migration.name)")
            
        } catch {
            print("❌ Migration failed: \(migration.name) - \(error)")
            throw ContentVersioningError.migrationFailed(migration.name, error.localizedDescription)
        }
    }
    
    /// Simulate migration execution
    private func simulateMigration(_ sql: String) async throws {
        // Simulate database operation
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        print("📊 Executed SQL: \(sql.prefix(50))...")
    }
    
    /// Run custom migration logic
    private func runCustomMigrationLogic(_ migration: SchemaMigration) async {
        switch migration.migrationType {
        case "add_column":
            await handleAddColumnMigration(migration)
        case "update_data":
            await handleDataUpdateMigration(migration)
        case "create_index":
            await handleCreateIndexMigration(migration)
        default:
            print("ℹ️ No custom logic for migration type: \(migration.migrationType)")
        }
    }
    
    /// Handle add column migration
    private func handleAddColumnMigration(_ migration: SchemaMigration) async {
        print("➕ Adding column for migration: \(migration.name)")
        // Custom logic for adding columns
    }
    
    /// Handle data update migration
    private func handleDataUpdateMigration(_ migration: SchemaMigration) async {
        print("🔄 Updating data for migration: \(migration.name)")
        // Custom logic for data updates
    }
    
    /// Handle create index migration
    private func handleCreateIndexMigration(_ migration: SchemaMigration) async {
        print("📇 Creating index for migration: \(migration.name)")
        // Custom logic for creating indexes
    }
    
    // MARK: - Content Updates
    
    /// Update content based on update info
    private func updateContent(_ updates: [ContentUpdate]) async {
        updateStatus = "Updating content..."
        updateProgress = 0.5
        
        for (index, update) in updates.enumerated() {
            await processContentUpdate(update)
            
            let progress = 0.5 + (0.4 * Double(index + 1) / Double(updates.count))
            updateProgress = progress
        }
        
        // Clear local cache to force refresh
        await LocalCacheService.shared.clearCache()
        
        updateProgress = 0.9
        updateStatus = "Finalizing update..."
    }
    
    /// Process individual content update
    private func processContentUpdate(_ update: ContentUpdate) async {
        print("📝 Processing content update: \(update.updateType)")
        
        switch update.updateType {
        case "lesson_content":
            await updateLessonContent(update)
        case "vocabulary_data":
            await updateVocabularyData(update)
        case "audio_files":
            await updateAudioFiles(update)
        case "cultural_content":
            await updateCulturalContent(update)
        default:
            print("⚠️ Unknown update type: \(update.updateType)")
        }
    }
    
    /// Update lesson content
    private func updateLessonContent(_ update: ContentUpdate) async {
        print("📚 Updating lesson content")
        // Refresh lesson data from Supabase
        await SupabaseContentService.shared.fetchLessons(for: .a1)
    }
    
    /// Update vocabulary data
    private func updateVocabularyData(_ update: ContentUpdate) async {
        print("📖 Updating vocabulary data")
        // Refresh vocabulary data
    }
    
    /// Update audio files
    private func updateAudioFiles(_ update: ContentUpdate) async {
        print("🎵 Updating audio files")
        // Clear audio cache to force re-download
        AudioFileManagementService.shared.clearAudioCache()
    }
    
    /// Update cultural content
    private func updateCulturalContent(_ update: ContentUpdate) async {
        print("🏛️ Updating cultural content")
        // Refresh cultural content
    }
    
    // MARK: - Version Utilities
    
    /// Check if version A is newer than version B
    private func isVersionNewer(_ versionA: String, than versionB: String) -> Bool {
        let componentsA = versionA.split(separator: ".").compactMap { Int($0) }
        let componentsB = versionB.split(separator: ".").compactMap { Int($0) }
        
        for i in 0..<max(componentsA.count, componentsB.count) {
            let a = i < componentsA.count ? componentsA[i] : 0
            let b = i < componentsB.count ? componentsB[i] : 0
            
            if a > b { return true }
            if a < b { return false }
        }
        
        return false
    }
    
    /// Start periodic update checks
    private func startPeriodicUpdateChecks() {
        Timer.publish(every: 3600, on: .main, in: .common) // Check every hour
            .autoconnect()
            .sink { _ in
                Task {
                    await self.checkForUpdates()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Persistence
    
    /// Load current version from UserDefaults
    private func loadCurrentVersion() {
        currentVersion = UserDefaults.standard.string(forKey: versionKey) ?? "1.0.0"
    }
    
    /// Save current version to UserDefaults
    private func saveCurrentVersion() {
        UserDefaults.standard.set(currentVersion, forKey: versionKey)
        UserDefaults.standard.synchronize()
    }
    
    /// Load migration history
    private func loadMigrationHistory() {
        if let data = UserDefaults.standard.data(forKey: migrationKey),
           let decoded = try? JSONDecoder().decode([SchemaMigration].self, from: data) {
            migrationHistory = decoded
        }
    }
    
    /// Save migration history
    private func saveMigrationHistory() {
        if let encoded = try? JSONEncoder().encode(migrationHistory) {
            UserDefaults.standard.set(encoded, forKey: migrationKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    /// Force check for updates
    func forceUpdateCheck() async {
        await checkForUpdates()
    }
    
    /// Get version history
    func getVersionHistory() async -> [ContentVersion] {
        do {
            let versions: [ContentVersion] = try await supabase
                .from("content_versions")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value
            
            return versions
            
        } catch {
            print("❌ Failed to get version history: \(error)")
            return []
        }
    }
}

// MARK: - Supporting Types

struct UpdateInfo {
    let contentUpdates: [ContentUpdate]
    let migrations: [SchemaMigration]
}

struct ContentVersion: Codable, Identifiable {
    let id: String
    let versionNumber: String
    let releaseNotes: String
    let isRequired: Bool
    let createdAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case versionNumber = "version_number"
        case releaseNotes = "release_notes"
        case isRequired = "is_required"
        case createdAt = "created_at"
    }
}

struct ContentUpdate: Codable, Identifiable {
    let id: String
    let fromVersion: String
    let toVersion: String
    let updateType: String
    let description: String
    let sequenceNumber: Int
    let createdAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case fromVersion = "from_version"
        case toVersion = "to_version"
        case updateType = "update_type"
        case description
        case sequenceNumber = "sequence_number"
        case createdAt = "created_at"
    }
}

struct SchemaMigration: Codable, Identifiable {
    let id: String
    let name: String
    let fromVersion: String
    let toVersion: String
    let migrationType: String
    let migrationSql: String?
    let sequenceNumber: Int
    let createdAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case fromVersion = "from_version"
        case toVersion = "to_version"
        case migrationType = "migration_type"
        case migrationSql = "migration_sql"
        case sequenceNumber = "sequence_number"
        case createdAt = "created_at"
    }
}

enum ContentVersioningError: LocalizedError {
    case migrationFailed(String, String)
    case updateFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .migrationFailed(let name, let reason):
            return "Migration '\(name)' failed: \(reason)"
        case .updateFailed(let reason):
            return "Content update failed: \(reason)"
        }
    }
}
