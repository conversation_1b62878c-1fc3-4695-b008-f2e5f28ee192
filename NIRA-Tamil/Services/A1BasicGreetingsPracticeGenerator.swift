//
//  A1BasicGreetingsPracticeGenerator.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 25/06/2025.
//  Focused practice exercise generator for A1 Basic Greetings lesson
//

import Foundation

/// Focused practice exercise generator that ONLY uses the 25 A1 Basic Greetings vocabulary words
class A1BasicGreetingsPracticeGenerator {
    
    // EXACT 25 vocabulary words from A1 Basic Greetings lesson
    private static let a1Vocabulary = [
        ("Hello", "வணக்கம்", "vaṇakkam"),
        ("Good morning", "காலை வணக்கம்", "kālai vaṇakkam"),
        ("Thank you", "நன்றி", "naṉṟi"),
        ("Please", "தயவுசெய்து", "tayavuceytu"),
        ("Sorry", "மன்னிக்கவும்", "maṉṉikkavum"),
        ("Yes", "ஆம்", "ām"),
        ("No", "இல்லை", "illai"),
        ("Water", "தண்ணீர்", "taṇṇīr"),
        ("Food", "உணவு", "uṇavu"),
        ("House", "வீடு", "vīṭu"),
        ("Mother", "அம்மா", "ammā"),
        ("Father", "அப்பா", "appā"),
        ("Friend", "நண்பன்", "naṇpaṉ"),
        ("School", "பள்ளி", "paḷḷi"),
        ("Book", "புத்தகம்", "puttakam"),
        ("Time", "நேரம்", "nēram"),
        ("Money", "பணம்", "paṇam"),
        ("Work", "வேலை", "vēlai"),
        ("Love", "அன்பு", "aṉpu"),
        ("Happy", "மகிழ்ச்சி", "makiḻcci"),
        ("Beautiful", "அழகான", "aḻakāṉa"),
        ("Big", "பெரிய", "periya"),
        ("Small", "சிறிய", "ciṟiya"),
        ("Good", "நல்ல", "nalla"),
        ("Come", "வா", "vā")
    ]

    // MARK: - Audio URL Mapping

    /// Pre-generated audio URLs for Tamil practice exercise options
    /// Generated using: python3 Scripts/generate_practice_exercise_audio.py --lesson-id 7b8c60af-dd2f-4754-9363-ab09a5bcea95
    /// Following A1_BASIC_GREETINGS_IMPLEMENTATION_GUIDE.md approach
    /// ✅ VERIFIED: All 14 audio files successfully uploaded to Supabase Storage
    private static let tamilAudioURLs: [String: String] = [
        "வணக்கம்": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_vanakkam.mp3",
        "நன்றி": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_nanri.mp3",
        "தயவுசெய்து": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_thayavu_seithu.mp3",
        "மன்னிக்கவும்": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_mannikkavum.mp3",
        "ஆம்": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_aam.mp3",
        "இல்லை": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_illai.mp3",
        "அம்மா": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_amma.mp3",
        "அப்பா": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_appa.mp3",
        "நண்பன்": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_nanban.mp3",
        "வீடு": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_veedu.mp3",
        "மகிழ்ச்சி": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_magizhchi.mp3",
        "அன்பு": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_anbu.mp3",
        "நேரம்": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_neram.mp3",
        "வேலை": "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/practice_exercises/practice_option_velai.mp3"
    ]

    /// Get audio URLs for Tamil options array
    private static func getAudioURLsForOptions(_ tamilOptions: [String]) -> [String] {
        return tamilOptions.compactMap { tamilAudioURLs[$0] }
    }

    /// Generate 10 focused practice exercises using ONLY the 25 A1 vocabulary words
    static func generateFocusedA1Exercises() -> [TamilSupabasePracticeExercise] {
        var exercises: [TamilSupabasePracticeExercise] = []

        // Exercise 1: Basic Tamil to English (Greeting)
        var exercise1 = TamilSupabasePracticeExercise(
            id: "a1-practice-1",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E01",
            exerciseType: "multiple_choice",
            titleEnglish: "Tamil to English Translation",
            titleTamil: "தமிழிலிருந்து ஆங்கிலத்திற்கு மொழிபெயர்ப்பு",
            instructionsEnglish: "Choose the correct English meaning for the Tamil word.",
            instructionsTamil: "தமிழ் சொல்லுக்கான சரியான ஆங்கில அர்த்தத்தைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise1.questionData = ExerciseQuestionData(
            question: "What does 'vanakkam' mean in English?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["Hello", "Thank you", "Please", "Good morning"],
            optionsTamil: ["Hello", "Thank you", "Please", "Good morning"], // English options
            optionsRomanization: nil, // Not needed for English options
            optionsPronunciation: nil, // Not needed for English options
            optionsAudioUrls: nil, // Not needed for English options
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'வணக்கம்' means 'Hello' in English. It's the most common Tamil greeting.",
            explanationTamil: "'வணக்கம்' என்பது ஆங்கிலத்தில் 'Hello' என்று பொருள். இது மிகவும் பொதுவான தமிழ் வாழ்த்து.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        // 🐛 DEBUG: Log exercise 1 data
        print("🔧 Exercise 1 Debug:")
        print("   Question: \(exercise1.questionData?.question ?? "nil")")
        print("   Options: \(exercise1.questionData?.options ?? [])")
        print("   Correct Answer: \(exercise1.questionData?.correctAnswer ?? -1)")

        exercises.append(exercise1)

        // Exercise 2: English to Tamil (Gratitude)
        var exercise2 = TamilSupabasePracticeExercise(
            id: "a1-practice-2",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E02",
            exerciseType: "multiple_choice",
            titleEnglish: "English to Tamil Translation",
            titleTamil: "ஆங்கிலத்திலிருந்து தமிழுக்கு மொழிபெயர்ப்பு",
            instructionsEnglish: "Choose the correct Tamil word for the English word.",
            instructionsTamil: "ஆங்கில சொல்லுக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        let tamilOptions2 = ["நன்றி", "வணக்கம்", "தயவுசெய்து", "மன்னிக்கவும்"]
        exercise2.questionData = ExerciseQuestionData(
            question: "How do you say 'Thank you' in Tamil?",
            questionTamil: "", // No Tamil question for practice exercises
            options: tamilOptions2,
            optionsTamil: tamilOptions2,
            optionsRomanization: ["Nanri", "Vanakkam", "Thayavu seithu", "Mannikkavum"], // Basic learners need romanization!
            optionsPronunciation: ["NAN-ri", "va-NAK-kam", "tha-ya-vu SAY-thu", "man-nik-ka-vum"], // Pronunciation guides
            optionsAudioUrls: getAudioURLsForOptions(tamilOptions2), // Pre-generated Google TTS audio!
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'நன்றி' (Nanri) means 'Thank you' in Tamil.",
            explanationTamil: "'நன்றி' என்பது தமிழில் 'Thank you' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        // 🐛 DEBUG: Log exercise 2 data
        print("🔧 Exercise 2 Debug:")
        print("   Question: \(exercise2.questionData?.question ?? "nil")")
        print("   Options: \(exercise2.questionData?.options ?? [])")
        print("   Correct Answer: \(exercise2.questionData?.correctAnswer ?? -1)")

        exercises.append(exercise2)

        // Exercise 3: Family vocabulary
        var exercise3 = TamilSupabasePracticeExercise(
            id: "a1-practice-3",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E03",
            exerciseType: "multiple_choice",
            titleEnglish: "Family Vocabulary",
            titleTamil: "குடும்ப சொற்கள்",
            instructionsEnglish: "Choose the correct Tamil word for the family member.",
            instructionsTamil: "குடும்ப உறுப்பினருக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        let tamilOptions3 = ["அம்மா", "அப்பா", "நண்பன்", "வீடு"]
        exercise3.questionData = ExerciseQuestionData(
            question: "What is the Tamil word for 'Mother'?",
            questionTamil: "", // No Tamil question for practice exercises
            options: tamilOptions3,
            optionsTamil: tamilOptions3,
            optionsRomanization: ["Amma", "Appa", "Nanban", "Veedu"], // Basic learners need romanization!
            optionsPronunciation: ["AM-ma", "AP-pa", "NAN-ban", "VEE-du"], // Pronunciation guides
            optionsAudioUrls: getAudioURLsForOptions(tamilOptions3), // Pre-generated Google TTS audio!
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'அம்மா' (Amma) means 'Mother' in Tamil.",
            explanationTamil: "'அம்மா' என்பது தமிழில் 'Mother' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        // 🐛 DEBUG: Log exercise 3 data
        print("🔧 Exercise 3 Debug:")
        print("   Question: \(exercise3.questionData?.question ?? "nil")")
        print("   Options: \(exercise3.questionData?.options ?? [])")
        print("   Correct Answer: \(exercise3.questionData?.correctAnswer ?? -1)")

        exercises.append(exercise3)

        // Exercise 4: Yes/No responses
        var exercise4 = TamilSupabasePracticeExercise(
            id: "a1-practice-4",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E04",
            exerciseType: "multiple_choice",
            titleEnglish: "Basic Responses",
            titleTamil: "அடிப்படை பதில்கள்",
            instructionsEnglish: "Choose the correct Tamil word for the response.",
            instructionsTamil: "பதிலுக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise4.questionData = ExerciseQuestionData(
            question: "How do you say 'Yes' in Tamil?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["ஆம்", "இல்லை", "நன்றி", "வணக்கம்"],
            optionsTamil: ["ஆம்", "இல்லை", "நன்றி", "வணக்கம்"],
            optionsRomanization: ["Aam", "Illai", "Nanri", "Vanakkam"], // Basic learners need romanization!
            optionsPronunciation: ["AAM", "il-LAI", "NAN-ri", "va-NAK-kam"], // Pronunciation guides
            optionsAudioUrls: nil, // Will be generated by audio script
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'ஆம்' (Aam) means 'Yes' in Tamil.",
            explanationTamil: "'ஆம்' என்பது தமிழில் 'Yes' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        exercises.append(exercise4)

        // Exercise 5: Basic needs vocabulary
        var exercise5 = TamilSupabasePracticeExercise(
            id: "a1-practice-5",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E05",
            exerciseType: "multiple_choice",
            titleEnglish: "Basic Needs Vocabulary",
            titleTamil: "அடிப்படை தேவைகள் சொற்கள்",
            instructionsEnglish: "Choose the correct Tamil word for the basic need.",
            instructionsTamil: "அடிப்படை தேவைக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise5.questionData = ExerciseQuestionData(
            question: "What is the Tamil word for 'Water'?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["தண்ணீர்", "உணவு", "வீடு", "பணம்"],
            optionsTamil: ["தண்ணீர்", "உணவு", "வீடு", "பணம்"],
            optionsRomanization: ["Thanneer", "Unavu", "Veedu", "Panam"], // Basic learners need romanization!
            optionsPronunciation: ["than-NEER", "u-NA-vu", "VEE-du", "pa-NAM"], // Pronunciation guides
            optionsAudioUrls: nil, // Will be generated by audio script
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'தண்ணீர்' (Thanneer) means 'Water' in Tamil.",
            explanationTamil: "'தண்ணீர்' என்பது தமிழில் 'Water' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        exercises.append(exercise5)

        // Exercise 6: Polite expressions
        var exercise6 = TamilSupabasePracticeExercise(
            id: "a1-practice-6",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E06",
            exerciseType: "multiple_choice",
            titleEnglish: "Polite Expressions",
            titleTamil: "கண்ணியமான வெளிப்பாடுகள்",
            instructionsEnglish: "Choose the correct Tamil word for the polite expression.",
            instructionsTamil: "கண்ணியமான வெளிப்பாட்டுக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise6.questionData = ExerciseQuestionData(
            question: "How do you say 'Please' in Tamil?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["தயவுசெய்து", "மன்னிக்கவும்", "நன்றி", "வணக்கம்"],
            optionsTamil: ["தயவுசெய்து", "மன்னிக்கவும்", "நன்றி", "வணக்கம்"],
            optionsRomanization: ["Thayavu seithu", "Mannikkavum", "Nanri", "Vanakkam"], // Basic learners need romanization!
            optionsPronunciation: ["tha-ya-vu SAY-thu", "man-nik-ka-vum", "NAN-ri", "va-NAK-kam"], // Pronunciation guides
            optionsAudioUrls: nil, // Will be generated by audio script
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'தயவுசெய்து' (Thayavu seithu) means 'Please' in Tamil.",
            explanationTamil: "'தயவுசெய்து' என்பது தமிழில் 'Please' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        exercises.append(exercise6)

        // Exercise 7: Size adjectives
        var exercise7 = TamilSupabasePracticeExercise(
            id: "a1-practice-7",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E07",
            exerciseType: "multiple_choice",
            titleEnglish: "Descriptive Words",
            titleTamil: "விவரணை சொற்கள்",
            instructionsEnglish: "Choose the correct Tamil word for the descriptive word.",
            instructionsTamil: "விவரணை சொல்லுக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise7.questionData = ExerciseQuestionData(
            question: "What is the Tamil word for 'Big'?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["பெரிய", "சிறிய", "அழகான", "நல்ல"],
            optionsTamil: ["பெரிய", "சிறிய", "அழகான", "நல்ல"],
            optionsRomanization: ["Periya", "Siriya", "Azhagana", "Nalla"], // Basic learners need romanization!
            optionsPronunciation: ["pe-RI-ya", "si-RI-ya", "a-zha-GA-na", "NAL-la"], // Pronunciation guides
            optionsAudioUrls: nil, // Will be generated by audio script
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'பெரிய' (Periya) means 'Big' in Tamil.",
            explanationTamil: "'பெரிய' என்பது தமிழில் 'Big' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        exercises.append(exercise7)

        // Exercise 8: Emotions vocabulary
        var exercise8 = TamilSupabasePracticeExercise(
            id: "a1-practice-8",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E08",
            exerciseType: "multiple_choice",
            titleEnglish: "Emotions Vocabulary",
            titleTamil: "உணர்வுகள் சொற்கள்",
            instructionsEnglish: "Choose the correct Tamil word for the emotion.",
            instructionsTamil: "உணர்வுக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise8.questionData = ExerciseQuestionData(
            question: "How do you say 'Happy' in Tamil?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["மகிழ்ச்சி", "அன்பு", "நேரம்", "வேலை"],
            optionsTamil: ["மகிழ்ச்சி", "அன்பு", "நேரம்", "வேலை"],
            optionsRomanization: ["Magizhchi", "Anbu", "Neram", "Velai"], // Basic learners need romanization!
            optionsPronunciation: ["ma-GIZH-chi", "AN-bu", "NEH-ram", "VEH-lai"], // Pronunciation guides
            optionsAudioUrls: nil, // Will be generated by audio script
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'மகிழ்ச்சி' (Magizhchi) means 'Happy' in Tamil.",
            explanationTamil: "'மகிழ்ச்சி' என்பது தமிழில் 'Happy' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        exercises.append(exercise8)

        // Exercise 9: Basic verbs
        var exercise9 = TamilSupabasePracticeExercise(
            id: "a1-practice-9",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E09",
            exerciseType: "multiple_choice",
            titleEnglish: "Basic Verbs",
            titleTamil: "அடிப்படை வினைச்சொற்கள்",
            instructionsEnglish: "Choose the correct Tamil word for the action.",
            instructionsTamil: "செயலுக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise9.questionData = ExerciseQuestionData(
            question: "What is the Tamil word for 'Come'?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["வா", "வீடு", "வேலை", "வணக்கம்"],
            optionsTamil: ["வா", "வீடு", "வேலை", "வணக்கம்"],
            optionsRomanization: ["Vaa", "Veedu", "Velai", "Vanakkam"], // Basic learners need romanization!
            optionsPronunciation: ["VAA", "VEE-du", "VEH-lai", "va-NAK-kam"], // Pronunciation guides
            optionsAudioUrls: nil, // Will be generated by audio script
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'வா' (Vaa) means 'Come' in Tamil.",
            explanationTamil: "'வா' என்பது தமிழில் 'Come' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        exercises.append(exercise9)

        // Exercise 10: School vocabulary
        var exercise10 = TamilSupabasePracticeExercise(
            id: "a1-practice-10",
            lessonId: "a1-lesson-1",
            exerciseId: "L1E10",
            exerciseType: "multiple_choice",
            titleEnglish: "Education Vocabulary",
            titleTamil: "கல்வி சொற்கள்",
            instructionsEnglish: "Choose the correct Tamil word for the education term.",
            instructionsTamil: "கல்வி சொல்லுக்கான சரியான தமிழ் சொல்லைத் தேர்ந்தெடுக்கவும்.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        exercise10.questionData = ExerciseQuestionData(
            question: "How do you say 'School' in Tamil?",
            questionTamil: "", // No Tamil question for practice exercises
            options: ["பள்ளி", "புத்தகம்", "நண்பன்", "நேரம்"],
            optionsTamil: ["பள்ளி", "புத்தகம்", "நண்பன்", "நேரம்"],
            optionsRomanization: ["Palli", "Puthagam", "Nanban", "Neram"], // Basic learners need romanization!
            optionsPronunciation: ["PAL-li", "pu-tha-GAM", "NAN-ban", "NEH-ram"], // Pronunciation guides
            optionsAudioUrls: nil, // Will be generated by audio script
            correctAnswer: 0,
            correctAnswers: nil,
            explanation: "'பள்ளி' (Palli) means 'School' in Tamil.",
            explanationTamil: "'பள்ளி' என்பது தமிழில் 'School' என்று பொருள்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        exercises.append(exercise10)

        return exercises
    }
    
    /// Helper function to get random vocabulary words for creating distractors
    private static func getRandomVocabularyWords(excluding correctWord: String, count: Int = 3) -> [String] {
        let availableWords = a1Vocabulary.filter { $0.0 != correctWord }.map { $0.1 }
        return Array(availableWords.shuffled().prefix(count))
    }
    
    /// Helper function to romanize Tamil text
    private static func romanizeTamilText(_ tamilText: String) -> String {
        for (_, tamil, romanization) in a1Vocabulary {
            if tamilText.contains(tamil) {
                return romanization
            }
        }
        return tamilText // Return original if no match found
    }
}
