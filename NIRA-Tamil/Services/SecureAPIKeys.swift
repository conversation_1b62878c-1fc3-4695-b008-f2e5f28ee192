//
//  SecureAPIKeys.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation
import Security

// MARK: - Secure API Key Management

struct SecureAPIKeys {
    
    // MARK: - Keychain Service Identifiers
    private static let keychainService = "com.nira.app.apikeys"
    private static let geminiKeyIdentifier = "gemini_api_key"
    private static let supabaseURLIdentifier = "supabase_url"
    private static let supabaseKeyIdentifier = "supabase_anon_key"
    private static let openAIKeyIdentifier = "openai_api_key"
    // Note: ElevenLabs removed - using Google TTS instead

    // MARK: - Fallback Values (for when keychain is empty)
    private static let fallbackGeminiKey = ""
    private static let fallbackSupabaseURL = ""
    private static let fallbackSupabaseKey = ""
    private static let fallbackOpenAIKey = ""
    // Note: ElevenLabs removed - using Google TTS instead
    
    // MARK: - Secure Key Retrieval

    static var geminiAPIKey: String {
        return getSecureKey(identifier: geminiKeyIdentifier) ?? fallbackGeminiKey
    }

    static var supabaseURL: String {
        return getSecureKey(identifier: supabaseURLIdentifier) ?? fallbackSupabaseURL
    }

    static var supabaseAnonKey: String {
        return getSecureKey(identifier: supabaseKeyIdentifier) ?? fallbackSupabaseKey
    }

    static var openAIAPIKey: String {
        return getSecureKey(identifier: openAIKeyIdentifier) ?? fallbackOpenAIKey
    }
    
    // MARK: - Configuration Validation
    
    static var isConfigured: Bool {
        return !geminiAPIKey.isEmpty && 
               !supabaseURL.isEmpty && 
               !supabaseAnonKey.isEmpty &&
               isValidURL(supabaseURL) &&
               isValidAPIKey(geminiAPIKey)
    }
    
    static func validateConfiguration() throws {
        guard isConfigured else {
            throw SecureConfigurationError.missingAPIKeys
        }
        
        // Validate API key formats
        guard isValidAPIKey(geminiAPIKey) else {
            throw SecureConfigurationError.invalidGeminiKey
        }
        
        guard isValidURL(supabaseURL) else {
            throw SecureConfigurationError.invalidSupabaseURL
        }
        
        guard isValidAPIKey(supabaseAnonKey) else {
            throw SecureConfigurationError.invalidSupabaseKey
        }
    }
    
    // MARK: - Secure Key Storage (Development Only)
    
    #if DEBUG
    static func storeAPIKeys(
        gemini: String,
        supabaseURL: String,
        supabaseKey: String,
        openAI: String? = nil
    ) throws {
        try storeSecureKey(gemini, identifier: geminiKeyIdentifier)
        try storeSecureKey(supabaseURL, identifier: supabaseURLIdentifier)
        try storeSecureKey(supabaseKey, identifier: supabaseKeyIdentifier)
        
        if let openAI = openAI {
            try storeSecureKey(openAI, identifier: openAIKeyIdentifier)
        }
    }
    #endif
    
    // MARK: - Private Methods
    
    private static func getSecureKey(identifier: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: identifier,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let data = result as? Data,
              let key = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return key
    }
    
    private static func storeSecureKey(_ key: String, identifier: String) throws {
        guard let data = key.data(using: .utf8) else {
            throw SecureConfigurationError.invalidKeyFormat
        }
        
        // Delete existing key first
        let deleteQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: identifier
        ]
        SecItemDelete(deleteQuery as CFDictionary)
        
        // Add new key
        let addQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: identifier,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(addQuery as CFDictionary, nil)
        guard status == errSecSuccess else {
            throw SecureConfigurationError.keychainStorageError
        }
    }
    
    private static func isValidAPIKey(_ key: String) -> Bool {
        // Basic API key validation
        return key.count >= 20 && 
               !key.contains(" ") && 
               !key.contains("YOUR_") &&
               !key.contains("PLACEHOLDER")
    }
    
    private static func isValidURL(_ urlString: String) -> Bool {
        guard let url = URL(string: urlString) else { return false }
        return url.scheme == "https" && url.host != nil
    }
}

// MARK: - Secure Configuration Errors

enum SecureConfigurationError: LocalizedError {
    case missingAPIKeys
    case invalidGeminiKey
    case invalidSupabaseURL
    case invalidSupabaseKey
    case invalidKeyFormat
    case keychainStorageError
    
    var errorDescription: String? {
        switch self {
        case .missingAPIKeys:
            return "API keys not configured. Please set up your API keys securely."
        case .invalidGeminiKey:
            return "Invalid Gemini API key format."
        case .invalidSupabaseURL:
            return "Invalid Supabase URL format."
        case .invalidSupabaseKey:
            return "Invalid Supabase API key format."
        case .invalidKeyFormat:
            return "Invalid key format for storage."
        case .keychainStorageError:
            return "Failed to store key in keychain."
        }
    }
}

// MARK: - Migration from Old APIKeys

extension SecureAPIKeys {
    
    /// Migrate from old hardcoded keys to secure storage (Development only)
    #if DEBUG
    static func migrateFromLegacyKeys() {
        // This would be used to migrate from the old APIKeys.swift file
        // Only available in debug builds for security
        print("⚠️ Legacy API key migration should be done manually for security")
    }
    #endif
}
