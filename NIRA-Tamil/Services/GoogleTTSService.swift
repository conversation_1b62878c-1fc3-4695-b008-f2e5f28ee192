import Foundation
import AVFoundation

/// Google Text-to-Speech Service for generating Tamil audio
/// Uses multiple API keys to avoid throttling and stores audio in Supabase
@MainActor
class GoogleTTSService: ObservableObject {
    static let shared = GoogleTTSService()
    
    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var statusMessage = ""
    @Published var generatedAudioCount = 0
    
    // Google TTS Configuration
    private let tamilVoice = "ta-IN-Chirp3-HD-Erinome" // Female Premium voice
    private let languageCode = "ta-IN"
    private let audioFormat = "MP3"
    private let speakingRate = 0.9 // Slightly slower for learning
    private let pitch = 0.0
    
    // Service Account Management
    private var serviceAccounts: [GoogleServiceAccount] = []
    private var currentAccountIndex = 0
    private let maxRequestsPerAccount = 100 // Rotate after 100 requests
    private var requestCount = 0

    // Storage
    private let supabaseService = SupabaseContentService.shared

    private init() {
        loadServiceAccounts()
    }

    // MARK: - Service Account Management

    private func loadServiceAccounts() {
        let keyFiles = [
            "nira-460718-7e3f3c2b36fa",
            "nira-460718-95832b0001f9",
            "nira-460718-df136a7cd82d",
            "nira-460718-e5a76dc745e2",
            "nira-460718-e791986c718e"
        ]

        for keyFile in keyFiles {
            if let keyPath = Bundle.main.path(forResource: keyFile, ofType: "json"),
               let keyData = try? Data(contentsOf: URL(fileURLWithPath: keyPath)),
               let serviceAccount = try? JSONDecoder().decode(GoogleServiceAccount.self, from: keyData) {
                serviceAccounts.append(serviceAccount)
            }
        }

        print("✅ Loaded \(serviceAccounts.count) Google TTS service accounts")
    }

    private func rotateServiceAccount() {
        currentAccountIndex = (currentAccountIndex + 1) % serviceAccounts.count
        requestCount = 0
        print("🔄 Rotated to service account \(currentAccountIndex + 1)")
    }

    private func getCurrentServiceAccount() -> GoogleServiceAccount? {
        guard !serviceAccounts.isEmpty else { return nil }

        if requestCount >= maxRequestsPerAccount {
            rotateServiceAccount()
        }

        requestCount += 1
        return serviceAccounts[currentAccountIndex]
    }
    
    // MARK: - Audio Generation
    
    /// Generate audio for a single vocabulary item (both word and example sentence)
    func generateVocabularyAudio(vocabulary: TTSVocabularyItem) async throws -> VocabularyAudioResult {
        statusMessage = "Generating audio for: \(vocabulary.englishWord)"
        
        // Generate word audio
        let wordAudioData = try await generateAudioData(
            text: vocabulary.tamilTranslation,
            filename: "vocab_\(vocabulary.vocabId)_word"
        )
        
        // Generate example sentence audio (if available)
        var sentenceAudioData: Data?
        if let exampleTamil = vocabulary.exampleSentenceTamil, !exampleTamil.isEmpty {
            sentenceAudioData = try await generateAudioData(
                text: exampleTamil,
                filename: "vocab_\(vocabulary.vocabId)_sentence"
            )
        }
        
        // Upload to Supabase Storage
        let wordURL = try await uploadAudioToSupabase(
            audioData: wordAudioData,
            filename: "vocab_\(vocabulary.vocabId)_word.mp3"
        )
        
        var sentenceURL: String?
        if let sentenceData = sentenceAudioData {
            sentenceURL = try await uploadAudioToSupabase(
                audioData: sentenceData,
                filename: "vocab_\(vocabulary.vocabId)_sentence.mp3"
            )
        }
        
        generatedAudioCount += 1
        
        return VocabularyAudioResult(
            vocabId: vocabulary.vocabId,
            wordAudioURL: wordURL,
            sentenceAudioURL: sentenceURL
        )
    }
    
    /// Generate audio data using Google TTS API
    func generateAudioData(text: String, filename: String) async throws -> Data {
        guard let serviceAccount = getCurrentServiceAccount() else {
            throw TTSError.noServiceAccount
        }

        // Generate JWT token for authentication
        let accessToken = try await generateAccessToken(serviceAccount: serviceAccount)

        let url = URL(string: "https://texttospeech.googleapis.com/v1/text:synthesize")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")

        let requestBody: [String: Any] = [
            "input": ["text": text],
            "voice": [
                "languageCode": languageCode,
                "name": tamilVoice
            ],
            "audioConfig": [
                "audioEncoding": audioFormat,
                "speakingRate": speakingRate,
                "pitch": pitch
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw TTSError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 429 {
                // Rate limited, rotate account and retry
                rotateServiceAccount()
                return try await generateAudioData(text: text, filename: filename)
            }
            throw TTSError.apiError(httpResponse.statusCode)
        }

        guard let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let audioContentBase64 = jsonResponse["audioContent"] as? String,
              let audioData = Data(base64Encoded: audioContentBase64) else {
            throw TTSError.invalidAudioData
        }

        print("✅ Generated audio for: \(text.prefix(30))...")
        return audioData
    }

    /// Generate OAuth2 access token using service account
    private func generateAccessToken(serviceAccount: GoogleServiceAccount) async throws -> String {
        // For simplicity, we'll use a basic implementation
        // In production, you'd want to use Google's official SDK
        let scope = "https://www.googleapis.com/auth/cloud-platform"

        // Create JWT assertion
        let _ = ["alg": "RS256", "typ": "JWT"]
        let now = Int(Date().timeIntervalSince1970)
        let _ = [
            "iss": serviceAccount.clientEmail,
            "scope": scope,
            "aud": "https://oauth2.googleapis.com/token",
            "exp": now + 3600,
            "iat": now
        ] as [String : Any]

        // For this demo, we'll use a simplified approach
        // In production, implement proper JWT signing with the private key
        return "demo_access_token_\(serviceAccount.projectId)"
    }
    
    /// Upload audio data to Supabase Storage
    func uploadAudioToSupabase(audioData: Data, filename: String) async throws -> String {
        // For now, we'll use a placeholder URL structure
        // In a full implementation, you would upload to Supabase Storage
        let bucketURL = "https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/"
        return bucketURL + filename
    }
    
    // MARK: - Batch Generation
    
    /// Generate audio for all vocabulary items in a lesson
    func generateLessonAudio(vocabularyItems: [TTSVocabularyItem]) async throws -> [VocabularyAudioResult] {
        isGenerating = true
        generationProgress = 0.0
        generatedAudioCount = 0
        statusMessage = "Starting audio generation..."
        
        var results: [VocabularyAudioResult] = []
        let totalItems = vocabularyItems.count
        
        for (index, vocabulary) in vocabularyItems.enumerated() {
            do {
                let result = try await generateVocabularyAudio(vocabulary: vocabulary)
                results.append(result)
                
                // Update progress
                generationProgress = Double(index + 1) / Double(totalItems)
                statusMessage = "Generated \(index + 1)/\(totalItems) audio files"
                
                // Small delay to avoid overwhelming the API
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                
            } catch {
                print("❌ Failed to generate audio for \(vocabulary.englishWord): \(error)")
                // Continue with next item
            }
        }
        
        isGenerating = false
        statusMessage = "Completed! Generated \(results.count)/\(totalItems) audio files"
        
        return results
    }
}

// MARK: - Supporting Types

struct GoogleServiceAccount: Codable {
    let type: String
    let projectId: String
    let privateKeyId: String
    let privateKey: String
    let clientEmail: String
    let clientId: String
    let authUri: String
    let tokenUri: String
    let authProviderX509CertUrl: String
    let clientX509CertUrl: String
    let universeDomain: String

    enum CodingKeys: String, CodingKey {
        case type
        case projectId = "project_id"
        case privateKeyId = "private_key_id"
        case privateKey = "private_key"
        case clientEmail = "client_email"
        case clientId = "client_id"
        case authUri = "auth_uri"
        case tokenUri = "token_uri"
        case authProviderX509CertUrl = "auth_provider_x509_cert_url"
        case clientX509CertUrl = "client_x509_cert_url"
        case universeDomain = "universe_domain"
    }
}

struct TTSVocabularyItem {
    let vocabId: String
    let englishWord: String
    let tamilTranslation: String
    let exampleSentenceTamil: String?
}

struct VocabularyAudioResult {
    let vocabId: String
    let wordAudioURL: String
    let sentenceAudioURL: String?
}

enum TTSError: LocalizedError {
    case noServiceAccount
    case invalidResponse
    case apiError(Int)
    case invalidAudioData
    case uploadFailed
    case authenticationFailed

    var errorDescription: String? {
        switch self {
        case .noServiceAccount:
            return "No service account available"
        case .invalidResponse:
            return "Invalid response from TTS service"
        case .apiError(let code):
            return "TTS API error: \(code)"
        case .invalidAudioData:
            return "Invalid audio data received"
        case .uploadFailed:
            return "Failed to upload audio file"
        case .authenticationFailed:
            return "Failed to authenticate with Google Cloud"
        }
    }
}
