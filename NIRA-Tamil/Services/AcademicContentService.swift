//
//  AcademicContentService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import Foundation
import SwiftUI

@MainActor
class AcademicContentService: ObservableObject {
    static let shared = AcademicContentService()

    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var textbooks: [TNTextbook] = []
    @Published var academicModules: [AcademicContentModule] = []
    @Published var currentStandard: Int = 1
    @Published var academicProgress: AcademicProgress = AcademicProgress()

    private let contentLoader = TamilContentLoader()
    private let supabaseAcademicService = SupabaseAcademicService.shared
    private let userDefaults = UserDefaults.standard
    
    private init() {
        loadAcademicContent()
        loadAcademicProgress()
    }
    
    // MARK: - Content Loading
    
    func loadAcademicContent() {
        Task {
            await MainActor.run {
                isLoading = true
                errorMessage = nil
            }

            // Load both textbooks (local files) and academic modules (Supabase)
            await loadTextbooks()
            await loadAcademicModules()

            await MainActor.run {
                isLoading = false
            }
        }
    }

    private func loadTextbooks() async {
        do {
            let loadedTextbooks = try await contentLoader.loadTNTextbooks()
            await MainActor.run {
                textbooks = loadedTextbooks
            }
            print("📚 Loaded \(loadedTextbooks.count) Tamil Nadu textbooks from local files")
        } catch {
            print("❌ Textbook loading failed: \(error)")
        }
    }

    private func loadAcademicModules() async {
        // Load academic modules from Supabase for current standard
        await supabaseAcademicService.fetchAcademicModules(for: currentStandard)

        await MainActor.run {
            academicModules = supabaseAcademicService.academicModules
            if let error = supabaseAcademicService.errorMessage {
                errorMessage = error
            }
        }
    }
    
    // MARK: - Content Access
    
    func getTextbooksForStandard(_ standard: Int) -> [TNTextbook] {
        return textbooks.filter { $0.standard == standard }
    }

    func getAcademicModulesForStandard(_ standard: Int) -> [AcademicContentModule] {
        return academicModules.filter { $0.standardLevel == standard }
    }

    func getAcademicModules(for standard: Int, type: String) -> [AcademicContentModule] {
        return academicModules.filter { $0.standardLevel == standard && $0.moduleType == type }
    }

    func getAvailableModuleTypes(for standard: Int) -> [String] {
        let types = Set(academicModules.filter { $0.standardLevel == standard }.map { $0.moduleType })
        return Array(types).sorted()
    }

    func loadAcademicModulesForStandard(_ standard: Int) async {
        currentStandard = standard
        await loadAcademicModules()
    }
    
    func getAvailableStandards() -> [Int] {
        return Array(Set(textbooks.map { $0.standard })).sorted()
    }
    
    func getTextbook(for standard: Int) -> TNTextbook? {
        return textbooks.first { $0.standard == standard }
    }
    
    func getPageContent(standard: Int, pageNumber: String) -> String? {
        return getTextbook(for: standard)?.pages[pageNumber]
    }
    
    func getPageNumbers(for standard: Int) -> [String] {
        guard let textbook = getTextbook(for: standard) else { return [] }
        return Array(textbook.pages.keys).sorted { page1, page2 in
            // Sort pages numerically if possible
            if let num1 = Int(page1), let num2 = Int(page2) {
                return num1 < num2
            }
            return page1 < page2
        }
    }
    
    // MARK: - Academic Progress Tracking
    
    func markPageCompleted(standard: Int, pageNumber: String) {
        let pageId = "std\(standard)-page\(pageNumber)"
        
        if !academicProgress.completedPages.contains(pageId) {
            academicProgress.completedPages.insert(pageId)
            academicProgress.totalPagesRead += 1
            
            // Update current standard if needed
            if standard > academicProgress.currentStandard {
                academicProgress.currentStandard = standard
            }
            
            saveAcademicProgress()
        }
    }
    
    func isPageCompleted(standard: Int, pageNumber: String) -> Bool {
        let pageId = "std\(standard)-page\(pageNumber)"
        return academicProgress.completedPages.contains(pageId)
    }
    
    func getStandardProgress(for standard: Int) -> Double {
        guard let textbook = getTextbook(for: standard) else { return 0.0 }
        
        let totalPages = textbook.pageCount
        let completedPages = textbook.pages.keys.filter { pageNumber in
            isPageCompleted(standard: standard, pageNumber: pageNumber)
        }.count
        
        return totalPages > 0 ? Double(completedPages) / Double(totalPages) : 0.0
    }
    
    func getOverallProgress() -> Double {
        let totalPages = textbooks.reduce(0) { $0 + $1.pageCount }
        return totalPages > 0 ? Double(academicProgress.totalPagesRead) / Double(totalPages) : 0.0
    }
    
    // MARK: - Academic Learning Path
    
    func getRecommendedStandard() -> Int {
        // Start with Standard 1 if no progress
        if academicProgress.completedPages.isEmpty {
            return 1
        }
        
        // Find the highest standard with significant progress
        let availableStandards = getAvailableStandards()
        
        for standard in availableStandards.reversed() {
            let progress = getStandardProgress(for: standard)
            if progress > 0.1 { // At least 10% progress
                // If current standard is nearly complete, recommend next
                if progress > 0.8 && standard < availableStandards.max() ?? 12 {
                    return standard + 1
                }
                return standard
            }
        }
        
        return 1 // Default to Standard 1
    }
    
    func getNextPage(for standard: Int) -> String? {
        let pageNumbers = getPageNumbers(for: standard)
        
        // Find first uncompleted page
        for pageNumber in pageNumbers {
            if !isPageCompleted(standard: standard, pageNumber: pageNumber) {
                return pageNumber
            }
        }
        
        return nil // All pages completed
    }
    
    // MARK: - Content Search
    
    func searchContent(_ query: String) -> [AcademicSearchResult] {
        var results: [AcademicSearchResult] = []
        
        for textbook in textbooks {
            for (pageNumber, content) in textbook.pages {
                if content.localizedCaseInsensitiveContains(query) {
                    let result = AcademicSearchResult(
                        standard: textbook.standard,
                        pageNumber: pageNumber,
                        content: content,
                        matchingText: extractMatchingText(from: content, query: query)
                    )
                    results.append(result)
                }
            }
        }
        
        return results.sorted { $0.standard < $1.standard }
    }
    
    private func extractMatchingText(from content: String, query: String) -> String {
        // Extract a snippet around the matching text
        let range = content.range(of: query, options: .caseInsensitive)
        guard let matchRange = range else { return String(content.prefix(100)) }
        
        let start = max(content.startIndex, content.index(matchRange.lowerBound, offsetBy: -50, limitedBy: content.startIndex) ?? content.startIndex)
        let end = min(content.endIndex, content.index(matchRange.upperBound, offsetBy: 50, limitedBy: content.endIndex) ?? content.endIndex)
        
        return String(content[start..<end])
    }
    
    // MARK: - Academic Achievements
    
    func checkAchievements() -> [AcademicAchievement] {
        var achievements: [AcademicAchievement] = []
        
        // Standard completion achievements
        for standard in getAvailableStandards() {
            let progress = getStandardProgress(for: standard)
            if progress >= 1.0 {
                achievements.append(.standardCompleted(standard))
            } else if progress >= 0.5 {
                achievements.append(.standardHalfway(standard))
            }
        }
        
        // Reading milestones
        let totalPages = academicProgress.totalPagesRead
        if totalPages >= 100 {
            achievements.append(.readingMilestone(100))
        } else if totalPages >= 50 {
            achievements.append(.readingMilestone(50))
        } else if totalPages >= 10 {
            achievements.append(.readingMilestone(10))
        }
        
        return achievements
    }
    
    // MARK: - Data Persistence
    
    private func saveAcademicProgress() {
        if let data = try? JSONEncoder().encode(academicProgress) {
            userDefaults.set(data, forKey: "academicProgress")
        }
    }
    
    private func loadAcademicProgress() {
        guard let data = userDefaults.data(forKey: "academicProgress"),
              let progress = try? JSONDecoder().decode(AcademicProgress.self, from: data) else {
            return
        }
        
        academicProgress = progress
    }
}

// MARK: - Supporting Types

struct AcademicProgress: Codable {
    var currentStandard: Int = 1
    var completedPages: Set<String> = []
    var totalPagesRead: Int = 0
    var lastAccessDate: Date = Date()
}

struct AcademicSearchResult: Identifiable {
    let id = UUID()
    let standard: Int
    let pageNumber: String
    let content: String
    let matchingText: String
}

enum AcademicAchievement: Identifiable {
    case standardCompleted(Int)
    case standardHalfway(Int)
    case readingMilestone(Int)
    
    var id: String {
        switch self {
        case .standardCompleted(let std): return "std_complete_\(std)"
        case .standardHalfway(let std): return "std_halfway_\(std)"
        case .readingMilestone(let pages): return "reading_\(pages)"
        }
    }
    
    var title: String {
        switch self {
        case .standardCompleted(let std): return "Standard \(std) Completed!"
        case .standardHalfway(let std): return "Standard \(std) - Halfway There!"
        case .readingMilestone(let pages): return "\(pages) Pages Read!"
        }
    }
    
    var description: String {
        switch self {
        case .standardCompleted(let std): return "You've completed all content for Standard \(std)"
        case .standardHalfway(let std): return "You're halfway through Standard \(std)"
        case .readingMilestone(let pages): return "You've read \(pages) pages of Tamil content"
        }
    }
    
    var icon: String {
        switch self {
        case .standardCompleted: return "graduationcap.fill"
        case .standardHalfway: return "book.fill"
        case .readingMilestone: return "book.closed.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .standardCompleted: return .green
        case .standardHalfway: return .blue
        case .readingMilestone: return .orange
        }
    }
}
