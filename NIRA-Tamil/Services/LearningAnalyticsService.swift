import Foundation
import Combine
import UIKit

@MainActor
class LearningAnalyticsService: ObservableObject {
    static let shared = LearningAnalyticsService()
    
    @Published var currentSession: StudySession?
    @Published var userProgress: [UUID: AdaptiveUserProgress] = [:]
    @Published var learningProfile: AdaptiveLearningProfile?
    @Published var achievements: [LearningAchievement] = []
    @Published var userAchievements: [UserLearningAchievement] = []
    @Published var recommendations: [ContentRecommendation] = []
    
    private let supabaseClient = NIRASupabaseClient.shared
    private var sessionId = UUID()
    private var sessionStartTime: Date?
    private var interactionBuffer: [LearningAnalytics] = []
    private let bufferFlushInterval: TimeInterval = 30 // Flush every 30 seconds
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupPeriodicFlush()
    }
    
    // MARK: - Session Management
    
    func startSession(userId: UUID, languageId: UUID, sessionType: StudySession.SessionType) {
        sessionId = UUID()
        sessionStartTime = Date()
        
        currentSession = StudySession(
            id: sessionId,
            userId: userId,
            languageId: languageId,
            sessionType: sessionType,
            lessonsCompleted: 0,
            exercisesAttempted: 0,
            exercisesCorrect: 0,
            totalTimeSeconds: 0,
            wordsLearned: 0,
            wordsReviewed: 0,
            interactionsCount: 0,
            hintsUsed: 0,
            breaksTaken: 0,
            deviceType: getCurrentDeviceType(),
            locationContext: nil,
            startedAt: Date(),
            endedAt: nil,
            createdAt: Date()
        )
        
        // Track session start
        trackInteraction(
            userId: userId,
            interactionType: .lessonStart,
            contentType: nil,
            contentId: nil,
            isCorrect: nil,
            responseTime: nil
        )
    }
    
    func endSession() async {
        guard var session = currentSession,
              let startTime = sessionStartTime else { return }
        
        let endTime = Date()
        session.totalTimeSeconds = Int(endTime.timeIntervalSince(startTime))
        
        // Save session to database
        await saveSession(session)
        
        // Flush any remaining analytics
        await flushInteractionBuffer()
        
        currentSession = nil
        sessionStartTime = nil
    }
    
    // MARK: - Interaction Tracking
    
    func trackInteraction(
        userId: UUID,
        lessonId: UUID? = nil,
        interactionType: LearningAnalytics.InteractionType,
        contentType: LearningAnalytics.ContentType?,
        contentId: String?,
        isCorrect: Bool?,
        responseTime: Int?,
        hintUsed: Bool = false,
        attemptsBeforeCorrect: Int? = nil,
        metadata: [String: SupabaseAnyCodable]? = nil
    ) {
        let analytics = LearningAnalytics(
            id: UUID(),
            userId: userId,
            lessonId: lessonId,
            sessionId: sessionId,
            interactionType: interactionType,
            contentId: contentId,
            contentType: contentType,
            isCorrect: isCorrect,
            responseTimeMs: responseTime,
            hintUsed: hintUsed,
            attemptsBeforeCorrect: attemptsBeforeCorrect,
            difficultyLevel: getCurrentDifficultyLevel(),
            timeOfDay: Calendar.current.component(.hour, from: Date()),
            deviceType: getCurrentDeviceType(),
            metadata: metadata,
            createdAt: Date()
        )
        
        interactionBuffer.append(analytics)
        
        // Update current session
        updateCurrentSession(for: interactionType, isCorrect: isCorrect, hintUsed: hintUsed)
        
        // Check for immediate achievements
        Task {
            await checkForAchievements(userId: userId, interaction: analytics)
        }
    }
    
    // MARK: - Progress Tracking
    
    func updateLessonProgress(
        userId: UUID,
        lessonId: UUID,
        languageId: UUID,
        completionStatus: AdaptiveUserProgress.CompletionStatus,
        accuracyScore: Double?,
        timeSpent: Int,
        vocabularyResults: [String: VocabularyMasteryData]? = nil,
        exerciseResults: [String: AdaptiveExerciseResult]? = nil
    ) async {
        let progressId = UUID()
        
        let progress = AdaptiveUserProgress(
            id: progressId,
            userId: userId,
            lessonId: lessonId,
            languageId: languageId,
            completionStatus: completionStatus,
            accuracyScore: accuracyScore,
            timeSpentSeconds: timeSpent,
            attemptsCount: 1,
            vocabularyMastery: vocabularyResults,
            exerciseResults: exerciseResults,
            mistakesPattern: nil,
            sessionDuration: timeSpent,
            engagementScore: calculateEngagementScore(),
            difficultyRating: nil,
            startedAt: Date().addingTimeInterval(-TimeInterval(timeSpent)),
            completedAt: completionStatus == .completed || completionStatus == .mastered ? Date() : nil,
            lastReviewedAt: Date(),
            createdAt: Date(),
            updatedAt: Date()
        )
        
        userProgress[lessonId] = progress
        
        // Save to database
        await saveUserProgress(progress)
        
        // Update adaptive learning profile
        await updateLearningProfile(userId: userId, languageId: languageId, progress: progress)
        
        // Check for achievements
        await checkForAchievements(userId: userId, progress: progress)
        
        // Generate new recommendations
        await generateRecommendations(userId: userId, languageId: languageId)
    }
    
    // MARK: - Adaptive Learning Profile
    
    private func updateLearningProfile(userId: UUID, languageId: UUID, progress: AdaptiveUserProgress) async {
        // This would contain ML algorithms to analyze patterns and update the profile
        // For now, we'll implement basic heuristics
        
        var profile = learningProfile ?? AdaptiveLearningProfile(
            id: UUID(),
            userId: userId,
            languageId: languageId,
            optimalSessionLength: nil,
            preferredDifficultyProgression: nil,
            learningStyleWeights: nil,
            strengthAreas: nil,
            weaknessAreas: nil,
            learningVelocity: nil,
            retentionRate: nil,
            optimalStudyTimes: nil,
            engagementPatterns: nil,
            challengePreference: nil,
            forgettingCurveParams: nil,
            nextReviewSchedule: nil,
            createdAt: Date(),
            updatedAt: Date()
        )
        
        // Update learning velocity
        let recentProgress = Array(userProgress.values.suffix(10))
        if !recentProgress.isEmpty {
            let completedLessons = recentProgress.filter { $0.completionStatus == .completed || $0.completionStatus == .mastered }
            let timeSpan = recentProgress.last!.createdAt.timeIntervalSince(recentProgress.first!.createdAt)
            let weeksSpan = max(timeSpan / (7 * 24 * 3600), 1.0)
            profile.learningVelocity = Double(completedLessons.count) / weeksSpan
        }
        
        // Update retention rate based on accuracy scores
        let accuracyScores = recentProgress.compactMap { $0.accuracyScore }
        if !accuracyScores.isEmpty {
            profile.retentionRate = accuracyScores.reduce(0, +) / Double(accuracyScores.count) / 100.0
        }
        
        // Determine optimal session length
        let sessionDurations = recentProgress.compactMap { $0.sessionDuration }
        if !sessionDurations.isEmpty {
            profile.optimalSessionLength = sessionDurations.reduce(0, +) / sessionDurations.count / 60 // Convert to minutes
        }
        
        // Update strength and weakness areas based on performance
        updateSkillAreas(profile: &profile, progress: recentProgress)
        
        learningProfile = profile
        await saveLearningProfile(profile)
    }
    
    private func updateSkillAreas(profile: inout AdaptiveLearningProfile, progress: [AdaptiveUserProgress]) {
        var skillPerformance: [String: [Double]] = [:]
        
        for progressItem in progress {
            // Analyze vocabulary mastery
            if let vocabMastery = progressItem.vocabularyMastery {
                let avgMastery = vocabMastery.values.map { $0.masteryLevel }.reduce(0, +) / Double(vocabMastery.count)
                skillPerformance["vocabulary", default: []].append(avgMastery)
            }
            
            // Analyze exercise performance
            if let exerciseResults = progressItem.exerciseResults {
                let avgAccuracy = exerciseResults.values.map { $0.correct ? 1.0 : 0.0 }.reduce(0, +) / Double(exerciseResults.count)
                skillPerformance["exercises", default: []].append(avgAccuracy)
            }
            
            // Overall accuracy
            if let accuracy = progressItem.accuracyScore {
                skillPerformance["overall", default: []].append(accuracy / 100.0)
            }
        }
        
        // Determine strengths (>0.8 average) and weaknesses (<0.6 average)
        var strengths: [String] = []
        var weaknesses: [String] = []
        
        for (skill, performances) in skillPerformance {
            let average = performances.reduce(0, +) / Double(performances.count)
            if average > 0.8 {
                strengths.append(skill)
            } else if average < 0.6 {
                weaknesses.append(skill)
            }
        }
        
        profile.strengthAreas = strengths.isEmpty ? nil : strengths
        profile.weaknessAreas = weaknesses.isEmpty ? nil : weaknesses
    }
    
    // MARK: - Achievement System
    
    func loadAchievements() async {
        // For now, we'll populate with comprehensive mock data
        // In production, this would load from Supabase
        await populateMockAchievements()
        await populateMockUserAchievements()
        
        // Uncomment when Supabase achievements table is ready:
        /*
        do {
            let response = try await supabaseClient.client
                .from("achievements")
                .select("*")
                .eq("is_active", value: true)
                .execute()
            
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            achievements = try decoder.decode([LearningAchievement].self, from: response.data)
        } catch {
            print("❌ Error loading achievements: \(error)")
        }
        */
    }
    
    private func populateMockAchievements() async {
        achievements = [
            // PROGRESS ACHIEVEMENTS
            LearningAchievement(
                id: UUID(),
                name: "First Steps",
                description: "Complete your first lesson",
                category: .progress,
                difficulty: .bronze,
                iconName: "🎯",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .lessonCompletion,
                    count: 1,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 10,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Getting Started",
                description: "Complete 5 lessons",
                category: .progress,
                difficulty: .bronze,
                iconName: "📚",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .lessonCompletion,
                    count: 5,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 25,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Dedicated Learner",
                description: "Complete 25 lessons",
                category: .progress,
                difficulty: .silver,
                iconName: "🎓",
                colorHex: "#C0C0C0",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .lessonCompletion,
                    count: 25,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 75,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Scholar",
                description: "Complete 100 lessons",
                category: .progress,
                difficulty: .gold,
                iconName: "🏆",
                colorHex: "#FFD700",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .lessonCompletion,
                    count: 100,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 200,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            // PERFORMANCE ACHIEVEMENTS
            LearningAchievement(
                id: UUID(),
                name: "Perfect Score",
                description: "Get 100% accuracy on a lesson",
                category: .performance,
                difficulty: .bronze,
                iconName: "⭐",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .perfectScore,
                    count: nil,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: 100.0,
                    consecutiveDays: nil
                ),
                pointsReward: 15,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Accuracy Master",
                description: "Maintain 90% accuracy over 10 lessons",
                category: .performance,
                difficulty: .silver,
                iconName: "🎯",
                colorHex: "#C0C0C0",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .accuracyAchievement,
                    count: 10,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: 90.0,
                    consecutiveDays: nil
                ),
                pointsReward: 50,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Speed Demon",
                description: "Complete a lesson in under 5 minutes",
                category: .performance,
                difficulty: .silver,
                iconName: "⚡",
                colorHex: "#C0C0C0",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .speedCompletion,
                    count: nil,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 30,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            // CONSISTENCY ACHIEVEMENTS
            LearningAchievement(
                id: UUID(),
                name: "Streak Starter",
                description: "Study for 3 days in a row",
                category: .consistency,
                difficulty: .bronze,
                iconName: "🔥",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .streakMaintenance,
                    count: nil,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: 3
                ),
                pointsReward: 20,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Week Warrior",
                description: "Study for 7 days in a row",
                category: .consistency,
                difficulty: .silver,
                iconName: "🗓️",
                colorHex: "#C0C0C0",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .streakMaintenance,
                    count: nil,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: 7
                ),
                pointsReward: 50,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Unstoppable",
                description: "Study for 30 days in a row",
                category: .consistency,
                difficulty: .gold,
                iconName: "💪",
                colorHex: "#FFD700",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .streakMaintenance,
                    count: nil,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: 30
                ),
                pointsReward: 150,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Legend",
                description: "Study for 100 days in a row",
                category: .consistency,
                difficulty: .platinum,
                iconName: "👑",
                colorHex: "#E5E4E2",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .streakMaintenance,
                    count: nil,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: 100
                ),
                pointsReward: 500,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            // VOCABULARY ACHIEVEMENTS
            LearningAchievement(
                id: UUID(),
                name: "Word Collector",
                description: "Learn 50 new words",
                category: .progress,
                difficulty: .bronze,
                iconName: "📝",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .vocabularyMastery,
                    count: 50,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 25,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Vocabulary Master",
                description: "Learn 500 new words",
                category: .progress,
                difficulty: .gold,
                iconName: "📖",
                colorHex: "#FFD700",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .vocabularyMastery,
                    count: 500,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 100,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            // SOCIAL ACHIEVEMENTS
            LearningAchievement(
                id: UUID(),
                name: "Social Butterfly",
                description: "Add your first friend",
                category: .social,
                difficulty: .bronze,
                iconName: "👥",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .socialInteraction,
                    count: 1,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 15,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Community Leader",
                description: "Help 10 friends with their learning",
                category: .social,
                difficulty: .silver,
                iconName: "🤝",
                colorHex: "#C0C0C0",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .socialInteraction,
                    count: 10,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 75,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            // SPECIAL ACHIEVEMENTS
            LearningAchievement(
                id: UUID(),
                name: "Early Bird",
                description: "Complete a lesson before 8 AM",
                category: .special,
                difficulty: .bronze,
                iconName: "🌅",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .timeSpent,
                    count: nil,
                    language: nil,
                    timeframe: "morning",
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 20,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Night Owl",
                description: "Complete a lesson after 10 PM",
                category: .special,
                difficulty: .bronze,
                iconName: "🦉",
                colorHex: "#CD7F32",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .timeSpent,
                    count: nil,
                    language: nil,
                    timeframe: "night",
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 20,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "Weekend Warrior",
                description: "Study on both Saturday and Sunday",
                category: .special,
                difficulty: .silver,
                iconName: "🏖️",
                colorHex: "#C0C0C0",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .timeSpent,
                    count: nil,
                    language: nil,
                    timeframe: "weekend",
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 40,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            ),
            
            LearningAchievement(
                id: UUID(),
                name: "NIRA Explorer",
                description: "Try all available features in the app",
                category: .special,
                difficulty: .gold,
                iconName: "🗺️",
                colorHex: "#FFD700",
                badgeImageUrl: nil,
                criteria: AchievementCriteria(
                    type: .socialInteraction,
                    count: nil,
                    language: nil,
                    timeframe: nil,
                    accuracyThreshold: nil,
                    consecutiveDays: nil
                ),
                pointsReward: 100,
                isActive: true,
                isSecret: false,
                createdAt: Date()
            )
        ]
    }
    
    private func populateMockUserAchievements() async {
        // Simulate some achievements the user has already earned
        let currentUserId = UUID() // In real app, this would be the actual user ID
        
        // User has earned some basic achievements
        let earnedAchievementIds = achievements.prefix(6).map { $0.id } // First 6 achievements
        
        userAchievements = earnedAchievementIds.map { achievementId in
            UserLearningAchievement(
                id: UUID(),
                userId: currentUserId,
                achievementId: achievementId,
                earnedAt: Date().addingTimeInterval(-TimeInterval.random(in: 0...604800)), // Random time in last week
                progressData: nil,
                isShowcased: Bool.random(), // Random showcase status
                sharedAt: Bool.random() ? Date().addingTimeInterval(-TimeInterval.random(in: 0...86400)) : nil
            )
        }
    }
    
    private func checkForAchievements(userId: UUID, interaction: LearningAnalytics? = nil, progress: AdaptiveUserProgress? = nil) async {
        for achievement in achievements {
            if await shouldAwardAchievement(achievement, userId: userId, interaction: interaction, progress: progress) {
                await awardAchievement(achievement, userId: userId)
            }
        }
    }
    
    private func shouldAwardAchievement(_ achievement: LearningAchievement, userId: UUID, interaction: LearningAnalytics?, progress: AdaptiveUserProgress?) async -> Bool {
        // Check if user already has this achievement
        if userAchievements.contains(where: { $0.achievementId == achievement.id }) {
            return false
        }
        
        switch achievement.criteria.type {
        case .lessonCompletion:
            if let count = achievement.criteria.count {
                let completedLessons = userProgress.values.filter { 
                    $0.completionStatus == .completed || $0.completionStatus == .mastered 
                }.count
                return completedLessons >= count
            }
            
        case .streakMaintenance:
            if let days = achievement.criteria.consecutiveDays {
                return await getCurrentStreak(userId: userId) >= days
            }
            
        case .accuracyAchievement:
            if let threshold = achievement.criteria.accuracyThreshold,
               let progress = progress,
               let accuracy = progress.accuracyScore {
                return accuracy >= threshold
            }
            
        case .perfectScore:
            if let progress = progress,
               let accuracy = progress.accuracyScore {
                return accuracy >= 100.0
            }
            
        case .vocabularyMastery:
            if let count = achievement.criteria.count {
                let masteredWords = userProgress.values.compactMap { $0.vocabularyMastery?.values }
                    .flatMap { $0 }
                    .filter { $0.masteryLevel >= 0.9 }
                    .count
                return masteredWords >= count
            }
            
        default:
            break
        }
        
        return false
    }
    
    private func awardAchievement(_ achievement: LearningAchievement, userId: UUID) async {
        let userAchievement = UserLearningAchievement(
            id: UUID(),
            userId: userId,
            achievementId: achievement.id,
            earnedAt: Date(),
            progressData: nil,
            isShowcased: false,
            sharedAt: nil
        )
        
        userAchievements.append(userAchievement)
        
        // Save to database
        await saveUserAchievement(userAchievement)
        
        // Show celebration
        showAchievementCelebration(achievement)
    }
    
    // MARK: - Recommendation Engine
    
    private func generateRecommendations(userId: UUID, languageId: UUID) async {
        guard let profile = learningProfile else { return }
        
        var newRecommendations: [ContentRecommendation] = []
        
        // Recommend lessons based on weakness areas
        if let weaknesses = profile.weaknessAreas {
            for weakness in weaknesses {
                if let lessonId = await findLessonForSkill(weakness, languageId: languageId) {
                    let recommendation = ContentRecommendation(
                        id: UUID(),
                        userId: userId,
                        contentType: "lesson",
                        contentId: lessonId,
                        recommendationReason: "Improve your \(weakness) skills",
                        confidenceScore: 0.8,
                        shownAt: Date(),
                        clickedAt: nil,
                        completedAt: nil,
                        dismissedAt: nil,
                        algorithmVersion: "1.0",
                        contextData: ["skill_area": SupabaseAnyCodable(weakness)]
                    )
                    newRecommendations.append(recommendation)
                }
            }
        }
        
        // Recommend review sessions for spaced repetition
        if let schedule = profile.nextReviewSchedule {
            for (contentId, reviewDate) in schedule {
                if reviewDate <= Date() {
                    let recommendation = ContentRecommendation(
                        id: UUID(),
                        userId: userId,
                        contentType: "vocabulary_review",
                        contentId: UUID(uuidString: contentId),
                        recommendationReason: "Time to review vocabulary",
                        confidenceScore: 0.9,
                        shownAt: Date(),
                        clickedAt: nil,
                        completedAt: nil,
                        dismissedAt: nil,
                        algorithmVersion: "1.0",
                        contextData: ["review_type": SupabaseAnyCodable("spaced_repetition")]
                    )
                    newRecommendations.append(recommendation)
                }
            }
        }
        
        recommendations = newRecommendations
        
        // Save recommendations to database
        for recommendation in newRecommendations {
            await saveRecommendation(recommendation)
        }
    }
    
    // MARK: - Analytics and Insights
    
    func generateAnalyticsSummary(userId: UUID, languageId: UUID, timeframe: LearningAnalyticsSummary.AnalyticsTimeframe) async -> LearningAnalyticsSummary? {
        let userProgressItems = userProgress.values.filter { $0.userId == userId && $0.languageId == languageId }
        
        guard !userProgressItems.isEmpty else { return nil }
        
        let completedLessons = userProgressItems.filter { 
            $0.completionStatus == .completed || $0.completionStatus == .mastered 
        }
        
        let totalTimeSpent = userProgressItems.reduce(0) { $0 + $1.timeSpentSeconds }
        let accuracyScores = userProgressItems.compactMap { $0.accuracyScore }
        let averageAccuracy = accuracyScores.isEmpty ? 0.0 : accuracyScores.reduce(0, +) / Double(accuracyScores.count)
        
        let totalWordsLearned = userProgressItems.compactMap { $0.vocabularyMastery?.count }.reduce(0, +)
        
        return LearningAnalyticsSummary(
            userId: userId,
            languageId: languageId,
            timeframe: timeframe,
            totalLessonsCompleted: completedLessons.count,
            totalTimeSpent: totalTimeSpent,
            averageAccuracy: averageAccuracy,
            totalWordsLearned: totalWordsLearned,
            studyDaysCount: calculateStudyDays(userProgressItems),
            averageSessionLength: calculateAverageSessionLength(userProgressItems),
            totalInteractions: interactionBuffer.count,
            currentStreak: await getCurrentStreak(userId: userId),
            longestStreak: await getLongestStreak(userId: userId),
            cefrLevelProgress: calculateCEFRProgress(userProgressItems),
            optimalStudyHour: learningProfile?.optimalStudyTimes?.max(by: { $0.performance < $1.performance })?.hour,
            preferredSessionLength: learningProfile?.optimalSessionLength,
            strongestSkillAreas: learningProfile?.strengthAreas ?? [],
            improvementAreas: learningProfile?.weaknessAreas ?? []
        )
    }
    
    // MARK: - Helper Methods
    
    private func setupPeriodicFlush() {
        Timer.publish(every: bufferFlushInterval, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                Task {
                    await self.flushInteractionBuffer()
                }
            }
            .store(in: &cancellables)
    }
    
    private func flushInteractionBuffer() async {
        guard !interactionBuffer.isEmpty else { return }
        
        // Save analytics to database
        for analytics in interactionBuffer {
            await saveLearningAnalytics(analytics)
        }
        
        interactionBuffer.removeAll()
    }
    
    private func updateCurrentSession(for interactionType: LearningAnalytics.InteractionType, isCorrect: Bool?, hintUsed: Bool) {
        guard var session = currentSession else { return }
        
        session.interactionsCount += 1
        
        if hintUsed {
            session.hintsUsed += 1
        }
        
        switch interactionType {
        case .lessonComplete:
            session.lessonsCompleted += 1
        case .exerciseAttempt:
            session.exercisesAttempted += 1
            if isCorrect == true {
                session.exercisesCorrect += 1
            }
        case .vocabularyReview:
            session.wordsReviewed += 1
        default:
            break
        }
        
        currentSession = session
    }
    
    private func calculateEngagementScore() -> Double {
        // Simple engagement calculation based on interaction frequency
        // In a real implementation, this would be more sophisticated
        return min(Double(interactionBuffer.count) / 10.0, 1.0)
    }
    
    private func getCurrentDeviceType() -> String {
        #if os(iOS)
        return UIDevice.current.userInterfaceIdiom == UIUserInterfaceIdiom.pad ? "tablet" : "mobile"
        #else
        return "desktop"
        #endif
    }
    
    private func getCurrentDifficultyLevel() -> Int {
        // This would be determined based on the current lesson context
        return 1 // Default to A1
    }
    
    private func showAchievementCelebration(_ achievement: LearningAchievement) {
        // This would trigger a celebration animation in the UI
        // For now, we'll just print
        print("🎉 Achievement unlocked: \(achievement.name)")
    }
    
    // MARK: - Database Operations
    
    private func saveSession(_ session: StudySession) async {
        // Implementation would save to Supabase
        print("💾 Saving session: \(session.id)")
    }
    
    private func saveLearningAnalytics(_ analytics: LearningAnalytics) async {
        // Implementation would save to Supabase
        print("📊 Saving analytics: \(analytics.interactionType)")
    }
    
    private func saveUserProgress(_ progress: AdaptiveUserProgress) async {
        // Implementation would save to Supabase
        print("📈 Saving progress: \(progress.lessonId)")
    }
    
    private func saveLearningProfile(_ profile: AdaptiveLearningProfile) async {
        // Implementation would save to Supabase
        print("🧠 Saving learning profile: \(profile.userId)")
    }
    
    private func saveUserAchievement(_ achievement: UserLearningAchievement) async {
        // Implementation would save to Supabase
        print("🏆 Saving achievement: \(achievement.achievementId)")
    }
    
    private func saveRecommendation(_ recommendation: ContentRecommendation) async {
        // Implementation would save to Supabase
        print("💡 Saving recommendation: \(recommendation.contentType)")
    }
    
    // MARK: - Analytics Calculations
    
    private func calculateStudyDays(_ progressItems: [AdaptiveUserProgress]) -> Int {
        let uniqueDays = Set(progressItems.map { 
            Calendar.current.startOfDay(for: $0.createdAt)
        })
        return uniqueDays.count
    }
    
    private func calculateAverageSessionLength(_ progressItems: [AdaptiveUserProgress]) -> Int {
        let sessionLengths = progressItems.compactMap { $0.sessionDuration }
        return sessionLengths.isEmpty ? 0 : sessionLengths.reduce(0, +) / sessionLengths.count
    }
    
    private func calculateCEFRProgress(_ progressItems: [AdaptiveUserProgress]) -> [String: Double] {
        // This would analyze progress across CEFR levels
        // For now, return a simple calculation
        return [
            "A1": 0.8,
            "A2": 0.6,
            "B1": 0.3,
            "B2": 0.1,
            "C1": 0.0,
            "C2": 0.0
        ]
    }
    
    private func getCurrentStreak(userId: UUID) async -> Int {
        // This would calculate the current study streak
        return 7 // Placeholder
    }
    
    private func getLongestStreak(userId: UUID) async -> Int {
        // This would calculate the longest study streak
        return 15 // Placeholder
    }
    
    private func findLessonForSkill(_ skill: String, languageId: UUID) async -> UUID? {
        // This would find appropriate lessons for skill improvement
        return UUID() // Placeholder
    }
} 