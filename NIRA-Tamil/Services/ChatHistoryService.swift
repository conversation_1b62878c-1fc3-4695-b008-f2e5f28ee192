import Foundation
import Combine

// MARK: - Simple Chat Agent Model for Chat History
struct ChatAgent {
    let id: String
    let name: String
    let description: String
    let expertise: [String]
    let rating: Double
    let isOnline: Bool
    let profileImageName: String
    let emoji: String
}

// MARK: - Chat History Service
@MainActor
class ChatHistoryService: ObservableObject {
    static let shared = ChatHistoryService()
    
    @Published var conversations: [SupabaseChatConversation] = []
    @Published var currentConversation: SupabaseChatConversation?
    @Published var currentMessages: [SupabaseChatMessage] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseClient = NIRASupabaseClient.shared
    private let authService = AuthenticationService.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Listen for authentication changes from AuthenticationService
        authService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    Task {
                        await self?.loadUserConversations()
                    }
                }
            }
            .store(in: &cancellables)

        // Load conversations if already authenticated
        Task {
            if authService.isAuthenticated {
                await loadUserConversations()
            }
        }
    }

    private func ensureAuthenticated() async {
        print("🔐 Ensuring user authentication...")

        // Check if user is authenticated through AuthenticationService
        if authService.isAuthenticated && authService.currentUser != nil {
            print("✅ User is authenticated via AuthenticationService")
            // Sync the Supabase session with the authenticated user
            await syncSupabaseSession()
            await loadUserConversations()
        } else if supabaseClient.isConnected {
            print("✅ Already authenticated via Supabase")
            await loadUserConversations()
        } else {
            print("❌ User not authenticated - conversation history requires authentication")
            errorMessage = "Please sign in to save conversation history"
        }
    }

    private func syncSupabaseSession() async {
        // Ensure Supabase client is using the same session as AuthenticationService
        if let currentUser = authService.currentUser {
            print("🔄 Syncing Supabase session with authenticated user: \(currentUser.id)")
            await supabaseClient.syncWithCurrentSession()
        }
    }
    
    // MARK: - Conversation Management

    private func getAgentId(for agentName: String) async throws -> UUID {
        print("🔍 Looking up agent ID for: \(agentName)")

        let response: [AIAgent] = try await supabaseClient.client
            .from("ai_agents")
            .select("id, name")
            .eq("name", value: agentName.lowercased())
            .eq("is_active", value: true)
            .execute()
            .value

        guard let agent = response.first else {
            print("❌ Agent not found: \(agentName)")
            throw SupabaseError.invalidData
        }

        print("✅ Found agent: \(agent.name) with ID: \(agent.id)")
        return agent.id
    }
    
    func createNewConversation(
        with agent: ChatAgent,
        lessonId: UUID? = nil
    ) async throws -> SupabaseChatConversation {
        isLoading = true
        errorMessage = nil

        do {
            print("🆕 Creating new conversation with agent: \(agent.name)")

            // Check if user is authenticated
            guard authService.isAuthenticated, let currentUser = authService.currentUser else {
                print("❌ User not authenticated - cannot create conversation")
                throw SupabaseError.notAuthenticated
            }

            print("✅ User authenticated: \(currentUser.id.uuidString)")

            // Get the agent ID from the database
            let agentId = try await getAgentId(for: agent.name)
            print("✅ Found agent ID: \(agentId) for agent: \(agent.name)")

            let conversation = try await supabaseClient.createConversation(
                agentId: agentId,
                agentName: agent.name,
                title: "Chat with \(agent.name)"
            )

            print("✅ Conversation created with ID: \(conversation.id)")
            print("📝 Conversation title: \(conversation.title ?? "No title")")
            print("👤 User ID: \(conversation.userId)")

            currentConversation = conversation
            currentMessages = []

            // Add to conversations list
            conversations.insert(conversation, at: 0)

            print("📋 Added conversation to list. Total conversations: \(conversations.count)")

            // Immediately reload conversations to verify persistence
            print("🔄 Reloading conversations to verify...")
            await loadUserConversations()

            isLoading = false
            return conversation

        } catch {
            print("❌ Failed to create conversation: \(error)")
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    func loadConversation(_ conversation: SupabaseChatConversation) async {
        isLoading = true
        errorMessage = nil
        
        do {
            currentConversation = conversation
            currentMessages = try await supabaseClient.getConversationHistory(
                conversationId: conversation.id
            )
            isLoading = false
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
        }
    }
    
    func saveMessage(
        content: String,
        isFromUser: Bool,
        messageType: SupabaseMessageType = .text,
        metadata: [String: Any] = [:]
    ) async throws -> SupabaseChatMessage {
        guard let conversation = currentConversation else {
            print("❌ No current conversation to save message to")
            throw SupabaseError.invalidData
        }

        print("💾 Saving message to conversation \(conversation.id): \(content.prefix(50))...")
        print("👤 Message from user: \(isFromUser)")
        print("📝 Message type: \(messageType)")

        let message = try await supabaseClient.saveMessage(
            conversationId: conversation.id,
            content: content,
            isFromUser: isFromUser,
            messageType: messageType,
            metadata: metadata
        )

        print("✅ Message saved with ID: \(message.id)")

        // Add to current messages list
        currentMessages.append(message)

        print("✅ Message saved successfully with ID: \(message.id)")

        // Add to current messages
        currentMessages.append(message)

        // Update conversation timestamp and move to top
        if let index = conversations.firstIndex(where: { $0.id == conversation.id }) {
            // Create updated conversation with new timestamp
            let updatedConversation = conversations[index]
            // Note: We'll update this in the database, local list will be refreshed
            conversations.remove(at: index)
            conversations.insert(updatedConversation, at: 0)
            print("📝 Updated conversation position in list")
        }

        return message
    }
    
    func loadUserConversations() async {
        isLoading = true
        errorMessage = nil

        do {
            print("🔍 Loading user conversations...")

            // Check authentication through AuthenticationService
            guard authService.isAuthenticated, let currentUser = authService.currentUser else {
                print("🔑 Not authenticated, cannot load conversations")
                throw SupabaseError.notAuthenticated
            }

            // Sync Supabase session if not connected
            if !supabaseClient.isConnected {
                print("🔄 Supabase not connected, syncing session...")
                await supabaseClient.syncWithCurrentSession()
            }

            print("🔐 Supabase connected: \(supabaseClient.isConnected)")
            print("👤 Current user: \(currentUser.id.uuidString)")

            conversations = try await supabaseClient.getUserConversations()
            print("✅ Loaded \(conversations.count) conversations")

            // Debug: Print conversation details
            for conversation in conversations {
                print("📋 Conversation: \(conversation.title ?? "Untitled") - \(conversation.id)")
            }

            isLoading = false
        } catch {
            print("❌ Failed to load conversations: \(error)")
            isLoading = false
            errorMessage = error.localizedDescription
        }
    }
    
    func deleteConversation(_ conversation: SupabaseChatConversation) async {
        do {
            try await supabaseClient.deleteConversation(conversationId: conversation.id)
            
            // Remove from local list
            conversations.removeAll { $0.id == conversation.id }
            
            // Clear current conversation if it was deleted
            if currentConversation?.id == conversation.id {
                currentConversation = nil
                currentMessages = []
            }
            
        } catch {
            errorMessage = error.localizedDescription
        }
    }
    
    func searchConversations(query: String) async {
        guard !query.isEmpty else {
            await loadUserConversations()
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            conversations = try await supabaseClient.searchConversations(query: query)
            isLoading = false
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
        }
    }
    
    // MARK: - Conversation Analytics
    
    func getConversationStats() -> ConversationStats {
        let totalConversations = conversations.count
        let totalMessages = conversations.reduce(0) { total, conversation in
            // This would need to be calculated from actual message counts
            return total + 1 // Placeholder
        }
        
        let averageMessagesPerConversation = totalConversations > 0 ? 
            Double(totalMessages) / Double(totalConversations) : 0
        
        return ConversationStats(
            totalConversations: totalConversations,
            totalMessages: totalMessages,
            averageMessagesPerConversation: averageMessagesPerConversation,
            mostActiveAgent: getMostActiveAgent(),
            lastChatDate: conversations.first?.updatedAt
        )
    }
    
    private func getMostActiveAgent() -> String? {
        let agentCounts = Dictionary(grouping: conversations) { conversation in
            if let metadata = conversation.metadata.value as? [String: Any],
               let agentName = metadata["agentName"] as? String {
                return agentName
            }
            return "Unknown"
        }.mapValues { $0.count }

        return agentCounts.max(by: { $0.value < $1.value })?.key
    }
    
    // MARK: - Export/Import
    
    func exportConversation(_ conversation: SupabaseChatConversation) async -> String {
        do {
            let messages = try await supabaseClient.getConversationHistory(
                conversationId: conversation.id
            )
            
            var exportText = "Chat with \(conversation.title ?? "Unknown")\n"
            exportText += "Date: \(conversation.createdAt.formatted())\n\n"
            
            for message in messages {
                let sender = message.senderType == .user ? "You" : "Assistant"
                exportText += "\(sender): \(message.content)\n\n"
            }
            
            return exportText
            
        } catch {
            return "Error exporting conversation: \(error.localizedDescription)"
        }
    }

    func getConversationMessages(conversationId: UUID) async throws -> [SupabaseChatMessage] {
        return try await supabaseClient.getConversationHistory(conversationId: conversationId)
    }
}

// MARK: - Supporting Models

struct ConversationStats {
    let totalConversations: Int
    let totalMessages: Int
    let averageMessagesPerConversation: Double
    let mostActiveAgent: String?
    let lastChatDate: Date?
}
