//
//  TamilRomanizationService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import Foundation

// MARK: - Tamil Romanization Service

@MainActor
class TamilRomanizationService: ObservableObject {
    static let shared = TamilRomanizationService()
    
    @Published var isProcessing = false
    @Published var processingProgress = 0.0
    @Published var statusMessage = ""
    
    // Tamil Unicode ranges
    private let tamilVowelRange = 0x0B85...0x0B94
    private let tamilConsonantRange = 0x0B95...0x0BB9
    private let tamilDependentVowelRange = 0x0BBE...0x0BCD
    private let tamilNumberRange = 0x0BE6...0x0BEF
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// Convert Tamil text to romanized form
    func romanize(_ tamilText: String) -> String {
        guard !tamilText.isEmpty else { return "" }
        
        var romanized = ""
        var i = tamilText.startIndex
        
        while i < tamilText.endIndex {
            let char = tamilText[i]
            let unicode = char.unicodeScalars.first?.value ?? 0
            
            // Check if it's a Tamil character
            if isTamilCharacter(unicode) {
                let romanizedChar = romanizeCharacter(char, in: tamilText, at: i)
                romanized += romanizedChar
            } else {
                // Keep non-Tamil characters as is (spaces, punctuation, etc.)
                romanized += String(char)
            }
            
            i = tamilText.index(after: i)
        }
        
        return cleanupRomanization(romanized)
    }
    
    /// Batch romanize multiple texts
    func batchRomanize(_ texts: [String]) async -> [String] {
        isProcessing = true
        processingProgress = 0.0
        statusMessage = "Romanizing \(texts.count) texts..."
        
        var results: [String] = []
        
        for (index, text) in texts.enumerated() {
            let romanized = romanize(text)
            results.append(romanized)
            
            processingProgress = Double(index + 1) / Double(texts.count)
            statusMessage = "Romanized \(index + 1) of \(texts.count) texts"
            
            // Small delay to allow UI updates
            if index % 10 == 0 {
                try? await Task.sleep(nanoseconds: 10_000_000) // 10ms
            }
        }
        
        statusMessage = "Romanization completed!"
        isProcessing = false
        
        return results
    }
    
    /// Update all explore content with romanization
    func updateAllExploreContentRomanization() async {
        isProcessing = true
        statusMessage = "Updating romanization for all explore content..."
        
        do {
            // Update literature content
            await updateLiteratureRomanization()
            
            // Update calendar concepts
            await updateCalendarConceptsRomanization()
            
            // Update festivals
            await updateFestivalsRomanization()
            
            // Update cultural locations
            await updateLocationsRomanization()
            
            statusMessage = "All romanization updates completed!"
            
        } catch {
            statusMessage = "Romanization update failed: \(error.localizedDescription)"
            print("❌ Romanization update error: \(error)")
        }
        
        isProcessing = false
    }
    
    // MARK: - Private Methods
    
    private func isTamilCharacter(_ unicode: UInt32) -> Bool {
        return tamilVowelRange.contains(Int(unicode)) ||
               tamilConsonantRange.contains(Int(unicode)) ||
               tamilDependentVowelRange.contains(Int(unicode)) ||
               tamilNumberRange.contains(Int(unicode))
    }
    
    private func romanizeCharacter(_ char: Character, in text: String, at index: String.Index) -> String {
        let unicode = char.unicodeScalars.first?.value ?? 0
        
        // Tamil vowels (independent)
        switch unicode {
        case 0x0B85: return "a"      // அ
        case 0x0B86: return "aa"     // ஆ
        case 0x0B87: return "i"      // இ
        case 0x0B88: return "ii"     // ஈ
        case 0x0B89: return "u"      // உ
        case 0x0B8A: return "uu"     // ஊ
        case 0x0B8E: return "e"      // எ
        case 0x0B8F: return "ee"     // ஏ
        case 0x0B90: return "ai"     // ஐ
        case 0x0B92: return "o"      // ஒ
        case 0x0B93: return "oo"     // ஓ
        case 0x0B94: return "au"     // ஔ
        default: break
        }
        
        // Tamil consonants
        switch unicode {
        case 0x0B95: return "ka"     // க
        case 0x0B99: return "nga"    // ங
        case 0x0B9A: return "cha"    // ச
        case 0x0B9E: return "nya"    // ஞ
        case 0x0B9F: return "ta"     // ட
        case 0x0BA3: return "na"     // ண
        case 0x0BA4: return "tha"    // த
        case 0x0BA8: return "na"     // ந
        case 0x0BAA: return "pa"     // ப
        case 0x0BAE: return "ma"     // ம
        case 0x0BAF: return "ya"     // ய
        case 0x0BB0: return "ra"     // ர
        case 0x0BB2: return "la"     // ல
        case 0x0BB5: return "va"     // வ
        case 0x0BB4: return "zha"    // ழ
        case 0x0BB3: return "la"     // ள
        case 0x0BB1: return "ra"     // ற
        case 0x0BA9: return "na"     // ன
        case 0x0B9C: return "ja"     // ஜ
        case 0x0BB7: return "sha"    // ஷ
        case 0x0BB8: return "sa"     // ஸ
        case 0x0BB9: return "ha"     // ஹ
        // Combined characters handled separately
        default: break
        }
        
        // Tamil dependent vowel signs
        switch unicode {
        case 0x0BBE: return "aa"     // ா
        case 0x0BBF: return "i"      // ி
        case 0x0BC0: return "ii"     // ீ
        case 0x0BC1: return "u"      // ு
        case 0x0BC2: return "uu"     // ூ
        case 0x0BC6: return "e"      // ெ
        case 0x0BC7: return "ee"     // ே
        case 0x0BC8: return "ai"     // ை
        case 0x0BCA: return "o"      // ொ
        case 0x0BCB: return "oo"     // ோ
        case 0x0BCC: return "au"     // ௌ
        case 0x0BCD: return ""       // ் (virama - removes inherent vowel)
        default: break
        }
        
        // Tamil numbers
        if tamilNumberRange.contains(Int(unicode)) {
            let digit = Int(unicode) - 0x0BE6
            return String(digit)
        }
        
        // If no mapping found, return the original character
        return String(char)
    }
    
    private func cleanupRomanization(_ text: String) -> String {
        var cleaned = text
        
        // Remove extra spaces
        cleaned = cleaned.replacingOccurrences(of: "  +", with: " ", options: .regularExpression)
        
        // Trim whitespace
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Handle common combinations
        cleaned = cleaned.replacingOccurrences(of: "nka", with: "nga")
        cleaned = cleaned.replacingOccurrences(of: "ncha", with: "ncha")
        cleaned = cleaned.replacingOccurrences(of: "nta", with: "nda")
        cleaned = cleaned.replacingOccurrences(of: "npa", with: "mpa")
        
        return cleaned
    }
    
    // MARK: - Content Update Methods
    
    private func updateLiteratureRomanization() async {
        statusMessage = "Updating literature romanization..."
        
        let literatureService = LiteratureService.shared
        await literatureService.loadContent()
        
        for content in literatureService.allContent {
            let romanizedTitle = romanize(content.titleTamil)
            let romanizedContent = romanize(content.contentTamil)
            
            // Update in database
            let _ = try? await NIRASupabaseClient.shared.client.from("literature_content")
                .update([
                    "romanization": romanizedTitle,
                    "content_romanization": romanizedContent
                ])
                .eq("id", value: content.id.uuidString)
                .execute()
        }
    }
    
    private func updateCalendarConceptsRomanization() async {
        statusMessage = "Updating calendar concepts romanization..."
        
        let calendarService = EnhancedCalendarService.shared
        await calendarService.loadContent()
        
        for concept in calendarService.calendarConcepts {
            let romanized = romanize(concept.conceptNameTamil)
            
            // Update in database
            let _ = try? await NIRASupabaseClient.shared.client.from("calendar_concepts")
                .update(["romanization": romanized])
                .eq("id", value: concept.id.uuidString)
                .execute()
        }
    }
    
    private func updateFestivalsRomanization() async {
        statusMessage = "Updating festivals romanization..."
        
        let calendarService = EnhancedCalendarService.shared
        await calendarService.loadContent()
        
        for festival in calendarService.allFestivals {
            let romanized = romanize(festival.nameTamil)
            
            // Update in database
            let _ = try? await NIRASupabaseClient.shared.client.from("tamil_festivals")
                .update(["romanization": romanized])
                .eq("id", value: festival.id.uuidString)
                .execute()
        }
    }
    
    private func updateLocationsRomanization() async {
        statusMessage = "Updating locations romanization..."
        
        let mapService = CulturalMapService.shared
        await mapService.loadLocations()
        
        for location in mapService.allLocations {
            let romanized = romanize(location.nameTamil)
            
            // Update in database
            let _ = try? await NIRASupabaseClient.shared.client.from("cultural_locations")
                .update(["romanization": romanized])
                .eq("id", value: location.id.uuidString)
                .execute()
        }
    }
}

// MARK: - Romanization Extensions

extension String {
    /// Check if string contains Tamil characters
    var containsTamil: Bool {
        return self.unicodeScalars.contains { scalar in
            let value = scalar.value
            return (0x0B85...0x0BCD).contains(Int(value))
        }
    }
}
