//
//  TamilCharacterRecognizer.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Vision
import UIKit
import CoreML

@MainActor
class TamilCharacterRecognizer: ObservableObject {
    
    @Published var isProcessing = false
    @Published var lastRecognitionResults: [CharacterRecognitionResult] = []
    
    private let scriptService = TamilScriptService.shared
    
    // MARK: - Character Recognition
    
    /// Recognize Tamil characters in the given image
    func recognizeCharacter(in image: UIImage) async throws -> [CharacterRecognitionResult] {
        isProcessing = true
        defer { isProcessing = false }
        
        guard let cgImage = image.cgImage else {
            throw RecognitionError.invalidImage
        }
        
        // Use Vision framework for text recognition
        let results = try await performVisionRecognition(cgImage: cgImage)
        
        // Enhance results with Tamil-specific processing
        let enhancedResults = await enhanceWithTamilProcessing(results, image: image)
        
        lastRecognitionResults = enhancedResults
        return enhancedResults
    }
    
    // MARK: - Vision Framework Recognition
    
    private func performVisionRecognition(cgImage: CGImage) async throws -> [VNRecognizedTextObservation] {
        return try await withCheckedThrowingContinuation { continuation in
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    continuation.resume(returning: [])
                    return
                }
                
                continuation.resume(returning: observations)
            }
            
            // Configure for Tamil text recognition
            request.recognitionLanguages = ["ta"] // Tamil
            request.recognitionLevel = .accurate
            request.usesLanguageCorrection = true
            request.customWords = getTamilCustomWords()
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }
    
    // MARK: - Tamil-Specific Processing
    
    private func enhanceWithTamilProcessing(_ observations: [VNRecognizedTextObservation], 
                                          image: UIImage) async -> [CharacterRecognitionResult] {
        var results: [CharacterRecognitionResult] = []
        
        for observation in observations {
            guard let topCandidate = observation.topCandidates(1).first else { continue }
            
            let recognizedText = topCandidate.string
            let confidence = topCandidate.confidence
            let boundingBox = observation.boundingBox
            
            // Convert normalized coordinates to image coordinates
            let imageBounds = CGRect(
                x: boundingBox.origin.x * image.size.width,
                y: (1 - boundingBox.origin.y - boundingBox.height) * image.size.height,
                width: boundingBox.width * image.size.width,
                height: boundingBox.height * image.size.height
            )
            
            // Process each character in the recognized text
            for character in recognizedText {
                let characterString = String(character)
                
                // Validate if it's a valid Tamil character
                if isTamilCharacter(characterString) {
                    let alternativeMatches = await findAlternativeMatches(for: characterString, in: image)
                    
                    let result = CharacterRecognitionResult(
                        recognizedCharacter: characterString,
                        confidence: Double(confidence),
                        boundingBox: imageBounds,
                        alternativeMatches: alternativeMatches
                    )
                    
                    results.append(result)
                }
            }
        }
        
        // Sort by confidence
        return results.sorted { $0.confidence > $1.confidence }
    }
    
    // MARK: - Tamil Character Validation
    
    private func isTamilCharacter(_ character: String) -> Bool {
        // Check if character is in Tamil Unicode range
        guard let scalar = character.unicodeScalars.first else { return false }
        
        let tamilRange = 0x0B80...0x0BFF // Tamil Unicode block
        return tamilRange.contains(Int(scalar.value))
    }
    
    // MARK: - Alternative Matches
    
    private func findAlternativeMatches(for character: String, in image: UIImage) async -> [String] {
        // Use shape similarity to find alternative matches
        let allTamilCharacters = await scriptService.allCharacters
        
        var similarities: [(character: String, similarity: Double)] = []
        
        for tamilChar in allTamilCharacters {
            let similarity = calculateShapeSimilarity(character, tamilChar.character)
            if similarity > 0.3 { // Threshold for considering as alternative
                similarities.append((character: tamilChar.character, similarity: similarity))
            }
        }
        
        // Sort by similarity and return top matches
        return similarities
            .sorted { $0.similarity > $1.similarity }
            .prefix(3)
            .map { $0.character }
    }
    
    // MARK: - Shape Similarity Analysis
    
    private func calculateShapeSimilarity(_ char1: String, _ char2: String) -> Double {
        // This is a simplified similarity calculation
        // In practice, you'd use more sophisticated shape analysis
        
        if char1 == char2 {
            return 1.0
        }
        
        // Check for similar character categories
        let char1Type = getCharacterType(char1)
        let char2Type = getCharacterType(char2)
        
        if char1Type == char2Type {
            return 0.6 // Same type characters have some similarity
        }
        
        // Check for visual similarity patterns
        let visualSimilarity = calculateVisualSimilarity(char1, char2)
        
        return visualSimilarity
    }
    
    private func getCharacterType(_ character: String) -> TamilCharacter.CharacterType {
        // Determine character type based on Unicode ranges
        guard let scalar = character.unicodeScalars.first else { return .special }
        
        let value = Int(scalar.value)
        
        switch value {
        case 0x0B85...0x0B94: // Tamil vowels
            return .vowel
        case 0x0B95...0x0BB9: // Tamil consonants
            return .consonant
        case 0x0B83: // Aytham
            return .special
        default:
            return .combined
        }
    }
    
    private func calculateVisualSimilarity(_ char1: String, _ char2: String) -> Double {
        // Visual similarity patterns for Tamil characters
        let similarityMap: [String: [String: Double]] = [
            "அ": ["ஆ": 0.8, "இ": 0.3],
            "ஆ": ["அ": 0.8, "ஓ": 0.4],
            "இ": ["ஈ": 0.9, "அ": 0.3],
            "ஈ": ["இ": 0.9, "ஏ": 0.4],
            "க": ["ச": 0.5, "ப": 0.3],
            "ம": ["ய": 0.4, "வ": 0.3]
        ]
        
        return similarityMap[char1]?[char2] ?? 0.1
    }
    
    // MARK: - Custom Words for Recognition
    
    private func getTamilCustomWords() -> [String] {
        // Return common Tamil words to improve recognition accuracy
        return [
            "அம்மா", "அப்பா", "வணக்கம்", "நன்றி", "வாங்க",
            "போங்க", "இருங்க", "வாங்க", "கொடுங்க", "சொல்லுங்க"
        ]
    }
    
    // MARK: - Confidence Adjustment
    
    /// Adjust confidence based on Tamil-specific factors
    private func adjustConfidenceForTamil(_ baseConfidence: Double, 
                                        character: String, 
                                        context: RecognitionContext) -> Double {
        var adjustedConfidence = baseConfidence
        
        // Boost confidence for common characters
        if isCommonTamilCharacter(character) {
            adjustedConfidence *= 1.1
        }
        
        // Reduce confidence for complex characters if stroke count doesn't match
        if isComplexCharacter(character) && context.strokeCount < getExpectedStrokeCount(character) {
            adjustedConfidence *= 0.8
        }
        
        // Boost confidence if character fits the lesson context
        if context.lessonCharacters.contains(character) {
            adjustedConfidence *= 1.2
        }
        
        return min(1.0, adjustedConfidence)
    }
    
    private func isCommonTamilCharacter(_ character: String) -> Bool {
        let commonChars = ["அ", "ஆ", "இ", "ஈ", "உ", "க", "ம", "ன", "த", "ர"]
        return commonChars.contains(character)
    }
    
    private func isComplexCharacter(_ character: String) -> Bool {
        let complexChars = ["ஞ", "ழ", "ற", "ள"]
        return complexChars.contains(character)
    }
    
    private func getExpectedStrokeCount(_ character: String) -> Int {
        // Return expected stroke count for character
        // This would be looked up from the database in practice
        let strokeCounts: [String: Int] = [
            "அ": 2, "ஆ": 3, "இ": 2, "ஈ": 3,
            "க": 2, "ம": 2, "ன": 3, "த": 2
        ]
        
        return strokeCounts[character] ?? 3
    }
}

// MARK: - Supporting Types

struct RecognitionContext {
    let strokeCount: Int
    let lessonCharacters: [String]
    let writingMode: WritingMode
    let userLevel: CEFRLevel
}

enum RecognitionError: Error {
    case invalidImage
    case recognitionFailed
    case noTamilCharactersFound
    
    var localizedDescription: String {
        switch self {
        case .invalidImage:
            return "Invalid image provided for recognition"
        case .recognitionFailed:
            return "Character recognition failed"
        case .noTamilCharactersFound:
            return "No Tamil characters found in the image"
        }
    }
}

// MARK: - Recognition Quality Assessment

extension TamilCharacterRecognizer {
    
    /// Assess the quality of recognition results
    func assessRecognitionQuality(_ results: [CharacterRecognitionResult]) -> RecognitionQuality {
        guard !results.isEmpty else {
            return RecognitionQuality(
                overall: 0.0,
                confidence: 0.0,
                clarity: 0.0,
                completeness: 0.0,
                suggestions: ["No characters were recognized. Try writing more clearly."]
            )
        }
        
        let averageConfidence = results.map { $0.confidence }.reduce(0, +) / Double(results.count)
        let highConfidenceCount = results.filter { $0.confidence > 0.8 }.count
        let completeness = Double(highConfidenceCount) / Double(results.count)
        
        let clarity = assessWritingClarity(results)
        let overall = (averageConfidence + completeness + clarity) / 3.0
        
        let suggestions = generateQualitySuggestions(
            confidence: averageConfidence,
            completeness: completeness,
            clarity: clarity
        )
        
        return RecognitionQuality(
            overall: overall,
            confidence: averageConfidence,
            clarity: clarity,
            completeness: completeness,
            suggestions: suggestions
        )
    }
    
    private func assessWritingClarity(_ results: [CharacterRecognitionResult]) -> Double {
        // Assess clarity based on confidence distribution
        let confidences = results.map { $0.confidence }
        let variance = calculateVariance(confidences)
        
        // Lower variance indicates more consistent, clearer writing
        return max(0, 1.0 - variance)
    }
    
    private func calculateVariance(_ values: [Double]) -> Double {
        guard !values.isEmpty else { return 0 }
        
        let mean = values.reduce(0, +) / Double(values.count)
        let squaredDifferences = values.map { pow($0 - mean, 2) }
        return squaredDifferences.reduce(0, +) / Double(values.count)
    }
    
    private func generateQualitySuggestions(confidence: Double, completeness: Double, clarity: Double) -> [String] {
        var suggestions: [String] = []
        
        if confidence < 0.7 {
            suggestions.append("Try writing more slowly and carefully")
        }
        
        if completeness < 0.8 {
            suggestions.append("Make sure to complete all strokes of the character")
        }
        
        if clarity < 0.7 {
            suggestions.append("Focus on consistent stroke pressure and spacing")
        }
        
        if suggestions.isEmpty {
            suggestions.append("Great writing! Keep practicing to maintain this quality.")
        }
        
        return suggestions
    }
}

struct RecognitionQuality {
    let overall: Double
    let confidence: Double
    let clarity: Double
    let completeness: Double
    let suggestions: [String]
}
