import Foundation

/// Service for updating vocabulary records with generated audio URLs
/// Uses types from GoogleTTSService: VocabularyAudioResult, VocabularyItem
@MainActor
class AudioDatabaseService: ObservableObject {
    static let shared = AudioDatabaseService()
    
    @Published var isUpdating = false
    @Published var updateProgress = 0.0
    @Published var statusMessage = ""
    @Published var updatedCount = 0
    
    private let supabaseService = SupabaseContentService.shared
    
    private init() {}
    
    // MARK: - Database Updates
    
    /// Update vocabulary records with generated audio URLs
    func updateVocabularyWithAudioURLs(results: [VocabularyAudioResult]) async throws {
        isUpdating = true
        updateProgress = 0.0
        updatedCount = 0
        statusMessage = "Starting database updates..."
        
        let totalItems = results.count
        
        for (index, result) in results.enumerated() {
            do {
                try await updateSingleVocabularyRecord(result: result)
                
                updatedCount += 1
                updateProgress = Double(index + 1) / Double(totalItems)
                statusMessage = "Updated \(index + 1)/\(totalItems) records"
                
                // Small delay to avoid overwhelming the database
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                
            } catch {
                print("❌ Failed to update vocabulary \(result.vocabId): \(error)")
                // Continue with next item
            }
        }
        
        isUpdating = false
        statusMessage = "Database update completed! Updated \(updatedCount)/\(totalItems) records"
    }
    
    /// Update a single vocabulary record with audio URLs
    private func updateSingleVocabularyRecord(result: VocabularyAudioResult) async throws {
        // Find the vocabulary record by vocabId
        let vocabularyItems = await supabaseService.fetchVocabulary(for: "7b8c60af-dd2f-4754-9363-ab09a5bcea95")
        
        guard let vocabulary = vocabularyItems.first(where: { $0.vocabId == result.vocabId }) else {
            throw AudioDatabaseError.vocabularyNotFound(result.vocabId)
        }
        
        // Update the record with new audio URLs
        try await updateVocabularyAudioURLs(
            vocabularyId: vocabulary.id,
            wordAudioURL: result.wordAudioURL,
            sentenceAudioURL: result.sentenceAudioURL
        )
        
        print("✅ Updated vocabulary \(result.vocabId) with audio URLs")
    }
    
    /// Update vocabulary record in Supabase with audio URLs
    private func updateVocabularyAudioURLs(
        vocabularyId: String,
        wordAudioURL: String,
        sentenceAudioURL: String?
    ) async throws {
        // For now, we'll simulate the database update
        // In a real implementation, you would call the Supabase API
        
        print("📝 Updating vocabulary \(vocabularyId):")
        print("   Word Audio: \(wordAudioURL)")
        if let sentenceURL = sentenceAudioURL {
            print("   Sentence Audio: \(sentenceURL)")
        }
        
        // Simulate database update delay
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // TODO: Implement actual Supabase update
        /*
        let updateData: [String: Any] = [
            "audio_word_url": wordAudioURL,
            "audio_sentence_url": sentenceAudioURL ?? NSNull()
        ]
        
        try await supabaseService.updateVocabulary(id: vocabularyId, data: updateData)
        */
    }
    
    // MARK: - Batch Operations
    
    /// Generate and update audio for all Basic Greetings vocabulary
    func generateAndUpdateBasicGreetingsAudio() async throws -> [VocabularyAudioResult] {
        statusMessage = "Loading Basic Greetings vocabulary..."
        
        // Load vocabulary
        let vocabularyItems = await supabaseService.fetchVocabulary(for: "7b8c60af-dd2f-4754-9363-ab09a5bcea95")
        
        // Convert to TTSVocabularyItem format
        let vocabItems = vocabularyItems.map { vocab in
            TTSVocabularyItem(
                vocabId: vocab.vocabId,
                englishWord: vocab.englishWord,
                tamilTranslation: vocab.tamilTranslation,
                exampleSentenceTamil: vocab.exampleSentenceTamil
            )
        }
        
        statusMessage = "Generating audio files..."
        
        // Generate audio
        let ttsService = SimpleTTSService.shared
        let audioResults = try await ttsService.generateLessonAudio(vocabularyItems: vocabItems)
        
        statusMessage = "Updating database..."
        
        // Update database
        try await updateVocabularyWithAudioURLs(results: audioResults)
        
        return audioResults
    }
    
    // MARK: - Verification
    
    /// Verify that audio URLs are accessible
    func verifyAudioURLs(results: [VocabularyAudioResult]) async -> AudioVerificationResult {
        statusMessage = "Verifying audio URLs..."
        
        var successfulWords = 0
        var successfulSentences = 0
        var failedWords = 0
        var failedSentences = 0
        
        for result in results {
            // Verify word audio URL
            if await isURLAccessible(result.wordAudioURL) {
                successfulWords += 1
            } else {
                failedWords += 1
            }
            
            // Verify sentence audio URL (if exists)
            if let sentenceURL = result.sentenceAudioURL {
                if await isURLAccessible(sentenceURL) {
                    successfulSentences += 1
                } else {
                    failedSentences += 1
                }
            }
        }
        
        let verificationResult = AudioVerificationResult(
            totalItems: results.count,
            successfulWords: successfulWords,
            failedWords: failedWords,
            successfulSentences: successfulSentences,
            failedSentences: failedSentences
        )
        
        statusMessage = "Verification completed: \(successfulWords + successfulSentences) successful, \(failedWords + failedSentences) failed"
        
        return verificationResult
    }
    
    /// Check if a URL is accessible
    private func isURLAccessible(_ urlString: String) async -> Bool {
        guard let url = URL(string: urlString) else { return false }
        
        do {
            let (_, response) = try await URLSession.shared.data(from: url)
            return (response as? HTTPURLResponse)?.statusCode == 200
        } catch {
            return false
        }
    }
}

// MARK: - Supporting Types

struct AudioVerificationResult {
    let totalItems: Int
    let successfulWords: Int
    let failedWords: Int
    let successfulSentences: Int
    let failedSentences: Int
    
    var totalSuccessful: Int {
        successfulWords + successfulSentences
    }
    
    var totalFailed: Int {
        failedWords + failedSentences
    }
    
    var successRate: Double {
        let total = totalSuccessful + totalFailed
        return total > 0 ? Double(totalSuccessful) / Double(total) : 0.0
    }
}

enum AudioDatabaseError: LocalizedError {
    case vocabularyNotFound(String)
    case updateFailed(String)
    case verificationFailed
    
    var errorDescription: String? {
        switch self {
        case .vocabularyNotFound(let vocabId):
            return "Vocabulary item not found: \(vocabId)"
        case .updateFailed(let reason):
            return "Database update failed: \(reason)"
        case .verificationFailed:
            return "Audio URL verification failed"
        }
    }
}
