import Foundation
import Combine

// MARK: - Tamil Calendar Models
// Using TamilEvent and EventType from TamilCulturalModels.swift

// MARK: - Tamil Calendar Service

@MainActor
class TamilCalendarService: ObservableObject {
    static let shared = TamilCalendarService()
    
    @Published var currentTamilDate: String = ""
    @Published var currentTamilMonth: String = ""
    @Published var todayEvent: TamilEvent?
    @Published var upcomingEvents: [TamilEvent] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private var cancellables = Set<AnyCancellable>()
    private let calendar = Calendar.current
    
    // Enhanced Tamil events database with comprehensive cultural content
    private let tamilEvents: [TamilEvent] = TamilCalendarService.enhancedTamilEvents
    
    private init() {
        updateCurrentTamilDate()
        setupDailyUpdates()
    }
    
    // MARK: - Public Methods
    
    func loadTodayEvents() async {
        isLoading = true
        
        updateCurrentTamilDate()
        findTodayEvents()
        loadUpcomingEvents()
        
        isLoading = false
    }
    
    func getTamilDateString(for date: Date = Date()) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "ta")
        formatter.dateStyle = .long
        return formatter.string(from: date)
    }
    
    func getEventsForDate(_ date: Date) -> [TamilEvent] {
        let calendar = Calendar.current
        return tamilEvents.filter { event in
            calendar.isDate(event.date, inSameDayAs: date)
        }
    }
    
    func isAuspiciousDay(_ date: Date = Date()) -> Bool {
        let weekday = calendar.component(.weekday, from: date)
        let day = calendar.component(.day, from: date)
        
        let hasSpecialEvent = !getEventsForDate(date).isEmpty
        let isGoodWeekday = ![3, 7].contains(weekday) // Not Tuesday or Saturday
        let isGoodDate = day % 3 != 0
        
        return hasSpecialEvent || (isGoodWeekday && isGoodDate)
    }
    
    func getNakshatra(for date: Date = Date()) -> String {
        let nakshatras = [
            "அஸ்வினி", "பரணி", "கிருத்திகை", "ரோகிணி", "மிருகசீர்ஷம்",
            "ஆர்த்ரா", "புனர்வசு", "புஷ்யம்", "ஆஸ்லேஷா", "மகம்"
        ]
        
        let dayOfYear = calendar.ordinality(of: .day, in: .year, for: date) ?? 1
        let nakshatraIndex = (dayOfYear - 1) % nakshatras.count
        return nakshatras[nakshatraIndex]
    }
    
    func getTithi(for date: Date = Date()) -> String {
        let tithis = [
            "பிரதமை", "துவிதீயை", "திருதீயை", "சதுர்த்தி", "பஞ்சமி",
            "ஷஷ்டி", "சப்தமி", "அஷ்டமி", "நவமி", "தசமி"
        ]
        
        let day = calendar.component(.day, from: date)
        let tithiIndex = (day - 1) % tithis.count
        return tithis[tithiIndex]
    }
    
    func getSeasonForDate(_ date: Date = Date()) -> TamilSeason {
        let month = calendar.component(.month, from: date)

        switch month {
        case 3, 4, 5:
            return .spring
        case 6, 7, 8:
            return .monsoon
        case 9, 10, 11:
            return .autumn
        default:
            return .winter
        }
    }
    
    // MARK: - Private Methods
    
    private func updateCurrentTamilDate() {
        let today = Date()
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "ta")
        formatter.dateFormat = "dd MMMM"
        
        currentTamilDate = formatter.string(from: today)
        
        formatter.dateFormat = "MMMM"
        currentTamilMonth = formatter.string(from: today)
    }
    
    private func findTodayEvents() {
        let today = Date()
        let todayEvents = getEventsForDate(today)
        todayEvent = todayEvents.first
    }
    
    private func loadUpcomingEvents() {
        let today = Date()
        let futureDate = calendar.date(byAdding: .day, value: 7, to: today) ?? today
        
        upcomingEvents = tamilEvents.filter { event in
            event.date >= today && event.date <= futureDate
        }.sorted { $0.date < $1.date }
    }
    
    private func setupDailyUpdates() {
        Timer.publish(every: 3600, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await MainActor.run {
                        let hour = Calendar.current.component(.hour, from: Date())
                        if hour == 0 {
                            self?.updateCurrentTamilDate()
                            Task {
                                await self?.loadTodayEvents()
                            }
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - Supporting Models
// Using TamilSeason from TamilCulturalModels.swift
