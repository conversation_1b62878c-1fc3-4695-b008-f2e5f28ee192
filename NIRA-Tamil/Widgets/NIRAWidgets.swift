//
//  NIRAWidgets.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  iOS 18 Enhanced Interactive Widgets
//

import SwiftUI

// iOS 18 Widgets - will be available when entitlements are enabled
#if canImport(WidgetKit)
import WidgetKit
#endif

// iOS 18 App Intents - will be available when entitlements are enabled
// import AppIntents

// MARK: - Daily Vocabulary Widget

struct DailyVocabularyWidget: Widget {
    let kind: String = "DailyVocabularyWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: DailyVocabularyProvider()
        ) { (entry: DailyVocabularyProvider.Entry) in
            DailyVocabularyWidgetView(entry: entry)
        }
        .configurationDisplayName("Daily Tamil Word")
        .description("Learn a new Tamil word every day")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
        .contentMarginsDisabled()
    }
}

// MARK: - Learning Progress Widget

struct LearningProgressWidget: Widget {
    let kind: String = "LearningProgressWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: ProgressProvider()
        ) { entry in
            LearningProgressWidgetView(entry: entry)
        }
        .configurationDisplayName("Learning Progress")
        .description("Track your Tamil learning journey")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

// MARK: - Quick Practice Widget

struct QuickPracticeWidget: Widget {
    let kind: String = "QuickPracticeWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: QuickPracticeProvider()
        ) { entry in
            QuickPracticeWidgetView(entry: entry)
        }
        .configurationDisplayName("Quick Practice")
        .description("Start a quick Tamil practice session")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

// MARK: - Cultural Insight Widget

struct CulturalInsightWidget: Widget {
    let kind: String = "CulturalInsightWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: CulturalInsightProvider()
        ) { entry in
            CulturalInsightWidgetView(entry: entry)
        }
        .configurationDisplayName("Tamil Culture")
        .description("Discover Tamil cultural insights")
        .supportedFamilies([.systemMedium, .systemLarge])
    }
}

// MARK: - Widget Providers

struct DailyVocabularyProvider: TimelineProvider {
    typealias Entry = DailyVocabularyEntry

    func placeholder(in context: Context) -> DailyVocabularyEntry {
        DailyVocabularyEntry(
            date: Date(),
            word: VocabularyWord(
                tamil: "வணக்கம்",
                english: "Hello",
                pronunciation: "vanakkam",
                category: "Greetings",
                audioURL: nil
            )
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (DailyVocabularyEntry) -> ()) {
        let entry = DailyVocabularyEntry(
            date: Date(),
            word: getDailyWord()
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<DailyVocabularyEntry>) -> ()) {
        let currentDate = Date()
        let word = getDailyWord()

        let entry = DailyVocabularyEntry(
            date: currentDate,
            word: word
        )

        // Update daily at midnight
        let nextUpdate = Calendar.current.startOfDay(for: Calendar.current.date(byAdding: .day, value: 1, to: currentDate)!)

        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }

    private func getDailyWord() -> VocabularyWord {
        let words = [
            VocabularyWord(tamil: "வணக்கம்", english: "Hello", pronunciation: "vanakkam", category: "Greetings", audioURL: nil),
            VocabularyWord(tamil: "நன்றி", english: "Thank you", pronunciation: "nandri", category: "Greetings", audioURL: nil),
            VocabularyWord(tamil: "அம்மா", english: "Mother", pronunciation: "amma", category: "Family", audioURL: nil),
            VocabularyWord(tamil: "அப்பா", english: "Father", pronunciation: "appa", category: "Family", audioURL: nil)
        ]

        let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: Date()) ?? 1
        return words[dayOfYear % words.count]
    }
}

// // struct DailyVocabularyProvider: AppIntentTimelineProvider {
//     func placeholder(in context: Context) -> DailyVocabularyEntry {
//         DailyVocabularyEntry(
//             date: Date(),
//             word: VocabularyWord(
//                 tamil: "வணக்கம்",
//                 english: "Hello",
//                 pronunciation: "vanakkam",
//                 category: "Greetings",
//                 difficulty: .beginner,
//                 audioURL: nil
//             ),
//             configuration: DailyVocabularyConfigurationIntent()
//         )
//     }
//     
//     func snapshot(for configuration: DailyVocabularyConfigurationIntent, in context: Context) async -> DailyVocabularyEntry {
//         return DailyVocabularyEntry(
//             date: Date(),
//             word: await getDailyWord(for: configuration),
//             configuration: configuration
//         )
//     }
//     
//     func timeline(for configuration: DailyVocabularyConfigurationIntent, in context: Context) async -> Timeline<DailyVocabularyEntry> {
//         let currentDate = Date()
//         let word = await getDailyWord(for: configuration)
//         
//         let entry = DailyVocabularyEntry(
//             date: currentDate,
//             word: word,
//             configuration: configuration
//         )
//         
//         // Update daily at midnight
//         let nextUpdate = Calendar.current.startOfDay(for: Calendar.current.date(byAdding: .day, value: 1, to: currentDate)!)
//         
//         return Timeline(entries: [entry], policy: .after(nextUpdate))
//     }
//     
//     private func getDailyWord(for configuration: DailyVocabularyConfigurationIntent) async -> VocabularyWord {
//         let category = configuration.category?.category ?? .daily
//         let difficulty = configuration.difficulty?.level ?? .beginner
//         
//         // Get word based on day of year and configuration
//         let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: Date()) ?? 1
//         
//         let words = getWordsForCategory(category, difficulty: difficulty)
//         return words[dayOfYear % words.count]
//     }
//     
//     private func getWordsForCategory(_ category: VocabCategoryEntity.VocabCategory, difficulty: DifficultyEntity.DifficultyLevel) -> [VocabularyWord] {
//         // Return words based on category and difficulty
//         switch category {
//         case .daily:
//             return [
//                 VocabularyWord(tamil: "வணக்கம்", english: "Hello", pronunciation: "vanakkam", category: "Greetings", difficulty: .beginner, audioURL: nil),
//                 VocabularyWord(tamil: "நன்றி", english: "Thank you", pronunciation: "nandri", category: "Greetings", difficulty: .beginner, audioURL: nil),
//                 VocabularyWord(tamil: "மன்னிக்கவும்", english: "Sorry", pronunciation: "mannikkavum", category: "Greetings", difficulty: .intermediate, audioURL: nil)
//             ]
//         case .family:
//             return [
//                 VocabularyWord(tamil: "அம்மா", english: "Mother", pronunciation: "amma", category: "Family", difficulty: .beginner, audioURL: nil),
//                 VocabularyWord(tamil: "அப்பா", english: "Father", pronunciation: "appa", category: "Family", difficulty: .beginner, audioURL: nil),
//                 VocabularyWord(tamil: "அண்ணன்", english: "Elder brother", pronunciation: "annan", category: "Family", difficulty: .intermediate, audioURL: nil)
//             ]
//         case .food:
//             return [
//                 VocabularyWord(tamil: "சாதம்", english: "Rice", pronunciation: "saatham", category: "Food", difficulty: .beginner, audioURL: nil),
//                 VocabularyWord(tamil: "சாம்பார்", english: "Sambar", pronunciation: "sambar", category: "Food", difficulty: .intermediate, audioURL: nil),
//                 VocabularyWord(tamil: "ரசம்", english: "Rasam", pronunciation: "rasam", category: "Food", difficulty: .intermediate, audioURL: nil)
//             ]
//         default:
//             return [
//                 VocabularyWord(tamil: "தமிழ்", english: "Tamil", pronunciation: "tamizh", category: "Language", difficulty: .beginner, audioURL: nil)
//             ]
//         }
//     }
// }

struct ProgressProvider: TimelineProvider {
    func placeholder(in context: Context) -> ProgressEntry {
        ProgressEntry(
            date: Date(),
            progress: LearningProgress(
                completedLessons: 15,
                totalLessons: 30,
                currentStreak: 7,
                weeklyGoal: 5,
                weeklyProgress: 4,
                lastStudyDate: Date()
            )
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (ProgressEntry) -> ()) {
        let entry = ProgressEntry(
            date: Date(),
            progress: getCurrentProgress()
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<ProgressEntry>) -> ()) {
        let currentDate = Date()
        let progress = getCurrentProgress()
        
        let entry = ProgressEntry(date: currentDate, progress: progress)
        
        // Update every hour
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
    
    private func getCurrentProgress() -> LearningProgress {
        // In a real app, this would fetch from UserDefaults or Core Data
        return LearningProgress(
            completedLessons: 18,
            totalLessons: 30,
            currentStreak: 7,
            weeklyGoal: 5,
            weeklyProgress: 4,
            lastStudyDate: Date()
        )
    }
}

struct QuickPracticeProvider: TimelineProvider {
    func placeholder(in context: Context) -> QuickPracticeEntry {
        QuickPracticeEntry(
            date: Date(),
            practiceType: .vocabulary,
            estimatedDuration: 5,
            availablePractices: 3
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (QuickPracticeEntry) -> ()) {
        let entry = QuickPracticeEntry(
            date: Date(),
            practiceType: .vocabulary,
            estimatedDuration: 5,
            availablePractices: 3
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<QuickPracticeEntry>) -> ()) {
        let currentDate = Date()
        
        let entry = QuickPracticeEntry(
            date: currentDate,
            practiceType: getRecommendedPracticeType(),
            estimatedDuration: 5,
            availablePractices: getAvailablePracticesCount()
        )
        
        // Update every 4 hours
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 4, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
    
    private func getRecommendedPracticeType() -> PracticeType {
        let types: [PracticeType] = [.vocabulary, .pronunciation, .conversation, .grammar]
        let hour = Calendar.current.component(.hour, from: Date())
        return types[hour % types.count]
    }
    
    private func getAvailablePracticesCount() -> Int {
        return Int.random(in: 2...5)
    }
}

struct CulturalInsightProvider: TimelineProvider {
    func placeholder(in context: Context) -> CulturalInsightEntry {
        CulturalInsightEntry(
            date: Date(),
            insight: CulturalInsight(
                title: "Tamil Festivals",
                description: "Pongal is the harvest festival of Tamil Nadu",
                category: "Festivals",
                imageURL: nil,
                funFact: "Pongal means 'to boil over' in Tamil"
            )
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (CulturalInsightEntry) -> ()) {
        let entry = CulturalInsightEntry(
            date: Date(),
            insight: getDailyInsight()
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<CulturalInsightEntry>) -> ()) {
        let currentDate = Date()
        let insight = getDailyInsight()
        
        let entry = CulturalInsightEntry(date: currentDate, insight: insight)
        
        // Update daily
        let nextUpdate = Calendar.current.startOfDay(for: Calendar.current.date(byAdding: .day, value: 1, to: currentDate)!)
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
    
    private func getDailyInsight() -> CulturalInsight {
        let insights = [
            CulturalInsight(
                title: "Tamil Festivals",
                description: "Pongal celebrates the harvest season",
                category: "Festivals",
                imageURL: nil,
                funFact: "Pongal means 'to boil over' in Tamil"
            ),
            CulturalInsight(
                title: "Classical Dance",
                description: "Bharatanatyam originated in Tamil Nadu",
                category: "Arts",
                imageURL: nil,
                funFact: "It's one of the oldest dance forms in India"
            ),
            CulturalInsight(
                title: "Tamil Literature",
                description: "Thirukkural is a classic Tamil text",
                category: "Literature",
                imageURL: nil,
                funFact: "It contains 1330 couplets on ethics"
            )
        ]
        
        let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: Date()) ?? 1
        return insights[dayOfYear % insights.count]
    }
}

// MARK: - Widget Views

struct DailyVocabularyWidgetView: View {
    let entry: DailyVocabularyEntry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallVocabularyView(word: entry.word)
        case .systemMedium:
            MediumVocabularyView(word: entry.word)
        case .systemLarge:
            LargeVocabularyView(word: entry.word)
        default:
            SmallVocabularyView(word: entry.word)
        }
    }
}

struct SmallVocabularyView: View {
    let word: VocabularyWord
    
    var body: some View {
        VStack(spacing: 8) {
            Text("🇮🇳")
                .font(.title)
            
            Text(word.tamil)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .minimumScaleFactor(0.7)
            
            Text(word.english)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(word.pronunciation)
                .font(.caption2)
                .foregroundColor(.blue)
                .italic()
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

struct MediumVocabularyView: View {
    let word: VocabularyWord
    
    var body: some View {
        HStack(spacing: 16) {
            VStack {
                Text("🇮🇳")
                    .font(.largeTitle)
                
                Text(word.category)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(alignment: .leading, spacing: 6) {
                Text(word.tamil)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(word.english)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("[\(word.pronunciation)]")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .italic()
                
                HStack {
                    Text("Beginner")
                        .font(.caption2)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.2))
                        .foregroundColor(.blue)
                        .cornerRadius(4)

                    Spacer()
                }
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

struct LargeVocabularyView: View {
    let word: VocabularyWord
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Daily Tamil Word")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("🇮🇳")
                    .font(.title2)
            }
            
            VStack(spacing: 12) {
                Text(word.tamil)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(word.english)
                    .font(.title3)
                    .foregroundColor(.secondary)
                
                Text("[\(word.pronunciation)]")
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    .italic()
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Category")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text(word.category)
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Level")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("Beginner")
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.2))
                        .foregroundColor(.blue)
                        .cornerRadius(4)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

struct LearningProgressWidgetView: View {
    let entry: ProgressEntry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallProgressView(progress: entry.progress)
        case .systemMedium:
            MediumProgressView(progress: entry.progress)
        case .systemLarge:
            LargeProgressView(progress: entry.progress)
        default:
            SmallProgressView(progress: entry.progress)
        }
    }
}

struct SmallProgressView: View {
    let progress: LearningProgress
    
    var body: some View {
        VStack(spacing: 8) {
            Text("📚")
                .font(.title2)
            
            Text("\(progress.completedLessons)/\(progress.totalLessons)")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Lessons")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(spacing: 4) {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                    .font(.caption)
                
                Text("\(progress.currentStreak)")
                    .font(.caption)
                    .fontWeight(.semibold)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

struct MediumProgressView: View {
    let progress: LearningProgress
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Tamil Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("📚")
                    .font(.title2)
            }
            
            ProgressView(value: Double(progress.completedLessons), total: Double(progress.totalLessons))
                .progressViewStyle(LinearProgressViewStyle(tint: .green))
                .scaleEffect(x: 1, y: 2, anchor: .center)
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("\(progress.completedLessons)/\(progress.totalLessons)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Text("Lessons")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    HStack(spacing: 4) {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                        Text("\(progress.currentStreak)")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                    Text("Day Streak")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

struct LargeProgressView: View {
    let progress: LearningProgress
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Tamil Learning Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("📚")
                    .font(.title2)
            }
            
            VStack(spacing: 8) {
                ProgressView(value: Double(progress.completedLessons), total: Double(progress.totalLessons))
                    .progressViewStyle(LinearProgressViewStyle(tint: .green))
                    .scaleEffect(x: 1, y: 3, anchor: .center)
                
                HStack {
                    Text("\(progress.completedLessons) of \(progress.totalLessons) lessons completed")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(Int(Double(progress.completedLessons) / Double(progress.totalLessons) * 100))%")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
            }
            
            HStack(spacing: 20) {
                VStack(spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.orange)
                        Text("\(progress.currentStreak)")
                            .fontWeight(.semibold)
                    }
                    Text("Day Streak")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                VStack(spacing: 4) {
                    Text("\(progress.weeklyProgress)/\(progress.weeklyGoal)")
                        .fontWeight(.semibold)
                    Text("This Week")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

// MARK: - Widget Entries

struct DailyVocabularyEntry: TimelineEntry {
    let date: Date
    let word: VocabularyWord
//     let configuration: DailyVocabularyConfigurationIntent
}

struct ProgressEntry: TimelineEntry {
    let date: Date
    let progress: LearningProgress
}

struct QuickPracticeEntry: TimelineEntry {
    let date: Date
    let practiceType: PracticeType
    let estimatedDuration: Int
    let availablePractices: Int
}

struct CulturalInsightEntry: TimelineEntry {
    let date: Date
    let insight: CulturalInsight
}

struct QuickPracticeWidgetView: View {
    let entry: QuickPracticeEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: entry.practiceType.icon)
                    .foregroundColor(.blue)

                Text("Quick Practice")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 8) {
                Text(entry.practiceType.displayName)
                    .font(.title2)
                    .fontWeight(.bold)

                Text("\(entry.estimatedDuration) min session")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("\(entry.availablePractices) practices available")
                    .font(.caption2)
                    .foregroundColor(.blue)
            }

            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

struct CulturalInsightWidgetView: View {
    let entry: CulturalInsightEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("🏛️")
                    .font(.title2)

                Text("Tamil Culture")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 8) {
                Text(entry.insight.title)
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(entry.insight.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(family == .systemLarge ? 4 : 2)

                if family == .systemLarge {
                    Text("💡 \(entry.insight.funFact)")
                        .font(.caption2)
                        .foregroundColor(.blue)
                        .italic()
                        .lineLimit(2)
                }
            }

            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

// MARK: - Supporting Models

struct VocabularyWord {
    let tamil: String
    let english: String
    let pronunciation: String
    let category: String
//     let difficulty: DifficultyEntity.DifficultyLevel
    let audioURL: String?
}

// MARK: - Widget-specific Models

struct LearningProgress: Codable {
    let completedLessons: Int
    let totalLessons: Int
    let currentStreak: Int
    let weeklyGoal: Int
    let weeklyProgress: Int
    let lastStudyDate: Date
}

enum PracticeType: String, CaseIterable {
    case vocabulary = "vocabulary"
    case pronunciation = "pronunciation"
    case conversation = "conversation"
    case grammar = "grammar"
    
    var displayName: String {
        return rawValue.capitalized
    }
    
    var icon: String {
        switch self {
        case .vocabulary: return "text.book.closed.fill"
        case .pronunciation: return "mic.fill"
        case .conversation: return "bubble.left.and.bubble.right.fill"
        case .grammar: return "textformat.abc"
        }
    }
}

struct CulturalInsight {
    let title: String
    let description: String
    let category: String
    let imageURL: String?
    let funFact: String
}

// MARK: - Configuration Intent

// // struct DailyVocabularyConfigurationIntent: WidgetConfigurationIntent {
//     static var title: LocalizedStringResource = "Daily Vocabulary Configuration"
//     static var description = IntentDescription("Configure your daily vocabulary widget")
//     
//     @Parameter(title: "Category", description: "Vocabulary category to focus on")
//     var category: VocabCategoryEntity?
//     
//     @Parameter(title: "Difficulty", description: "Difficulty level for vocabulary")
//     var difficulty: DifficultyEntity?
// }

// MARK: - Widget Bundle

// @main
struct NIRAWidgetBundle: WidgetBundle {
    var body: some Widget {
        DailyVocabularyWidget()
        LearningProgressWidget()
        QuickPracticeWidget()
        CulturalInsightWidget()
    }
}
