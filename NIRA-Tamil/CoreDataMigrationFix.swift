//
//  CoreDataMigrationFix.swift
//  NIRA
//
//  Created by NIRA Team on 28/05/2025.
//

import SwiftUI
import SwiftData
import Foundation

// MARK: - Migration Fix for CoreData Array Issues

class CoreDataMigrationFix {

    static func createFixedModelContainer() -> ModelContainer {
        let schema = Schema([
            // Core Local Models (for offline caching)
            User.self,
            Lesson.self,
            Exercise.self,
            Progress.self,

            // Cultural & Learning Models (local only)
            CulturalContext.self,
            PronunciationData.self,
            TonalMarker.self,
            Achievement.self
        ])

        let modelConfiguration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            allowsSave: true,
            cloudKitDatabase: .none
        )

        do {
            let container = try ModelContainer(for: schema, configurations: [modelConfiguration])

            // Perform migration fix if needed
            Task { @MainActor in
                performMigrationFix(container: container)
            }

            return container
        } catch {
            print("❌ ModelContainer creation failed: \(error)")

            // If migration fails, create a fresh container
            return createFreshModelContainer(schema: schema)
        }
    }

    private static func createFreshModelContainer(schema: Schema) -> ModelContainer {
        print("🔄 Creating fresh ModelContainer due to migration issues...")

        // Delete the existing store to start fresh
        deleteExistingStore()

        let modelConfiguration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            allowsSave: true,
            cloudKitDatabase: .none
        )

        do {
            let container = try ModelContainer(for: schema, configurations: [modelConfiguration])
            print("✅ Fresh ModelContainer created successfully")
            return container
        } catch {
            print("❌ Even fresh container creation failed: \(error)")

            // Last resort: in-memory container
            let memoryConfiguration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true
            )

            do {
                let memoryContainer = try ModelContainer(for: schema, configurations: [memoryConfiguration])
                print("⚠️ Using in-memory container as fallback")
                return memoryContainer
            } catch {
                fatalError("Could not create even in-memory ModelContainer: \(error)")
            }
        }
    }

    private static func deleteExistingStore() {
        let fileManager = FileManager.default

        // Get the application support directory
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory,
                                                  in: .userDomainMask).first else {
            print("⚠️ Could not find application support directory")
            return
        }

        let storeURL = appSupportURL.appendingPathComponent("default.store")

        do {
            if fileManager.fileExists(atPath: storeURL.path) {
                try fileManager.removeItem(at: storeURL)
                print("🗑️ Deleted existing CoreData store")
            }

            // Also delete related files
            let shmURL = storeURL.appendingPathExtension("shm")
            let walURL = storeURL.appendingPathExtension("wal")

            if fileManager.fileExists(atPath: shmURL.path) {
                try fileManager.removeItem(at: shmURL)
            }

            if fileManager.fileExists(atPath: walURL.path) {
                try fileManager.removeItem(at: walURL)
            }

        } catch {
            print("⚠️ Error deleting existing store: \(error)")
        }
    }

    @MainActor
    private static func performMigrationFix(container: ModelContainer) {
        let context = container.mainContext

        do {
            // Check if we have any users without createdAt dates
            let userFetchRequest = FetchDescriptor<User>()
            let users = try context.fetch(userFetchRequest)

            var needsSave = false

            for user in users {
                // Fix missing createdAt dates
                if user.createdAt == Date(timeIntervalSince1970: 0) {
                    user.createdAt = user.joinDate
                    needsSave = true
                }

                // Ensure other required dates are set
                if user.joinDate == Date(timeIntervalSince1970: 0) {
                    user.joinDate = Date()
                    needsSave = true
                }

                if user.lastActiveDate == Date(timeIntervalSince1970: 0) {
                    user.lastActiveDate = Date()
                    needsSave = true
                }
            }

            if needsSave {
                try context.save()
                print("✅ Fixed missing dates in User entities")
            }

        } catch {
            print("⚠️ Migration fix failed: \(error)")
        }
    }
}

// MARK: - Array Type Fixes for SwiftData Models

extension Array where Element == String {
    /// Convert Array<String> to a format that SwiftData can handle
    var swiftDataCompatible: String {
        return self.joined(separator: "|||")
    }

    /// Create Array<String> from SwiftData compatible string
    static func fromSwiftDataString(_ string: String) -> [String] {
        return string.isEmpty ? [] : string.components(separatedBy: "|||")
    }
}

extension Array where Element == Int {
    /// Convert Array<Int> to a format that SwiftData can handle
    var swiftDataCompatible: String {
        return self.map { String($0) }.joined(separator: ",")
    }

    /// Create Array<Int> from SwiftData compatible string
    static func fromSwiftDataString(_ string: String) -> [Int] {
        return string.isEmpty ? [] : string.components(separatedBy: ",").compactMap { Int($0) }
    }
}

extension Array where Element == UUID {
    /// Convert Array<UUID> to a format that SwiftData can handle
    var swiftDataCompatible: String {
        return self.map { $0.uuidString }.joined(separator: ",")
    }

    /// Create Array<UUID> from SwiftData compatible string
    static func fromSwiftDataString(_ string: String) -> [UUID] {
        return string.isEmpty ? [] : string.components(separatedBy: ",").compactMap { UUID(uuidString: $0) }
    }
}

// MARK: - Model Property Wrappers for Array Handling

@propertyWrapper
struct SwiftDataStringArray {
    private var storage: String = ""

    var wrappedValue: [String] {
        get {
            return Array<String>.fromSwiftDataString(storage)
        }
        set {
            storage = newValue.swiftDataCompatible
        }
    }

    var projectedValue: String {
        get { storage }
        set { storage = newValue }
    }
}

@propertyWrapper
struct SwiftDataIntArray {
    private var storage: String = ""

    var wrappedValue: [Int] {
        get {
            return Array<Int>.fromSwiftDataString(storage)
        }
        set {
            storage = newValue.swiftDataCompatible
        }
    }

    var projectedValue: String {
        get { storage }
        set { storage = newValue }
    }
}

@propertyWrapper
struct SwiftDataUUIDArray {
    private var storage: String = ""

    var wrappedValue: [UUID] {
        get {
            return Array<UUID>.fromSwiftDataString(storage)
        }
        set {
            storage = newValue.swiftDataCompatible
        }
    }

    var projectedValue: String {
        get { storage }
        set { storage = newValue }
    }
}

// MARK: - Migration Helper Functions

extension ModelContext {
    func safeInsert<T: PersistentModel>(_ model: T) {
        do {
            insert(model)
            try save()
        } catch {
            print("❌ Error inserting model: \(error)")
        }
    }

    func safeSave() {
        do {
            try save()
        } catch {
            print("❌ Error saving context: \(error)")
        }
    }
}

// MARK: - Debug Helpers

extension CoreDataMigrationFix {
    @MainActor
    static func debugModelContainer(_ container: ModelContainer) {
        let context = container.mainContext

        do {
            let userCount = try context.fetch(FetchDescriptor<User>()).count
            let lessonCount = try context.fetch(FetchDescriptor<Lesson>()).count
            let exerciseCount = try context.fetch(FetchDescriptor<Exercise>()).count
            let progressCount = try context.fetch(FetchDescriptor<Progress>()).count

            print("📊 ModelContainer Debug Info:")
            print("   Users: \(userCount)")
            print("   Lessons: \(lessonCount)")
            print("   Exercises: \(exerciseCount)")
            print("   Progress: \(progressCount)")

        } catch {
            print("❌ Error debugging ModelContainer: \(error)")
        }
    }
}
