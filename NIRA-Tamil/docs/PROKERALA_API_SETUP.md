# ProKerala API Setup Guide

## Overview
NIRA-Tamil now uses ProKerala API for real panchang data. ProKerala offers a **FREE tier with 5,000 credits per month** - perfect for development and moderate usage.

## Why ProKerala?
- ✅ **FREE Tier**: 5,000 credits/month at no cost
- ✅ **Real Data**: Authentic astronomical calculations
- ✅ **Comprehensive**: Complete panchang with Tamil support
- ✅ **Reliable**: Professional API with good uptime
- ✅ **No Expiry**: Free plan doesn't expire

## Setup Instructions

### Step 1: Sign Up for Free Account
1. Visit: https://api.prokerala.com/register
2. Create a free account (no credit card required)
3. Verify your email address
4. Login to your dashboard

### Step 2: Create API Application
1. Go to your ProKerala dashboard
2. Navigate to "Applications" section
3. Click "Create New Application"
4. Fill in application details:
   - **Name**: NIRA-Tamil
   - **Description**: Tamil language learning app with panchang calendar
   - **Website**: (optional)
5. Save the application

### Step 3: Get API Credentials
1. In your application dashboard, you'll find:
   - **Client ID**: Your unique application identifier
   - **Client Secret**: Your application secret key
2. Copy these credentials securely

### Step 4: Update NIRA-Tamil Configuration
1. Open `NIRA-Tamil/Services/PanchangAPIClient.swift`
2. Replace the placeholder credentials:

```swift
private let prokeralaCredentials = [
    ("YOUR_CLIENT_ID_HERE", "YOUR_CLIENT_SECRET_HERE"),
    ("BACKUP_CLIENT_ID", "BACKUP_CLIENT_SECRET") // Optional backup
]
```

### Step 5: Implement OAuth2 Flow
ProKerala uses OAuth2 authentication. You need to:

1. **Get Access Token**: Exchange client credentials for access token
2. **Use Bearer Token**: Include access token in API requests
3. **Handle Refresh**: Refresh tokens when they expire

#### Sample OAuth2 Implementation:
```swift
private func getAccessToken() async throws -> String {
    let url = URL(string: "https://api.prokerala.com/v2/oauth/token")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
    
    let credentials = prokeralaCredentials[0]
    let body = "grant_type=client_credentials&client_id=\(credentials.0)&client_secret=\(credentials.1)"
    request.httpBody = body.data(using: .utf8)
    
    let (data, _) = try await URLSession.shared.data(for: request)
    let response = try JSONDecoder().decode(TokenResponse.self, from: data)
    return response.access_token
}

struct TokenResponse: Codable {
    let access_token: String
    let token_type: String
    let expires_in: Int
}
```

## API Endpoints Used

### Panchang Endpoint
- **URL**: `https://api.prokerala.com/v2/astrology/panchang`
- **Method**: POST
- **Authentication**: Bearer Token
- **Credits**: 1 credit per request

### Request Format:
```json
{
    "datetime": "2025-07-09T06:00:00+05:30",
    "coordinates": {
        "latitude": 13.0827,
        "longitude": 80.2707
    },
    "ayanamsa": "lahiri"
}
```

### Response Format:
```json
{
    "data": {
        "tithi": {
            "index": 1,
            "name": "Pratipada",
            "percentage": 45.2,
            "end_time": {"hour": 14, "minute": 30}
        },
        "nakshatra": {
            "index": 1,
            "name": "Ashwini",
            "lord": "Ketu",
            "percentage": 67.8,
            "end_time": {"hour": 16, "minute": 45}
        },
        "yoga": {
            "index": 1,
            "name": "Vishkumbha",
            "percentage": 23.1,
            "end_time": {"hour": 11, "minute": 15}
        },
        "karana": {
            "index": 1,
            "name": "Bava",
            "percentage": 89.4,
            "end_time": {"hour": 8, "minute": 30}
        },
        "sunrise": "06:15:30",
        "sunset": "18:45:20",
        "moonrise": "07:30:15",
        "moonset": "19:20:45"
    }
}
```

## Credit Usage
- **Panchang Request**: 1 credit
- **Monthly Limit**: 5,000 credits (free tier)
- **Daily Requests**: ~166 requests per day
- **Typical Usage**: 30-50 requests per day for calendar

## Error Handling
Common error codes:
- **401**: Invalid credentials
- **403**: Insufficient credits
- **429**: Rate limit exceeded
- **500**: Server error

## Testing
1. Use the demo at: https://api.prokerala.com/demo/panchang.php
2. Test your credentials with a simple API call
3. Verify response format matches expectations

## Support
- **Documentation**: https://api.prokerala.com/docs
- **Support**: https://api.prokerala.com/contact
- **FAQ**: https://api.prokerala.com/faq

## Next Steps
1. Sign up for ProKerala account
2. Get your API credentials
3. Update the configuration in PanchangAPIClient.swift
4. Implement OAuth2 token management
5. Test the integration
6. Deploy with real data!

---

**Note**: The free tier provides 5,000 credits per month, which is sufficient for development and moderate usage. For production apps with high traffic, consider upgrading to a paid plan.
