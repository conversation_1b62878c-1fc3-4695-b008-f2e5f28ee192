#!/usr/bin/env python3
"""
Test script to verify the ProKerala Panchang API is working correctly.
This script tests the API endpoint that our Swift service will use.
"""

import requests
import json
from datetime import datetime, timedelta
import os

# API Configuration
API_BASE_URL = "https://api.prokerala.com/v2/astrology"
CLIENT_ID = "6df0ec16-722b-4acd-a574-bfd546c0c270"
CLIENT_SECRET = "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"

def get_access_token():
    """Get OAuth2 access token from ProKerala"""
    token_url = "https://api.prokerala.com/token"

    data = {
        'grant_type': 'client_credentials',
        'client_id': CLIENT_ID,
        'client_secret': CLIENT_SECRET
    }

    try:
        response = requests.post(token_url, data=data, timeout=30)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get('access_token')
        else:
            print(f"❌ Token request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Token request error: {e}")
        return None

def test_panchang_api():
    """Test the panchang API endpoint"""

    # Get access token first
    print("🔑 Getting access token...")
    access_token = get_access_token()
    if not access_token:
        print("❌ Failed to get access token")
        return False

    print("✅ Access token obtained successfully")

    # Test parameters (Chennai coordinates)
    params = {
        'ayanamsa': 1,  # Lahiri ayanamsa
        'coordinates': '13.0827,80.2707',  # Chennai
        'datetime': datetime.now().strftime('%Y-%m-%dT%H:%M:%S+05:30'),  # IST timezone
        'la': 'en'  # Language
    }

    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    
    try:
        print("🔍 Testing ProKerala Panchang API...")
        print(f"📍 Location: Chennai (13.0827, 80.2707)")
        print(f"📅 Date: {params['datetime']}")
        print()
        
        # Make API request
        response = requests.get(
            f"{API_BASE_URL}/panchang",
            params=params,
            headers=headers,
            timeout=30
        )
        
        print(f"📡 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API call successful!")
            print()
            
            # Print the response structure
            print("📊 Response Structure:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            print()
            
            # Extract key information
            if 'data' in data:
                panchang_data = data['data']

                print("🌙 Panchang Information:")
                print(f"   Day: {panchang_data.get('vaara', 'N/A')}")

                if 'tithi' in panchang_data and panchang_data['tithi']:
                    tithi = panchang_data['tithi'][0]  # Get current tithi
                    print(f"   Tithi: {tithi.get('name', 'N/A')} (ID: {tithi.get('id', 'N/A')})")
                    print(f"   Paksha: {tithi.get('paksha', 'N/A')}")

                if 'nakshatra' in panchang_data and panchang_data['nakshatra']:
                    nakshatra = panchang_data['nakshatra'][0]  # Get current nakshatra
                    print(f"   Nakshatra: {nakshatra.get('name', 'N/A')} (ID: {nakshatra.get('id', 'N/A')})")
                    if 'lord' in nakshatra:
                        lord = nakshatra['lord']
                        print(f"   Lord: {lord.get('name', 'N/A')} ({lord.get('vedic_name', 'N/A')})")

                if 'yoga' in panchang_data and panchang_data['yoga']:
                    yoga = panchang_data['yoga'][0]  # Get current yoga
                    print(f"   Yoga: {yoga.get('name', 'N/A')} (ID: {yoga.get('id', 'N/A')})")

                if 'karana' in panchang_data and panchang_data['karana']:
                    karana = panchang_data['karana'][0]  # Get current karana
                    print(f"   Karana: {karana.get('name', 'N/A')} (ID: {karana.get('id', 'N/A')})")

                # Sun and moon times
                print(f"   Sunrise: {panchang_data.get('sunrise', 'N/A')}")
                print(f"   Sunset: {panchang_data.get('sunset', 'N/A')}")
                print(f"   Moonrise: {panchang_data.get('moonrise', 'N/A')}")
                print(f"   Moonset: {panchang_data.get('moonset', 'N/A')}")
                
                print()
                print("✅ Panchang API test completed successfully!")
                return True
            else:
                print("❌ No 'data' field in response")
                return False
                
        elif response.status_code == 401:
            print("❌ Authentication failed - Invalid API token")
            print("💡 Please check your ProKerala API token")
            return False
            
        elif response.status_code == 429:
            print("❌ Rate limit exceeded")
            print("💡 Please wait before making more requests")
            return False
            
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_multiple_dates():
    """Test API with multiple dates to ensure consistency"""
    print("\n🔄 Testing multiple dates...")

    # Get access token first
    access_token = get_access_token()
    if not access_token:
        print("❌ Failed to get access token for multiple dates test")
        return False

    dates_to_test = [
        datetime.now(),
        datetime.now() + timedelta(days=1),
        datetime.now() + timedelta(days=7),
        datetime(2025, 1, 15)  # Specific date
    ]

    success_count = 0

    for i, test_date in enumerate(dates_to_test, 1):
        print(f"\n📅 Test {i}: {test_date.strftime('%Y-%m-%d')}")

        params = {
            'ayanamsa': 1,
            'coordinates': '13.0827,80.2707',
            'datetime': test_date.strftime('%Y-%m-%dT%H:%M:%S+05:30'),  # IST timezone
            'la': 'en'
        }

        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        try:
            response = requests.get(
                f"{API_BASE_URL}/panchang",
                params=params,
                headers=headers,
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    panchang_data = data['data']
                    tithi_name = panchang_data.get('tithi', {}).get('name', 'N/A')
                    nakshatra_name = panchang_data.get('nakshatra', {}).get('name', 'N/A')
                    print(f"   ✅ Tithi: {tithi_name}, Nakshatra: {nakshatra_name}")
                    success_count += 1
                else:
                    print("   ❌ No data in response")
            else:
                print(f"   ❌ Failed with status {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Results: {success_count}/{len(dates_to_test)} tests passed")
    return success_count == len(dates_to_test)

if __name__ == "__main__":
    print("🚀 ProKerala Panchang API Test Suite")
    print("=" * 50)
    
    # Test basic API functionality
    basic_test_passed = test_panchang_api()
    
    if basic_test_passed:
        # Test multiple dates if basic test passes
        multiple_dates_passed = test_multiple_dates()
        
        if multiple_dates_passed:
            print("\n🎉 All tests passed! The API is working correctly.")
            print("✅ Your Swift service should be able to connect successfully.")
        else:
            print("\n⚠️  Basic test passed but some date tests failed.")
            print("💡 The API is working but may have intermittent issues.")
    else:
        print("\n❌ Basic API test failed.")
        print("💡 Please check your API configuration and network connection.")
    
    print("\n" + "=" * 50)
