#!/usr/bin/env python3
"""
Fix Practice Exercise Content Quality
Replaces poor quality practice exercises with high-quality ones based on real lesson content
"""

import os
import sys
import json
from supabase import create_client, Client
from typing import List, Dict, Any

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDI3NTM5OCwiZXhwIjoyMDY1ODUxMzk4fQ.huEg6PWHSgAaXAtREOLf1aRa3OENbOb437b8lEGyubc"

# Basic Greetings lesson ID
LESSON_ID = "7b8c60af-dd2f-4754-9363-ab09a5bcea95"

def main():
    """Main function to fix practice exercise content"""
    print("🔧 Starting Practice Exercise Content Fix...")
    
    # Initialize Supabase client
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
    
    try:
        # Step 1: Clear existing poor quality exercises
        print("🗑️ Clearing existing practice exercises...")
        clear_existing_exercises(supabase)
        
        # Step 2: Get lesson vocabulary for generating quality exercises
        print("📚 Fetching lesson vocabulary...")
        vocabulary = fetch_vocabulary(supabase)
        
        # Step 3: Generate and insert quality exercises
        print("✨ Generating quality practice exercises...")
        quality_exercises = generate_quality_exercises(vocabulary)
        
        # Step 4: Insert exercises into database
        print("💾 Inserting quality exercises into database...")
        insert_exercises(supabase, quality_exercises)
        
        print("✅ Practice exercise content fix completed successfully!")
        print(f"📊 Generated {len(quality_exercises)} high-quality exercises")
        
    except Exception as e:
        print(f"❌ Error fixing practice exercises: {e}")
        sys.exit(1)

def clear_existing_exercises(supabase: Client):
    """Clear existing poor quality exercises"""
    try:
        # Delete exercise options first (foreign key constraint)
        supabase.table("exercise_options").delete().eq("question_id", "in", 
            f"(SELECT id FROM exercise_questions WHERE exercise_id IN (SELECT id FROM practice_exercises WHERE lesson_id = '{LESSON_ID}'))"
        ).execute()
        
        # Delete exercise questions
        supabase.table("exercise_questions").delete().eq("exercise_id", "in",
            f"(SELECT id FROM practice_exercises WHERE lesson_id = '{LESSON_ID}')"
        ).execute()
        
        # Delete practice exercises
        supabase.table("practice_exercises").delete().eq("lesson_id", LESSON_ID).execute()
        
        print("✅ Cleared existing exercises")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not clear existing exercises: {e}")

def fetch_vocabulary(supabase: Client) -> List[Dict[str, Any]]:
    """Fetch vocabulary for the lesson"""
    try:
        response = supabase.table("vocabulary").select("*").eq("lesson_id", LESSON_ID).execute()
        vocabulary = response.data
        print(f"📖 Found {len(vocabulary)} vocabulary items")
        return vocabulary
        
    except Exception as e:
        print(f"❌ Error fetching vocabulary: {e}")
        return []

def generate_quality_exercises(vocabulary: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Generate high-quality practice exercises"""
    exercises = []
    
    if len(vocabulary) < 4:
        print("⚠️ Not enough vocabulary items to generate quality exercises")
        return exercises
    
    # Exercise 1: Tamil to English Translation
    exercises.append(create_tamil_to_english_exercise(vocabulary, 1))
    
    # Exercise 2: English to Tamil Translation  
    exercises.append(create_english_to_tamil_exercise(vocabulary, 2))
    
    # Exercise 3: Fill in the Blank
    exercises.append(create_fill_blank_exercise(vocabulary, 3))
    
    # Exercise 4: Pronunciation Recognition
    exercises.append(create_pronunciation_exercise(vocabulary, 4))
    
    # Exercise 5: Context Understanding
    exercises.append(create_context_exercise(vocabulary, 5))
    
    # Exercise 6: Appropriate Response
    exercises.append(create_response_exercise(vocabulary, 6))
    
    # Exercise 7: Conversation Completion
    exercises.append(create_conversation_exercise(vocabulary, 7))
    
    # Exercise 8: Grammar True/False
    exercises.append(create_grammar_exercise(vocabulary, 8))
    
    # Exercise 9: Word Order
    exercises.append(create_word_order_exercise(vocabulary, 9))
    
    # Exercise 10: Cultural Context
    exercises.append(create_cultural_exercise(vocabulary, 10))
    
    return exercises

def create_tamil_to_english_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create Tamil to English translation exercise"""
    import random
    
    # Select target vocabulary
    target = random.choice(vocabulary)
    others = [v for v in vocabulary if v['id'] != target['id']]
    distractors = random.sample(others, min(3, len(others)))
    
    # Create options
    options = [target['english_word']] + [d['english_word'] for d in distractors]
    random.shuffle(options)
    correct_index = options.index(target['english_word'])
    
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'Tamil to English',
        'title_tamil': 'தமிழிலிருந்து ஆங்கிலம்',
        'instructions_english': 'Select the correct English meaning of the Tamil word.',
        'instructions_tamil': 'தமிழ் சொல்லின் சரியான ஆங்கில அர்த்தத்தைத் தேர்ந்தெடு.',
        'difficulty_level': 1,
        'points_value': 10,
        'time_limit_seconds': 30,
        'question': {
            'question_text_english': f"What is the English meaning of '{target['tamil_translation']}'?",
            'question_text_tamil': f"'{target['tamil_translation']}' என்பதன் ஆங்கில அர்த்தம் என்ன?",
            'correct_answer': target['english_word'],
            'options': options,
            'correct_index': correct_index,
            'romanization': target.get('romanization', ''),
            'audio_url': target.get('audio_word_url', '')
        }
    }

def create_english_to_tamil_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create English to Tamil translation exercise"""
    import random
    
    target = random.choice(vocabulary)
    others = [v for v in vocabulary if v['id'] != target['id']]
    distractors = random.sample(others, min(3, len(others)))
    
    options = [target['tamil_translation']] + [d['tamil_translation'] for d in distractors]
    random.shuffle(options)
    correct_index = options.index(target['tamil_translation'])
    
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'English to Tamil',
        'title_tamil': 'ஆங்கிலத்திலிருந்து தமிழ்',
        'instructions_english': 'Select the correct Tamil translation of the English word.',
        'instructions_tamil': 'ஆங்கில சொல்லின் சரியான தமிழ் மொழிபெயர்ப்பைத் தேர்ந்தெடு.',
        'difficulty_level': 1,
        'points_value': 10,
        'time_limit_seconds': 30,
        'question': {
            'question_text_english': f"How do you say '{target['english_word']}' in Tamil?",
            'question_text_tamil': f"'{target['english_word']}' என்பதை தமிழில் எப்படி சொல்வது?",
            'correct_answer': target['tamil_translation'],
            'options': options,
            'correct_index': correct_index,
            'romanization': target.get('romanization', ''),
            'audio_url': target.get('audio_word_url', '')
        }
    }

def create_fill_blank_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create fill in the blank exercise"""
    import random
    
    # Find vocabulary with example sentences
    vocab_with_examples = [v for v in vocabulary if v.get('example_sentence_tamil')]
    if not vocab_with_examples:
        # Fallback to basic sentence
        target = random.choice(vocabulary)
        sentence = f"நான் {target['tamil_translation']} பேசுகிறேன்"
        sentence_with_blank = f"நான் ______ பேசுகிறேன்"
    else:
        target = random.choice(vocab_with_examples)
        sentence = target['example_sentence_tamil']
        sentence_with_blank = sentence.replace(target['tamil_translation'], '______')
    
    others = [v for v in vocabulary if v['id'] != target['id']]
    distractors = random.sample(others, min(3, len(others)))
    
    options = [target['tamil_translation']] + [d['tamil_translation'] for d in distractors]
    random.shuffle(options)
    correct_index = options.index(target['tamil_translation'])
    
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'fill_blank',
        'title_english': 'Complete the Sentence',
        'title_tamil': 'வாக்கியத்தை நிறைவு செய்',
        'instructions_english': 'Fill in the blank with the correct Tamil word.',
        'instructions_tamil': 'வெற்று இடத்தை சரியான தமிழ் சொல்லால் நிரப்பவும்.',
        'difficulty_level': 2,
        'points_value': 15,
        'time_limit_seconds': 45,
        'question': {
            'question_text_english': sentence_with_blank,
            'question_text_tamil': f"வெற்று இடத்தை நிரப்பவும்: {sentence_with_blank}",
            'correct_answer': target['tamil_translation'],
            'options': options,
            'correct_index': correct_index,
            'romanization': target.get('romanization', ''),
            'audio_url': target.get('audio_sentence_url', '')
        }
    }

def create_pronunciation_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create pronunciation recognition exercise"""
    import random
    
    target = random.choice(vocabulary)
    others = [v for v in vocabulary if v['id'] != target['id']]
    distractors = random.sample(others, min(3, len(others)))
    
    options = [target['tamil_translation']] + [d['tamil_translation'] for d in distractors]
    random.shuffle(options)
    correct_index = options.index(target['tamil_translation'])
    
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'Pronunciation Recognition',
        'title_tamil': 'உச்சரிப்பு அடையாளம்',
        'instructions_english': 'Listen to the pronunciation and select the correct Tamil word.',
        'instructions_tamil': 'உச்சரிப்பைக் கேட்டு சரியான தமிழ் சொல்லைத் தேர்ந்தெடு.',
        'difficulty_level': 2,
        'points_value': 15,
        'time_limit_seconds': 30,
        'question': {
            'question_text_english': f"Which Tamil word is pronounced as '{target.get('romanization', target['tamil_translation'])}'?",
            'question_text_tamil': f"'{target.get('romanization', target['tamil_translation'])}' என்று உச்சரிக்கப்படும் தமிழ் சொல் எது?",
            'correct_answer': target['tamil_translation'],
            'options': options,
            'correct_index': correct_index,
            'romanization': target.get('romanization', ''),
            'audio_url': target.get('audio_word_url', '')
        }
    }

def create_context_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create context understanding exercise"""
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'Context Understanding',
        'title_tamil': 'சூழல் புரிதல்',
        'instructions_english': 'Choose the most appropriate greeting for this situation.',
        'instructions_tamil': 'இந்த சூழ்நிலைக்கு மிகவும் பொருத்தமான வாழ்த்தைத் தேர்ந்தெடு.',
        'difficulty_level': 2,
        'points_value': 15,
        'time_limit_seconds': 30,
        'question': {
            'question_text_english': 'What greeting would you use when meeting someone in the evening?',
            'question_text_tamil': 'மாலை நேரத்தில் யாரையாவது சந்திக்கும்போது எந்த வாழ்த்தைப் பயன்படுத்துவீர்கள்?',
            'correct_answer': 'மாலை வணக்கம்',
            'options': ['மாலை வணக்கம்', 'காலை வணக்கம்', 'நல்ல இரவு', 'நன்றி'],
            'correct_index': 0,
            'romanization': 'Maalai Vanakkam',
            'audio_url': ''
        }
    }

def create_response_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create appropriate response exercise"""
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'Appropriate Response',
        'title_tamil': 'சரியான பதில்',
        'instructions_english': 'Choose the most appropriate response.',
        'instructions_tamil': 'மிகவும் பொருத்தமான பதிலைத் தேர்ந்தெடு.',
        'difficulty_level': 2,
        'points_value': 15,
        'time_limit_seconds': 30,
        'question': {
            'question_text_english': "What is the appropriate response to 'வணக்கம்' (Hello)?",
            'question_text_tamil': "'வணக்கம்' என்பதற்கு சரியான பதில் என்ன?",
            'correct_answer': 'வணக்கம்',
            'options': ['வணக்கம்', 'நன்றி', 'மன்னிக்கவும்', 'போய் வாருங்கள்'],
            'correct_index': 0,
            'romanization': 'Vanakkam',
            'audio_url': ''
        }
    }

def create_conversation_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create conversation completion exercise"""
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'Complete the Conversation',
        'title_tamil': 'உரையாடலை நிறைவு செய்',
        'instructions_english': 'Choose the best way to continue this conversation.',
        'instructions_tamil': 'இந்த உரையாடலைத் தொடர சிறந்த வழியைத் தேர்ந்தெடு.',
        'difficulty_level': 3,
        'points_value': 20,
        'time_limit_seconds': 45,
        'question': {
            'question_text_english': 'Complete the conversation: A: வணக்கம்! B: ______',
            'question_text_tamil': 'உரையாடலை நிறைவு செய்யவும்: அ: வணக்கம்! ஆ: ______',
            'correct_answer': 'வணக்கம்! எப்படி இருக்கிறீர்கள்?',
            'options': ['வணக்கம்! எப்படி இருக்கிறீர்கள்?', 'நன்றி, போகிறேன்', 'தெரியாது', 'பார்க்கிறேன்'],
            'correct_index': 0,
            'romanization': 'Vanakkam! Eppadi irukkireergal?',
            'audio_url': ''
        }
    }

def create_grammar_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create grammar true/false exercise"""
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'true_false',
        'title_english': 'Grammar True/False',
        'title_tamil': 'இலக்கணம் உண்மை/பொய்',
        'instructions_english': 'Determine if the grammar statement is true or false.',
        'instructions_tamil': 'இலக்கண கூற்று உண்மையா பொய்யா என்பதைத் தீர்மானிக்கவும்.',
        'difficulty_level': 2,
        'points_value': 15,
        'time_limit_seconds': 30,
        'question': {
            'question_text_english': 'True or False: In Tamil, verbs typically come at the end of the sentence.',
            'question_text_tamil': 'உண்மை அல்லது பொய்: தமிழில், வினைச்சொற்கள் பொதுவாக வாக்கியத்தின் இறுதியில் வரும்.',
            'correct_answer': 'True',
            'options': ['True', 'False'],
            'correct_index': 0,
            'romanization': '',
            'audio_url': ''
        }
    }

def create_word_order_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create word order exercise"""
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'Word Order',
        'title_tamil': 'சொல் வரிசை',
        'instructions_english': 'Choose the sentence with correct Tamil word order.',
        'instructions_tamil': 'சரியான தமிழ் சொல் வரிசையுடன் கூடிய வாக்கியத்தைத் தேர்ந்தெடு.',
        'difficulty_level': 3,
        'points_value': 20,
        'time_limit_seconds': 45,
        'question': {
            'question_text_english': 'Which sentence has the correct Tamil word order for "I speak Tamil"?',
            'question_text_tamil': '"நான் தமிழ் பேசுகிறேன்" என்பதற்கு சரியான தமிழ் சொல் வரிசை எது?',
            'correct_answer': 'நான் தமிழ் பேசுகிறேன்',
            'options': ['நான் தமிழ் பேசுகிறேன்', 'தமிழ் நான் பேசுகிறேன்', 'பேசுகிறேன் நான் தமிழ்', 'தமிழ் பேசுகிறேன் நான்'],
            'correct_index': 0,
            'romanization': 'Naan Tamil pesugireen',
            'audio_url': ''
        }
    }

def create_cultural_exercise(vocabulary: List[Dict[str, Any]], exercise_num: int) -> Dict[str, Any]:
    """Create cultural context exercise"""
    return {
        'exercise_id': f"L1E{exercise_num:02d}",
        'exercise_type': 'multiple_choice',
        'title_english': 'Cultural Context',
        'title_tamil': 'கலாச்சார சூழல்',
        'instructions_english': 'Choose the culturally appropriate behavior.',
        'instructions_tamil': 'கலாச்சார ரீதியாக பொருத்தமான நடத்தையைத் தேர்ந்தெடு.',
        'difficulty_level': 2,
        'points_value': 15,
        'time_limit_seconds': 30,
        'question': {
            'question_text_english': 'What is the traditional Tamil greeting gesture?',
            'question_text_tamil': 'பாரம்பரிய தமிழ் வாழ்த்து சைகை என்ன?',
            'correct_answer': 'Joining palms together (Namaste)',
            'options': ['Joining palms together (Namaste)', 'Handshake', 'Bow', 'Wave'],
            'correct_index': 0,
            'romanization': 'Vanakkam',
            'audio_url': ''
        }
    }

def insert_exercises(supabase: Client, exercises: List[Dict[str, Any]]):
    """Insert exercises into database"""
    for exercise in exercises:
        try:
            # Insert practice exercise
            exercise_data = {
                'lesson_id': LESSON_ID,
                'exercise_id': exercise['exercise_id'],
                'exercise_type': exercise['exercise_type'],
                'title_english': exercise['title_english'],
                'title_tamil': exercise['title_tamil'],
                'instructions_english': exercise['instructions_english'],
                'instructions_tamil': exercise['instructions_tamil'],
                'difficulty_level': exercise['difficulty_level'],
                'points_value': exercise['points_value'],
                'time_limit_seconds': exercise['time_limit_seconds']
            }

            exercise_response = supabase.table("practice_exercises").insert(exercise_data).execute()
            exercise_id = exercise_response.data[0]['id']

            # Insert question
            question_data = {
                'exercise_id': exercise_id,
                'question_text_english': exercise['question']['question_text_english'],
                'question_text_tamil': exercise['question']['question_text_tamil'],
                'correct_answer': exercise['question']['correct_answer'],
                'question_audio_url': exercise['question'].get('audio_url', '')
            }

            question_response = supabase.table("exercise_questions").insert(question_data).execute()
            question_id = question_response.data[0]['id']

            # Insert options
            for i, option in enumerate(exercise['question']['options']):
                option_data = {
                    'question_id': question_id,
                    'option_text': option,
                    'is_correct': i == exercise['question']['correct_index'],
                    'option_order': i + 1
                }
                supabase.table("exercise_options").insert(option_data).execute()

            print(f"✅ Inserted exercise: {exercise['exercise_id']}")

        except Exception as e:
            print(f"❌ Error inserting exercise {exercise['exercise_id']}: {e}")

if __name__ == "__main__":
    main()
