#!/usr/bin/env python3
"""
Test Audio Generation Script
Simple test to verify Google TTS setup and generate sample audio
"""

import os
import json
from pathlib import Path
from google.cloud import texttospeech
from google.oauth2 import service_account

# Configuration
TAMIL_FEMALE_VOICE = "ta-IN-Chirp3-HD-Erinome"
TAMIL_MALE_VOICE = "ta-IN-Chirp3-HD-Iapetus"
LANGUAGE_CODE = "ta-IN"
AUDIO_FORMAT = texttospeech.AudioEncoding.MP3
SPEAKING_RATE = 0.9
PITCH = 0.0

# Paths
KEYS_DIR = Path("/Users/<USER>/Documents/NIRA-Tamil/googlettskeys")
OUTPUT_DIR = Path("/Users/<USER>/Documents/NIRA-Tamil/test_audio")

def load_service_account():
    """Load the first available service account"""
    try:
        json_files = list(KEYS_DIR.glob("*.json"))
        if not json_files:
            raise Exception("No service account files found")
        
        with open(json_files[0], 'r') as f:
            service_account_info = json.load(f)
        
        print(f"✅ Loaded service account: {service_account_info.get('project_id', 'unknown')}")
        return service_account_info
        
    except Exception as e:
        print(f"❌ Failed to load service account: {e}")
        raise

def test_audio_generation():
    """Test audio generation with sample Tamil text"""
    
    # Create output directory
    OUTPUT_DIR.mkdir(exist_ok=True)
    
    # Load service account
    service_account_info = load_service_account()
    credentials = service_account.Credentials.from_service_account_info(service_account_info)
    client = texttospeech.TextToSpeechClient(credentials=credentials)
    
    # Test text
    test_text = "வணக்கம்! இது தமிழ் மொழியின் அழகான ஒலி சோதனை."
    
    print(f"🎵 Testing audio generation with text: {test_text}")
    
    # Test both voices
    voices = [
        ("female", TAMIL_FEMALE_VOICE),
        ("male", TAMIL_MALE_VOICE)
    ]
    
    for voice_type, voice_name in voices:
        try:
            print(f"\n🎤 Generating {voice_type} voice ({voice_name})...")
            
            # Set up synthesis input
            synthesis_input = texttospeech.SynthesisInput(text=test_text)
            
            # Build voice request
            voice = texttospeech.VoiceSelectionParams(
                language_code=LANGUAGE_CODE,
                name=voice_name
            )
            
            # Audio config
            audio_config = texttospeech.AudioConfig(
                audio_encoding=AUDIO_FORMAT,
                speaking_rate=SPEAKING_RATE,
                pitch=PITCH
            )
            
            # Generate audio
            response = client.synthesize_speech(
                input=synthesis_input,
                voice=voice,
                audio_config=audio_config
            )
            
            # Save to file
            output_file = OUTPUT_DIR / f"test_{voice_type}.mp3"
            with open(output_file, "wb") as out:
                out.write(response.audio_content)
            
            print(f"✅ Generated: {output_file}")
            print(f"📁 File size: {len(response.audio_content)} bytes")
            
        except Exception as e:
            print(f"❌ Failed to generate {voice_type} voice: {e}")

def main():
    """Main test function"""
    print("🚀 NIRA Tamil Audio Generation Test")
    print("=" * 40)
    
    try:
        test_audio_generation()
        print("\n🎉 Audio generation test completed!")
        print(f"📁 Check output files in: {OUTPUT_DIR}")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    main()
