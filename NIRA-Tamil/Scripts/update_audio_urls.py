#!/usr/bin/env python3
"""
Update Literature Audio URLs Script
Updates database with mock audio URLs for testing
"""

import asyncio
from pathlib import Path
from typing import List, Dict
from supabase import create_client, Client

# Configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

# Paths
AUDIO_DIR = Path("/Users/<USER>/Documents/NIRA-Tamil/literature_audio")

class AudioURLUpdater:
    def __init__(self):
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        self.updated_count = 0
        self.failed_count = 0
        
    def get_audio_files(self) -> Dict[str, Dict[str, str]]:
        """Get all audio files and organize by content ID"""
        content_audio = {}
        
        for file_path in AUDIO_DIR.glob("*.mp3"):
            filename = file_path.name
            
            # Extract content ID and voice type from filename
            if filename.startswith("literature_") and filename.endswith(".mp3"):
                core_name = filename[11:-4]
                
                if core_name.endswith("_female"):
                    voice_type = "female"
                    content_id = core_name[:-7]
                elif core_name.endswith("_male"):
                    voice_type = "male"
                    content_id = core_name[:-5]
                else:
                    continue
                
                if content_id not in content_audio:
                    content_audio[content_id] = {}
                
                # Create mock URL for testing
                mock_url = f"https://wnsorhbsucjguaoquhvr.supabase.co/storage/v1/object/public/audio/literature/{filename}"
                content_audio[content_id][voice_type] = mock_url
        
        return content_audio
    
    async def update_literature_audio_urls(self, content_id: str, female_url: str, male_url: str):
        """Update literature content with audio URLs"""
        try:
            result = self.supabase.table("literature_content").update({
                "audio_female_url": female_url,
                "audio_male_url": male_url,
                "audio_url": female_url  # Default to female voice
            }).eq("id", content_id).execute()
            
            if result.data:
                print(f"✅ Updated audio URLs for content: {content_id}")
                self.updated_count += 1
                return True
            else:
                print(f"⚠️  No content found with ID: {content_id}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to update audio URLs for {content_id}: {e}")
            self.failed_count += 1
            return False
    
    async def update_all_audio_urls(self):
        """Update all literature content with audio URLs"""
        print("🎵 Starting Literature Audio URL Updates")
        print("=" * 60)
        
        # Get all audio files
        content_audio = self.get_audio_files()
        
        print(f"📁 Found audio files for {len(content_audio)} literature items")
        print(f"📁 Source directory: {AUDIO_DIR}")
        print()
        
        # Process each content item
        for i, (content_id, voice_urls) in enumerate(content_audio.items(), 1):
            print(f"📖 Processing item {i}/{len(content_audio)}")
            print(f"🆔 Content ID: {content_id}")
            
            female_url = voice_urls.get("female", "")
            male_url = voice_urls.get("male", "")
            
            if female_url or male_url:
                success = await self.update_literature_audio_urls(content_id, female_url, male_url)
                if success:
                    print(f"🎵 Female URL: {female_url}")
                    print(f"🎵 Male URL: {male_url}")
                
                # Progress update
                progress = (i / len(content_audio)) * 100
                print(f"📊 Progress: {progress:.1f}%")
                print()
            else:
                print(f"❌ No audio URLs found for content: {content_id}")
                self.failed_count += 1
                print()
        
        # Final summary
        print("=" * 60)
        print("🎵 Literature Audio URL Update Complete!")
        print(f"✅ Successfully updated: {self.updated_count}")
        print(f"❌ Failed updates: {self.failed_count}")
        print(f"📊 Success rate: {(self.updated_count / len(content_audio) * 100):.1f}%")
        
        if self.failed_count == 0:
            print("\n🎉 All audio URLs updated successfully!")
            print("\n📋 Next Steps:")
            print("1. Test audio playback in the NIRA Tamil app")
            print("2. Verify voice selection functionality")
            print("3. Upload actual audio files to Supabase storage")
            print("4. Update URLs to point to real audio files")
        else:
            print(f"\n⚠️  {self.failed_count} updates failed. Check logs above for details.")

async def main():
    """Main execution function"""
    try:
        updater = AudioURLUpdater()
        await updater.update_all_audio_urls()
        
    except KeyboardInterrupt:
        print("\n⏹️  Update stopped by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        raise

if __name__ == "__main__":
    print("🚀 NIRA Tamil Literature Audio URL Updater")
    print("🗄️  Updating database with mock audio URLs for testing")
    print("🎵 This enables audio functionality testing in the app")
    print()
    
    # Run the async main function
    asyncio.run(main())
