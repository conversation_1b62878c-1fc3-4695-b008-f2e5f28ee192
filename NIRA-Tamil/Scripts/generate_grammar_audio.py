#!/usr/bin/env python3
"""
Grammar Audio Generation Script
Generates audio files for grammar examples using Google TTS and uploads to Supabase
"""

import os
import json
import base64
import asyncio
import aiohttp
import aiofiles
from google.cloud import texttospeech
from google.oauth2 import service_account
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"
AUDIO_BUCKET = "audio"
LESSON_ID = "7b8c60af-dd2f-4754-9363-ab09a5bcea95"

# TTS Configuration - Approved Voices
FEMALE_VOICE = "ta-IN-Chirp3-HD-Erinome"  # Premium Tamil Female
MALE_VOICE = "ta-IN-Chirp3-HD-Iapetus"    # Premium Tamil Male
LANGUAGE_CODE = "ta-IN"

# Service Account Keys Directory
KEYS_DIR = "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys"

class GrammarAudioGenerator:
    def __init__(self):
        self.service_accounts = self.load_service_accounts()
        self.current_account_index = 0
        self.generated_files = []
        
    def load_service_accounts(self):
        """Load all service account keys"""
        accounts = []
        for filename in os.listdir(KEYS_DIR):
            if filename.endswith('.json'):
                key_path = os.path.join(KEYS_DIR, filename)
                accounts.append(key_path)
        logger.info(f"Loaded {len(accounts)} service accounts")
        return accounts
    
    def get_current_client(self):
        """Get current TTS client with service account rotation"""
        if not self.service_accounts:
            raise Exception("No service accounts available")
            
        key_path = self.service_accounts[self.current_account_index]
        credentials = service_account.Credentials.from_service_account_file(key_path)
        client = texttospeech.TextToSpeechClient(credentials=credentials)
        return client
    
    def rotate_service_account(self):
        """Rotate to next service account"""
        self.current_account_index = (self.current_account_index + 1) % len(self.service_accounts)
        logger.info(f"Rotated to service account {self.current_account_index + 1}")
    
    async def fetch_grammar_examples(self):
        """Fetch grammar examples from Supabase"""
        query = """
        SELECT ge.id, ge.example_tamil, ge.example_english, ge.example_romanization, 
               gt.title_english, gt.title_tamil 
        FROM grammar_examples ge 
        JOIN grammar_topics gt ON ge.grammar_topic_id = gt.id 
        WHERE gt.lesson_id = %s 
        ORDER BY gt.grammar_id, ge.example_order;
        """ % f"'{LESSON_ID}'"
        
        headers = {
            "Authorization": f"Bearer {SUPABASE_KEY}",
            "apikey": SUPABASE_KEY,
            "Content-Type": "application/json"
        }
        
        # Use PostgREST query
        url = f"{SUPABASE_URL}/rest/v1/rpc/execute_query"
        data = {"query": query}
        
        async with aiohttp.ClientSession() as session:
            try:
                # Alternative approach - direct table query with joins
                url = f"{SUPABASE_URL}/rest/v1/grammar_examples"
                params = {
                    "select": "id,example_tamil,example_english,example_romanization,grammar_topics!inner(title_english,title_tamil,lesson_id)",
                    "grammar_topics.lesson_id": f"eq.{LESSON_ID}",
                    "order": "grammar_topics(grammar_id),example_order"
                }
                
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        examples = await response.json()
                        logger.info(f"Fetched {len(examples)} grammar examples")
                        return examples
                    else:
                        logger.error(f"Failed to fetch examples: {response.status}")
                        return []
            except Exception as e:
                logger.error(f"Error fetching examples: {e}")
                return []
    
    def determine_voice(self, text):
        """Determine appropriate voice for text"""
        # For now, use female voice as default
        # You can enhance this with gender detection logic
        return FEMALE_VOICE
    
    async def generate_audio_file(self, text, filename, voice):
        """Generate audio file using Google TTS"""
        try:
            client = self.get_current_client()
            
            # Set up the request
            synthesis_input = texttospeech.SynthesisInput(text=text)
            voice_params = texttospeech.VoiceSelectionParams(
                language_code=LANGUAGE_CODE,
                name=voice
            )
            audio_config = texttospeech.AudioConfig(
                audio_encoding=texttospeech.AudioEncoding.MP3,
                speaking_rate=0.9,  # Slightly slower for learning
                pitch=0.0
            )
            
            # Perform the text-to-speech request
            response = client.synthesize_speech(
                input=synthesis_input,
                voice=voice_params,
                audio_config=audio_config
            )
            
            # Save audio file locally
            local_path = f"/tmp/{filename}"
            async with aiofiles.open(local_path, "wb") as f:
                await f.write(response.audio_content)
            
            logger.info(f"Generated audio: {filename} ({len(response.audio_content)} bytes)")
            
            return {
                "filename": filename,
                "local_path": local_path,
                "audio_data": response.audio_content,
                "text": text,
                "voice": voice
            }
            
        except Exception as e:
            if "quota" in str(e).lower() or "rate" in str(e).lower():
                logger.warning(f"Rate limit hit, rotating service account: {e}")
                self.rotate_service_account()
                # Retry with new account
                return await self.generate_audio_file(text, filename, voice)
            else:
                logger.error(f"Error generating audio for {filename}: {e}")
                raise
    
    async def upload_to_supabase(self, audio_file):
        """Upload audio file to Supabase Storage"""
        headers = {
            "Authorization": f"Bearer {SUPABASE_KEY}",
            "apikey": SUPABASE_KEY,
            "Content-Type": "audio/mpeg"
        }
        
        url = f"{SUPABASE_URL}/storage/v1/object/{AUDIO_BUCKET}/{audio_file['filename']}"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, data=audio_file['audio_data']) as response:
                if response.status in [200, 201]:
                    public_url = f"{SUPABASE_URL}/storage/v1/object/public/{AUDIO_BUCKET}/{audio_file['filename']}"
                    logger.info(f"Uploaded: {audio_file['filename']} -> {public_url}")
                    return public_url
                else:
                    error_text = await response.text()
                    logger.error(f"Upload failed for {audio_file['filename']}: {response.status} - {error_text}")
                    raise Exception(f"Upload failed: {response.status}")
    
    async def update_database(self, example_id, audio_url):
        """Update grammar example with audio URL"""
        headers = {
            "Authorization": f"Bearer {SUPABASE_KEY}",
            "apikey": SUPABASE_KEY,
            "Content-Type": "application/json"
        }
        
        url = f"{SUPABASE_URL}/rest/v1/grammar_examples"
        params = {"id": f"eq.{example_id}"}
        data = {"audio_url": audio_url}
        
        async with aiohttp.ClientSession() as session:
            async with session.patch(url, headers=headers, params=params, json=data) as response:
                if response.status in [200, 204]:
                    logger.info(f"Updated database for example {example_id}")
                else:
                    error_text = await response.text()
                    logger.error(f"Database update failed for {example_id}: {response.status} - {error_text}")
    
    async def process_all_examples(self):
        """Main processing function"""
        logger.info("Starting grammar audio generation workflow...")
        
        # Step 1: Fetch examples
        examples = await self.fetch_grammar_examples()
        if not examples:
            logger.error("No examples found, exiting")
            return
        
        # Step 2: Process each example
        for i, example in enumerate(examples, 1):
            try:
                # Extract data based on the response structure
                if 'grammar_topics' in example:
                    # Nested structure
                    example_id = example['id']
                    tamil_text = example['example_tamil']
                    english_text = example['example_english']
                else:
                    # Flat structure
                    example_id = example['id']
                    tamil_text = example['example_tamil']
                    english_text = example['example_english']
                
                # Generate filename
                filename = f"lesson_01_grammar_{i:02d}_example_{i:02d}.mp3"
                
                # Determine voice
                voice = self.determine_voice(tamil_text)
                
                logger.info(f"Processing {i}/{len(examples)}: {english_text}")
                
                # Generate audio
                audio_file = await self.generate_audio_file(tamil_text, filename, voice)
                
                # Upload to Supabase
                public_url = await self.upload_to_supabase(audio_file)
                
                # Update database
                await self.update_database(example_id, public_url)
                
                # Clean up local file
                os.remove(audio_file['local_path'])
                
                # Small delay to avoid overwhelming services
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Failed to process example {i}: {e}")
                continue
        
        logger.info("Grammar audio generation workflow completed!")

async def main():
    """Main function"""
    generator = GrammarAudioGenerator()
    await generator.process_all_examples()

if __name__ == "__main__":
    asyncio.run(main())
