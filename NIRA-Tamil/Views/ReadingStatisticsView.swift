import SwiftUI
#if canImport(Charts)
import Charts
#endif

struct ReadingStatisticsView: View {
    @StateObject private var statsService = ReadingStatisticsService.shared
    @StateObject private var progressService = ReadingProgressService.shared
    @State private var selectedTab: StatisticsTab = .overview
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue.opacity(0.1),
                        Color.purple.opacity(0.1),
                        Color.pink.opacity(0.05)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Header with tabs
                        headerSection
                        
                        // Content based on selected tab
                        switch selectedTab {
                        case .overview:
                            overviewSection
                        case .progress:
                            progressSection
                        case .achievements:
                            achievementsSection
                        case .bookmarks:
                            bookmarksSection
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("Reading Statistics")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            statsService.updateStatistics()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Quick stats cards
            HStack(spacing: 12) {
                StatCard(
                    title: "Today: \(statsService.dailyStats.contentRead)",
                    value: "Content Read",
                    icon: "book.fill",
                    color: .blue
                )

                StatCard(
                    title: "Streak: \(statsService.currentStreak)",
                    value: "Days",
                    icon: "flame.fill",
                    color: .orange
                )

                StatCard(
                    title: "Total: \(statsService.overallStats.totalContentCompleted)",
                    value: "Completed",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
            }
            
            // Tab selector
            HStack(spacing: 0) {
                ForEach(StatisticsTab.allCases, id: \.self) { tab in
                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedTab = tab
                        }
                    } label: {
                        VStack(spacing: 4) {
                            Text(tab.title)
                                .font(.subheadline)
                                .fontWeight(selectedTab == tab ? .semibold : .regular)
                                .foregroundColor(selectedTab == tab ? .white : .secondary)
                            
                            if selectedTab == tab {
                                Rectangle()
                                    .frame(height: 2)
                                    .foregroundColor(.white)
                            } else {
                                Rectangle()
                                    .frame(height: 2)
                                    .foregroundColor(.clear)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Overview Section
    
    private var overviewSection: some View {
        VStack(spacing: 20) {
            // Reading time chart
            readingTimeChart
            
            // Category breakdown
            categoryBreakdown
            
            // Level progress
            levelProgress
        }
    }
    
    private var readingTimeChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Weekly Reading Time")
                .font(.headline)
                .foregroundColor(.primary)
            
            #if canImport(Charts)
            if #available(iOS 16.0, *) {
                Chart {
                    ForEach(Array(statsService.weeklyStats.dailyBreakdown.sorted(by: { $0.key < $1.key })), id: \.key) { date, count in
                        BarMark(
                            x: .value("Day", date, unit: .day),
                            y: .value("Content", count)
                        )
                        .foregroundStyle(.blue.gradient)
                    }
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day)) { _ in
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.weekday(.abbreviated))
                    }
                }
                .chartYAxis {
                    AxisMarks { _ in
                        AxisGridLine()
                        AxisValueLabel()
                    }
                }
            } else {
                // Fallback for iOS 15
                weeklyProgressBars
            }
            #else
            // Fallback when Charts is not available
            weeklyProgressBars
            #endif
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var categoryBreakdown: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Category Progress")
                .font(.headline)
                .foregroundColor(.primary)
            
            ForEach(Array(statsService.overallStats.categoryStats.sorted(by: { $0.key.rawValue < $1.key.rawValue })), id: \.key) { category, stats in
                CategoryProgressRow(category: category, stats: stats)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var levelProgress: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Level Mastery")
                .font(.headline)
                .foregroundColor(.primary)
            
            ForEach(Array(statsService.overallStats.levelStats.sorted(by: { $0.key.rawValue < $1.key.rawValue })), id: \.key) { level, stats in
                LevelProgressRow(level: level, stats: stats)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Progress Section
    
    private var progressSection: some View {
        VStack(spacing: 20) {
            // Daily stats
            dailyStatsCard
            
            // Weekly stats
            weeklyStatsCard
            
            // Overall stats
            overallStatsCard
        }
    }
    
    private var dailyStatsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Today's Progress")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("\(statsService.dailyStats.contentRead)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    Text("Content Read")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text(statsService.dailyStats.readingTimeDisplay)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Reading Time")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            if statsService.dailyStats.completedContent > 0 {
                HStack {
                    Text("\(statsService.dailyStats.completedContent) completed")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text("\(Int(statsService.dailyStats.averageCompletionRate))% avg completion")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var weeklyStatsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("This Week")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("\(statsService.weeklyStats.totalContentRead)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                    Text("Total Content")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text(String(format: "%.1f", statsService.weeklyStats.averageDailyReading))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Text("Daily Average")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            HStack {
                Text("Consistency: \(Int(statsService.weeklyStats.consistencyScore))%")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if let bestDay = statsService.weeklyStats.bestDay {
                    Text("Best: \(bestDay, formatter: dayFormatter)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var overallStatsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Overall Statistics")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                OverallStatItem(
                    title: "Total Read",
                    value: "\(statsService.overallStats.totalContentRead)",
                    color: .blue
                )
                
                OverallStatItem(
                    title: "Completed",
                    value: "\(statsService.overallStats.totalContentCompleted)",
                    color: .green
                )
                
                OverallStatItem(
                    title: "Reading Time",
                    value: formatTotalTime(statsService.overallStats.totalReadingTime),
                    color: .purple
                )
                
                OverallStatItem(
                    title: "Completion Rate",
                    value: "\(Int(statsService.overallStats.completionRate))%",
                    color: .orange
                )
            }
            
            if let favoriteCategory = statsService.overallStats.favoriteCategory {
                Text("Favorite Category: \(favoriteCategory.displayName)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            if let strongestLevel = statsService.overallStats.strongestLevel {
                Text("Strongest Level: \(strongestLevel.rawValue.uppercased())")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Achievements Section
    
    private var achievementsSection: some View {
        VStack(spacing: 20) {
            // Earned achievements
            if !statsService.achievements.isEmpty {
                earnedAchievements
            }
            
            // All achievements
            allAchievements
        }
    }
    
    private var earnedAchievements: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Earned Achievements")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(statsService.achievements, id: \.self) { achievement in
                    ReadingAchievementCard(achievement: achievement, isEarned: true)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var allAchievements: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("All Achievements")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(ReadingAchievement.allCases, id: \.self) { achievement in
                    ReadingAchievementCard(
                        achievement: achievement,
                        isEarned: statsService.achievements.contains(achievement)
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    // MARK: - Bookmarks Section

    private var bookmarksSection: some View {
        VStack(spacing: 20) {
            // Bookmarks overview
            bookmarksOverview

            // Reading sessions summary
            readingSessionsSummary

            // Progress tracking insights
            progressInsights
        }
    }

    private var bookmarksOverview: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Bookmarks & Reading Sessions")
                .font(.headline)
                .foregroundColor(.primary)

            let bookmarks = progressService.getAllBookmarks()
            let statistics = progressService.getReadingStatistics()

            HStack {
                VStack(alignment: .leading) {
                    Text("\(bookmarks.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    Text("Total Bookmarks")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing) {
                    Text("\(statistics.readingSessionsCount)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Reading Sessions")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            HStack {
                VStack(alignment: .leading) {
                    Text(statistics.totalReadingTimeFormatted)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                    Text("Total Reading Time")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing) {
                    Text("\(Int(statistics.averageProgressPercentage * 100))%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Text("Avg Progress")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private var readingSessionsSummary: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Reading Activity")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 8) {
                ForEach(0..<min(5, progressService.getAllBookmarks().count), id: \.self) { index in
                    let bookmarks = progressService.getAllBookmarks()
                    if index < bookmarks.count {
                        let bookmark = bookmarks[index]
                        HStack {
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Literature Content")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)

                                Text("Bookmarked \(bookmark.createdAt, style: .relative)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            VStack(alignment: .trailing, spacing: 2) {
                                Text("\(Int(progressService.getProgress(contentId: bookmark.contentId) * 100))%")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)

                                ProgressView(value: progressService.getProgress(contentId: bookmark.contentId), total: 1.0)
                                    .frame(width: 60, height: 4)
                                    .tint(.blue)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }

                if progressService.getAllBookmarks().isEmpty {
                    Text("No bookmarks yet. Start reading to create bookmarks!")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private var progressInsights: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Reading Insights")
                .font(.headline)
                .foregroundColor(.primary)

            let statistics = progressService.getReadingStatistics()

            VStack(spacing: 8) {
                ReadingInsightRow(
                    title: "Completion Rate",
                    value: "\(Int(statistics.completionRate * 100))%",
                    insight: statistics.completionRate > 0.7 ? "Excellent completion rate!" : "Try to complete more content",
                    color: statistics.completionRate > 0.7 ? .green : .orange
                )

                ReadingInsightRow(
                    title: "Reading Consistency",
                    value: "Daily",
                    insight: "Keep up the great reading habit!",
                    color: .blue
                )

                ReadingInsightRow(
                    title: "Bookmark Usage",
                    value: "\(statistics.totalBookmarks) saved",
                    insight: statistics.totalBookmarks > 5 ? "Great bookmark organization!" : "Consider bookmarking interesting content",
                    color: .purple
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    // MARK: - Helper Methods
    
    private func formatTotalTime(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    private var dayFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEE"
        return formatter
    }

    // Fallback weekly progress view for when Charts is not available
    private var weeklyProgressBars: some View {
        VStack(spacing: 8) {
            ForEach(Array(statsService.weeklyStats.dailyBreakdown.sorted(by: { $0.key < $1.key })), id: \.key) { date, count in
                HStack {
                    Text(date, formatter: dayFormatter)
                        .font(.caption)
                        .frame(width: 30, alignment: .leading)
                        .foregroundColor(.secondary)

                    ProgressView(value: Double(count), total: 10.0)
                        .tint(.blue)
                        .frame(height: 8)

                    Text("\(count)")
                        .font(.caption)
                        .foregroundColor(.primary)
                        .frame(width: 20, alignment: .trailing)
                }
            }
        }
        .frame(height: 200)
    }
}

// MARK: - Supporting Views

enum StatisticsTab: CaseIterable {
    case overview, progress, achievements, bookmarks

    var title: String {
        switch self {
        case .overview: return "Overview"
        case .progress: return "Progress"
        case .achievements: return "Achievements"
        case .bookmarks: return "Bookmarks"
        }
    }
}

// MARK: - Supporting View Components



struct CategoryProgressRow: View {
    let category: ReadingCategory
    let stats: CategoryStats

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(category.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text("\(stats.completedContent)/\(stats.totalContent) completed")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text("\(Int(stats.completionRate))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(stats.completionRate > 50 ? .green : .orange)

                ProgressView(value: stats.completionRate / 100.0)
                    .frame(width: 60)
                    .tint(stats.completionRate > 50 ? .green : .orange)
            }
        }
        .padding(.vertical, 4)
    }
}

struct LevelProgressRow: View {
    let level: CEFRLevel
    let stats: LevelStats

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(level.rawValue.uppercased())
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text("\(stats.completedContent)/\(stats.totalContent) mastered")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text("\(Int(stats.masteryLevel))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(level.uiColor)

                ProgressView(value: stats.masteryLevel / 100.0)
                    .frame(width: 60)
                    .tint(level.uiColor)
            }
        }
        .padding(.vertical, 4)
    }
}

struct OverallStatItem: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

struct ReadingAchievementCard: View {
    let achievement: ReadingAchievement
    let isEarned: Bool

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: achievement.icon)
                .font(.title2)
                .foregroundColor(isEarned ? achievement.color : .secondary)

            Text(achievement.title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isEarned ? .primary : .secondary)
                .multilineTextAlignment(.center)

            Text(achievement.description)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isEarned ? achievement.color.opacity(0.1) : Color.secondary.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isEarned ? achievement.color.opacity(0.3) : Color.secondary.opacity(0.2), lineWidth: 1)
                )
        )
        .opacity(isEarned ? 1.0 : 0.6)
    }
}

struct ReadingInsightRow: View {
    let title: String
    let value: String
    let insight: String
    let color: Color

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(insight)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Extensions

#Preview {
    ReadingStatisticsView()
}
