//
//  InteractivePracticeView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 25/06/2025.
//

import SwiftUI

struct InteractivePracticeView: View {
    let exercises: [TamilSupabasePracticeExercise]
    let level: CEFRLevel
    @Environment(\.dismiss) private var dismiss
    
    // Practice session state
    @State private var currentExerciseIndex = 0
    @State private var selectedAnswers: [Int?] = []
    @State private var showingResult = false
    @State private var sessionStartTime = Date()
    @State private var sessionEndTime = Date()
    @State private var exerciseStartTime = Date()
    @State private var isSessionComplete = false
    
    // Results tracking
    @State private var correctAnswers = 0
    @State private var totalAnswers = 0
    @State private var exerciseResults: [PracticeExerciseResult] = []
    
    private var currentExercise: TamilSupabasePracticeExercise? {
        guard currentExerciseIndex >= 0 && currentExerciseIndex < exercises.count else {
            print("❌ currentExercise: Invalid index \(currentExerciseIndex) for exercises.count=\(exercises.count)")
            return nil
        }
        return exercises[currentExerciseIndex]
    }
    
    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
    
    private var levelGradient: LinearGradient {
        LinearGradient(
            colors: [levelColor, levelColor.opacity(0.7)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    var body: some View {
        NavigationView {
            if exercises.isEmpty {
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.largeTitle)
                        .foregroundColor(.orange)

                    Text("No Practice Exercises")
                        .font(.headline)

                    Text("Please try again later")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.blue)
                }
                .padding()
            } else {
                ZStack {
                // Background with glassmorphism
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        levelColor.opacity(0.05)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                if isSessionComplete {
                    // Show results summary
                    PracticeResultsView(
                        results: exerciseResults,
                        totalTime: Date().timeIntervalSince(sessionStartTime),
                        level: level,
                        onDismiss: { dismiss() }
                    )
                } else if let exercise = currentExercise {
                    // Show current exercise
                    VStack(spacing: 0) {
                        // Header
                        practiceHeader
                        
                        // Exercise content
                        InteractiveExerciseCard(
                            exercise: exercise,
                            selectedAnswer: currentExerciseIndex < selectedAnswers.count ? selectedAnswers[currentExerciseIndex] : nil,
                            showResult: showingResult,
                            level: level,
                            onAnswerSelected: { answer in
                                print("🎯 Answer Selected: \(answer) for exercise \(currentExerciseIndex)")
                                if currentExerciseIndex < selectedAnswers.count {
                                    selectedAnswers[currentExerciseIndex] = answer
                                    print("   Updated selectedAnswers[\(currentExerciseIndex)] = \(answer)")
                                    print("   Current selectedAnswers: \(selectedAnswers)")
                                } else {
                                    print("❌ ERROR: currentExerciseIndex \(currentExerciseIndex) >= selectedAnswers.count \(selectedAnswers.count)")
                                }
                            },
                            onSubmit: {
                                submitAnswer()
                            }
                        )
                        
                        // Navigation controls
                        practiceControls
                    }
                } else {
                    // Error state
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)
                        Text("No exercises available")
                            .font(.headline)
                            .padding()
                    }
                }
            }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            if exercises.isEmpty {
                print("❌ onAppear: No exercises provided!")
                return
            }
            setupPracticeSession()
        }
    }
    
    private var practiceHeader: some View {
        HStack {
            Button("Exit") {
                dismiss()
            }
            .foregroundColor(levelColor)
            .fontWeight(.medium)

            Spacer()

            VStack {
                Text("Practice Session")
                    .font(.headline)
                    .fontWeight(.semibold)

                Text("\(currentExerciseIndex + 1) of \(exercises.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // Show current score progress
            Text("\(correctAnswers)/\(currentExerciseIndex + (showingResult ? 1 : 0))")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 16)
        .background(.ultraThinMaterial)
    }
    
    private var practiceControls: some View {
        HStack(spacing: 16) {
            if currentExerciseIndex > 0 {
                Button("Previous") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        currentExerciseIndex -= 1
                        showingResult = selectedAnswers[currentExerciseIndex] != nil
                        exerciseStartTime = Date()
                    }
                }
                .foregroundColor(levelColor)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(levelColor, lineWidth: 2)
                )
            }
            
            Spacer()
            
            // Progress indicator
            HStack(spacing: 6) {
                ForEach(0..<exercises.count, id: \.self) { index in
                    Circle()
                        .fill(getProgressColor(for: index))
                        .frame(width: index == currentExerciseIndex ? 12 : 8,
                               height: index == currentExerciseIndex ? 12 : 8)
                        .overlay(
                            Circle()
                                .stroke(Color.white.opacity(0.3), lineWidth: index == currentExerciseIndex ? 2 : 0)
                        )
                        .animation(.easeInOut(duration: 0.2), value: currentExerciseIndex)
                }
            }
            
            Spacer()
            
            if showingResult && currentExerciseIndex < exercises.count - 1 {
                Button("Next") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        print("🔄 Next button: currentIndex=\(currentExerciseIndex), exercises.count=\(exercises.count)")
                        if currentExerciseIndex + 1 < exercises.count {
                            currentExerciseIndex += 1
                            showingResult = false
                            exerciseStartTime = Date()
                            print("✅ Next button: Moved to index \(currentExerciseIndex)")
                        } else {
                            print("❌ Next button: Cannot move to next exercise")
                        }
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(levelGradient)
                )
            } else if showingResult && currentExerciseIndex == exercises.count - 1 {
                Button("Complete") {
                    completeSession()
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(levelGradient)
                )
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 16)
        .background(.ultraThinMaterial)
    }
    
    private func setupPracticeSession() {
        print("🔄 Setting up practice session with \(exercises.count) exercises")
        selectedAnswers = Array(repeating: nil, count: exercises.count)
        exerciseStartTime = Date()
        sessionStartTime = Date()
        print("✅ Practice session setup complete: selectedAnswers.count=\(selectedAnswers.count)")
    }
    
    private func submitAnswer() {
        guard currentExerciseIndex < selectedAnswers.count,
              let selectedAnswer = selectedAnswers[currentExerciseIndex],
              let exercise = currentExercise,
              let questionData = exercise.questionData else {
            print("❌ Submit answer failed: currentIndex=\(currentExerciseIndex), selectedAnswers.count=\(selectedAnswers.count)")
            return
        }

        // 🐛 DEBUG: Log answer validation details
        print("🔍 DEBUG Answer Validation:")
        print("   Question: \(questionData.question)")
        print("   Selected Answer Index: \(selectedAnswer)")
        print("   Correct Answer Index: \(questionData.correctAnswer)")
        print("   Options: \(questionData.options)")
        print("   Selected Option: \(questionData.options[safe: selectedAnswer] ?? "INVALID INDEX")")
        print("   Correct Option: \(questionData.options[safe: questionData.correctAnswer] ?? "INVALID INDEX")")

        let isCorrect = selectedAnswer == questionData.correctAnswer
        let responseTime = Date().timeIntervalSince(exerciseStartTime)

        print("   Is Correct: \(isCorrect)")
        print("   Points Awarded: \(isCorrect ? exercise.pointsValue : 0)")
        print("---")
        
        // Record result
        let result = PracticeExerciseResult(
            exerciseId: exercise.exerciseId,
            exerciseType: exercise.exerciseType,
            question: questionData.question,
            selectedAnswer: selectedAnswer,
            correctAnswer: questionData.correctAnswer,
            isCorrect: isCorrect,
            responseTime: responseTime,
            points: isCorrect ? exercise.pointsValue : 0
        )
        
        exerciseResults.append(result)
        
        // Update counters
        totalAnswers += 1
        if isCorrect {
            correctAnswers += 1
        }
        
        // Show result
        print("🎬 Showing result for exercise \(currentExerciseIndex)")
        print("   Final selectedAnswer: \(selectedAnswers[currentExerciseIndex] ?? -1)")
        print("   Final isCorrect: \(isCorrect)")

        withAnimation(.easeInOut(duration: 0.3)) {
            showingResult = true
        }
    }
    
    private func completeSession() {
        print("🏁 completeSession: currentIndex=\(currentExerciseIndex), exercises.count=\(exercises.count)")
        sessionEndTime = Date()
        withAnimation(.easeInOut(duration: 0.5)) {
            isSessionComplete = true
        }
        print("✅ completeSession: Session marked as complete")
    }
    
    private func getProgressColor(for index: Int) -> Color {
        guard index < exercises.count && index < selectedAnswers.count else {
            return Color.secondary.opacity(0.3)
        }

        if index < currentExerciseIndex {
            // Completed
            return selectedAnswers[index] != nil ?
                (exerciseResults.first { $0.exerciseId == exercises[index].exerciseId }?.isCorrect == true ? .green : .red) :
                .gray
        } else if index == currentExerciseIndex {
            // Current
            return levelColor
        } else {
            // Not started
            return Color.secondary.opacity(0.3)
        }
    }
}

// MARK: - Supporting Models

struct PracticeExerciseResult {
    let exerciseId: String
    let exerciseType: String
    let question: String
    let selectedAnswer: Int
    let correctAnswer: Int
    let isCorrect: Bool
    let responseTime: TimeInterval
    let points: Int
}
