//
//  AcademicContentView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import SwiftUI

struct AcademicContentView: View {
    @StateObject private var academicService = AcademicContentService.shared
    @State private var selectedGradeGroup: GradeGroup = .primary
    @State private var selectedStandard: Int = 1
    @State private var selectedModule: AcademicContentModule?
    @State private var showingModuleDetail = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Grade group selector with beautiful cards
                VStack(alignment: .leading, spacing: 12) {
                    Text("Select Grade Level")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 16) {
                            ForEach(GradeGroup.allCases, id: \.self) { gradeGroup in
                                GradeGroupCard(
                                    gradeGroup: gradeGroup,
                                    isSelected: selectedGradeGroup == gradeGroup
                                ) {
                                    selectedGradeGroup = gradeGroup
                                    selectedStandard = gradeGroup.standards.first ?? 1
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                }

                // Standard selector within selected grade group
                if !selectedGradeGroup.standards.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Select Standard")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.horizontal)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                ForEach(selectedGradeGroup.standards, id: \.self) { standard in
                                    StandardCard(
                                        standard: standard,
                                        gradeGroup: selectedGradeGroup,
                                        isSelected: selectedStandard == standard
                                    ) {
                                        selectedStandard = standard
                                    }
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                }

                // Academic modules content
                if academicService.isLoading {
                    loadingView
                } else if let errorMessage = academicService.errorMessage {
                    errorView(errorMessage)
                } else {
                    academicModulesView
                }
            }
            .padding(.vertical)
        }
        .background(Color(.systemBackground))
        .task {
            academicService.loadAcademicContent()
        }
        .onChange(of: selectedStandard) {
            Task {
                await academicService.loadAcademicModulesForStandard(selectedStandard)
            }
        }
        .sheet(isPresented: $showingModuleDetail) {
            if let selectedModule = selectedModule {
                AcademicModuleDetailView(module: selectedModule)
            }
        }
    }
    

    // MARK: - Academic Modules View

    private var academicModulesView: some View {
        VStack(spacing: 0) {
            // Standard selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(academicService.getAvailableStandards(), id: \.self) { standard in
                        StandardSelectorButton(
                            standard: standard,
                            isSelected: selectedStandard == standard,
                            progress: academicService.getStandardProgress(for: standard)
                        ) {
                            selectedStandard = standard
                        }
                    }
                }
                .padding(.horizontal)
            }
            .padding(.vertical)
            
            // Academic modules content
            ScrollView {
                let modules = academicService.getAcademicModulesForStandard(selectedStandard)

                if !modules.isEmpty {
                    // Group modules by type
                    let groupedModules = Dictionary(grouping: modules, by: { $0.moduleType })

                    LazyVStack(spacing: 20) {
                        ForEach(groupedModules.keys.sorted(), id: \.self) { moduleType in
                            VStack(alignment: .leading, spacing: 12) {
                                // Module type header
                                HStack {
                                    Text(moduleType.capitalized)
                                        .font(.headline)
                                        .fontWeight(.semibold)

                                    Spacer()

                                    Text("\(groupedModules[moduleType]?.count ?? 0) modules")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding(.horizontal)

                                // Module cards with beautiful design
                                LazyVStack(spacing: 12) {
                                    ForEach(groupedModules[moduleType] ?? [], id: \.id) { module in
                                        BeautifulAcademicModuleCard(
                                            module: module,
                                            gradeGroup: selectedGradeGroup
                                        ) {
                                            selectedModule = module
                                            showingModuleDetail = true
                                        }
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                    .padding(.vertical)
                } else {
                    VStack(spacing: 16) {
                        Image(systemName: "book.closed")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)

                        Text("No Academic Content Available")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text("Standard \(selectedStandard) academic content is loading...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                    .padding()
                }
            }
        }
    }
    

    
    // MARK: - Loading and Error Views
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Loading Tamil Nadu curriculum...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("Content Loading Error")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Retry") {
                academicService.loadAcademicContent()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

// MARK: - Supporting Views

struct StandardSelectorButton: View {
    let standard: Int
    let isSelected: Bool
    let progress: Double
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(isSelected ? Color.blue : Color(.systemGray5))
                        .frame(width: 50, height: 50)
                    
                    if progress > 0 {
                        Circle()
                            .trim(from: 0, to: progress)
                            .stroke(Color.green, lineWidth: 3)
                            .frame(width: 50, height: 50)
                            .rotationEffect(.degrees(-90))
                    }
                    
                    Text("\(standard)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(isSelected ? .white : .primary)
                }
                
                Text("Std \(standard)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .blue : .secondary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AcademicPageCard: View {
    let standard: Int
    let pageNumber: String
    let isCompleted: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                        .frame(height: 120)
                    
                    VStack(spacing: 8) {
                        Image(systemName: isCompleted ? "checkmark.circle.fill" : "doc.text")
                            .font(.system(size: 32))
                            .foregroundColor(isCompleted ? .green : .blue)
                        
                        Text("Page \(pageNumber)")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                }
                
                Text("Standard \(standard)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isCompleted ? Color.green.opacity(0.5) : Color(.systemGray4), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}



struct CircularProgressView: View {
    let progress: Double
    let color: Color
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.2), lineWidth: 4)
            
            Circle()
                .trim(from: 0, to: progress)
                .stroke(color, lineWidth: 4)
                .rotationEffect(.degrees(-90))
            
            Text("\(Int(progress * 100))%")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
    }
}

// MARK: - Academic Page Detail View

struct AcademicPageDetailView: View {
    let standard: Int
    let pageNumber: String

    @StateObject private var academicService = AcademicContentService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var isCompleted = false
    @State private var showingCompletionAnimation = false

    var pageContent: String {
        academicService.getPageContent(standard: standard, pageNumber: pageNumber) ?? "Content not available"
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Standard \(standard)")
                                .font(.caption)
                                .fontWeight(.bold)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(8)

                            Spacer()

                            if isCompleted {
                                HStack(spacing: 4) {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                    Text("Completed")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.green)
                                }
                            }
                        }

                        Text("Page \(pageNumber)")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                    }

                    // Content
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Content")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text(pageContent)
                            .font(.body)
                            .lineSpacing(4)
                            .multilineTextAlignment(.leading)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.systemGray6))
                            )
                    }

                    // Navigation
                    HStack(spacing: 16) {
                        if let previousPage = getPreviousPage() {
                            NavigationButton(
                                title: "Previous",
                                subtitle: "Page \(previousPage)",
                                icon: "chevron.left",
                                color: .blue
                            ) {
                                // Navigate to previous page
                            }
                        }

                        Spacer()

                        if let nextPage = getNextPage() {
                            NavigationButton(
                                title: "Next",
                                subtitle: "Page \(nextPage)",
                                icon: "chevron.right",
                                color: .blue
                            ) {
                                // Navigate to next page
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Tamil Textbook")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isCompleted ? "Completed" : "Mark Complete") {
                        if !isCompleted {
                            markAsCompleted()
                        }
                    }
                    .disabled(isCompleted)
                    .foregroundColor(isCompleted ? .green : .blue)
                }
            }
        }
        .onAppear {
            isCompleted = academicService.isPageCompleted(standard: standard, pageNumber: pageNumber)
        }
        .overlay(
            // Completion animation
            Group {
                if showingCompletionAnimation {
                    ZStack {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()

                        VStack(spacing: 16) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.green)

                            Text("Page Completed!")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("Great job reading this page!")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color(.systemBackground))
                        )
                        .scaleEffect(showingCompletionAnimation ? 1.0 : 0.5)
                        .opacity(showingCompletionAnimation ? 1.0 : 0.0)
                    }
                }
            }
        )
    }

    private func markAsCompleted() {
        academicService.markPageCompleted(standard: standard, pageNumber: pageNumber)
        isCompleted = true

        // Show completion animation
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            showingCompletionAnimation = true
        }

        // Hide animation after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeOut(duration: 0.3)) {
                showingCompletionAnimation = false
            }
        }
    }

    private func getPreviousPage() -> String? {
        let pages = academicService.getPageNumbers(for: standard)
        guard let currentIndex = pages.firstIndex(of: pageNumber),
              currentIndex > 0 else {
            return nil
        }
        return pages[currentIndex - 1]
    }

    private func getNextPage() -> String? {
        let pages = academicService.getPageNumbers(for: standard)
        guard let currentIndex = pages.firstIndex(of: pageNumber),
              currentIndex < pages.count - 1 else {
            return nil
        }
        return pages[currentIndex + 1]
    }
}

struct NavigationButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                if icon == "chevron.left" {
                    Image(systemName: icon)
                        .font(.caption)
                }

                VStack(alignment: icon == "chevron.left" ? .leading : .trailing, spacing: 2) {
                    Text(title)
                        .font(.caption)
                        .fontWeight(.medium)
                    Text(subtitle)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }

                if icon == "chevron.right" {
                    Image(systemName: icon)
                        .font(.caption)
                }
            }
            .foregroundColor(color)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Grade Group Enum

enum GradeGroup: String, CaseIterable {
    case primary = "primary"
    case middle = "middle"
    case high = "high"
    case higherSecondary = "higher_secondary"

    var displayName: String {
        switch self {
        case .primary: return "Primary"
        case .middle: return "Middle School"
        case .high: return "High School"
        case .higherSecondary: return "Higher Secondary"
        }
    }

    var subtitle: String {
        switch self {
        case .primary: return "Grades 1-3"
        case .middle: return "Grades 4-6"
        case .high: return "Grades 7-9"
        case .higherSecondary: return "Grades 10-12"
        }
    }

    var standards: [Int] {
        switch self {
        case .primary: return [1, 2, 3]
        case .middle: return [4, 5, 6]
        case .high: return [7, 8, 9]
        case .higherSecondary: return [10, 11, 12]
        }
    }

    var color: Color {
        switch self {
        case .primary: return .green
        case .middle: return .blue
        case .high: return .orange
        case .higherSecondary: return .purple
        }
    }

    var gradient: LinearGradient {
        switch self {
        case .primary:
            return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .middle:
            return LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .high:
            return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .higherSecondary:
            return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }

    // MARK: - Helper Functions

    private func moduleTypeIcon(for type: String) -> String {
        switch type.lowercased() {
        case "story": return "book.fill"
        case "poetry": return "quote.bubble.fill"
        case "literature": return "text.book.closed.fill"
        case "grammar": return "textformat.abc"
        case "cultural": return "building.columns.fill"
        default: return "doc.fill"
        }
    }

    private func moduleTypeColor(for type: String) -> Color {
        switch type.lowercased() {
        case "story": return .blue
        case "poetry": return .purple
        case "literature": return .green
        case "grammar": return .orange
        case "cultural": return .red
        default: return .gray
        }
    }
}

// MARK: - Grade Group Card

struct GradeGroupCard: View {
    let gradeGroup: GradeGroup
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(gradeGroup.gradient)
                    .frame(width: 120, height: 120)
                    .blur(radius: 30)
                    .opacity(0.3)
                    .offset(x: 40, y: -40)

                VStack(spacing: 12) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(gradeGroup.gradient)
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    VStack(spacing: 8) {
                        Text(gradeGroup.displayName)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        Text(gradeGroup.subtitle)
                            .font(.subheadline)
                            .foregroundColor(gradeGroup.color)
                            .fontWeight(.medium)

                        Text("Standards \(gradeGroup.standards.map(String.init).joined(separator: ", "))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal, 4)
                }
                .padding(16)
            }
            .frame(width: 160, height: 120)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                isSelected ? gradeGroup.color : gradeGroup.color.opacity(0.3),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .shadow(
                color: isSelected ? gradeGroup.color.opacity(0.3) : Color.clear,
                radius: 8, x: 0, y: 4
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Standard Card

struct StandardCard: View {
    let standard: Int
    let gradeGroup: GradeGroup
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Text("Std")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : gradeGroup.color)

                Text("\(standard)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(isSelected ? .white : gradeGroup.color)
            }
            .frame(width: 50, height: 50)
            .background(
                Circle()
                    .fill(isSelected ? AnyShapeStyle(gradeGroup.gradient) : AnyShapeStyle(gradeGroup.color.opacity(0.1)))
            )
            .overlay(
                Circle()
                    .stroke(gradeGroup.color.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.1 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Beautiful Academic Module Card

struct BeautifulAcademicModuleCard: View {
    let module: AcademicContentModule
    let gradeGroup: GradeGroup
    let action: () -> Void

    // Dynamic vibrant gradient based on module type
    private var cardVariant: AcademicCardVariant {
        switch module.moduleType.lowercased() {
        case "story": return .blue
        case "poetry": return .purple
        case "literature": return .green
        case "grammar": return .orange
        case "cultural": return .pink
        default: return .blue
        }
    }

    private var moduleTypeIcon: String {
        switch module.moduleType.lowercased() {
        case "story": return "book.fill"
        case "poetry": return "quote.bubble.fill"
        case "literature": return "text.book.closed.fill"
        case "grammar": return "textformat.abc"
        case "cultural": return "building.columns.fill"
        default: return "doc.fill"
        }
    }

    var body: some View {
        Button(action: action) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(cardVariant.gradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)

                VStack(alignment: .leading, spacing: 12) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(cardVariant.gradient)
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    VStack(alignment: .leading, spacing: 8) {
                        // Header with module info
                        HStack {
                            HStack(spacing: 8) {
                                // Module type icon
                                Image(systemName: moduleTypeIcon)
                                    .foregroundColor(gradeGroup.color)
                                    .font(.title3)

                                Text(module.moduleTypeDisplayName)
                                    .font(.subheadline)
                                    .fontWeight(.bold)
                                    .foregroundColor(gradeGroup.color)

                                Text("Std \(module.standardLevel)")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            HStack(spacing: 4) {
                                Image(systemName: "clock")
                                    .font(.caption)
                                Text("\(module.estimatedReadingTime) mins")
                                    .font(.caption)
                            }
                            .foregroundColor(.secondary)
                        }

                        // Title and description
                        VStack(alignment: .leading, spacing: 8) {
                            Text(module.titleEnglish)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                                .lineLimit(2)

                            Text(module.titleTamil)
                                .font(.title3)
                                .fontWeight(.medium)
                                .foregroundColor(gradeGroup.color)
                                .multilineTextAlignment(.leading)
                                .lineLimit(2)

                            // Content excerpt
                            if let excerpt = module.contentExcerpt, !excerpt.isEmpty {
                                Text(excerpt)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(2)
                            }
                        }

                        // Difficulty indicator
                        HStack {
                            ForEach(1...5, id: \.self) { level in
                                Circle()
                                    .fill(level <= module.difficultyLevel ? cardVariant.primaryColor : Color.gray.opacity(0.3))
                                    .frame(width: 6, height: 6)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 4)
                }
                .padding(16)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(cardVariant.primaryColor.opacity(0.3), lineWidth: 1)
                    )
            )
            .shadow(
                color: cardVariant.primaryColor.opacity(0.2),
                radius: 8, x: 0, y: 4
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Academic Module Detail View (Placeholder)

struct AcademicModuleDetailView: View {
    let module: AcademicContentModule

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text(module.titleEnglish)
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text(module.titleTamil)
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Text(module.fullContent)
                        .font(.body)
                }
                .padding()
            }
            .navigationTitle("Academic Content")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - Academic Card Variant Enum

enum AcademicCardVariant {
    case blue, purple, green, orange, pink

    var primaryColor: Color {
        switch self {
        case .blue: return .blue
        case .purple: return .purple
        case .green: return .green
        case .orange: return .orange
        case .pink: return .pink
        }
    }

    var gradient: LinearGradient {
        switch self {
        case .blue:
            return LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .purple:
            return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .green:
            return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .orange:
            return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .pink:
            return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
}
