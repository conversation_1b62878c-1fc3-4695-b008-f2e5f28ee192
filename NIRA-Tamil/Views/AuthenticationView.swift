//
//  AuthenticationView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 07/07/2025.
//

import SwiftUI
import AuthenticationServices

// MARK: - Comprehensive Authentication View with Multiple Providers

struct AuthenticationView: View {
    @StateObject private var authService = AuthenticationService.shared
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var isSignUp = false
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var showForgotPassword = false
    @State private var animateElements = false

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Glassmorphic Background Gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.1, green: 0.2, blue: 0.4),
                        Color(red: 0.2, green: 0.1, blue: 0.3),
                        Color(red: 0.3, green: 0.2, blue: 0.5)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                // Floating particles animation
                ForEach(0..<20, id: \.self) { _ in
                    Circle()
                        .fill(Color.white.opacity(0.1))
                        .frame(width: CGFloat.random(in: 4...12))
                        .position(
                            x: CGFloat.random(in: 0...geometry.size.width),
                            y: CGFloat.random(in: 0...geometry.size.height)
                        )
                        .animation(
                            Animation.linear(duration: Double.random(in: 10...20))
                                .repeatForever(autoreverses: false),
                            value: UUID()
                        )
                }

                ScrollView {
                    VStack(spacing: 30) {
                        Spacer(minLength: 60)

                        // App Logo and Title
                        VStack(spacing: 16) {
                            // Tamil Script Logo
                            Text("நீரா")
                                .font(.system(size: 48, weight: .bold, design: .rounded))
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [.white, .blue.opacity(0.8)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)

                            Text("NIRA Tamil Learning")
                                .font(.title2)
                                .fontWeight(.medium)
                                .foregroundColor(.white.opacity(0.9))

                            Text("Master the beauty of Tamil language")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.7))
                                .multilineTextAlignment(.center)
                        }
                        .padding(.bottom, 20)

                        // Main Authentication Card
                        VStack(spacing: 24) {
                            // Toggle between Sign In / Sign Up
                            HStack(spacing: 0) {
                                Button(action: {
                                    withAnimation(.spring()) {
                                        isSignUp = false
                                    }
                                }) {
                                    Text("Sign In")
                                        .font(.headline)
                                        .foregroundColor(isSignUp ? .white.opacity(0.6) : .white)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 12)
                                        .background(
                                            RoundedRectangle(cornerRadius: 12)
                                                .fill(isSignUp ? Color.clear : Color.white.opacity(0.2))
                                        )
                                }

                                Button(action: {
                                    withAnimation(.spring()) {
                                        isSignUp = true
                                    }
                                }) {
                                    Text("Sign Up")
                                        .font(.headline)
                                        .foregroundColor(!isSignUp ? .white.opacity(0.6) : .white)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 12)
                                        .background(
                                            RoundedRectangle(cornerRadius: 12)
                                                .fill(!isSignUp ? Color.clear : Color.white.opacity(0.2))
                                        )
                                }
                            }
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.black.opacity(0.2))
                            )
                            .padding(.horizontal, 4)

                            // Email Field
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Email")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.white.opacity(0.8))

                                TextField("Enter your email", text: $email)
                                    .textFieldStyle(GlassmorphicTextFieldStyle())
                                    .keyboardType(.emailAddress)
                                    .autocapitalization(.none)
                                    .disabled(isLoading)
                            }

                            // Password Field
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Password")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.white.opacity(0.8))

                                SecureField("Enter your password", text: $password)
                                    .textFieldStyle(GlassmorphicTextFieldStyle())
                                    .disabled(isLoading)
                            }

                            // Confirm Password (Sign Up only)
                            if isSignUp {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Confirm Password")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.white.opacity(0.8))

                                    SecureField("Confirm your password", text: $confirmPassword)
                                        .textFieldStyle(GlassmorphicTextFieldStyle())
                                        .disabled(isLoading)
                                }
                                .transition(.asymmetric(
                                    insertion: .move(edge: .top).combined(with: .opacity),
                                    removal: .move(edge: .top).combined(with: .opacity)
                                ))
                            }

                            // Forgot Password Link (Sign In only)
                            if !isSignUp {
                                HStack {
                                    Spacer()
                                    Button("Forgot Password?") {
                                        showForgotPassword = true
                                    }
                                    .font(.subheadline)
                                    .foregroundColor(.blue.opacity(0.8))
                                }
                                .transition(.opacity)
                            }

                            // Email/Password Sign In/Up Button
                            Button(action: handleEmailAuthentication) {
                                HStack {
                                    if isLoading {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            .scaleEffect(0.8)
                                    } else {
                                        Image(systemName: "envelope.fill")
                                    }

                                    Text(isSignUp ? "Create Account" : "Sign In")
                                        .fontWeight(.semibold)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(
                                            LinearGradient(
                                                colors: [.blue, .purple],
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
                                )
                                .foregroundColor(.white)
                            }
                            .disabled(isLoading || !isFormValid)
                            .opacity(isFormValid ? 1.0 : 0.6)

                            // Divider
                            HStack {
                                Rectangle()
                                    .fill(Color.white.opacity(0.3))
                                    .frame(height: 1)

                                Text("or continue with")
                                    .font(.subheadline)
                                    .foregroundColor(.white.opacity(0.7))
                                    .padding(.horizontal, 16)

                                Rectangle()
                                    .fill(Color.white.opacity(0.3))
                                    .frame(height: 1)
                            }

                            // Social Authentication Buttons
                            VStack(spacing: 12) {
                                // Apple Sign In
                                SignInWithAppleButton(
                                    onRequest: { request in
                                        request.requestedScopes = [.fullName, .email]
                                    },
                                    onCompletion: handleAppleSignIn
                                )
                                .signInWithAppleButtonStyle(.white)
                                .frame(height: 50)
                                .cornerRadius(16)

                                // Google Sign In
                                SocialAuthButton(
                                    title: "Continue with Google",
                                    icon: "globe",
                                    backgroundColor: .white,
                                    textColor: .black
                                ) {
                                    handleGoogleSignIn()
                                }

                                // Facebook Sign In
                                SocialAuthButton(
                                    title: "Continue with Facebook",
                                    icon: "f.circle.fill",
                                    backgroundColor: Color(red: 0.23, green: 0.35, blue: 0.60),
                                    textColor: .white
                                ) {
                                    handleFacebookSignIn()
                                }

                                // Twitter Sign In
                                SocialAuthButton(
                                    title: "Continue with Twitter",
                                    icon: "bird.fill",
                                    backgroundColor: Color(red: 0.11, green: 0.63, blue: 0.95),
                                    textColor: .white
                                ) {
                                    handleTwitterSignIn()
                                }
                            }
                        }
                        .padding(24)
                        .background(
                            RoundedRectangle(cornerRadius: 24)
                                .fill(
                                    .ultraThinMaterial.opacity(0.8)
                                )
                                .background(
                                    RoundedRectangle(cornerRadius: 24)
                                        .stroke(
                                            LinearGradient(
                                                colors: [.white.opacity(0.3), .clear],
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            ),
                                            lineWidth: 1
                                        )
                                )
                                .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
                        )
                        .padding(.horizontal, 24)

                        Spacer(minLength: 40)
                    }
                }

                // Loading Overlay
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()

                        VStack(spacing: 16) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)

                            Text("Please wait...")
                                .font(.subheadline)
                                .foregroundColor(.white)
                        }
                        .padding(30)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                        )
                    }
                }
            }
        }
        .alert("Authentication Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .sheet(isPresented: $showForgotPassword) {
            ForgotPasswordView()
        }
    }

    // MARK: - Computed Properties

    private var isFormValid: Bool {
        if isSignUp {
            return !email.isEmpty &&
                   !password.isEmpty &&
                   !confirmPassword.isEmpty &&
                   password == confirmPassword &&
                   password.count >= 6 &&
                   email.contains("@")
        } else {
            return !email.isEmpty && !password.isEmpty
        }
    }

    // MARK: - Authentication Methods

    private func handleEmailAuthentication() {
        Task {
            await MainActor.run {
                isLoading = true
            }

            do {
                if isSignUp {
                    try await authService.signUp(email: email, password: password)
                } else {
                    try await authService.signIn(email: email, password: password)
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }

            await MainActor.run {
                isLoading = false
            }
        }
    }

    private func handleAppleSignIn(_ result: Result<ASAuthorization, Error>) {
        Task {
            do {
                try await authService.signInWithApple(result)
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }
        }
    }

    private func handleGoogleSignIn() {
        Task {
            do {
                try await authService.signInWithGoogle()
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }
        }
    }

    private func handleFacebookSignIn() {
        Task {
            do {
                try await authService.signInWithFacebook()
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }
        }
    }

    private func handleTwitterSignIn() {
        Task {
            do {
                try await authService.signInWithTwitter()
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }
        }
    }
}

// MARK: - Supporting Views and Styles

struct GlassmorphicTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 16)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial.opacity(0.6))
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                LinearGradient(
                                    colors: [.white.opacity(0.3), .clear],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
            .foregroundColor(.white)
            .font(.body)
    }
}

struct SocialAuthButton: View {
    let title: String
    let icon: String
    let backgroundColor: Color
    let textColor: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))

                Text(title)
                    .font(.system(size: 16, weight: .medium))
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(backgroundColor)
                    .shadow(color: backgroundColor.opacity(0.3), radius: 4, x: 0, y: 2)
            )
            .foregroundColor(textColor)
        }
    }
}

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authService = AuthenticationService.shared
    @State private var email = ""
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var showError = false
    @State private var errorMessage = ""

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.1, green: 0.2, blue: 0.4),
                        Color(red: 0.2, green: 0.1, blue: 0.3),
                        Color(red: 0.3, green: 0.2, blue: 0.5)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                VStack(spacing: 24) {
                    Spacer()

                    VStack(spacing: 16) {
                        Text("Reset Password")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("Enter your email address and we'll send you a link to reset your password.")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 32)
                    }

                    VStack(spacing: 16) {
                        TextField("Email address", text: $email)
                            .textFieldStyle(GlassmorphicTextFieldStyle())
                            .keyboardType(.emailAddress)
                            .autocapitalization(.none)

                        Button(action: sendResetEmail) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "envelope.fill")
                                }

                                Text("Send Reset Link")
                                    .fontWeight(.semibold)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(
                                        LinearGradient(
                                            colors: [.blue, .purple],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                            )
                            .foregroundColor(.white)
                        }
                        .disabled(email.isEmpty || isLoading)
                        .opacity(email.isEmpty ? 0.6 : 1.0)
                    }
                    .padding(.horizontal, 24)

                    Spacer()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
        .alert("Success", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Password reset link has been sent to your email.")
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }

    private func sendResetEmail() {
        Task {
            await MainActor.run {
                isLoading = true
            }

            do {
                try await authService.resetPassword(email: email)
                await MainActor.run {
                    showSuccess = true
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }

            await MainActor.run {
                isLoading = false
            }
        }
    }
}

// MARK: - Legacy Views (to be removed)

struct LegacyAuthenticationView: View {
    // MARK: - State Variables
    @State private var email = ""
    @State private var password = ""
    @State private var showPassword = false
    @State private var animateElements = false
    @FocusState private var emailFocused: Bool
    @FocusState private var passwordFocused: Bool
    @StateObject private var authService = AuthenticationService.shared

    var body: some View {
        Text("Legacy Authentication View")
    }

    // MARK: - Premium Sign In Form

    private var premiumSignInForm: some View {
        PremiumCard {
            VStack(spacing: Spacing.lg) {
                Text("Welcome Back")
                    .font(.headlineSmall)
                    .fontWeight(.medium)
                    .foregroundColor(Color.authPrimaryGradient[0])

                VStack(spacing: Spacing.lg) {
                    // Email Field
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        Text("Email Address")
                            .font(.labelLarge)
                            .foregroundColor(Color.authPrimaryGradient[0])

                        TextField("Enter your email", text: $email)
                            .textFieldStyle(PremiumTextFieldStyle())
                            .keyboardType(.emailAddress)
                            .textContentType(.emailAddress)
                            .autocapitalization(.none)
                            .focused($emailFocused)
                    }

                    // Password Field
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        Text("Password")
                            .font(.labelLarge)
                            .foregroundColor(Color.authPrimaryGradient[0])

                        HStack {
                            Group {
                                if showPassword {
                                    TextField("Enter your password", text: $password)
                                } else {
                                    SecureField("Enter your password", text: $password)
                                }
                            }
                            .textFieldStyle(PremiumTextFieldStyle())
                            .textContentType(.password)
                            .focused($passwordFocused)

                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    showPassword.toggle()
                                }
                            }) {
                                Image(systemName: showPassword ? "eye.slash.fill" : "eye.fill")
                                    .foregroundColor(Color.authPrimaryGradient[0].opacity(0.6))
                                    .font(.bodyLarge)
                            }
                            .padding(.trailing, Spacing.md)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, Spacing.xl)
        .opacity(animateElements ? 1.0 : 0.0)
        .offset(y: animateElements ? 0 : 30)
        .animation(.easeOut(duration: 1.2).delay(1.0), value: animateElements)
    }

    // MARK: - Actions

    private func signIn() async {
        guard !email.isEmpty, !password.isEmpty else { return }

        do {
            try await authService.signIn(email: email, password: password)
        } catch {
            // Error is handled by the AuthenticationService
            print("❌ Sign in failed: \(error)")
        }
    }

    private func clearForm() {
        email = ""
        password = ""
        showPassword = false
    }
}

// MARK: - Premium Supporting Views

struct PremiumFeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: Spacing.lg) {
            // Premium Icon Container
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: Color.authAccentGradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 44, height: 44)

                Image(systemName: icon)
                    .font(.titleMedium)
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(title)
                    .font(.titleSmall)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.authPrimaryGradient[0])

                Text(description)
                    .font(.bodyMedium)
                    .foregroundColor(Color.authPrimaryGradient[0].opacity(0.7))
                    .lineLimit(2)
            }

            Spacer()
        }
    }
}

struct SignUpView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authService = AuthenticationService.shared
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var showPassword = false
    @State private var showConfirmPassword = false
    @State private var agreedToTerms = false

    var body: some View {
        NavigationView {
            ZStack {
                LinearGradient(
                    colors: [.niraGradientStart, .niraGradientEnd],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 30) {
                        Text("Join the NIRA Community! 🎉")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .padding(.top, 20)

                        Text("Start your language learning journey with millions of learners worldwide!")
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        // Sign Up Form
                        VStack(spacing: 20) {
                            HStack(spacing: 12) {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("First Name")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)

                                    TextField("First name", text: $firstName)
                                        .textFieldStyle(ModernTextFieldStyle())
                                        .textContentType(.givenName)
                                }

                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Last Name")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)

                                    TextField("Last name", text: $lastName)
                                        .textFieldStyle(ModernTextFieldStyle())
                                        .textContentType(.familyName)
                                }
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Email")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                TextField("Enter your email", text: $email)
                                    .textFieldStyle(ModernTextFieldStyle())
                                    .keyboardType(.emailAddress)
                                    .textContentType(.emailAddress)
                                    .autocapitalization(.none)
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Password")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                HStack {
                                    if showPassword {
                                        TextField("Create a password", text: $password)
                                    } else {
                                        SecureField("Create a password", text: $password)
                                    }

                                    Button(action: { showPassword.toggle() }) {
                                        Image(systemName: showPassword ? "eye.slash" : "eye")
                                            .foregroundColor(.gray)
                                    }
                                }
                                .textFieldStyle(ModernTextFieldStyle())
                                .textContentType(.newPassword)
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Confirm Password")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                HStack {
                                    if showConfirmPassword {
                                        TextField("Confirm your password", text: $confirmPassword)
                                    } else {
                                        SecureField("Confirm your password", text: $confirmPassword)
                                    }

                                    Button(action: { showConfirmPassword.toggle() }) {
                                        Image(systemName: showConfirmPassword ? "eye.slash" : "eye")
                                            .foregroundColor(.gray)
                                    }
                                }
                                .textFieldStyle(ModernTextFieldStyle())
                                .textContentType(.newPassword)
                            }

                            // Terms Agreement
                            HStack(alignment: .top, spacing: 12) {
                                Button(action: { agreedToTerms.toggle() }) {
                                    Image(systemName: agreedToTerms ? "checkmark.square.fill" : "square")
                                        .font(.title3)
                                        .foregroundColor(agreedToTerms ? .green : .white.opacity(0.7))
                                }

                                Text("I agree to the Terms of Service and Privacy Policy")
                                    .font(.subheadline)
                                    .foregroundColor(.white.opacity(0.9))
                                    .multilineTextAlignment(.leading)

                                Spacer()
                            }
                        }
                        .padding(.horizontal, 32)

                        Button("Create Account 🚀") {
                            Task {
                                await signUp()
                            }
                        }
                        .buttonStyle(AuthPrimaryButtonStyle())
                        .disabled(!isFormValid || authService.isLoading)
                        .padding(.horizontal, 32)

                        Spacer()
                    }
                    .padding()
                }

                if authService.isLoading {
                    LoadingOverlay()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }

    private var isFormValid: Bool {
        !email.isEmpty &&
        !password.isEmpty &&
        !confirmPassword.isEmpty &&
        !firstName.isEmpty &&
        !lastName.isEmpty &&
        password == confirmPassword &&
        password.count >= 6 &&
        agreedToTerms
    }

    private func signUp() async {
        guard isFormValid else { return }

        do {
            try await authService.signUp(
                email: email,
                password: password,
                firstName: firstName,
                lastName: lastName
            )
            dismiss()
        } catch {
            // Error is handled by the AuthenticationService
            print("❌ Sign up failed: \(error)")
        }
    }
}

struct PasswordResetView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authService = AuthenticationService.shared
    @State private var email = ""
    @State private var resetSent = false

    var body: some View {
        NavigationView {
            ZStack {
                LinearGradient(
                    colors: [.niraGradientStart, .niraGradientEnd],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                VStack(spacing: 30) {
                    if resetSent {
                        VStack(spacing: 20) {
                            Image(systemName: "envelope.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.white)

                            Text("Check Your Email")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            Text("We've sent a password reset link to \(email)")
                                .font(.body)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)

                            Button("Done") {
                                dismiss()
                            }
                            .buttonStyle(AuthPrimaryButtonStyle())
                        }
                    } else {
                        VStack(spacing: 30) {
                            VStack(spacing: 20) {
                                Image(systemName: "lock.rotation")
                                    .font(.system(size: 60))
                                    .foregroundColor(.white)

                                Text("Reset Password")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)

                                Text("Enter your email address and we'll send you a link to reset your password.")
                                    .font(.body)
                                    .foregroundColor(.white.opacity(0.9))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Email")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                TextField("Enter your email", text: $email)
                                    .textFieldStyle(ModernTextFieldStyle())
                                    .keyboardType(.emailAddress)
                                    .textContentType(.emailAddress)
                                    .autocapitalization(.none)
                            }
                            .padding(.horizontal, 32)

                            Button("Send Reset Link") {
                                Task {
                                    await resetPassword()
                                }
                            }
                            .buttonStyle(AuthPrimaryButtonStyle())
                            .disabled(email.isEmpty || authService.isLoading)
                            .padding(.horizontal, 32)
                        }
                    }

                    Spacer()
                }
                .padding()

                if authService.isLoading {
                    LoadingOverlay()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }

    private func resetPassword() async {
        guard !email.isEmpty else { return }

        do {
            try await authService.resetPassword(email: email)
            resetSent = true
        } catch {
            // Error is handled by the AuthenticationService
            print("❌ Password reset failed: \(error)")
        }
    }
}

// MARK: - Custom Styles

struct AuthPrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .font(.headline)
            .fontWeight(.bold)
            .foregroundColor(.niraGradientStart)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.white)
            .cornerRadius(25)
            .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct AuthSecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.white.opacity(0.2))
            .cornerRadius(25)
            .overlay(
                RoundedRectangle(cornerRadius: 25)
                    .stroke(Color.white.opacity(0.5), lineWidth: 2)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct ModernTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding()
            .background(Color.white.opacity(0.9))
            .cornerRadius(12)
            .font(.body)
    }
}

struct LoadingOverlay: View {
    var body: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.5)

                Text("Please wait...")
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(30)
            .background(Color.black.opacity(0.7))
            .cornerRadius(20)
        }
    }
}

#Preview {
    AuthenticationView()
}