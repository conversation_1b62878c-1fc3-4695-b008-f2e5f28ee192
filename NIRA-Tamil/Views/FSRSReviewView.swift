import SwiftUI

struct FSRSReviewView: View {
    @StateObject private var fsrsService = FSRSService()
    @State private var dueCards: [FSRSCard] = []
    @State private var currentCardIndex = 0
    @State private var isLoading = true
    @State private var showAnswer = false
    @State private var reviewStats: ReviewViewStats?
    @State private var currentStreak: Int = 0
    
    let userId: String
    
    var body: some View {
        NavigationView {
            ZStack {
                // Gradient background
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                if isLoading {
                    loadingView
                } else if dueCards.isEmpty {
                    noReviewsView
                } else {
                    reviewContentView
                }
            }
            .navigationTitle("Daily Reviews")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    statsButton
                }
            }
            .task {
                await loadDueReviews()
                await loadReviewStats()
                await loadCurrentStreak()
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading your reviews...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - No Reviews View
    private var noReviewsView: some View {
        VStack(spacing: 24) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("All caught up! 🎉")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("No reviews due today. Come back tomorrow for your next learning session.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if currentStreak > 0 {
                streakDisplay
            }
            
            Spacer()
        }
        .padding()
    }
    
    // MARK: - Review Content View
    private var reviewContentView: some View {
        VStack(spacing: 20) {
            // Progress indicator
            progressIndicator
            
            // Card content
            if currentCardIndex < dueCards.count {
                let currentCard = dueCards[currentCardIndex]
                
                Spacer()
                
                reviewCard(for: currentCard)
                
                Spacer()
                
                // Rating buttons (only show when answer is revealed)
                if showAnswer {
                    ratingButtons
                }
            }
        }
        .padding()
    }
    
    // MARK: - Progress Indicator
    private var progressIndicator: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Review \(currentCardIndex + 1) of \(dueCards.count)")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(dueCards.count - currentCardIndex - 1) remaining")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: Double(currentCardIndex), total: Double(dueCards.count))
                .progressViewStyle(LinearProgressViewStyle())
                .scaleEffect(y: 2)
        }
    }
    
    // MARK: - Review Card
    private func reviewCard(for card: FSRSCard) -> some View {
        VStack(spacing: 20) {
            // Card front (always visible)
            CardContentView(
                contentId: card.learningItemId.uuidString,
                showAnswer: showAnswer
            )
            .frame(maxHeight: 400)
            
            // Show/Hide answer button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showAnswer.toggle()
                }
            }) {
                Text(showAnswer ? "Hide Answer" : "Show Answer")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 40)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.blue)
                    )
            }
            .scaleEffect(showAnswer ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: showAnswer)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }
    
    // MARK: - Rating Buttons
    private var ratingButtons: some View {
        VStack(spacing: 16) {
            Text("How well did you know this?")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack(spacing: 12) {
                ForEach(ReviewRating.allCases, id: \.self) { rating in
                    ratingButton(for: rating)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.secondarySystemBackground))
        )
    }
    
    private func ratingButton(for rating: ReviewRating) -> some View {
        Button(action: {
            Task {
                await processRating(rating)
            }
        }) {
            VStack(spacing: 4) {
                Text(rating.emoji)
                    .font(.title2)
                
                Text(rating.title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .frame(width: 70, height: 60)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(rating.color.opacity(0.2))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(rating.color, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Streak Display
    private var streakDisplay: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                Text("\(currentStreak) day streak!")
                    .fontWeight(.semibold)
            }
            .font(.title3)
            
            Text("Keep it up! Daily reviews build stronger memories.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.orange.opacity(0.1))
        )
    }
    
    // MARK: - Stats Button
    private var statsButton: some View {
        Button(action: {
            // TODO: Navigate to analytics view
        }) {
            Image(systemName: "chart.bar.fill")
                .font(.title3)
        }
    }
    
    // MARK: - Functions
    private func loadDueReviews() async {
        isLoading = true
        defer { isLoading = false }
        
        // Mock implementation - TODO: Implement getDueReviews(for:) in FSRSService
        dueCards = [
            FSRSCard(
                id: UUID(),
                learningItemId: UUID(),
                due: Date(),
                stability: 5.0,
                difficulty: 3.0,
                elapsedDays: 1,
                scheduledDays: 1,
                reps: 3,
                lapses: 0,
                state: .review,
                lastReview: Date(),
                retrievability: 0.8
            )
        ]
    }
    
    private func loadReviewStats() async {
        // Mock implementation - TODO: Implement getReviewStats(for:) in FSRSService
        reviewStats = ReviewViewStats(
            totalReviews: 245,
            retentionRate: 0.87,
            averageInterval: 12.5,
            completedToday: 8
        )
    }
    
    private func loadCurrentStreak() async {
        // Mock implementation - TODO: Implement getCurrentStreak(for:) in FSRSService
        currentStreak = 7
    }
    
    private func processRating(_ rating: ReviewRating) async {
        guard currentCardIndex < dueCards.count else { return }
        
        let _ = dueCards[currentCardIndex] // Will be used when processReview is implemented

        // TODO: Implement processReview method in FSRSService
        // For now, simulate the review processing
            
        // Move to next card or finish
        withAnimation(.easeInOut(duration: 0.5)) {
            if currentCardIndex < dueCards.count - 1 {
                currentCardIndex += 1
                showAnswer = false
            } else {
                // All reviews completed
                Task {
                    await loadCurrentStreak() // Update streak
                }
            }
        }
    }
}

// MARK: - Supporting Types  
extension ReviewRating {
    var emoji: String {
        switch self {
        case .again: return "😓"
        case .hard: return "😐"
        case .good: return "😊"
        case .easy: return "😄"
        }
    }
    
    var title: String {
        switch self {
        case .again: return "Again"
        case .hard: return "Hard"
        case .good: return "Good"
        case .easy: return "Easy"
        }
    }
    
    var color: Color {
        switch self {
        case .again: return .red
        case .hard: return .orange
        case .good: return .green
        case .easy: return .blue
        }
    }
}

// MARK: - Card Content View
struct CardContentView: View {
    let contentId: String
    let showAnswer: Bool
    
    @State private var content: ReviewContent?
    @State private var isLoading = true
    
    var body: some View {
        Group {
            if isLoading {
                ProgressView("Loading content...")
            } else if let content = content {
                VStack(spacing: 16) {
                    // Question/Front side
                    Text(content.question)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                    
                    if showAnswer {
                        Divider()
                        
                        // Answer/Back side
                        Text(content.answer)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        if let example = content.example {
                            Text("Example: \(example)")
                                .font(.caption)
                                .italic()
                                .foregroundColor(.secondary)
                                .padding(.top, 8)
                        }
                    }
                }
            } else {
                Text("Content not found")
                    .foregroundColor(.secondary)
            }
        }
        .task {
            await loadContent()
        }
    }
    
    private func loadContent() async {
        isLoading = true
        defer { isLoading = false }
        
        // TODO: Load actual content from database
        // For now, using mock data
        content = ReviewContent(
            id: contentId,
            question: "வணக்கம்",
            answer: "Hello (formal greeting)",
            example: "வணக்கம், எப்படி இருக்கீங்க?"
        )
    }
}

// MARK: - Mock Data Structure
struct ReviewContent {
    let id: String
    let question: String
    let answer: String
    let example: String?
}

struct ReviewViewStats {
    let totalReviews: Int
    let retentionRate: Double
    let averageInterval: Double
    let completedToday: Int
}

#Preview {
    FSRSReviewView(userId: "test-user-id")
} 