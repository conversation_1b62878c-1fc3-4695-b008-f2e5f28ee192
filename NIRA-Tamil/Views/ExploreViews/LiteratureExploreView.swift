//
//  LiteratureExploreView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import SwiftUI

struct LiteratureExploreView: View {
    @StateObject private var literatureService = LiteratureService.shared
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("இலக்கியம்")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Explore the rich heritage of Tamil literature")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Featured Literature Card
                if let featuredContent = literatureService.featuredContent {
                    modernLiteratureCard(content: featuredContent, isFeatured: true)
                }
                
                // Literature Categories Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Explore by Category")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(literatureService.categories, id: \.id) { category in
                            modernCategoryCard(category: category)
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Recent Literature Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Recent Additions")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(literatureService.recentContent.prefix(3), id: \.id) { content in
                        modernLiteratureCard(content: content, isFeatured: false)
                    }
                }
            }
            .padding(.bottom, 100)
        }
        .task {
            await literatureService.loadContent()
        }
    }
    
    // MARK: - Modern Literature Card
    
    private func modernLiteratureCard(content: LiteratureContent, isFeatured: Bool) -> some View {
        NavigationLink(destination: LiteratureDetailView(content: content)) {
            ZStack {
                if isFeatured {
                    // Animated glowing orb background for featured content
                    Circle()
                        .fill(literatureGradient(for: content.category))
                        .frame(width: 160, height: 160)
                        .blur(radius: 40)
                        .opacity(0.3)
                        .offset(x: 60, y: -60)
                }

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(literatureGradient(for: content.category))
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header with content info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(isFeatured ? "Featured Literature" : content.category.name)
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                if let author = content.author {
                                    Text(author)
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }

                            Spacer()

                            if isFeatured {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.title3)
                            }
                        }

                        // Title
                        Text(content.title)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        // Tamil Title with Audio & Romanization
                        TamilTextWithAudio(
                            tamilText: content.titleTamil,
                            romanization: content.romanization,
                            contentId: content.id,
                            contentType: .literature,
                            voiceType: .female,
                            showRomanization: true,
                            fontSize: .subheadline,
                            alignment: .leading,
                            maxLines: 1
                        )

                        // Content preview
                        if let preview = content.contentPreview {
                            Text(preview)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }

                        // Metrics row
                        HStack(spacing: 16) {
                            LiteratureMetricItem(
                                icon: "book.fill", 
                                label: "Reading", 
                                value: "\(content.readingTimeMinutes)m",
                                gradient: literatureGradient(for: content.category)
                            )
                            LiteratureMetricItem(
                                icon: "tag.fill", 
                                label: "Level", 
                                value: content.difficultyLevel.capitalized,
                                gradient: literatureGradient(for: content.category)
                            )

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("Read More")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
    
    // MARK: - Modern Category Card
    
    private func modernCategoryCard(category: LiteratureCategory) -> some View {
        NavigationLink(destination: LiteratureCategoryView(category: category)) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(literatureGradient(for: category))
                    .frame(width: 80, height: 80)
                    .blur(radius: 20)
                    .opacity(0.3)
                    .offset(x: 30, y: -30)

                VStack(spacing: 12) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(literatureGradient(for: category))
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    VStack(spacing: 8) {
                        // Emoji icon
                        Text(category.emoji)
                            .font(.title)
                            .frame(width: 40, height: 40)

                        // Title
                        Text(category.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)

                        // Count
                        Text("\(category.contentCount) works")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(16)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    // MARK: - Helper Functions
    
    private func literatureGradient(for category: LiteratureCategory) -> LinearGradient {
        switch category.colorTheme {
        case "blue": return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "orange": return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "purple": return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "green": return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
}

// MARK: - Literature Metric Item

struct LiteratureMetricItem: View {
    let icon: String
    let label: String
    let value: String
    let gradient: LinearGradient

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(gradient)
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

#Preview {
    LiteratureExploreView()
}
