import SwiftUI

struct NewsExploreView: View {
    @StateObject private var newsService = TamilNewsService.shared
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("தமிழ்நாடு செய்திகள்")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Stay updated with Tamil Nadu news")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Featured News
                VStack(alignment: .leading, spacing: 12) {
                    Text("Featured News")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(newsService.breakingNews.prefix(3), id: \.id) { article in
                        modernNewsCard(article: article, isFeatured: true)
                    }
                }
                
                // Latest News
                VStack(alignment: .leading, spacing: 12) {
                    Text("Latest Updates")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(newsService.latestNews.prefix(5), id: \.id) { article in
                        modernNewsCard(article: article, isFeatured: false)
                    }
                }
            }
            .padding(.bottom, 100)
        }
        .task {
            await newsService.loadLatestNews()
        }
    }
    
    // MARK: - Modern News Card

    private func modernNewsCard(article: TamilNewsArticle, isFeatured: Bool) -> some View {
        Button(action: {
            // Navigate to article detail
        }) {
            ZStack {
                if isFeatured {
                    // Animated glowing orb background for featured articles
                    Circle()
                        .fill(newsGradient)
                        .frame(width: 120, height: 120)
                        .blur(radius: 30)
                        .opacity(0.3)
                        .offset(x: 50, y: -50)
                }

                VStack(alignment: .leading, spacing: 12) {
                    if isFeatured {
                        // Top gradient stripe for featured articles
                        Rectangle()
                            .fill(newsGradient)
                            .frame(height: 6)
                            .clipShape(RoundedRectangle(cornerRadius: 3))
                    }

                    VStack(alignment: .leading, spacing: 12) {
                        // Category and time
                        HStack {
                            Text(article.category.rawValue)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(isFeatured ? newsGradient : LinearGradient(colors: [.blue], startPoint: .leading, endPoint: .trailing))
                                )

                            Spacer()

                            Text(formatTimeAgo(article.publishedDate))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        // Title
                        Text(article.title)
                            .font(isFeatured ? .title3 : .subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(3)

                        // Tamil title if available
                        if let tamilTitle = article.tamilTitle {
                            Text(tamilTitle)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }

                        // Summary
                        Text(article.summary)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(isFeatured ? 3 : 2)

                        // Reading time and source
                        HStack {
                            HStack(spacing: 4) {
                                Image(systemName: "clock")
                                    .font(.caption2)
                                Text("\(article.readingTime) min read")
                                    .font(.caption2)
                            }
                            .foregroundColor(.secondary)

                            Spacer()

                            Text(article.source)
                                .font(.caption2)
                                .foregroundColor(.primary)
                                .fontWeight(.medium)
                        }
                    }
                }
                .padding(16)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private var newsGradient: LinearGradient {
        LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
    }

    // MARK: - Helper Functions

    private func formatTimeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

#Preview {
    NewsExploreView()
}
