import SwiftUI

struct ThirukkuralExploreView: View {
    @StateObject private var thirukkuralService = ThirukkuralService.shared
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("திருக்குறள்")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Ancient Tamil wisdom for modern life")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Today's Thirukkural Card
                if let todayKural = thirukkuralService.todayKural {
                    modernThirukkuralCard(kural: todayKural, isToday: true)
                }
                
                // Categories Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Explore by Category")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(thirukkuralCategories, id: \.name) { category in
                            modernCategoryCard(category: category)
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Recent Kurals Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Recent Kurals")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(thirukkuralService.favoriteKurals.prefix(3), id: \.id) { kural in
                        modernThirukkuralCard(kural: kural, isToday: false)
                    }
                }
            }
            .padding(.bottom, 100)
        }
        .task {
            await thirukkuralService.loadTodayKural()
        }
    }
    
    // MARK: - Modern Thirukkural Card

    private func modernThirukkuralCard(kural: Thirukkural, isToday: Bool) -> some View {
        Button(action: {
            // Navigate to detailed view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(thirukkuralGradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(thirukkuralGradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header with lesson info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(isToday ? "Today's Wisdom" : "Kural \(kural.number)")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(kural.chapter)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            if isToday {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.title3)
                            }
                        }

                        // Tamil Text
                        Text(kural.tamilText)
                            .font(.title3)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                            .lineLimit(2)

                        // English Translation
                        Text(kural.englishTranslation)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(3)

                        // Metrics row
                        HStack(spacing: 16) {
                            MetricItem(icon: "heart.fill", label: "Wisdom", value: kural.number, variant: .thirukkural)
                            MetricItem(icon: "book.fill", label: "Chapter", value: Int(kural.chapter.prefix(2)) ?? 1, variant: .thirukkural)

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("Read More")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private var thirukkuralGradient: LinearGradient {
        LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
    }
    
    // MARK: - Modern Category Card

    private func modernCategoryCard(category: ThirukkuralCategory) -> some View {
        Button(action: {
            // Navigate to category view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(categoryGradient(for: category))
                    .frame(width: 80, height: 80)
                    .blur(radius: 20)
                    .opacity(0.3)
                    .offset(x: 30, y: -30)

                VStack(spacing: 12) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(categoryGradient(for: category))
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    VStack(spacing: 8) {
                        // Icon with gradient background
                        Image(systemName: category.icon)
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(categoryGradient(for: category))
                            .clipShape(Circle())

                        // Title
                        Text(category.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)

                        // Count
                        Text("\(category.count) kurals")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(16)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }

    private func categoryGradient(for category: ThirukkuralCategory) -> LinearGradient {
        switch category.name {
        case "Virtue": return CardVariant.blue.gradient
        case "Wealth": return CardVariant.green.gradient
        case "Love": return CardVariant.pink.gradient
        case "Friendship": return CardVariant.purple.gradient
        case "Wisdom": return CardVariant.orange.gradient
        case "Justice": return CardVariant.blue.gradient
        default: return CardVariant.orange.gradient
        }
    }
}

// MARK: - Sample Data

private let thirukkuralCategories = [
    ThirukkuralCategory(name: "Virtue", icon: "heart.fill", count: 380),
    ThirukkuralCategory(name: "Wealth", icon: "dollarsign.circle.fill", count: 700),
    ThirukkuralCategory(name: "Love", icon: "heart.circle.fill", count: 250),
    ThirukkuralCategory(name: "Friendship", icon: "person.2.fill", count: 100),
    ThirukkuralCategory(name: "Wisdom", icon: "brain.head.profile", count: 200),
    ThirukkuralCategory(name: "Justice", icon: "scale.3d", count: 150)
]

struct ThirukkuralCategory {
    let name: String
    let icon: String
    let count: Int
}



#Preview {
    ThirukkuralExploreView()
}
