//
//  LiteratureCategoryView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 08/07/2025.
//

import SwiftUI

struct LiteratureCategoryView: View {
    let category: LiteratureCategory
    @StateObject private var literatureService = LiteratureService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var selectedSortOption: SortOption = .newest
    @State private var selectedDifficulty: DifficultyFilter = .all
    @State private var showingFilters = false
    
    // Screen size detection for optimization
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    @Environment(\.verticalSizeClass) var verticalSizeClass
    
    private var isCompactScreen: Bool {
        horizontalSizeClass == .compact || verticalSizeClass == .compact
    }
    
    // Filtered and sorted content
    private var filteredContent: [LiteratureContent] {
        let categoryContent = literatureService.getContent(for: category.id)
        
        var filtered = categoryContent
        
        // Apply search filter
        if !searchText.isEmpty {
            filtered = filtered.filter { content in
                content.title.localizedCaseInsensitiveContains(searchText) ||
                content.titleTamil.contains(searchText) ||
                content.author?.localizedCaseInsensitiveContains(searchText) == true ||
                content.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }
        
        // Apply difficulty filter
        if selectedDifficulty != .all {
            filtered = filtered.filter { $0.difficultyLevel == selectedDifficulty.rawValue }
        }
        
        // Apply sorting
        switch selectedSortOption {
        case .newest:
            filtered = filtered.sorted { $0.viewCount > $1.viewCount } // Using viewCount as proxy for recency
        case .oldest:
            filtered = filtered.sorted { $0.viewCount < $1.viewCount }
        case .alphabetical:
            filtered = filtered.sorted { $0.title < $1.title }
        case .difficulty:
            filtered = filtered.sorted { $0.difficultyLevel < $1.difficultyLevel }
        case .readingTime:
            filtered = filtered.sorted { $0.readingTimeMinutes < $1.readingTimeMinutes }
        }
        
        return filtered
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Custom header
                headerView
                
                // Search and filter bar
                searchAndFilterBar
                
                // Content list
                if filteredContent.isEmpty {
                    emptyStateView
                } else {
                    contentListView
                }
            }
            .background(
                LinearGradient(
                    colors: [categoryGradient.0.opacity(0.02), categoryGradient.1.opacity(0.02)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingFilters) {
            filtersSheet
        }
        .task {
            await literatureService.loadContent()
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        VStack(spacing: 16) {
            // Navigation and title
            HStack {
                // Back button
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.systemBackground.opacity(0.8))
                                .shadow(color: Color.gray.opacity(0.2), radius: 4, x: 0, y: 2)
                        )
                }
                
                Spacer()
                
                // Category info
                VStack(spacing: 4) {
                    Text(category.emoji)
                        .font(.title)
                    
                    Text(category.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(category.nameTamil)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Filter button
                Button(action: { showingFilters = true }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .font(.title2)
                        .foregroundColor(.primary)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.systemBackground.opacity(0.8))
                                .shadow(color: Color.gray.opacity(0.2), radius: 4, x: 0, y: 2)
                        )
                }
            }
            
            // Category description
            if !category.description.isEmpty {
                Text(category.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            // Content count
            Text("\(filteredContent.count) of \(category.contentCount) items")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(Color.gray.opacity(0.1))
                )
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // MARK: - Search and Filter Bar
    
    private var searchAndFilterBar: some View {
        HStack(spacing: 12) {
            // Search field
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search literature...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
            
            // Sort menu
            Menu {
                ForEach(SortOption.allCases, id: \.self) { option in
                    Button(action: { selectedSortOption = option }) {
                        HStack {
                            Text(option.displayName)
                            if selectedSortOption == option {
                                Image(systemName: "checkmark")
                            }
                        }
                    }
                }
            } label: {
                HStack(spacing: 4) {
                    Image(systemName: "arrow.up.arrow.down")
                        .font(.caption)
                    Text("Sort")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                )
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
    
    // MARK: - Content List View
    
    private var contentListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredContent, id: \.id) { content in
                    NavigationLink(destination: LiteratureDetailView(content: content)) {
                        LiteratureCategoryItemCard(content: content, category: category)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 100)
        }
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "book.closed")
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("No Literature Found")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("Try adjusting your search or filters")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("Clear Filters") {
                searchText = ""
                selectedDifficulty = .all
                selectedSortOption = .newest
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(colors: [categoryGradient.0, categoryGradient.1], startPoint: .leading, endPoint: .trailing))
            )
            .foregroundColor(.white)
            .fontWeight(.medium)
            
            Spacer()
        }
        .padding()
    }
    
    // MARK: - Helper Properties
    
    private var categoryGradient: (Color, Color) {
        switch category.colorTheme {
        case "blue": return (.blue, .cyan)
        case "orange": return (.orange, .yellow)
        case "purple": return (.purple, .pink)
        case "green": return (.green, .mint)
        default: return (.blue, .cyan)
        }
    }
}

// MARK: - Sort and Filter Options

enum SortOption: String, CaseIterable {
    case newest = "newest"
    case oldest = "oldest"
    case alphabetical = "alphabetical"
    case difficulty = "difficulty"
    case readingTime = "reading_time"
    
    var displayName: String {
        switch self {
        case .newest: return "Newest First"
        case .oldest: return "Oldest First"
        case .alphabetical: return "A to Z"
        case .difficulty: return "By Difficulty"
        case .readingTime: return "Reading Time"
        }
    }
}

enum DifficultyFilter: String, CaseIterable {
    case all = "all"
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"

    var displayName: String {
        switch self {
        case .all: return "All Levels"
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
}

// MARK: - Extensions

extension LiteratureCategoryView {

    // MARK: - Filters Sheet

    var filtersSheet: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Difficulty filter
                VStack(alignment: .leading, spacing: 16) {
                    Text("Difficulty Level")
                        .font(.headline)
                        .fontWeight(.semibold)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        ForEach(DifficultyFilter.allCases, id: \.self) { difficulty in
                            Button(action: { selectedDifficulty = difficulty }) {
                                HStack {
                                    Text(difficulty.displayName)
                                        .font(.subheadline)
                                        .fontWeight(.medium)

                                    Spacer()

                                    if selectedDifficulty == difficulty {
                                        Image(systemName: "checkmark")
                                            .font(.subheadline)
                                            .foregroundColor(.white)
                                    }
                                }
                                .foregroundColor(selectedDifficulty == difficulty ? .white : .primary)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(selectedDifficulty == difficulty ?
                                              LinearGradient(colors: [categoryGradient.0, categoryGradient.1], startPoint: .leading, endPoint: .trailing) :
                                              LinearGradient(colors: [Color.gray.opacity(0.1)], startPoint: .leading, endPoint: .trailing)
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }

                // Sort options
                VStack(alignment: .leading, spacing: 16) {
                    Text("Sort By")
                        .font(.headline)
                        .fontWeight(.semibold)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: 8) {
                        ForEach(SortOption.allCases, id: \.self) { option in
                            Button(action: { selectedSortOption = option }) {
                                HStack {
                                    Text(option.displayName)
                                        .font(.subheadline)
                                        .fontWeight(.medium)

                                    Spacer()

                                    if selectedSortOption == option {
                                        Image(systemName: "checkmark")
                                            .font(.subheadline)
                                            .foregroundColor(.white)
                                    }
                                }
                                .foregroundColor(selectedSortOption == option ? .white : .primary)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(selectedSortOption == option ?
                                              LinearGradient(colors: [categoryGradient.0, categoryGradient.1], startPoint: .leading, endPoint: .trailing) :
                                              LinearGradient(colors: [Color.gray.opacity(0.1)], startPoint: .leading, endPoint: .trailing)
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }

                Spacer()

                // Reset filters button
                Button("Reset All Filters") {
                    selectedDifficulty = .all
                    selectedSortOption = .newest
                    searchText = ""
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                )
            }
            .padding()
            .navigationTitle("Filters")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") { showingFilters = false })
        }
    }
}

// MARK: - Literature Category Item Card

struct LiteratureCategoryItemCard: View {
    let content: LiteratureContent
    let category: LiteratureCategory
    @StateObject private var readingProgressService = ReadingProgressService.shared

    private var categoryGradient: LinearGradient {
        switch category.colorTheme {
        case "blue": return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "orange": return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "purple": return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "green": return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Top gradient stripe
            Rectangle()
                .fill(categoryGradient)
                .frame(height: 4)
                .clipShape(RoundedRectangle(cornerRadius: 2))

            VStack(alignment: .leading, spacing: 12) {
                // Header with bookmark and featured status
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        if content.isFeatured {
                            HStack(spacing: 4) {
                                Image(systemName: "star.fill")
                                    .font(.caption)
                                    .foregroundColor(.yellow)
                                Text("Featured")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.yellow)
                            }
                        }

                        if let author = content.author {
                            Text(author)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    // Reading progress indicator
                    let progress = readingProgressService.getProgress(contentId: content.id)
                    if progress > 0 {
                        ProgressView(value: progress, total: 1.0)
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                            .frame(width: 24, height: 24)
                    }

                    // Bookmark indicator
                    if readingProgressService.isBookmarked(contentId: content.id) {
                        Image(systemName: "bookmark.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                    }
                }

                // Title
                Text(content.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .lineLimit(2)

                // Tamil title with romanization
                VStack(alignment: .leading, spacing: 2) {
                    Text(content.titleTamil)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    if let romanization = content.romanization {
                        Text(romanization)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                            .lineLimit(1)
                    }
                }

                // Content preview
                if let preview = content.contentPreview {
                    Text(preview)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                // Metrics row
                HStack(spacing: 16) {
                    // Reading time
                    HStack(spacing: 4) {
                        Image(systemName: "clock.fill")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(content.readingTimeMinutes)m")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // Difficulty
                    Text(content.difficultyLevel.capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(categoryGradient)
                        )

                    Spacer()

                    // View count
                    HStack(spacing: 4) {
                        Image(systemName: "eye.fill")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(content.viewCount)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}



#Preview {
    LiteratureCategoryView(
        category: LiteratureCategory(
            id: UUID(),
            name: "Thirukkural",
            nameTamil: "திருக்குறள்",
            description: "Ancient Tamil wisdom literature by Thiruvalluvar",
            descriptionTamil: "திருவள்ளுவர் அருளிய பழந்தமிழ் நீதி நூல்",
            emoji: "📜",
            colorTheme: "blue",
            sortOrder: 1,
            isActive: true,
            contentCount: 10
        )
    )
}
