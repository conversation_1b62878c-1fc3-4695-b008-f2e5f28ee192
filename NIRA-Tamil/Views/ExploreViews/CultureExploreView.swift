import SwiftUI

struct CultureExploreView: View {
    @StateObject private var cultureService = CulturalInsightsService.shared
    @StateObject private var enhancedCultureService = EnhancedCultureService.shared

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("கலாச்சாரம்")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Explore Tamil culture and heritage")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Today's Cultural Insight
                modernCultureCard(insight: cultureService.todayInsight, isToday: true)
                
                // Cultural Categories
                VStack(alignment: .leading, spacing: 12) {
                    Text("Explore by Category")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(enhancedCultureService.categories, id: \.id) { category in
                            modernEnhancedCategoryCard(category: category)
                        }
                    }
                    .padding(.horizontal)
                }

                // Featured Cinema Content
                if !enhancedCultureService.featuredCinemaContent.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Tamil Cinema")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.horizontal)

                        ForEach(enhancedCultureService.featuredCinemaContent.prefix(2), id: \.id) { content in
                            modernCinemaCard(content: content)
                        }
                    }
                }

                // Featured Sports Content
                if !enhancedCultureService.featuredSportsContent.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Tamil Sports")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.horizontal)

                        ForEach(enhancedCultureService.featuredSportsContent.prefix(2), id: \.id) { content in
                            modernSportsCard(content: content)
                        }
                    }
                }
                
                // Recent Insights
                VStack(alignment: .leading, spacing: 12) {
                    Text("Recent Insights")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(cultureService.featuredInsights.prefix(3), id: \.id) { insight in
                        modernCultureCard(insight: insight, isToday: false)
                    }
                }
            }
            .padding(.bottom, 100)
        }
        .onAppear {
            Task.detached {
                await cultureService.loadTodayInsight()
                await enhancedCultureService.loadContent()
            }
        }
    }
    
    // MARK: - Modern Culture Card

    private func modernCultureCard(insight: TamilCulturalInsight, isToday: Bool) -> some View {
        Button(action: {
            // Navigate to insight detail
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(cultureGradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(cultureGradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header with insight info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(isToday ? "Today's Insight" : "Cultural Insight")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(insight.difficulty.rawValue)
                                    .font(.caption2)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(cultureGradient)
                                    .clipShape(Capsule())
                            }

                            Spacer()

                            if isToday {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.title3)
                            }
                        }

                        // Title
                        Text(insight.title)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        // Content preview
                        Text(insight.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(3)

                        // Metrics row
                        HStack(spacing: 16) {
                            CultureMetricItem(icon: "book.fill", label: "Reading", value: "\(insight.readingTime)m")
                            CultureMetricItem(icon: "tag.fill", label: "Tags", value: "\(insight.tags.count)")

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("Read More")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private var cultureGradient: LinearGradient {
        LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
    }

    // MARK: - Modern Enhanced Category Card

    private func modernEnhancedCategoryCard(category: EnhancedCulturalCategory) -> some View {
        Button(action: {
            // Navigate to category detail view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(categoryGradient(for: category.colorTheme))
                    .frame(width: 80, height: 80)
                    .blur(radius: 20)
                    .opacity(0.3)
                    .offset(x: 30, y: -30)

                VStack(spacing: 12) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(categoryGradient(for: category.colorTheme))
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    VStack(spacing: 8) {
                        // Emoji icon
                        Text(category.emoji)
                            .font(.title)
                            .frame(width: 40, height: 40)

                        // Title
                        Text(category.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)

                        // Count
                        Text("\(category.contentCount) items")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(16)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }

    // MARK: - Modern Cinema Card

    private func modernCinemaCard(content: CinemaContent) -> some View {
        Button(action: {
            // Navigate to cinema detail view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 120, height: 120)
                    .blur(radius: 30)
                    .opacity(0.3)
                    .offset(x: 50, y: -50)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing))
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Tamil Cinema")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(content.contentType.capitalized)
                                    .font(.caption2)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing))
                                    .clipShape(Capsule())
                            }

                            Spacer()

                            // Audio button
                            if content.audioUrl != nil {
                                Button(action: {
                                    // Play audio
                                }) {
                                    Image(systemName: "speaker.wave.2.fill")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                        .frame(width: 24, height: 24)
                                        .background(LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing))
                                        .clipShape(Circle())
                                }
                            }
                        }

                        // Title
                        Text(content.title)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        // Tamil title with romanization
                        if let titleTamil = content.titleTamil {
                            VStack(alignment: .leading, spacing: 2) {
                                Text(titleTamil)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)
                                    .lineLimit(1)

                                if let romanization = content.romanization {
                                    Text(romanization)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .italic()
                                }
                            }
                        }

                        // Description
                        Text(content.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)

                        // Metrics row
                        HStack(spacing: 16) {
                            if let year = content.yearReleased {
                                CinemaMetricItem(
                                    icon: "calendar",
                                    label: "Year",
                                    value: "\(year)"
                                )
                            }

                            CinemaMetricItem(
                                icon: "star.fill",
                                label: "Type",
                                value: content.contentType.capitalized
                            )

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("Learn More")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    // MARK: - Modern Sports Card

    private func modernSportsCard(content: SportsContent) -> some View {
        Button(action: {
            // Navigate to sports detail view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 120, height: 120)
                    .blur(radius: 30)
                    .opacity(0.3)
                    .offset(x: 50, y: -50)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Tamil Sports")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(content.sportType.capitalized)
                                    .font(.caption2)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                                    .clipShape(Capsule())
                            }

                            Spacer()

                            // Audio button
                            if content.audioUrl != nil {
                                Button(action: {
                                    // Play audio
                                }) {
                                    Image(systemName: "speaker.wave.2.fill")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                        .frame(width: 24, height: 24)
                                        .background(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                                        .clipShape(Circle())
                                }
                            }
                        }

                        // Name
                        Text(content.name)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        // Tamil name with romanization
                        VStack(alignment: .leading, spacing: 2) {
                            Text(content.nameTamil)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            if let romanization = content.romanization {
                                Text(romanization)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .italic()
                            }
                        }

                        // Description
                        Text(content.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)

                        // Metrics row
                        HStack(spacing: 16) {
                            SportsMetricItem(
                                icon: "figure.run",
                                label: "Type",
                                value: content.sportType.capitalized
                            )

                            SportsMetricItem(
                                icon: "star.fill",
                                label: "Level",
                                value: content.difficultyLevel.capitalized
                            )

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("Learn More")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    // MARK: - Helper Functions

    private func categoryGradient(for colorTheme: String) -> LinearGradient {
        switch colorTheme {
        case "pink": return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "purple": return LinearGradient(colors: [.purple, .blue], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "orange": return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "brown": return LinearGradient(colors: [.brown, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "green": return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "red": return LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "blue": return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
    
    // MARK: - Modern Category Card
    
    private func modernCategoryCard(category: SimpleCulturalCategory) -> some View {
        VStack(spacing: 12) {
            // Icon
            Text(category.emoji)
                .font(.title)
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(category.color.opacity(0.2))
                )
            
            // Title
            Text(category.name)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            // Count
            Text("\(category.count) insights")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
}

// MARK: - Metric Items

struct CinemaMetricItem: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing))
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

struct SportsMetricItem: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

// MARK: - Sample Data (Updated without Literature)

private let culturalCategories = [
    SimpleCulturalCategory(name: "Arts & Dance", emoji: "💃", color: .pink, count: 25),
    SimpleCulturalCategory(name: "Music", emoji: "🎵", color: .purple, count: 30),
    SimpleCulturalCategory(name: "Festivals", emoji: "🎊", color: .orange, count: 20),
    SimpleCulturalCategory(name: "Architecture", emoji: "🏛️", color: .brown, count: 15),
    SimpleCulturalCategory(name: "Cuisine", emoji: "🍛", color: .green, count: 35),
    SimpleCulturalCategory(name: "Cinema", emoji: "🎬", color: .red, count: 50),
    SimpleCulturalCategory(name: "Sports", emoji: "🏆", color: .blue, count: 20)
]

struct SimpleCulturalCategory {
    let name: String
    let emoji: String
    let color: Color
    let count: Int
}

struct CultureMetricItem: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing))
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

#Preview {
    CultureExploreView()
}
