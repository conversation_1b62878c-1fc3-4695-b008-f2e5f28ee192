import SwiftUI

struct CalendarExploreView: View {
    @StateObject private var calendarService = TamilCalendarService.shared
    @StateObject private var enhancedCalendarService = EnhancedCalendarService.shared

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("தமிழ் நாள்காட்டி")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)

                    Text("Traditional Tamil calendar with cultural insights")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)

                // Today's Date Card
                modernTodayDateCard

                // Calendar Concepts Section
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Tamil Calendar Concepts")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Spacer()

                        NavigationLink(destination: MonthlyCalendarView()) {
                            HStack(spacing: 4) {
                                Text("Full Calendar")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "calendar")
                                    .font(.caption)
                            }
                            .foregroundColor(.niraPrimary)
                        }
                    }
                    .padding(.horizontal)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(enhancedCalendarService.calendarConcepts, id: \.id) { concept in
                            modernConceptCard(concept: concept)
                        }
                    }
                    .padding(.horizontal)
                }

                // Multi-Religious Festivals Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Festivals & Celebrations")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)

                    // Religion filter tabs
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(["All", "Hindu", "Muslim", "Christian"], id: \.self) { religion in
                                religionFilterButton(religion: religion)
                            }
                        }
                        .padding(.horizontal)
                    }

                    ForEach(enhancedCalendarService.filteredFestivals.prefix(4), id: \.id) { festival in
                        modernEnhancedFestivalCard(festival: festival)
                    }
                }

                // Current Month Events
                VStack(alignment: .leading, spacing: 12) {
                    Text("This Month's Events")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)

                    ForEach(calendarService.upcomingEvents.prefix(3), id: \.id) { event in
                        modernEventCard(event: event)
                    }
                }

                // Tamil Months Grid
                VStack(alignment: .leading, spacing: 12) {
                    Text("Tamil Months")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(tamilMonths, id: \.name) { month in
                            modernMonthCard(month: month)
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .padding(.bottom, 100)
        }
        .onAppear {
            Task.detached {
                await calendarService.loadTodayEvents()
                await enhancedCalendarService.loadContent()
            }
        }
    }
    
    // MARK: - Modern Today Date Card

    private var modernTodayDateCard: some View {
        NavigationLink(destination: MonthlyCalendarView()) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(calendarGradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(calendarGradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header with date info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Today's Date")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(calendarService.currentTamilMonth)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "calendar.circle.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                        }

                        // Tamil Date Display
                        Text(calendarService.currentTamilDate)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        // English equivalent
                        Text("Today: \(Date().formatted(date: .abbreviated, time: .omitted))")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        // Metrics row
                        HStack(spacing: 16) {
                            CalendarMetricItem(icon: "calendar", label: "Month", value: calendarService.currentTamilMonth.prefix(3).description)
                            CalendarMetricItem(icon: "sun.max.fill", label: "Season", value: "Spring")

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("View Calendar")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private var calendarGradient: LinearGradient {
        LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
    }

    // MARK: - Modern Concept Card

    private func modernConceptCard(concept: CalendarConcept) -> some View {
        Button(action: {
            // Navigate to concept detail view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(conceptGradient(for: concept.category))
                    .frame(width: 80, height: 80)
                    .blur(radius: 20)
                    .opacity(0.3)
                    .offset(x: 30, y: -30)

                VStack(spacing: 12) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(conceptGradient(for: concept.category))
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    VStack(spacing: 8) {
                        // Icon with gradient background
                        Image(systemName: conceptIcon(for: concept.category))
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(conceptGradient(for: concept.category))
                            .clipShape(Circle())

                        // Title
                        Text(concept.conceptName)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)

                        // Tamil name
                        Text(concept.conceptNameTamil)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                .padding(12)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }

    // MARK: - Religion Filter Button

    private func religionFilterButton(religion: String) -> some View {
        Button(action: {
            enhancedCalendarService.selectedReligionFilter = religion
        }) {
            HStack(spacing: 6) {
                Text(religionEmoji(for: religion))
                    .font(.caption)

                Text(religion)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(enhancedCalendarService.selectedReligionFilter == religion ?
                          AnyShapeStyle(calendarGradient) : AnyShapeStyle(Color.gray.opacity(0.2)))
            )
            .foregroundColor(enhancedCalendarService.selectedReligionFilter == religion ?
                           .white : .primary)
        }
    }

    // MARK: - Modern Enhanced Festival Card

    private func modernEnhancedFestivalCard(festival: TamilFestival) -> some View {
        Button(action: {
            // Navigate to festival detail view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(religionGradient(for: festival.religion))
                    .frame(width: 120, height: 120)
                    .blur(radius: 30)
                    .opacity(0.3)
                    .offset(x: 50, y: -50)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(religionGradient(for: festival.religion))
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header with festival info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(festival.religion.capitalized)
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(festival.culturalImportance.capitalized)
                                    .font(.caption2)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(religionGradient(for: festival.religion))
                                    .clipShape(Capsule())
                            }

                            Spacer()

                            // Audio button
                            if festival.audioUrl != nil {
                                Button(action: {
                                    // Play audio
                                }) {
                                    Image(systemName: "speaker.wave.2.fill")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                        .frame(width: 24, height: 24)
                                        .background(religionGradient(for: festival.religion))
                                        .clipShape(Circle())
                                }
                            }
                        }

                        // Festival name
                        Text(festival.name)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(1)

                        // Tamil name with romanization
                        VStack(alignment: .leading, spacing: 2) {
                            Text(festival.nameTamil)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            if let romanization = festival.romanization {
                                Text(romanization)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .italic()
                            }
                        }

                        // Description
                        Text(festival.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)

                        // Metrics row
                        HStack(spacing: 16) {
                            FestivalMetricItem(
                                icon: "calendar",
                                label: "Duration",
                                value: "\(festival.durationDays) day\(festival.durationDays > 1 ? "s" : "")",
                                gradient: religionGradient(for: festival.religion)
                            )

                            if !festival.traditions.isEmpty {
                                FestivalMetricItem(
                                    icon: "star.fill",
                                    label: "Traditions",
                                    value: "\(festival.traditions.count)",
                                    gradient: religionGradient(for: festival.religion)
                                )
                            }

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("Learn More")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    // MARK: - Helper Functions

    private func conceptGradient(for category: String) -> LinearGradient {
        switch category {
        case "muhurtham": return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "raahu_kaalam": return LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "yemekandam": return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }

    private func conceptIcon(for category: String) -> String {
        switch category {
        case "muhurtham": return "star.fill"
        case "raahu_kaalam": return "exclamationmark.triangle.fill"
        case "yemekandam": return "moon.fill"
        default: return "calendar"
        }
    }

    private func religionEmoji(for religion: String) -> String {
        switch religion.lowercased() {
        case "hindu": return "🕉️"
        case "muslim": return "☪️"
        case "christian": return "✝️"
        case "all": return "🌍"
        default: return "🎊"
        }
    }

    private func religionGradient(for religion: String) -> LinearGradient {
        switch religion.lowercased() {
        case "hindu": return LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "muslim": return LinearGradient(colors: [.green, .teal], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "christian": return LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
    
    // MARK: - Modern Event Card
    
    private func modernEventCard(event: TamilEvent) -> some View {
        HStack(spacing: 16) {
            // Date indicator
            VStack(spacing: 4) {
                Text("15")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.niraPrimary)

                Text("Apr")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(width: 50)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.niraPrimary.opacity(0.1))
            )

            // Event details
            VStack(alignment: .leading, spacing: 4) {
                Text(event.tamilName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(event.name)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(event.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()

            // Event type icon
            Image(systemName: "star.fill")
                .foregroundColor(.orange)
                .font(.title3)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
    
    // MARK: - Modern Month Card
    
    private func modernMonthCard(month: SimpleTamilMonth) -> some View {
        VStack(spacing: 8) {
            Text(month.tamilName)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(month.name)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(month.season)
                .font(.caption2)
                .foregroundColor(.niraPrimary)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(Color.niraPrimary.opacity(0.1))
                )
        }
        .frame(maxWidth: .infinity)
        .frame(height: 80)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 6, x: 0, y: 3)
        )
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Modern Festival Card
    
    private func modernFestivalCard(festival: TamilFestival) -> some View {
        HStack(spacing: 16) {
            // Festival icon
            Text("🎉") // Default festival emoji since TamilFestival doesn't have emoji property
                .font(.title)
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(Color.blue.opacity(0.2)) // Default color since TamilFestival doesn't have color property
                )
            
            // Festival details
            VStack(alignment: .leading, spacing: 4) {
                Text(festival.nameTamil)
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(festival.name)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(festival.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
}

// MARK: - Festival Metric Item

struct FestivalMetricItem: View {
    let icon: String
    let label: String
    let value: String
    let gradient: LinearGradient

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(gradient)
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

// MARK: - Sample Data

private let tamilMonths = [
    SimpleTamilMonth(name: "Chithirai", tamilName: "சித்திரை", season: "Spring"),
    SimpleTamilMonth(name: "Vaikasi", tamilName: "வைகாசி", season: "Spring"),
    SimpleTamilMonth(name: "Aani", tamilName: "ஆனி", season: "Summer"),
    SimpleTamilMonth(name: "Aadi", tamilName: "ஆடி", season: "Summer"),
    SimpleTamilMonth(name: "Aavani", tamilName: "ஆவணி", season: "Monsoon"),
    SimpleTamilMonth(name: "Purattasi", tamilName: "புரட்டாசி", season: "Monsoon")
]

private let majorFestivals = [
    SimpleTamilFestival(name: "Pongal", tamilName: "பொங்கல்", description: "Harvest festival celebrating the Sun God", emoji: "🌾", color: .orange),
    SimpleTamilFestival(name: "Tamil New Year", tamilName: "தமிழ் புத்தாண்டு", description: "Beginning of the Tamil calendar year", emoji: "🎊", color: .green),
    SimpleTamilFestival(name: "Deepavali", tamilName: "தீபாவளி", description: "Festival of lights", emoji: "🪔", color: .yellow),
    SimpleTamilFestival(name: "Karthigai Deepam", tamilName: "கார்த்திகை தீபம்", description: "Festival of lamps", emoji: "🕯️", color: .red)
]

struct SimpleTamilMonth {
    let name: String
    let tamilName: String
    let season: String
}

struct SimpleTamilFestival {
    let name: String
    let tamilName: String
    let description: String
    let emoji: String
    let color: Color
}

struct CalendarMetricItem: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing))
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

#Preview {
    CalendarExploreView()
}
