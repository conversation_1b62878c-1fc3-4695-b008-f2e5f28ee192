//
//  LiteratureDetailView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 08/07/2025.
//

import SwiftUI

struct LiteratureDetailView: View {
    let content: LiteratureContent
    @Environment(\.dismiss) private var dismiss
    @StateObject private var readingProgressService = ReadingProgressService.shared
    @State private var isBookmarked = false
    @State private var readingProgress: Double = 0.0
    @State private var showingShareSheet = false
    @State private var fontSize: CGFloat = 16
    @State private var showingSettings = false
    @State private var selectedVoiceType: VoiceType = .female
    
    // Screen size detection for optimization
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    @Environment(\.verticalSizeClass) var verticalSizeClass
    
    private var isCompactScreen: Bool {
        horizontalSizeClass == .compact || verticalSizeClass == .compact
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Custom header
                headerView
                
                // Main content
                ScrollView {
                    VStack(alignment: .leading, spacing: 24) {
                        // Content header with title and author
                        contentHeaderSection
                        
                        // Tamil text with audio
                        tamilTextSection
                        
                        // English translation (if available)
                        if let englishContent = content.contentEnglish {
                            englishTranslationSection(englishContent)
                        }
                        
                        // Cultural context section
                        if let culturalContext = content.culturalContext {
                            contextSection(
                                title: "Cultural Context",
                                titleTamil: "கலாச்சார சூழல்",
                                content: culturalContext,
                                icon: "building.columns.fill",
                                gradient: literatureGradient
                            )
                        }
                        
                        // Historical significance
                        if let historicalSignificance = content.historicalSignificance {
                            contextSection(
                                title: "Historical Significance",
                                titleTamil: "வரலாற்று முக்கியத்துவம்",
                                content: historicalSignificance,
                                icon: "clock.fill",
                                gradient: literatureGradient
                            )
                        }
                        
                        // Modern relevance
                        if let modernRelevance = content.modernRelevance {
                            contextSection(
                                title: "Modern Relevance",
                                titleTamil: "நவீன பொருத்தம்",
                                content: modernRelevance,
                                icon: "lightbulb.fill",
                                gradient: literatureGradient
                            )
                        }
                        
                        // Tags section
                        if !content.tags.isEmpty {
                            tagsSection
                        }
                        
                        // Reading metrics
                        readingMetricsSection
                    }
                    .padding()
                    .padding(.bottom, 100) // Extra space for bottom actions
                }
                
                // Bottom action bar
                bottomActionBar
            }
            .background(
                LinearGradient(
                    colors: [Color.blue.opacity(0.02), Color.purple.opacity(0.02)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        }
        .navigationBarHidden(true)
        .onAppear {
            loadReadingProgress()
        }
        .onDisappear {
            // End reading session when leaving the view
            readingProgressService.endCurrentSession()
        }
        .sheet(isPresented: $showingSettings) {
            readingSettingsSheet
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(activityItems: [shareText])
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            // Back button
            Button(action: { dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(Color.systemBackground.opacity(0.8))
                            .shadow(color: Color.gray.opacity(0.2), radius: 4, x: 0, y: 2)
                    )
            }
            
            Spacer()
            
            // Category badge
            Text(content.category.name)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(literatureGradient)
                )
            
            Spacer()
            
            // Action buttons
            HStack(spacing: 12) {
                // Settings button
                Button(action: { showingSettings = true }) {
                    Image(systemName: "textformat.size")
                        .font(.title3)
                        .foregroundColor(.primary)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.systemBackground.opacity(0.8))
                                .shadow(color: Color.gray.opacity(0.2), radius: 4, x: 0, y: 2)
                        )
                }
                
                // Bookmark button
                Button(action: { toggleBookmark() }) {
                    Image(systemName: isBookmarked ? "bookmark.fill" : "bookmark")
                        .font(.title3)
                        .foregroundColor(isBookmarked ? .yellow : .primary)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.systemBackground.opacity(0.8))
                                .shadow(color: Color.gray.opacity(0.2), radius: 4, x: 0, y: 2)
                        )
                }
                
                // Share button
                Button(action: { showingShareSheet = true }) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.title3)
                        .foregroundColor(.primary)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.systemBackground.opacity(0.8))
                                .shadow(color: Color.gray.opacity(0.2), radius: 4, x: 0, y: 2)
                        )
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - Content Header Section
    
    private var contentHeaderSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Title
            Text(content.title)
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            // Tamil title with audio
            TamilTextWithAudio(
                tamilText: content.titleTamil,
                romanization: content.romanization,
                contentId: content.id,
                contentType: .literature,
                voiceType: selectedVoiceType,
                showRomanization: true,
                fontSize: .title2,
                alignment: .leading,
                maxLines: 3
            )
            
            // Author information
            if let author = content.author, let authorTamil = content.authorTamil {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("By \(author)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text(authorTamil)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // Difficulty and reading time
                    VStack(alignment: .trailing, spacing: 4) {
                        HStack(spacing: 4) {
                            Image(systemName: "clock.fill")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(content.readingTimeMinutes) min")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Text(content.difficultyLevel.capitalized)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(literatureGradient)
                            )
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Tamil Text Section
    
    private var tamilTextSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Tamil Text")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("தமிழ் உரை")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            
            // Main Tamil content with audio
            TamilTextWithAudio(
                tamilText: content.contentTamil,
                romanization: content.romanization,
                contentId: content.id,
                contentType: .literature,
                voiceType: selectedVoiceType,
                showRomanization: true,
                fontSize: .system(size: fontSize),
                alignment: .leading,
                maxLines: nil
            )
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Helper Properties
    
    private var literatureGradient: LinearGradient {
        switch content.category.colorTheme {
        case "blue": return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "orange": return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "purple": return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "green": return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
    
    private var shareText: String {
        var text = "\(content.title)\n\(content.titleTamil)\n\n"
        if let author = content.author {
            text += "By \(author)\n\n"
        }
        text += content.contentTamil
        if let englishContent = content.contentEnglish {
            text += "\n\nEnglish Translation:\n\(englishContent)"
        }
        text += "\n\nShared from NIRA Tamil Learning App"
        return text
    }
    
    // MARK: - Helper Functions
    
    private func loadReadingProgress() {
        // Load bookmark status and reading progress
        isBookmarked = readingProgressService.isBookmarked(contentId: content.id)
        readingProgress = readingProgressService.getProgress(contentId: content.id)

        // Start reading session when view loads
        readingProgressService.startReadingSession(contentId: content.id)
    }

    private func toggleBookmark() {
        isBookmarked.toggle()

        // Add bookmark with current scroll position if available
        let position = Int(readingProgress * 100) // Convert progress to position
        readingProgressService.setBookmark(
            contentId: content.id,
            isBookmarked: isBookmarked,
            position: position,
            note: isBookmarked ? "Bookmarked while reading \(content.title)" : nil
        )

        // Haptic feedback
        HapticFeedbackManager.shared.selection()
    }
}

// MARK: - Additional Sections Extension

extension LiteratureDetailView {

    // MARK: - English Translation Section

    func englishTranslationSection(_ englishContent: String) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("English Translation")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: "globe")
                    .font(.title3)
                    .foregroundColor(.secondary)
            }

            Text(englishContent)
                .font(.custom("", size: fontSize))
                .lineSpacing(4)
                .foregroundColor(.primary)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Context Section

    func contextSection(title: String, titleTamil: String, content: String, icon: String, gradient: LinearGradient) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.white)
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(gradient)
                    )

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(titleTamil)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            Text(content)
                .font(.subheadline)
                .lineSpacing(4)
                .foregroundColor(.primary)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Tags Section

    var tagsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Tags")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: isCompactScreen ? 2 : 3), spacing: 8) {
                ForEach(content.tags, id: \.self) { tag in
                    Text(tag.capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(Color.gray.opacity(0.1))
                        )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Reading Metrics Section

    var readingMetricsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Reading Statistics")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            HStack(spacing: 16) {
                // Reading progress
                VStack(alignment: .leading, spacing: 8) {
                    Text("Progress")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    HStack(spacing: 8) {
                        ProgressView(value: readingProgress, total: 1.0)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .frame(height: 6)

                        Text("\(Int(readingProgress * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                }

                Spacer()

                // View count
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Views")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("\(content.viewCount)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Bottom Action Bar

    var bottomActionBar: some View {
        VStack(spacing: 0) {
            // Progress indicator
            ProgressView(value: readingProgress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .frame(height: 3)

            HStack(spacing: 16) {
                // Mark as read button
                Button(action: { markAsRead() }) {
                    HStack(spacing: 8) {
                        Image(systemName: readingProgress >= 1.0 ? "checkmark.circle.fill" : "circle")
                            .font(.title3)

                        Text(readingProgress >= 1.0 ? "Read" : "Mark as Read")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(readingProgress >= 1.0 ? .green : .primary)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.1))
                    )
                }

                Spacer()

                // Continue reading button
                Button(action: { continueReading() }) {
                    HStack(spacing: 8) {
                        Text("Continue Reading")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Image(systemName: "arrow.right")
                            .font(.caption)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(literatureGradient)
                    )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                Color.systemBackground
                    .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: -4)
            )
        }
    }

    // MARK: - Reading Settings Sheet

    var readingSettingsSheet: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Font size adjustment
                VStack(alignment: .leading, spacing: 16) {
                    Text("Font Size")
                        .font(.headline)
                        .fontWeight(.semibold)

                    HStack {
                        Text("A")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Slider(value: $fontSize, in: 12...24, step: 1)
                            .accentColor(.blue)

                        Text("A")
                            .font(.title3)
                            .foregroundColor(.secondary)
                    }

                    Text("Sample text at current size")
                        .font(.custom("", size: fontSize))
                        .foregroundColor(.primary)
                }

                // Voice selection
                VStack(alignment: .leading, spacing: 16) {
                    Text("Audio Voice")
                        .font(.headline)
                        .foregroundColor(.primary)

                    HStack(spacing: 16) {
                        // Female voice option
                        Button(action: { selectedVoiceType = .female }) {
                            HStack {
                                Image(systemName: selectedVoiceType == .female ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(selectedVoiceType == .female ? .blue : .gray)
                                Text("Female Voice")
                                    .foregroundColor(.primary)
                                Spacer()
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(selectedVoiceType == .female ? Color.blue.opacity(0.1) : Color.gray.opacity(0.1))
                            )
                        }

                        // Male voice option
                        Button(action: { selectedVoiceType = .male }) {
                            HStack {
                                Image(systemName: selectedVoiceType == .male ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(selectedVoiceType == .male ? .blue : .gray)
                                Text("Male Voice")
                                    .foregroundColor(.primary)
                                Spacer()
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(selectedVoiceType == .male ? Color.blue.opacity(0.1) : Color.gray.opacity(0.1))
                            )
                        }
                    }

                    Text("Choose between premium Tamil voices for audio playback")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("Reading Settings")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") { showingSettings = false })
        }
    }

    // MARK: - Action Functions

    private func markAsRead() {
        readingProgress = 1.0

        // Calculate estimated words read (rough estimate)
        let wordsRead = content.contentTamil.split(separator: " ").count

        readingProgressService.updateProgress(
            contentId: content.id,
            progress: readingProgress,
            scrollPosition: 100, // End of content
            wordsRead: wordsRead
        )

        // End current reading session
        readingProgressService.endCurrentSession()

        // Haptic feedback for completion
        HapticFeedbackManager.shared.simulationComplete()

        print("📖 Marked content as read: \(content.title)")
    }

    private func continueReading() {
        // Simulate reading progress increment
        if readingProgress < 1.0 {
            let previousProgress = readingProgress
            readingProgress = min(readingProgress + 0.1, 1.0)

            // Calculate words read based on progress
            let totalWords = content.contentTamil.split(separator: " ").count
            let wordsRead = Int(Double(totalWords) * readingProgress)
            let scrollPosition = Int(readingProgress * 100)

            readingProgressService.updateProgress(
                contentId: content.id,
                progress: readingProgress,
                scrollPosition: scrollPosition,
                wordsRead: wordsRead
            )

            // Update current session progress
            readingProgressService.updateSessionProgress(
                scrollPosition: scrollPosition,
                wordsRead: wordsRead
            )

            // Haptic feedback for progress
            HapticFeedbackManager.shared.selection()

            print("📖 Reading progress updated: \(Int(previousProgress * 100))% → \(Int(readingProgress * 100))%")
        }
    }
}

// MARK: - Share Sheet

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Reading Progress Service

// MARK: - Reading Progress Models

struct LiteratureProgress: Codable, Identifiable {
    let id: UUID
    let contentId: UUID
    let progressPercentage: Double
    let readingTimeSeconds: Int
    let lastReadAt: Date
    let isCompleted: Bool
    let scrollPosition: Int
    let wordsRead: Int

    init(contentId: UUID, progressPercentage: Double = 0.0, readingTimeSeconds: Int = 0, scrollPosition: Int = 0, wordsRead: Int = 0) {
        self.id = UUID()
        self.contentId = contentId
        self.progressPercentage = progressPercentage
        self.readingTimeSeconds = readingTimeSeconds
        self.lastReadAt = Date()
        self.isCompleted = progressPercentage >= 1.0
        self.scrollPosition = scrollPosition
        self.wordsRead = wordsRead
    }
}

struct BookmarkData: Codable, Identifiable {
    let id: UUID
    let contentId: UUID
    let position: Int
    let note: String?
    let createdAt: Date
    let isActive: Bool

    init(contentId: UUID, position: Int = 0, note: String? = nil) {
        self.id = UUID()
        self.contentId = contentId
        self.position = position
        self.note = note
        self.createdAt = Date()
        self.isActive = true
    }
}

struct LiteratureSession: Codable, Identifiable {
    let id: UUID
    let contentId: UUID
    let sessionStart: Date
    var sessionEnd: Date?
    var durationSeconds: Int
    var scrollPosition: Int
    var wordsRead: Int
    var readingSpeedWPM: Double?

    init(contentId: UUID) {
        self.id = UUID()
        self.contentId = contentId
        self.sessionStart = Date()
        self.durationSeconds = 0
        self.scrollPosition = 0
        self.wordsRead = 0
    }
}

@MainActor
class ReadingProgressService: ObservableObject {
    static let shared = ReadingProgressService()

    @Published private var progressData: [UUID: LiteratureProgress] = [:]
    @Published private var bookmarks: [UUID: BookmarkData] = [:]
    @Published private var currentSession: LiteratureSession?
    @Published private var readingSessions: [LiteratureSession] = []

    private let supabaseClient = NIRASupabaseClient.shared
    private var sessionTimer: Timer?

    private init() {
        loadLocalData()
    }

    // MARK: - Progress Tracking

    func getProgress(contentId: UUID) -> Double {
        return progressData[contentId]?.progressPercentage ?? 0.0
    }

    func updateProgress(contentId: UUID, progress: Double, scrollPosition: Int = 0, wordsRead: Int = 0) {
        let readingProgress = LiteratureProgress(
            contentId: contentId,
            progressPercentage: min(max(progress, 0.0), 1.0),
            readingTimeSeconds: progressData[contentId]?.readingTimeSeconds ?? 0,
            scrollPosition: scrollPosition,
            wordsRead: wordsRead
        )

        progressData[contentId] = readingProgress
        saveLocalData()

        // Sync to Supabase
        Task {
            await syncProgressToSupabase(readingProgress)
        }
    }

    func getReadingTime(contentId: UUID) -> Int {
        return progressData[contentId]?.readingTimeSeconds ?? 0
    }

    func isCompleted(contentId: UUID) -> Bool {
        return progressData[contentId]?.isCompleted ?? false
    }

    // MARK: - Bookmark Management

    func isBookmarked(contentId: UUID) -> Bool {
        return bookmarks[contentId]?.isActive ?? false
    }

    func setBookmark(contentId: UUID, isBookmarked: Bool, position: Int = 0, note: String? = nil) {
        if isBookmarked {
            let bookmark = BookmarkData(contentId: contentId, position: position, note: note)
            bookmarks[contentId] = bookmark
        } else {
            bookmarks[contentId] = nil
        }
        saveLocalData()

        // Sync to Supabase
        Task {
            await syncBookmarkToSupabase(contentId: contentId, isBookmarked: isBookmarked, position: position, note: note)
        }
    }

    func getBookmark(contentId: UUID) -> BookmarkData? {
        return bookmarks[contentId]
    }

    func getAllBookmarks() -> [BookmarkData] {
        return Array(bookmarks.values).filter { $0.isActive }.sorted { $0.createdAt > $1.createdAt }
    }

    // MARK: - Reading Session Management

    func startReadingSession(contentId: UUID) {
        // End current session if exists
        endCurrentSession()

        // Start new session
        currentSession = LiteratureSession(contentId: contentId)

        // Start timer for tracking reading time
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateSessionTime()
            }
        }

        print("📖 Started reading session for content: \(contentId)")
    }

    func endCurrentSession() {
        guard let session = currentSession else { return }

        sessionTimer?.invalidate()
        sessionTimer = nil

        var endedSession = session
        endedSession.sessionEnd = Date()

        readingSessions.append(endedSession)
        currentSession = nil

        // Update total reading time for content
        if let existingProgress = progressData[session.contentId] {
            let updatedProgress = LiteratureProgress(
                contentId: session.contentId,
                progressPercentage: existingProgress.progressPercentage,
                readingTimeSeconds: existingProgress.readingTimeSeconds + session.durationSeconds,
                scrollPosition: session.scrollPosition,
                wordsRead: session.wordsRead
            )
            progressData[session.contentId] = updatedProgress
        }

        saveLocalData()

        // Sync to Supabase
        Task {
            await syncSessionToSupabase(endedSession)
        }

        print("📖 Ended reading session. Duration: \(session.durationSeconds) seconds")
    }

    func updateSessionProgress(scrollPosition: Int, wordsRead: Int) {
        guard var session = currentSession else { return }
        session.scrollPosition = scrollPosition
        session.wordsRead = wordsRead

        // Calculate reading speed
        if session.durationSeconds > 0 {
            let minutesRead = Double(session.durationSeconds) / 60.0
            session.readingSpeedWPM = Double(wordsRead) / minutesRead
        }

        currentSession = session
    }

    private func updateSessionTime() {
        guard var session = currentSession else { return }
        session.durationSeconds += 1
        currentSession = session
    }

    // MARK: - Data Persistence

    private func loadLocalData() {
        // Load progress data
        if let progressData = UserDefaults.standard.data(forKey: "literature_progress_v2"),
           let decodedProgress = try? JSONDecoder().decode([UUID: LiteratureProgress].self, from: progressData) {
            self.progressData = decodedProgress
        }

        // Load bookmarks
        if let bookmarkData = UserDefaults.standard.data(forKey: "literature_bookmarks_v2"),
           let decodedBookmarks = try? JSONDecoder().decode([UUID: BookmarkData].self, from: bookmarkData) {
            self.bookmarks = decodedBookmarks
        }

        // Load reading sessions
        if let sessionData = UserDefaults.standard.data(forKey: "reading_sessions"),
           let decodedSessions = try? JSONDecoder().decode([LiteratureSession].self, from: sessionData) {
            self.readingSessions = decodedSessions
        }
    }

    private func saveLocalData() {
        // Save progress data
        if let progressData = try? JSONEncoder().encode(progressData) {
            UserDefaults.standard.set(progressData, forKey: "literature_progress_v2")
        }

        // Save bookmarks
        if let bookmarkData = try? JSONEncoder().encode(bookmarks) {
            UserDefaults.standard.set(bookmarkData, forKey: "literature_bookmarks_v2")
        }

        // Save reading sessions
        if let sessionData = try? JSONEncoder().encode(readingSessions) {
            UserDefaults.standard.set(sessionData, forKey: "reading_sessions")
        }
    }

    // MARK: - Supabase Sync Methods

    private func syncProgressToSupabase(_ progress: LiteratureProgress) async {
        do {
            struct ProgressData: Codable {
                let user_id: String
                let content_id: String
                let reading_time_seconds: Int
                let completion_percentage: Double
                let is_completed: Bool
                let last_read_at: String
                let updated_at: String
            }

            let progressData = ProgressData(
                user_id: "00000000-0000-0000-0000-000000000000", // Placeholder user ID
                content_id: progress.contentId.uuidString,
                reading_time_seconds: progress.readingTimeSeconds,
                completion_percentage: progress.progressPercentage,
                is_completed: progress.isCompleted,
                last_read_at: ISO8601DateFormatter().string(from: progress.lastReadAt),
                updated_at: ISO8601DateFormatter().string(from: Date())
            )

            // Upsert progress data
            let _ = try await supabaseClient.client
                .from("reading_progress")
                .upsert(progressData)
                .execute()

            print("✅ Synced reading progress to Supabase for content: \(progress.contentId)")

        } catch {
            print("❌ Failed to sync reading progress to Supabase: \(error)")
        }
    }

    private func syncBookmarkToSupabase(contentId: UUID, isBookmarked: Bool, position: Int, note: String?) async {
        do {
            if isBookmarked {
                struct BookmarkData: Codable {
                    let user_id: String
                    let content_id: String
                    let content_type: String
                    let bookmark_position: Int
                    let bookmark_note: String
                    let is_active: Bool
                    let updated_at: String
                }

                let bookmarkData = BookmarkData(
                    user_id: "00000000-0000-0000-0000-000000000000", // Placeholder user ID
                    content_id: contentId.uuidString,
                    content_type: "literature",
                    bookmark_position: position,
                    bookmark_note: note ?? "",
                    is_active: true,
                    updated_at: ISO8601DateFormatter().string(from: Date())
                )

                let _ = try await supabaseClient.client
                    .from("bookmarks")
                    .upsert(bookmarkData)
                    .execute()

                print("✅ Synced bookmark to Supabase for content: \(contentId)")
            } else {
                struct BookmarkUpdate: Codable {
                    let is_active: Bool
                }

                // Remove bookmark
                let _ = try await supabaseClient.client
                    .from("bookmarks")
                    .update(BookmarkUpdate(is_active: false))
                    .eq("content_id", value: contentId.uuidString)
                    .execute()

                print("✅ Removed bookmark from Supabase for content: \(contentId)")
            }

        } catch {
            print("❌ Failed to sync bookmark to Supabase: \(error)")
        }
    }

    private func syncSessionToSupabase(_ session: LiteratureSession) async {
        do {
            struct SessionData: Codable {
                let user_id: String
                let content_id: String
                let content_type: String
                let session_start: String
                let session_end: String?
                let reading_duration_seconds: Int
                let scroll_position: Int
                let words_read: Int
                let reading_speed_wpm: Double?
                let device_type: String
            }

            let sessionData = SessionData(
                user_id: "00000000-0000-0000-0000-000000000000", // Placeholder user ID
                content_id: session.contentId.uuidString,
                content_type: "literature",
                session_start: ISO8601DateFormatter().string(from: session.sessionStart),
                session_end: session.sessionEnd.map { ISO8601DateFormatter().string(from: $0) },
                reading_duration_seconds: session.durationSeconds,
                scroll_position: session.scrollPosition,
                words_read: session.wordsRead,
                reading_speed_wpm: session.readingSpeedWPM,
                device_type: "iOS"
            )

            let _ = try await supabaseClient.client
                .from("reading_sessions")
                .insert(sessionData)
                .execute()

            print("✅ Synced reading session to Supabase for content: \(session.contentId)")

        } catch {
            print("❌ Failed to sync reading session to Supabase: \(error)")
        }
    }

    // MARK: - Analytics and Statistics

    func getReadingStatistics() -> ReadingStatistics {
        let totalReadingTime = progressData.values.reduce(0) { $0 + $1.readingTimeSeconds }
        let completedContent = progressData.values.filter { $0.isCompleted }.count
        let totalContent = progressData.count
        let averageProgress = progressData.isEmpty ? 0.0 : progressData.values.reduce(0.0) { $0 + $1.progressPercentage } / Double(progressData.count)
        let totalBookmarks = bookmarks.values.filter { $0.isActive }.count

        return ReadingStatistics(
            totalReadingTimeSeconds: totalReadingTime,
            completedContentCount: completedContent,
            totalContentCount: totalContent,
            averageProgressPercentage: averageProgress,
            totalBookmarks: totalBookmarks,
            readingSessionsCount: readingSessions.count
        )
    }

    func getContentStatistics(contentId: UUID) -> ContentStatistics? {
        guard let progress = progressData[contentId] else { return nil }

        let contentSessions = readingSessions.filter { $0.contentId == contentId }
        let averageSessionDuration = contentSessions.isEmpty ? 0 : contentSessions.reduce(0) { $0 + $1.durationSeconds } / contentSessions.count
        let averageReadingSpeed = contentSessions.compactMap { $0.readingSpeedWPM }.reduce(0.0, +) / Double(max(contentSessions.count, 1))

        return ContentStatistics(
            contentId: contentId,
            progressPercentage: progress.progressPercentage,
            totalReadingTime: progress.readingTimeSeconds,
            isCompleted: progress.isCompleted,
            isBookmarked: isBookmarked(contentId: contentId),
            sessionCount: contentSessions.count,
            averageSessionDuration: averageSessionDuration,
            averageReadingSpeed: averageReadingSpeed,
            lastReadAt: progress.lastReadAt
        )
    }
}

// MARK: - Statistics Models

struct ReadingStatistics {
    let totalReadingTimeSeconds: Int
    let completedContentCount: Int
    let totalContentCount: Int
    let averageProgressPercentage: Double
    let totalBookmarks: Int
    let readingSessionsCount: Int

    var totalReadingTimeFormatted: String {
        let hours = totalReadingTimeSeconds / 3600
        let minutes = (totalReadingTimeSeconds % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    var completionRate: Double {
        return totalContentCount > 0 ? Double(completedContentCount) / Double(totalContentCount) : 0.0
    }
}

struct ContentStatistics {
    let contentId: UUID
    let progressPercentage: Double
    let totalReadingTime: Int
    let isCompleted: Bool
    let isBookmarked: Bool
    let sessionCount: Int
    let averageSessionDuration: Int
    let averageReadingSpeed: Double
    let lastReadAt: Date

    var readingTimeFormatted: String {
        let minutes = totalReadingTime / 60
        let seconds = totalReadingTime % 60
        return "\(minutes):\(String(format: "%02d", seconds))"
    }
}

#Preview {
    LiteratureDetailView(
        content: LiteratureContent(
            id: UUID(),
            categoryId: UUID(),
            title: "Virtue - Chapter 1",
            titleTamil: "அறத்துப்பால் - அதிகாரம் 1",
            author: "Thiruvalluvar",
            authorTamil: "திருவள்ளுவர்",
            contentTamil: "அகர முதல எழுத்தெல்லாம் ஆதி\nபகவன் முதற்றே உலகு",
            contentEnglish: "A, as its first of letters, every speech maintains;\nThe Primal Deity is first through all the world's domains.",
            romanization: "Agara mudala ezhuththellaam aadhi\nBhagavan mudhatre ulagu",
            culturalContext: "This opening verse establishes the primacy of both language and divinity in Tamil culture",
            historicalSignificance: "Written around 1st century BCE to 5th century CE, represents the pinnacle of Tamil ethical literature",
            modernRelevance: "Emphasizes the importance of education and spiritual foundation in modern life",
            difficultyLevel: "intermediate",
            readingTimeMinutes: 3,
            audioUrl: nil,
            audioMaleUrl: nil,
            audioFemaleUrl: nil,
            tags: ["virtue", "ethics", "wisdom", "spirituality"],
            isFeatured: true,
            viewCount: 1250
        )
    )
}
