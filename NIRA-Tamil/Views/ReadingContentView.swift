import SwiftUI

// MARK: - Reading Content View

struct ReadingContentView: View {
    @StateObject private var readingService = ReadingContentService.shared
    @StateObject private var audioService = ReadingAudioService.shared
    @State private var selectedLevel: CEFRLevel = .a1
    @State private var selectedCategory: ReadingCategory? = nil
    @State private var showingReadingDetail = false
    @State private var selectedContent: ReadingContent?

    
    var body: some View {
        VStack(spacing: 0) {
            // Header with level selector
            readingHeader
            
            // Main content
            if readingService.isLoading {
                loadingView
            } else {
                readingContentGrid
            }
        }
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.05), Color.purple.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .task {
            await readingService.loadReadingContent()
        }
        .sheet(isPresented: $showingReadingDetail) {
            if let content = selectedContent {
                ReadingDetailView(content: content)
            }
        }

    }
    
    // MARK: - Header
    
    private var readingHeader: some View {
        VStack(spacing: 16) {
            // Minimal spacing for clean layout
            Spacer()
                .frame(height: 8)
            
            // Level selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(CEFRLevel.allCases, id: \.self) { level in
                        LevelChip(
                            level: level,
                            isSelected: selectedLevel == level,
                            action: {
                                withAnimation(.spring()) {
                                    selectedLevel = level
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
            
            // Category selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ReadingCategoryChip(
                        category: nil,
                        title: "All",
                        isSelected: selectedCategory == nil,
                        action: {
                            withAnimation(.spring()) {
                                selectedCategory = nil
                            }
                        }
                    )

                    ForEach(ReadingCategory.allCases, id: \.self) { category in
                        ReadingCategoryChip(
                            category: category,
                            title: category.displayName,
                            isSelected: selectedCategory == category,
                            action: {
                                withAnimation(.spring()) {
                                    selectedCategory = category
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 0)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
    
    // MARK: - Content Grid
    
    private var readingContentGrid: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredContent) { content in
                    ReadingContentCard(
                        content: content,
                        progress: readingService.getProgress(for: content.contentId),
                        audioService: audioService,
                        readingService: readingService,
                        onTap: {
                            HapticFeedbackManager.shared.cardSelection()
                            selectedContent = content
                            showingReadingDetail = true
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading Tamil reading content...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Computed Properties
    
    private var filteredContent: [ReadingContent] {
        var content = readingService.getContent(for: selectedLevel)
        
        if let category = selectedCategory {
            content = content.filter { $0.category == category }
        }
        
        return content.sorted { first, second in
            // Sort by category first, then by content ID
            if first.category != second.category {
                return first.category.rawValue < second.category.rawValue
            }
            return first.contentId < second.contentId
        }
    }
}

// MARK: - Level Chip

struct LevelChip: View {
    let level: CEFRLevel
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(level.rawValue.uppercased())
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : level.uiColor)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? level.uiColor : level.uiColor.opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Reading Category Chip

struct ReadingCategoryChip: View {
    let category: ReadingCategory?
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                if let category = category {
                    Image(systemName: category.icon)
                        .font(.caption)
                }
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.blue : Color.blue.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Reading Content Card

struct ReadingContentCard: View {
    let content: ReadingContent
    let progress: ReadingProgress?
    let audioService: ReadingAudioService
    let readingService: ReadingContentService
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 16) {
                // Header with title and difficulty
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(content.titleEnglish)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                        
                        Text(content.titleTamil)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        // Difficulty badge
                        Text(content.difficultyLevel.rawValue.uppercased())
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(content.difficultyColor)
                            )

                        // Reading time
                        Text(content.readingTimeDisplay)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        // Audio indicator
                        if content.audioUrl != nil {
                            Image(systemName: "speaker.wave.2.fill")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                }
                
                // Content preview
                VStack(alignment: .leading, spacing: 8) {
                    Text(content.contentPreview)
                        .font(.body)
                        .foregroundColor(.primary)

                    // Show romanization if enabled and available
                    if readingService.readingPreferences.showRomanization,
                       let romanization = content.contentRomanization {
                        Text(romanization.prefix(100) + (romanization.count > 100 ? "..." : ""))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.secondarySystemBackground))
                )
                
                // Progress and metadata
                HStack {
                    // Category icon and name
                    HStack(spacing: 6) {
                        Image(systemName: content.category.icon)
                            .font(.caption)
                            .foregroundColor(content.difficultyColor)
                        
                        Text(content.category.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()

                    // Audio play button
                    Button {
                        Task {
                            await audioService.playReadingAudio(for: content)
                        }
                    } label: {
                        Image(systemName: (audioService.isPlaying && audioService.currentlyPlaying == content.contentId) ? "pause.circle.fill" : "play.circle.fill")
                            .font(.title3)
                            .foregroundColor(.blue)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // Progress indicator
                    if let progress = progress {
                        HStack(spacing: 4) {
                            if progress.isCompleted {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                Text("Completed")
                                    .font(.caption)
                                    .foregroundColor(.green)
                            } else if progress.completionPercentage > 0 {
                                Text("\(Int(progress.completionPercentage))%")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        }
                    } else {
                        Text("Not Started")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                // Glassmorphic background
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    colors: [Color.white.opacity(0.2), Color.clear],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ReadingContentView()
}
