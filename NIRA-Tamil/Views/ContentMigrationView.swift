//
//  ContentMigrationView.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import SwiftUI

struct ContentMigrationView: View {
    @StateObject private var migrationService = ContentMigrationService.shared
    @State private var showingResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "arrow.up.doc.on.clipboard")
                        .font(.system(size: 50))
                        .foregroundColor(.niraPrimary)
                    
                    Text("Content Migration")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Migrate lesson content to Supabase database")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Progress Section
                if migrationService.isLoading {
                    VStack(spacing: 12) {
                        ProgressView(value: migrationService.migrationProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
                            .scaleEffect(1.2)
                        
                        Text(migrationService.currentOperation)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("\(Int(migrationService.migrationProgress * 100))% Complete")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.niraPrimary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                    .padding(.horizontal)
                }
                
                // Migration Button
                Button(action: {
                    Task {
                        await migrationService.runContentMigration()
                        showingResults = true
                    }
                }) {
                    HStack {
                        if migrationService.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "play.circle.fill")
                        }
                        
                        Text(migrationService.isLoading ? "Migrating Content..." : "Start Content Migration")
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(migrationService.isLoading ? Color.gray : Color.niraPrimary)
                    )
                }
                .disabled(migrationService.isLoading)
                .padding(.horizontal)
                
                // Migration Overview
                VStack(alignment: .leading, spacing: 12) {
                    Text("Migration Overview")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        MigrationStepCard(
                            title: "A1 Lessons",
                            description: "3 basic lessons",
                            icon: "1.circle.fill",
                            color: .green
                        )
                        
                        MigrationStepCard(
                            title: "A2 Lessons",
                            description: "2 intermediate lessons",
                            icon: "2.circle.fill",
                            color: .blue
                        )
                        
                        MigrationStepCard(
                            title: "Vocabulary",
                            description: "Essential words",
                            icon: "book.fill",
                            color: .orange
                        )
                        
                        MigrationStepCard(
                            title: "Conversations",
                            description: "Dialogue examples",
                            icon: "bubble.left.and.bubble.right.fill",
                            color: .purple
                        )
                        
                        MigrationStepCard(
                            title: "Grammar",
                            description: "Language rules",
                            icon: "textformat.abc",
                            color: .red
                        )
                        
                        MigrationStepCard(
                            title: "Practice",
                            description: "Exercise questions",
                            icon: "pencil.circle.fill",
                            color: .teal
                        )
                    }
                    .padding(.horizontal)
                }
                
                // Results Preview
                if !migrationService.migrationResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Migration Results")
                                .font(.headline)
                            
                            Spacer()
                            
                            Button("Clear") {
                                migrationService.clearResults()
                            }
                            .font(.caption)
                            .foregroundColor(.niraPrimary)
                        }
                        .padding(.horizontal)
                        
                        ScrollView {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(migrationService.migrationResults.prefix(10), id: \.self) { result in
                                    HStack {
                                        Text(result)
                                            .font(.caption)
                                            .foregroundColor(result.contains("✅") ? .green : .red)
                                        Spacer()
                                    }
                                }
                                
                                if migrationService.migrationResults.count > 10 {
                                    Text("... and \(migrationService.migrationResults.count - 10) more")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .padding(.top, 4)
                                }
                            }
                            .padding()
                        }
                        .frame(maxHeight: 150)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                        .padding(.horizontal)
                    }
                }
                
                Spacer()
            }
            .navigationTitle("Content Migration")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingResults) {
            MigrationResultsDetailView(results: migrationService.migrationResults)
        }
    }
}

struct MigrationStepCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

struct MigrationResultsDetailView: View {
    let results: [String]
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(results, id: \.self) { result in
                        HStack {
                            Text(result)
                                .font(.body)
                                .foregroundColor(result.contains("✅") ? .green : .red)
                            Spacer()
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
                .padding()
            }
            .navigationTitle("Migration Results")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") { dismiss() })
        }
    }
}

#Preview {
    ContentMigrationView()
}
