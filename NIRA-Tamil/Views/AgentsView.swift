//
//  AgentsView.swift
//  NIRA
//
//  Enhanced AI Agents Dashboard with Modern UX
//  Based on Language Learning Content Development Standards v2.0
//

import SwiftUI

struct AgentsView: View {
    @StateObject private var tamilContentService = TamilContentService.shared
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var selectedAgent: Agent?
    @State private var showingAgentChat = false
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    // Core 4 multicultural Tamil agents with personalized content
    private var coreAgents: [Agent] {
        let currentLesson = tamilContentService.currentLesson
        let userLevel = CEFRLevel.a1 // Default level for now

        return [
            Agent(
                id: "conversational_\(userPreferences.selectedLanguage.rawValue)",
                name: "<PERSON><PERSON><PERSON>",
                description: personalizedDescription(
                    base: "Friendly Tamil conversation partner specializing in daily conversations and cultural expressions.",
                    level: userLevel,
                    currentLesson: currentLesson,
                    focus: "conversation practice"
                ),
                expertise: ["Daily Conversations", "Cultural Context", "Pronunciation"],
                rating: 4.9,
                isOnline: true,
                profileImageName: "agent_conversational",
                emoji: "🌟"
            ),
            Agent(
                id: "academic_\(userPreferences.selectedLanguage.rawValue)",
                name: "Meenakshi",
                description: personalizedDescription(
                    base: "Academic Tamil tutor specializing in formal language, literature, and advanced grammar.",
                    level: userLevel,
                    currentLesson: currentLesson,
                    focus: "grammar and literature"
                ),
                expertise: ["Academic Tamil", "Literature", "Classical Texts"],
                rating: 4.8,
                isOnline: true,
                profileImageName: "agent_academic",
                emoji: "💼"
            ),
            Agent(
                id: "creative_\(userPreferences.selectedLanguage.rawValue)",
                name: "Mary",
                description: personalizedDescription(
                    base: "Creative Tamil writing coach specializing in Tamil script, storytelling, and artistic expression.",
                    level: userLevel,
                    currentLesson: currentLesson,
                    focus: "creative writing"
                ),
                expertise: ["Tamil Writing", "Creative Writing", "Storytelling"],
                rating: 4.9,
                isOnline: true,
                profileImageName: "agent_creative",
                emoji: "✈️"
            ),
            Agent(
                id: "cultural_\(userPreferences.selectedLanguage.rawValue)",
                name: "Ibrahim",
                description: personalizedDescription(
                    base: "Tamil culture and language expert specializing in practical usage and cultural immersion.",
                    level: userLevel,
                    currentLesson: currentLesson,
                    focus: "cultural context"
                ),
                expertise: ["Tamil Culture", "Practical Usage", "Language Application"],
                rating: 4.9,
                isOnline: true,
                profileImageName: "agent_cultural",
                emoji: "🏛️"
            )
        ]
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            modernHeaderView
            
            // Main Content
            if isLoading {
                loadingView
            } else if let errorMessage = errorMessage {
                errorView(message: errorMessage)
            } else {
                ScrollView {
                    VStack(spacing: 24) {
                        // Hero Section
                        heroSectionView

                        // Agents Grid
                        agentsGridView
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 100)
                }
            }
        }
        .navigationBarHidden(true)
        .background(Color(.systemBackground))
        .sheet(isPresented: $showingAgentChat) {
            if let agent = selectedAgent {
                AIAgentChatView(agent: convertToLanguageTutor(agent))
            }
        }
        .task {
            await loadAgents()
        }
    }

    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Loading Tamil assistants...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - Error View
    private func errorView(message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            Text("Unable to Load Assistants")
                .font(.headline)
                .fontWeight(.semibold)
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            Button("Retry") {
                Task {
                    await loadAgents()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }

    // MARK: - Helper Methods
    private func personalizedDescription(base: String, level: CEFRLevel, currentLesson: TamilLesson?, focus: String) -> String {
        var description = base

        // Add level-specific context
        description += " Perfect for \(level.displayName) level learners."

        // Add current lesson context if available
        if let lesson = currentLesson {
            description += " Ready to help with \(lesson.titleEnglish) and \(focus)."
        } else {
            description += " Ready to help with \(focus) and personalized learning."
        }

        return description
    }

    private func loadAgents() async {
        isLoading = true
        errorMessage = nil

        // Simulate loading time and potential errors
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

        // In a real implementation, this would load agent data from a service
        // For now, we'll just complete successfully
        isLoading = false
    }

    private func convertToLanguageTutor(_ agent: Agent) -> LanguageTutor {
        return LanguageTutor(
            persona: .beginnerEnthusiast, // This could be dynamic based on agent type
            language: .tamil,
            name: agent.name,
            avatar: agent.emoji,
            description: agent.description,
            specialties: agent.expertise,
            systemPrompt: "You are \(agent.name), a Tamil language learning assistant specializing in \(agent.expertise.joined(separator: ", ")). Help users learn Tamil in a friendly and encouraging way."
        )
    }
    
    // MARK: - Header View
    private var modernHeaderView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text("Tamil Assist")
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                Text("AI language learning assistants")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // Tamil cultural symbol
            Text("🤖")
                .font(.title2)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - Intro Section
    private var heroSectionView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Choose Your Assistant")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Connect with specialized AI tutors for personalized Tamil learning")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.top, 16)
    }
    
    // MARK: - Agents Grid
    private var agentsGridView: some View {
        VStack(spacing: 16) {
            // Agent Cards
            ForEach(Array(coreAgents.enumerated()), id: \.1.id) { index, agent in
                ModernAgentCardView(
                    agent: agent,
                    isPrimary: index == 0,
                    onTap: {
                        selectedAgent = agent
                        showingAgentChat = true
                    }
                )
            }
        }
    }
}

// MARK: - Modern Agent Card View
struct ModernAgentCardView: View {
    let agent: Agent
    let isPrimary: Bool
    let onTap: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // Agent Header
                HStack(spacing: 16) {
                    // Profile Section
                    ZStack {
                        Circle()
                            .fill(
                                isPrimary ?
                                LinearGradient(
                                    colors: [Color.blue.opacity(0.3), Color.blue],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ) :
                                LinearGradient(
                                    colors: [Color(.systemGray5), Color(.systemGray4)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 60, height: 60)
                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                        
                        Text(agent.emoji)
                            .font(.title)
                        
                        // Online Status
                        if agent.isOnline {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 16, height: 16)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 2)
                                )
                                .offset(x: 20, y: 20)
                        }
                    }
                    
                    // Agent Info
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(agent.name)
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            // Rating
                            HStack(spacing: 4) {
                                Image(systemName: "star.fill")
                                    .font(.caption)
                                    .foregroundColor(.yellow)
                                
                                Text("\(agent.rating, specifier: "%.1f")")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Text(agent.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                            .lineLimit(2)
                        
                        // Expertise Tags
                        HStack {
                            ForEach(agent.expertise.prefix(3), id: \.self) { skill in
                                Text(skill)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.blue.opacity(0.1))
                                    .clipShape(Capsule())
                            }
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                
                // Action Section
                HStack {
                    Spacer()
                    
                    HStack(spacing: 8) {
                        if agent.isOnline {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                        
                        Text(agent.isOnline ? "Start Chat" : "Offline")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(agent.isOnline ? .blue : .secondary)
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        agent.isOnline ?
                        Color.blue.opacity(0.1) :
                        Color(.systemGray6)
                    )
                    .clipShape(Capsule())
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(.systemBackground),
                                isPrimary ? Color.blue.opacity(0.03) : Color(.systemGray6).opacity(0.2)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                isPrimary ? Color.blue.opacity(0.1) : Color(.systemGray5).opacity(0.5),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: Color.black.opacity(isPressed ? 0.1 : 0.05),
                        radius: isPressed ? 4 : 8,
                        x: 0,
                        y: isPressed ? 1 : 2
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .disabled(!agent.isOnline)
    }
}

// MARK: - Agent Model
struct Agent {
    let id: String
    let name: String
    let description: String
    let expertise: [String]
    let rating: Double
    let isOnline: Bool
    let profileImageName: String
    let emoji: String
}

#Preview {
    AgentsView()
}
