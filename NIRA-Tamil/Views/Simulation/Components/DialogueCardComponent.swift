//
//  DialogueCardComponent.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI
import Foundation

struct DialogueCardComponent: View {
    let dialogue: SimulationDialogue
    let persona: Simulation<PERSON>ersona
    let onPlayAudio: (URL) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Speaker Info
            speakerInfoSection

            // Dialogue Text
            dialogueTextSection

            // Vocabulary Highlights
            if !dialogue.vocabularyHighlights.isEmpty {
                vocabularySection
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        .accessibilityElement(children: .contain)
    }

    private var speakerInfoSection: some View {
        HStack {
            Circle()
                .fill(dialogue.speakerRole == "user" ? Color.blue : Color.green)
                .frame(width: 40, height: 40)
                .overlay(
                    Text(dialogue.speakerRole == "user" ? "You" : (dialogue.speakerName?.prefix(1) ?? "N").uppercased())
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                )
                .accessibilityHidden(true)

            VStack(alignment: .leading, spacing: 2) {
                Text(dialogue.speakerRole == "user" ? "You" : dialogue.speakerName ?? "NPC")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(dialogue.speakerRole.capitalized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .accessibilityElement(children: .combine)
            .accessibilityLabel("Speaker: \(dialogue.speakerRole == "user" ? "You" : dialogue.speakerName ?? "NPC")")

            Spacer()

            // Audio Button
            if let audioUrlString = dialogue.audioUrl,
               let audioUrl = URL(string: audioUrlString) {
                Button(action: { onPlayAudio(audioUrl) }) {
                    Image(systemName: "speaker.wave.2.fill")
                        .foregroundColor(Color(hex: SimulationService.shared.getPersonaColor(persona.colorTheme)))
                        .font(.title3)
                }
                .accessibilityLabel("Play audio")
                .accessibilityHint("Plays pronunciation of this dialogue")
            }
        }
    }

    private var dialogueTextSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(dialogue.dialogueText)
                .font(.body)
                .padding()
                .background(
                    dialogue.speakerRole == "user" ?
                    Color.blue.opacity(0.1) :
                    Color(.systemGray6)
                )
                .cornerRadius(12)
                .accessibilityLabel("Dialogue: \(dialogue.dialogueText)")

            // Translation (if available)
            if let translation = dialogue.dialogueTranslation {
                Text(translation)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
                    .accessibilityLabel("Translation: \(translation)")
            }
        }
    }

    private var vocabularySection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Key Vocabulary")
                .font(.caption)
                .fontWeight(.medium)
                .accessibilityAddTraits(.isHeader)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(dialogue.vocabularyHighlights, id: \.self) { word in
                    Text(word)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(hex: SimulationService.shared.getPersonaColor(persona.colorTheme)).opacity(0.2))
                        .cornerRadius(8)
                        .accessibilityLabel("Vocabulary word: \(word)")
                }
            }
            .accessibilityElement(children: .contain)
            .accessibilityLabel("Key vocabulary words")
        }
    }
}

#Preview {
    DialogueCardComponent(
        dialogue: SimulationDialogue(
            id: UUID(),
            simulationId: UUID(),
            sequenceOrder: 1,
            speakerRole: "npc",
            speakerName: "Marie",
            dialogueText: "Bonjour! Comment allez-vous?",
            dialogueTranslation: "Hello! How are you?",
            audioUrl: nil,
            responseOptions: [],
            culturalContext: nil,
            vocabularyHighlights: ["Bonjour", "Comment", "allez-vous"],
            grammarNotes: nil,
            difficultyLevel: "beginner",
            isCriticalPath: true,
            createdAt: Date()
        ),
        persona: SimulationPersona(
            id: UUID(),
            name: "sample",
            displayName: "Sample Persona",
            description: "Sample",
            targetAudience: "Sample",
            difficultyRange: "A1-B2",
            colorTheme: "#007AFF",
            iconName: "person",
            learningObjectives: nil as [String]?,
            typicalScenarios: nil as [String]?,
            vocabularyFocus: nil as [String]?,
            personalityTraits: nil as [String: SupabaseAnyCodable]?,
            teachingStyle: nil as String?,
            isActive: true,
            sortOrder: 1,
            createdAt: Date(),
            updatedAt: Date()
        ),
        onPlayAudio: { _ in }
    )
}
