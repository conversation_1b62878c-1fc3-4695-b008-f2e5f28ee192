//
//  SimulationProgressComponent.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

struct SimulationProgressComponent: View {
    let currentIndex: Int
    let totalCount: Int
    let persona: SimulationPersona

    private var progress: Double {
        guard totalCount > 0 else { return 0.0 }
        return Double(currentIndex) / Double(totalCount)
    }

    var body: some View {
        VStack(spacing: 8) {
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: Color(hex: SimulationService.shared.getPersonaColor(persona.colorTheme))))
                .accessibilityLabel("Simulation progress")
                .accessibilityValue("\(currentIndex) of \(totalCount) completed")

            HStack {
                Text("Progress")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(currentIndex)/\(totalCount)")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal)
    }
}

#Preview {
    SimulationProgressComponent(
        currentIndex: 3,
        totalCount: 10,
        persona: SimulationPersona(
            id: UUID(),
            name: "sample",
            displayName: "Sample Persona",
            description: "Sample",
            targetAudience: "Sample",
            difficultyRange: "A1-B2",
            colorTheme: "#007AFF",
            iconName: "person",
            learningObjectives: nil,
            typicalScenarios: nil,
            vocabularyFocus: nil,
            personalityTraits: nil,
            teachingStyle: nil,
            isActive: true,
            sortOrder: 1,
            createdAt: Date(),
            updatedAt: Date()
        )
    )
}
