//
//  AIAgentChatView.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

// MARK: - AI Agent Chat View

struct AIAgentChatView: View {
    let agent: LanguageTutor
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var chatViewModel = ChatViewModel()
    @State private var messageText = ""
    @State private var showingAttachments = false
    @State private var showingHistory = false

    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Chat Header
                chatHeaderView
                
                // Messages List
                messagesListView
                
                // Input Area
                chatInputView
            }
            .navigationBarHidden(true)
            .onAppear {
                chatViewModel.initializeChat(with: agent)
            }
            .sheet(isPresented: $showingAttachments) {
                AttachmentPickerView { attachment in
                    chatViewModel.sendAttachment(attachment)
                }
            }
            .sheet(isPresented: $showingHistory) {
                ConversationHistoryView(messages: chatViewModel.messages, agent: agent)
            }
            .sheet(isPresented: $chatViewModel.showingAttachmentPicker) {
                AttachmentPickerView { attachment in
                    chatViewModel.sendAttachment(attachment)
                }
            }
            .fullScreenCover(isPresented: $chatViewModel.showingLiveVoiceInterface) {
                LiveVoiceInterfaceView(agent: agent)
            }
        }
    }
    
    // MARK: - Chat Header
    private var chatHeaderView: some View {
        HStack(spacing: 12) {
            Button("Done") {
                presentationMode.wrappedValue.dismiss()
            }
            .foregroundColor(.blue)
            
            Spacer()
            
            VStack(spacing: 2) {
                HStack(spacing: 8) {
                    Text(agent.avatar)
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 1) {
                        Text(agent.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        HStack(spacing: 4) {
                            Circle()
                                .fill(chatViewModel.isOnline ? .green : .gray)
                                .frame(width: 6, height: 6)
                            
                            Text(chatViewModel.isOnline ? "Online" : "Offline")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                if chatViewModel.isTyping {
                    Text("typing...")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .italic()
                }
            }
            
            Spacer()
            
            Button("History") {
                showingHistory = true
            }
            .font(.subheadline)
            .foregroundColor(.blue)
        }
        .padding()
        .background(Color(.systemBackground))
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(Color(.separator)),
            alignment: .bottom
        )
    }
    
    // MARK: - Messages List
    private var messagesListView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(chatViewModel.messages) { message in
                        ChatMessageView(message: message, agent: agent)
                            .id(message.id)
                    }
                    
                    if chatViewModel.isTyping {
                        AITypingIndicatorView(agent: agent)
                            .id("typing")
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
            }
            .onChange(of: chatViewModel.messages.count) { _, _ in
                withAnimation(.easeInOut(duration: 0.3)) {
                    if let lastMessage = chatViewModel.messages.last {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
            .onChange(of: chatViewModel.isTyping) { _, isTyping in
                if isTyping {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        proxy.scrollTo("typing", anchor: .bottom)
                    }
                }
            }
        }
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Chat Input
    private var chatInputView: some View {
        VStack(spacing: 0) {
            // Suggestions (if any)
            if !chatViewModel.suggestions.isEmpty {
                suggestionsView
            }
            
            // Input area
            HStack(spacing: 8) {
                // Attachment button
                Button(action: { showingAttachments = true }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
                
                // Live Voice button
                Button(action: { chatViewModel.startLiveVoiceConversation() }) {
                    Image(systemName: "phone.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                }
                
                // Text input
                HStack(spacing: 8) {
                    TextField("Message \(agent.name)...", text: $messageText, axis: .vertical)
                        .textFieldStyle(PlainTextFieldStyle())
                        .lineLimit(1...6)
                        .focused($isTextFieldFocused)
                        .onSubmit {
                            sendMessage()
                        }
                    
                    // Voice input button
                    Button(action: { chatViewModel.toggleVoiceRecording() }) {
                        Image(systemName: chatViewModel.isRecording ? "stop.circle.fill" : "mic.circle.fill")
                            .font(.title2)
                            .foregroundColor(chatViewModel.isRecording ? .red : .blue)
                            .scaleEffect(chatViewModel.isRecording ? 1.2 : 1.0)
                            .animation(.easeInOut(duration: 0.2), value: chatViewModel.isRecording)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(.systemGray6))
                )
                
                // Send button
                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .blue)
                }
                .disabled(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || chatViewModel.isLoading)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(Color(.systemBackground))
            .overlay(
                Rectangle()
                    .frame(height: 0.5)
                    .foregroundColor(Color(.separator)),
                alignment: .top
            )
        }
    }
    
    // MARK: - Suggestions View
    private var suggestionsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(chatViewModel.suggestions, id: \.self) { suggestion in
                    Button(suggestion) {
                        messageText = suggestion
                        sendMessage()
                    }
                    .font(.subheadline)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(16)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Helper Methods
    private func sendMessage() {
        let text = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return }
        
        messageText = ""
        isTextFieldFocused = false
        
        Task {
            await chatViewModel.sendMessage(text)
        }
    }
    
    private func toggleRecording() {
        chatViewModel.isRecording.toggle()
        if chatViewModel.isRecording {
            chatViewModel.startVoiceRecording()
        } else {
            chatViewModel.stopVoiceRecording()
        }
    }
}

// MARK: - Chat Message View
struct ChatMessageView: View {
    let message: AIChatMessage
    let agent: LanguageTutor
    @State private var showingDetails = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if message.isFromUser {
                Spacer(minLength: 50)
                userMessageView
            } else {
                agentMessageView
                Spacer(minLength: 50)
            }
        }
        .onTapGesture {
            showingDetails.toggle()
        }
    }
    
    private var userMessageView: some View {
        VStack(alignment: .trailing, spacing: 4) {
            messageContentView(isUser: true)
            messageMetadataView(isUser: true)
        }
    }
    
    private var agentMessageView: some View {
        HStack(alignment: .top, spacing: 8) {
            // Agent Avatar
            agentAvatarView
            
            VStack(alignment: .leading, spacing: 4) {
                messageContentView(isUser: false)
                messageMetadataView(isUser: false)
                
                // Show additional features for agent messages
                if !message.isFromUser && showingDetails {
                    agentMessageExtrasView
                }
            }
        }
    }
    
    private var agentAvatarView: some View {
        Text(agent.avatar)
            .font(.title2)
            .frame(width: 32, height: 32)
            .background(
                Circle()
                    .fill(Color.niraPrimary.opacity(0.2))
            )
    }
    
    private func messageContentView(isUser: Bool) -> some View {
        VStack(alignment: isUser ? .trailing : .leading, spacing: 8) {
            // Main message content
            Text(message.content)
                .font(.body)
                .foregroundColor(isUser ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 18, style: .continuous)
                        .fill(isUser ? Color.blue : Color(.systemGray5))
                )
                .frame(maxWidth: UIScreen.main.bounds.width * 0.7, alignment: isUser ? .trailing : .leading)
            
            // Attachments (if any)
            if let attachments = message.attachments, !attachments.isEmpty {
                ForEach(attachments, id: \.id) { attachment in
                    AttachmentView(attachment: attachment)
                }
            }
            
            // Vocabulary highlights (for agent messages)
            if !isUser, let highlights = message.vocabularyHighlights, !highlights.isEmpty {
                VocabularyHighlightsView(highlights: highlights)
            }
        }
    }
    
    private func messageMetadataView(isUser: Bool) -> some View {
        HStack(spacing: 8) {
            Text(message.timestamp.formatted(date: .omitted, time: .shortened))
                .font(.caption2)
                .foregroundColor(.secondary)
            
            if !isUser, let responseTime = message.responseTime {
                Text("• \(String(format: "%.1f", responseTime))s")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // Message status for user messages
            if isUser {
                Image(systemName: message.status.iconName)
                    .font(.caption2)
                    .foregroundColor(message.status.color)
            }
        }
    }
    
    private var agentMessageExtrasView: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Cultural notes
            if let culturalNotes = message.culturalNotes, !culturalNotes.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Cultural Notes")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                    
                    ForEach(culturalNotes, id: \.self) { note in
                        Text("• \(note)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
            
            // Grammar tips
            if let grammarTips = message.grammarTips, !grammarTips.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Grammar Tips")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                    
                    ForEach(grammarTips, id: \.self) { tip in
                        Text("• \(tip)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.green.opacity(0.1))
                .cornerRadius(8)
            }
        }
    }
}

// MARK: - Supporting Views

struct AITypingIndicatorView: View {
    let agent: LanguageTutor
    @State private var animationPhase = 0
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text(agent.avatar)
                .font(.title2)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(Color.niraPrimary.opacity(0.2))
                )
            
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.gray)
                        .frame(width: 8, height: 8)
                        .scaleEffect(animationPhase == index ? 1.2 : 0.8)
                        .animation(
                            .easeInOut(duration: 0.6)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                            value: animationPhase
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 18, style: .continuous)
                    .fill(Color(.systemGray5))
            )
            
            Spacer(minLength: 50)
        }
        .onAppear {
            animationPhase = 0
        }
    }
}

struct AttachmentView: View {
    let attachment: MessageAttachment
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: attachment.type.iconName)
                .font(.caption)
                .foregroundColor(.blue)
            
            Text(attachment.name)
                .font(.caption)
                .foregroundColor(.blue)
                .lineLimit(1)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }
}

struct VocabularyHighlightsView: View {
    let highlights: [VocabularyHighlight]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Vocabulary")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
            
            ForEach(highlights, id: \.word) { highlight in
                HStack(spacing: 8) {
                    Text(highlight.word)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                    
                    Text("-")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(highlight.definition)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.purple.opacity(0.1))
        .cornerRadius(8)
    }
}

struct ConversationHistoryView: View {
    let messages: [AIChatMessage]
    let agent: LanguageTutor
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var chatHistoryService = ChatHistoryService.shared
    @State private var selectedConversation: SupabaseChatConversation?

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 12) {
                    if chatHistoryService.conversations.isEmpty {
                        VStack(spacing: 16) {
                            Image(systemName: "message")
                                .font(.system(size: 50))
                                .foregroundColor(.gray)

                            Text("No conversation history")
                                .font(.headline)
                                .foregroundColor(.secondary)

                            Text("Start chatting with \(agent.name) to see your conversation history here.")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                        .padding(.top, 50)
                    } else {
                        ForEach(chatHistoryService.conversations) { conversation in
                            ConversationHistoryCard(
                                conversation: conversation,
                                agent: agent,
                                onTap: {
                                    selectedConversation = conversation
                                }
                            )
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Conversation History")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Refresh") {
                        Task {
                            await chatHistoryService.loadUserConversations()
                        }
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .onAppear {
                Task {
                    print("🔄 ConversationHistoryView appeared - loading conversations...")
                    await chatHistoryService.loadUserConversations()
                }
            }
            .refreshable {
                await chatHistoryService.loadUserConversations()
            }
        }
        .sheet(item: $selectedConversation) { conversation in
            ChatConversationDetailView(conversation: conversation, agent: agent)
        }
    }
}

struct ConversationHistoryCard: View {
    let conversation: SupabaseChatConversation
    let agent: LanguageTutor
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Agent Avatar
                Circle()
                    .fill(LinearGradient(
                        colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text(String(agent.name.prefix(1)))
                            .font(.title2.bold())
                            .foregroundColor(.white)
                    )

                VStack(alignment: .leading, spacing: 4) {
                    Text(conversation.title ?? "Untitled Conversation")
                        .font(.headline)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    Text("Chat with \(agent.name)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text(conversation.updatedAt, style: .relative)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ChatConversationDetailView: View {
    let conversation: SupabaseChatConversation
    let agent: LanguageTutor
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var chatHistoryService = ChatHistoryService.shared
    @State private var messages: [SupabaseChatMessage] = []
    @State private var isLoading = true

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 12) {
                    if isLoading {
                        ProgressView("Loading conversation...")
                            .padding()
                    } else if messages.isEmpty {
                        Text("No messages in this conversation")
                            .foregroundColor(.secondary)
                            .padding()
                    } else {
                        ForEach(messages) { message in
                            ConversationMessageView(message: message, agent: agent)
                        }
                    }
                }
                .padding()
            }
            .navigationTitle(conversation.title ?? "Conversation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .onAppear {
                loadConversationMessages()
            }
        }
    }

    private func loadConversationMessages() {
        Task {
            do {
                messages = try await chatHistoryService.getConversationMessages(conversationId: conversation.id)
                isLoading = false
            } catch {
                print("Failed to load conversation messages: \(error)")
                isLoading = false
            }
        }
    }
}

struct ConversationMessageView: View {
    let message: SupabaseChatMessage
    let agent: LanguageTutor

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if message.isFromUser {
                Spacer(minLength: 50)
                userMessageView
            } else {
                agentMessageView
                Spacer(minLength: 50)
            }
        }
    }

    private var userMessageView: some View {
        VStack(alignment: .trailing, spacing: 4) {
            Text(message.content)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(Color.blue)
                .foregroundColor(.white)
                .clipShape(RoundedRectangle(cornerRadius: 16))

            Text(message.createdAt, style: .time)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    private var agentMessageView: some View {
        HStack(alignment: .top, spacing: 8) {
            Circle()
                .fill(LinearGradient(
                    colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.8)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 32, height: 32)
                .overlay(
                    Text(String(agent.name.prefix(1)))
                        .font(.caption.bold())
                        .foregroundColor(.white)
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(message.content)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color(.systemGray5))
                    .clipShape(RoundedRectangle(cornerRadius: 16))

                Text(message.createdAt, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct HistoryMessageView: View {
    let message: AIChatMessage
    let agent: LanguageTutor
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if message.isFromUser {
                Spacer(minLength: 50)
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .font(.body)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 18, style: .continuous)
                                .fill(Color.blue)
                        )
                        .frame(maxWidth: UIScreen.main.bounds.width * 0.7, alignment: .trailing)
                    
                    Text(message.timestamp.formatted(date: .abbreviated, time: .shortened))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            } else {
                HStack(alignment: .top, spacing: 8) {
                    Text(agent.avatar)
                        .font(.title2)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(Color.niraPrimary.opacity(0.2))
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(message.content)
                            .font(.body)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 18, style: .continuous)
                                    .fill(Color(.systemGray5))
                            )
                            .frame(maxWidth: UIScreen.main.bounds.width * 0.7, alignment: .leading)
                        
                        Text(message.timestamp.formatted(date: .abbreviated, time: .shortened))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer(minLength: 50)
            }
        }
    }
}

// MARK: - Extensions

extension MessageStatus {
    var iconName: String {
        switch self {
        case .sending: return "clock"
        case .sent: return "checkmark"
        case .delivered: return "checkmark.circle"
        case .read: return "checkmark.circle.fill"
        case .failed: return "exclamationmark.triangle"
        }
    }
    
    var color: Color {
        switch self {
        case .sending: return .gray
        case .sent: return .blue
        case .delivered: return .blue
        case .read: return .green
        case .failed: return .red
        }
    }
}

extension AttachmentType {
    var iconName: String {
        switch self {
        case .image: return "photo"
        case .audio: return "mic"
        case .document: return "doc"
        case .video: return "video"
        }
    }
}

#Preview {
    AIAgentChatView(agent: LanguageTutor.mockAgents[0])
} 