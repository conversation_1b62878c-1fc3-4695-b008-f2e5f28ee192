//
//  ProgressTrackingView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 18/06/2025.
//

import SwiftUI

struct ProgressTrackingView: View {
    @StateObject private var progressService = ProgressiveLearningService.shared
    @StateObject private var contentService = TamilContentService.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Current Level & Progress
                    currentLevelSection
                    
                    // Daily Progress
                    dailyProgressSection
                    
                    // Learning Path
                    learningPathSection
                    
                    // Recommendations
                    recommendationsSection
                }
                .padding()
            }
            .navigationTitle("Your Progress")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    // MARK: - Current Level Section
    
    private var currentLevelSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Current Level")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            HStack(spacing: 20) {
                // Level indicator
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .fill(levelColor.opacity(0.2))
                            .frame(width: 80, height: 80)
                        
                        Text(progressService.currentUserLevel.rawValue)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(levelColor)
                    }
                    
                    Text(progressService.currentUserLevel.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text(progressService.currentUserLevel.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // Progress to next level
                    if let nextLevel = getNextLevel() {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Progress to \(nextLevel.rawValue)")
                                .font(.caption)
                                .fontWeight(.medium)
                            
                            ProgressView(value: currentLevelProgress)
                                .progressViewStyle(LinearProgressViewStyle(tint: levelColor))
                            
                            Text("\(Int(currentLevelProgress * 100))% complete")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Daily Progress Section
    
    private var dailyProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Today's Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text("Streak: \(progressService.currentStreak) days")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.orange)
            }
            
            HStack(spacing: 16) {
                ProgressMetric(
                    title: "Lessons",
                    current: progressService.todaysProgress.lessonsCompleted,
                    goal: progressService.dailyGoal,
                    icon: "book.fill",
                    color: .blue
                )
                
                ProgressMetric(
                    title: "Vocabulary",
                    current: progressService.todaysProgress.vocabularyMastered,
                    goal: 10, // Daily vocab goal
                    icon: "textbook.fill",
                    color: .green
                )
                
                ProgressMetric(
                    title: "Study Time",
                    current: Int(progressService.todaysProgress.studyTime / 60),
                    goal: 30, // 30 minutes daily
                    icon: "clock.fill",
                    color: .purple
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Learning Path Section
    
    private var learningPathSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Learning Path")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            LazyVStack(spacing: 12) {
                ForEach(progressService.learningPath.prefix(5)) { node in
                    LearningPathNodeView(node: node)
                }
            }
            
            if progressService.learningPath.count > 5 {
                Button("View Full Path") {
                    // Navigate to full learning path view
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Recommendations Section
    
    private var recommendationsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recommendations")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                // Next lesson recommendation
                if let nextLesson = progressService.getNextRecommendedLesson() {
                    RecommendationCard(
                        title: "Continue Learning",
                        subtitle: nextLesson.titleEnglish,
                        description: "Next lesson in your path",
                        icon: "arrow.right.circle.fill",
                        color: .blue
                    )
                }
                
                // Review recommendations
                let reviewLessons = progressService.getReviewLessons()
                if !reviewLessons.isEmpty {
                    RecommendationCard(
                        title: "Review Previous Lessons",
                        subtitle: "\(reviewLessons.count) lessons to review",
                        description: "Strengthen your foundation",
                        icon: "arrow.clockwise.circle.fill",
                        color: .orange
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Computed Properties
    
    private var levelColor: Color {
        switch progressService.currentUserLevel {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
    
    private var currentLevelProgress: Double {
        let currentLevelLessons = contentService.getLessonsForLevel(progressService.currentUserLevel)
        let completedInCurrentLevel = currentLevelLessons.filter { lesson in
            progressService.completedLessons.contains("\(lesson.levelCode)-L\(lesson.lessonNumber)")
        }.count
        
        return currentLevelLessons.isEmpty ? 0.0 : Double(completedInCurrentLevel) / Double(currentLevelLessons.count)
    }
    
    private func getNextLevel() -> CEFRLevel? {
        let levels = CEFRLevel.allCases
        guard let currentIndex = levels.firstIndex(of: progressService.currentUserLevel),
              currentIndex < levels.count - 1 else {
            return nil
        }
        return levels[currentIndex + 1]
    }
}

// MARK: - Supporting Views

struct ProgressMetric: View {
    let title: String
    let current: Int
    let goal: Int
    let icon: String
    let color: Color
    
    var progress: Double {
        return goal > 0 ? min(Double(current) / Double(goal), 1.0) : 0.0
    }
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            Text("\(current)/\(goal)")
                .font(.subheadline)
                .fontWeight(.bold)
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
                .frame(height: 4)
        }
        .frame(maxWidth: .infinity)
    }
}

struct LearningPathNodeView: View {
    let node: LearningPathNode
    
    var body: some View {
        HStack(spacing: 12) {
            // Status indicator
            ZStack {
                Circle()
                    .fill(statusColor.opacity(0.2))
                    .frame(width: 32, height: 32)
                
                Image(systemName: statusIcon)
                    .font(.caption)
                    .foregroundColor(statusColor)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(node.lesson.titleEnglish)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(node.isUnlocked ? .primary : .secondary)
                
                HStack {
                    Text(node.level.rawValue)
                        .font(.caption2)
                        .fontWeight(.bold)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(levelColor.opacity(0.2))
                        .foregroundColor(levelColor)
                        .cornerRadius(4)
                    
                    Text("Lesson \(node.lesson.lessonNumber)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    if node.adaptiveRecommendation != .standard {
                        Text(node.adaptiveRecommendation.description)
                            .font(.caption2)
                            .foregroundColor(.orange)
                    }
                }
            }
            
            Spacer()
        }
        .opacity(node.isUnlocked ? 1.0 : 0.6)
    }
    
    private var statusColor: Color {
        if node.isCompleted {
            return .green
        } else if node.isUnlocked {
            return .blue
        } else {
            return .gray
        }
    }
    
    private var statusIcon: String {
        if node.isCompleted {
            return "checkmark"
        } else if node.isUnlocked {
            return "play.fill"
        } else {
            return "lock.fill"
        }
    }
    
    private var levelColor: Color {
        switch node.level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
}

struct RecommendationCard: View {
    let title: String
    let subtitle: String
    let description: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
        )
    }
}
