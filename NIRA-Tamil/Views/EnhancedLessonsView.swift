import SwiftUI

struct EnhancedLessonsView: View {
    @State private var selectedSubTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Clean Header with Language Selector
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Learn Tamil")
                            .font(.system(size: 24, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                        Text("Discover Tamil culture & language")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()

                    // Tamil flag indicator
                    Text("🇮🇳")
                        .font(.title2)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                
                // Three-tab picker for Lessons, Read, Write
                Picker("Learning Type", selection: $selectedSubTab) {
                    Text("Lessons").tag(0)
                    Text("Read").tag(1)
                    Text("Write").tag(2)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal, 16)
                .padding(.bottom, 8)

                // Content based on selected sub-tab
                TabView(selection: $selectedSubTab) {
                    // Lessons tab: Existing lessons functionality
                    LessonsView()
                        .tag(0)

                    // Read tab: Reading practice moved from Practice
                    EnhancedReadingPracticeView()
                        .tag(1)

                    // Write tab: Writing practice moved from Practice
                    EnhancedWritingPracticeView()
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationBarHidden(true)
        }
    }
}



// Extension for system background color
extension Color {
    static let systemBackground = Color(UIColor.systemBackground)
}

#Preview {
    EnhancedLessonsView()
} 