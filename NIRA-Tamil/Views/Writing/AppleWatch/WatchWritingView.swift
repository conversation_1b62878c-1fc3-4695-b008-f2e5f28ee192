//
//  WatchWritingView.swift
//  NIRA-Tamil Watch App
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI
#if os(watchOS)
import WatchKit
import WatchConnectivity
#endif

#if os(watchOS)
struct WatchWritingView: View {
    @StateObject private var watchWritingService = WatchWritingService.shared
    @StateObject private var scriptService = TamilScriptService.shared
    
    @State private var selectedCharacter: TamilCharacter?
    @State private var currentSession: WatchWritingSession?
    @State private var showingCharacterPicker = false
    @State private var showingScribbleInput = false
    @State private var practiceMode: WatchPracticeMode = .quickPractice
    
    var body: some View {
        NavigationView {
            VStack(spacing: 12) {
                // Header
                watchHeader
                
                // Main content
                if let session = currentSession {
                    activeSessionView(session)
                } else {
                    practiceSelectionView
                }
            }
            .padding(.horizontal, 8)
            .navigationBarHidden(true)
        }
        .onAppear {
            setupWatchInterface()
        }
        .sheet(isPresented: $showingCharacterPicker) {
            WatchCharacterPickerView(
                selectedCharacter: $selectedCharacter,
                onCharacterSelected: startPracticeSession
            )
        }
        .sheet(isPresented: $showingScribbleInput) {
            if let character = selectedCharacter {
                WatchScribbleInputView(
                    character: character,
                    onComplete: handleScribbleComplete
                )
            }
        }
    }
    
    private var watchHeader: some View {
        VStack(spacing: 4) {
            Text("Tamil Writing")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.blue)
            
            if let character = selectedCharacter {
                Text(character.character)
                    .font(.largeTitle)
                    .fontWeight(.light)
            } else {
                Text("Select Character")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var practiceSelectionView: some View {
        VStack(spacing: 16) {
            // Practice mode selector
            WatchPracticeModeSelector(
                selectedMode: $practiceMode,
                onModeChanged: { mode in
                    practiceMode = mode
                }
            )
            
            // Quick action buttons
            VStack(spacing: 8) {
                WatchActionButton(
                    title: "Select Character",
                    icon: "textformat",
                    color: .blue,
                    action: { showingCharacterPicker = true }
                )
                
                if selectedCharacter != nil {
                    WatchActionButton(
                        title: "Start Practice",
                        icon: "pencil",
                        color: .green,
                        action: startQuickPractice
                    )
                    
                    WatchActionButton(
                        title: "Scribble Mode",
                        icon: "scribble",
                        color: .orange,
                        action: { showingScribbleInput = true }
                    )
                }
            }
            
            // Recent practice
            if !watchWritingService.recentCharacters.isEmpty {
                WatchRecentCharactersView(
                    characters: watchWritingService.recentCharacters,
                    onCharacterTap: { character in
                        selectedCharacter = character
                        startPracticeSession(character)
                    }
                )
            }
        }
    }
    
    private func activeSessionView(_ session: WatchWritingSession) -> some View {
        VStack(spacing: 12) {
            // Session progress
            WatchSessionProgressView(session: session)
            
            // Current character display
            WatchCharacterDisplayView(
                character: session.currentCharacter,
                attempt: session.currentAttempt
            )
            
            // Session controls
            WatchSessionControlsView(
                session: session,
                onNext: advanceSession,
                onComplete: completeSession,
                onCancel: cancelSession
            )
        }
    }
    
    // MARK: - Actions
    
    private func setupWatchInterface() {
        watchWritingService.loadRecentCharacters()
    }
    
    private func startPracticeSession(_ character: TamilCharacter) {
        selectedCharacter = character
        
        let session = WatchWritingSession(
            id: UUID(),
            characters: [character],
            practiceMode: practiceMode,
            startTime: Date(),
            currentIndex: 0,
            attempts: [],
            isCompleted: false
        )
        
        currentSession = session
        watchWritingService.startSession(session)
    }
    
    private func startQuickPractice() {
        guard let character = selectedCharacter else { return }
        startPracticeSession(character)
    }
    
    private func advanceSession() {
        guard let session = currentSession else { return }
        
        if session.currentIndex < session.characters.count - 1 {
            let updatedSession = session.advanceToNext()
            currentSession = updatedSession
            watchWritingService.updateSession(updatedSession)
        } else {
            completeSession()
        }
    }
    
    private func completeSession() {
        guard let session = currentSession else { return }
        
        let completedSession = session.complete()
        watchWritingService.completeSession(completedSession)
        
        currentSession = nil
        
        // Provide haptic feedback
        WKInterfaceDevice.current().play(.success)
    }
    
    private func cancelSession() {
        currentSession = nil
        WKInterfaceDevice.current().play(.click)
    }
    
    private func handleScribbleComplete(_ result: WatchScribbleResult) {
        watchWritingService.recordScribbleResult(result)
        
        // Provide feedback based on accuracy
        if result.accuracy > 0.8 {
            WKInterfaceDevice.current().play(.success)
        } else {
            WKInterfaceDevice.current().play(.retry)
        }
    }
}

// MARK: - Watch Practice Mode Selector

enum WatchPracticeMode: String, CaseIterable {
    case quickPractice = "Quick"
    case guided = "Guided"
    case challenge = "Challenge"
    
    var icon: String {
        switch self {
        case .quickPractice: return "bolt.fill"
        case .guided: return "hand.point.up.left.fill"
        case .challenge: return "target"
        }
    }
    
    var color: Color {
        switch self {
        case .quickPractice: return .blue
        case .guided: return .green
        case .challenge: return .orange
        }
    }
}

struct WatchPracticeModeSelector: View {
    @Binding var selectedMode: WatchPracticeMode
    let onModeChanged: (WatchPracticeMode) -> Void
    
    var body: some View {
        VStack(spacing: 6) {
            Text("Practice Mode")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            HStack(spacing: 4) {
                ForEach(WatchPracticeMode.allCases, id: \.self) { mode in
                    Button(action: {
                        selectedMode = mode
                        onModeChanged(mode)
                    }) {
                        VStack(spacing: 2) {
                            Image(systemName: mode.icon)
                                .font(.caption)
                            
                            Text(mode.rawValue)
                                .font(.caption2)
                        }
                        .foregroundColor(selectedMode == mode ? .white : mode.color)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(selectedMode == mode ? mode.color : Color.clear)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
}

// MARK: - Watch Action Button

struct WatchActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.caption)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Watch Recent Characters View

struct WatchRecentCharactersView: View {
    let characters: [TamilCharacter]
    let onCharacterTap: (TamilCharacter) -> Void
    
    var body: some View {
        VStack(spacing: 6) {
            Text("Recent")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 6) {
                    ForEach(characters.prefix(4), id: \.id) { character in
                        Button(action: { onCharacterTap(character) }) {
                            Text(character.character)
                                .font(.title3)
                                .foregroundColor(.primary)
                                .frame(width: 32, height: 32)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color(.systemGray6))
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
    }
}

// MARK: - Watch Session Progress View

struct WatchSessionProgressView: View {
    let session: WatchWritingSession
    
    var body: some View {
        VStack(spacing: 4) {
            HStack {
                Text("Progress")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(session.currentIndex + 1)/\(session.characters.count)")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
            
            ProgressView(value: session.progress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .frame(height: 2)
        }
    }
}

// MARK: - Watch Character Display View

struct WatchCharacterDisplayView: View {
    let character: TamilCharacter
    let attempt: Int
    
    var body: some View {
        VStack(spacing: 8) {
            Text(character.character)
                .font(.system(size: 60, weight: .light))
                .foregroundColor(.primary)
            
            VStack(spacing: 2) {
                Text(character.romanization)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                
                Text(character.characterNameEnglish)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            if attempt > 1 {
                Text("Attempt \(attempt)")
                    .font(.caption2)
                    .foregroundColor(.orange)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Watch Session Controls View

struct WatchSessionControlsView: View {
    let session: WatchWritingSession
    let onNext: () -> Void
    let onComplete: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            if session.currentIndex < session.characters.count - 1 {
                WatchActionButton(
                    title: "Next",
                    icon: "arrow.right",
                    color: .blue,
                    action: onNext
                )
            } else {
                WatchActionButton(
                    title: "Complete",
                    icon: "checkmark",
                    color: .green,
                    action: onComplete
                )
            }
            
            Button("Cancel") {
                onCancel()
            }
            .font(.caption)
            .foregroundColor(.red)
        }
    }
}

// MARK: - Watch Writing Session Model

struct WatchWritingSession: Identifiable {
    let id: UUID
    let characters: [TamilCharacter]
    let practiceMode: WatchPracticeMode
    let startTime: Date
    var currentIndex: Int
    var attempts: [WatchWritingAttempt]
    var isCompleted: Bool
    
    var currentCharacter: TamilCharacter {
        characters[currentIndex]
    }
    
    var currentAttempt: Int {
        attempts.filter { $0.characterId == currentCharacter.id }.count + 1
    }
    
    var progress: Double {
        Double(currentIndex) / Double(characters.count)
    }
    
    func advanceToNext() -> WatchWritingSession {
        var updated = self
        updated.currentIndex += 1
        return updated
    }
    
    func complete() -> WatchWritingSession {
        var updated = self
        updated.isCompleted = true
        return updated
    }
}

struct WatchWritingAttempt: Identifiable {
    let id: UUID
    let characterId: UUID
    let timestamp: Date
    let accuracy: Double
    let method: WritingMethod
    
    enum WritingMethod: String, Codable {
        case scribble = "scribble"
        case guided = "guided"
        case freeform = "freeform"
    }
}

struct WatchScribbleResult {
    let character: TamilCharacter
    let recognizedText: String
    let accuracy: Double
    let timestamp: Date
}

// MARK: - Watch Writing Service

@MainActor
class WatchWritingService: ObservableObject {
    static let shared = WatchWritingService()
    
    @Published var recentCharacters: [TamilCharacter] = []
    @Published var currentSession: WatchWritingSession?
    @Published var sessionHistory: [WatchWritingSession] = []
    
    private let scriptService = TamilScriptService.shared
    
    private init() {}
    
    func loadRecentCharacters() {
        // Load from UserDefaults or sync from iPhone
        recentCharacters = Array(scriptService.allCharacters.prefix(4))
    }
    
    func startSession(_ session: WatchWritingSession) {
        currentSession = session
    }
    
    func updateSession(_ session: WatchWritingSession) {
        currentSession = session
    }
    
    func completeSession(_ session: WatchWritingSession) {
        sessionHistory.append(session)
        currentSession = nil
        
        // Update recent characters
        for character in session.characters {
            if !recentCharacters.contains(where: { $0.id == character.id }) {
                recentCharacters.insert(character, at: 0)
                recentCharacters = Array(recentCharacters.prefix(4))
            }
        }
        
        // Sync with iPhone app
        syncWithiPhone()
    }
    
    func recordScribbleResult(_ result: WatchScribbleResult) {
        // Record scribble practice result
        let attempt = WatchWritingAttempt(
            id: UUID(),
            characterId: result.character.id,
            timestamp: result.timestamp,
            accuracy: result.accuracy,
            method: .scribble
        )
        
        // Add to current session if active
        if var session = currentSession {
            session.attempts.append(attempt)
            currentSession = session
        }
    }
    
    private func syncWithiPhone() {
        // Sync progress with iPhone app using Watch Connectivity
        // Implementation would use WCSession
    }
}

// MARK: - Watch Character Picker View

struct WatchCharacterPickerView: View {
    @Binding var selectedCharacter: TamilCharacter?
    let onCharacterSelected: (TamilCharacter) -> Void
    @Environment(\.dismiss) private var dismiss

    @StateObject private var scriptService = TamilScriptService.shared
    @State private var selectedCategory: WatchCharacterCategory = .vowels

    enum WatchCharacterCategory: String, CaseIterable {
        case vowels = "Vowels"
        case consonants = "Consonants"
        case recent = "Recent"

        var icon: String {
            switch self {
            case .vowels: return "a.circle"
            case .consonants: return "textformat"
            case .recent: return "clock"
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 8) {
                // Category selector
                WatchCategorySelector(
                    selectedCategory: $selectedCategory
                )

                // Character grid
                ScrollView {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                        ForEach(filteredCharacters, id: \.id) { character in
                            WatchCharacterCell(
                                character: character,
                                isSelected: selectedCharacter?.id == character.id,
                                onTap: {
                                    selectedCharacter = character
                                    onCharacterSelected(character)
                                    dismiss()
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
            .navigationTitle("Characters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(.caption)
                }
            }
        }
    }

    private var filteredCharacters: [TamilCharacter] {
        switch selectedCategory {
        case .vowels:
            return Array(scriptService.vowels.prefix(8))
        case .consonants:
            return Array(scriptService.consonants.prefix(8))
        case .recent:
            return Array(scriptService.allCharacters.prefix(8)) // Mock recent
        }
    }
}

struct WatchCategorySelector: View {
    @Binding var selectedCategory: WatchCharacterPickerView.WatchCharacterCategory

    var body: some View {
        HStack(spacing: 4) {
            ForEach(WatchCharacterPickerView.WatchCharacterCategory.allCases, id: \.self) { category in
                Button(action: { selectedCategory = category }) {
                    VStack(spacing: 2) {
                        Image(systemName: category.icon)
                            .font(.caption2)

                        Text(category.rawValue)
                            .font(.caption2)
                    }
                    .foregroundColor(selectedCategory == category ? .white : .blue)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(selectedCategory == category ? Color.blue : Color.clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

struct WatchCharacterCell: View {
    let character: TamilCharacter
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                Text(character.character)
                    .font(.title2)
                    .foregroundColor(.primary)

                Text(character.romanization)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)

                // Difficulty dots
                HStack(spacing: 1) {
                    ForEach(1...character.difficultyLevel, id: \.self) { _ in
                        Circle()
                            .fill(character.writingComplexity.color)
                            .frame(width: 3, height: 3)
                    }
                }
            }
            .padding(8)
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue.opacity(0.2) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Watch Scribble Input View

struct WatchScribbleInputView: View {
    let character: TamilCharacter
    let onComplete: (WatchScribbleResult) -> Void
    @Environment(\.dismiss) private var dismiss

    @State private var scribbleText = ""
    @State private var isRecognizing = false
    @State private var recognitionAccuracy: Double = 0.0
    @State private var showingResult = false

    var body: some View {
        NavigationView {
            VStack(spacing: 12) {
                // Target character
                VStack(spacing: 6) {
                    Text("Write this character:")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(character.character)
                        .font(.system(size: 50, weight: .light))
                        .foregroundColor(.blue)

                    Text(character.romanization)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                // Scribble input area
                VStack(spacing: 8) {
                    Text("Use Scribble to write:")
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    TextField("Write here...", text: $scribbleText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.title2)
                        .multilineTextAlignment(.center)
                        .onChange(of: scribbleText) { newValue in
                            if !newValue.isEmpty {
                                recognizeInput(newValue)
                            }
                        }
                }

                // Recognition feedback
                if isRecognizing {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)

                        Text("Recognizing...")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                } else if !scribbleText.isEmpty {
                    WatchRecognitionFeedbackView(
                        targetCharacter: character.character,
                        recognizedText: scribbleText,
                        accuracy: recognitionAccuracy
                    )
                }

                // Action buttons
                VStack(spacing: 6) {
                    if !scribbleText.isEmpty {
                        WatchActionButton(
                            title: "Submit",
                            icon: "checkmark",
                            color: .green,
                            action: submitResult
                        )
                    }

                    WatchActionButton(
                        title: "Clear",
                        icon: "trash",
                        color: .red,
                        action: clearInput
                    )
                }
            }
            .padding(.horizontal, 8)
            .navigationTitle("Scribble")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(.caption)
                }
            }
        }
        .alert("Result", isPresented: $showingResult) {
            Button("Continue") {
                dismiss()
            }
        } message: {
            Text(getResultMessage())
        }
    }

    private func recognizeInput(_ input: String) {
        isRecognizing = true

        // Simulate recognition delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isRecognizing = false

            // Calculate accuracy based on character match
            if input.contains(character.character) {
                recognitionAccuracy = 0.95
            } else if input.count == 1 && character.romanization.lowercased().contains(input.lowercased()) {
                recognitionAccuracy = 0.75
            } else {
                recognitionAccuracy = 0.3
            }
        }
    }

    private func submitResult() {
        let result = WatchScribbleResult(
            character: character,
            recognizedText: scribbleText,
            accuracy: recognitionAccuracy,
            timestamp: Date()
        )

        onComplete(result)
        showingResult = true
    }

    private func clearInput() {
        scribbleText = ""
        recognitionAccuracy = 0.0
    }

    private func getResultMessage() -> String {
        if recognitionAccuracy > 0.8 {
            return "Excellent! You wrote the character correctly."
        } else if recognitionAccuracy > 0.6 {
            return "Good attempt! Keep practicing to improve."
        } else {
            return "Try again. Focus on the character shape."
        }
    }
}

// MARK: - Watch Recognition Feedback View

struct WatchRecognitionFeedbackView: View {
    let targetCharacter: String
    let recognizedText: String
    let accuracy: Double

    var body: some View {
        VStack(spacing: 4) {
            HStack {
                Text("Target:")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Text(targetCharacter)
                    .font(.caption)
                    .fontWeight(.semibold)

                Spacer()

                Text("You:")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Text(recognizedText)
                    .font(.caption)
                    .fontWeight(.semibold)
            }

            HStack {
                Text("Accuracy:")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(Int(accuracy * 100))%")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(accuracyColor)
            }

            ProgressView(value: accuracy, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: accuracyColor))
                .frame(height: 2)
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color(.systemGray6))
        )
    }

    private var accuracyColor: Color {
        if accuracy > 0.8 {
            return .green
        } else if accuracy > 0.6 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Watch Connectivity Service

@MainActor
class WatchConnectivityService: NSObject, ObservableObject, WCSessionDelegate {
    static let shared = WatchConnectivityService()

    @Published var isConnected = false
    @Published var syncedProgress: [String: Any] = [:]

    private override init() {
        super.init()

        if WCSession.isSupported() {
            let session = WCSession.default
            session.delegate = self
            session.activate()
        }
    }

    func syncWritingProgress(_ progress: [String: Any]) {
        guard WCSession.default.isReachable else { return }

        WCSession.default.sendMessage(
            ["type": "writing_progress", "data": progress],
            replyHandler: nil,
            errorHandler: { error in
                print("Failed to sync progress: \(error)")
            }
        )
    }

    func requestProgressSync() {
        guard WCSession.default.isReachable else { return }

        WCSession.default.sendMessage(
            ["type": "request_progress"],
            replyHandler: { reply in
                DispatchQueue.main.async {
                    self.syncedProgress = reply
                }
            },
            errorHandler: { error in
                print("Failed to request progress: \(error)")
            }
        )
    }

    // MARK: - WCSessionDelegate

    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            self.isConnected = activationState == .activated
        }
    }

    func session(_ session: WCSession, didReceiveMessage message: [String : Any]) {
        DispatchQueue.main.async {
            if let type = message["type"] as? String {
                switch type {
                case "progress_update":
                    if let data = message["data"] as? [String: Any] {
                        self.syncedProgress = data
                    }
                default:
                    break
                }
            }
        }
    }
}
#endif

#if os(watchOS)
#Preview {
    WatchWritingView()
}
#endif
