//
//  WritingCurriculumView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct WritingCurriculumView: View {
    @StateObject private var curriculumService = TamilWritingCurriculumService.shared
    @State private var selectedLevel: CEFRLevel = .a1
    @State private var showingModuleDetail = false
    @State private var selectedModule: TamilWritingCurriculumService.WritingCurriculumLevel.WritingModule?
    @State private var userProgress: TamilWritingCurriculumService.CurriculumProgress?
    
    private let userId = "current-user" // This would come from authentication
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                curriculumHeader
                
                // Level selector
                levelSelector
                
                // Main content
                if curriculumService.isGeneratingCurriculum {
                    loadingView
                } else {
                    curriculumContentView
                }
            }
            .background(
                LinearGradient(
                    colors: [Color.indigo.opacity(0.05), Color.purple.opacity(0.05)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .navigationBarHidden(true)
            .onAppear {
                loadCurriculum()
            }
            .sheet(isPresented: $showingModuleDetail) {
                if let module = selectedModule {
                    ModuleDetailView(module: module)
                }
            }
        }
    }
    
    private var curriculumHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Tamil Writing Curriculum")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Structured progression from A1 to C2")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: "graduationcap.fill")
                        .font(.title2)
                        .foregroundColor(.indigo)
                    
                    Text("CEFR")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
            
            // Overall progress
            if let progress = userProgress {
                VStack(spacing: 8) {
                    HStack {
                        Text("Your Progress")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Text("\(Int(progress.overallProgress * 100))%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.indigo)
                    }
                    
                    ProgressView(value: progress.overallProgress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .indigo))
                    
                    HStack {
                        Text("Current Level: \(progress.currentLevel.rawValue.uppercased())")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text("\(progress.timeSpent / 60) hours completed")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.secondarySystemBackground))
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var levelSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach([CEFRLevel.a1, .a2, .b1, .b2, .c1, .c2], id: \.self) { level in
                    CEFRLevelChip(
                        level: level,
                        isSelected: selectedLevel == level,
                        isUnlocked: isLevelUnlocked(level),
                        progress: getLevelProgress(level),
                        action: {
                            selectedLevel = level
                        }
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
        .background(Color(.systemBackground))
    }
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Generating Tamil Writing Curriculum...")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Creating structured learning paths for all CEFR levels")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var curriculumContentView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if let curriculumLevel = curriculumService.getCurriculumLevel(for: selectedLevel) {
                    // Level overview
                    levelOverviewCard(curriculumLevel)
                    
                    // Learning outcomes
                    learningOutcomesCard(curriculumLevel.learningOutcomes)
                    
                    // Writing modules
                    ForEach(curriculumLevel.writingModules, id: \.id) { module in
                        WritingModuleCard(
                            module: module,
                            progress: getModuleProgress(module.id.uuidString),
                            onTap: {
                                selectedModule = module
                                showingModuleDetail = true
                            }
                        )
                    }
                    
                    // Assessment criteria
                    assessmentCriteriaCard(curriculumLevel.assessmentCriteria)
                } else {
                    Text("Curriculum not available for \(selectedLevel.rawValue.uppercased())")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .padding()
                }
            }
            .padding()
        }
    }
    
    private func levelOverviewCard(_ level: TamilWritingCurriculumService.WritingCurriculumLevel) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(level.levelName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(level.estimatedHours) hours")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.indigo.opacity(0.2))
                    .foregroundColor(.indigo)
                    .cornerRadius(8)
            }
            
            Text(level.description)
                .font(.body)
                .foregroundColor(.secondary)
            
            if !level.prerequisites.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Prerequisites")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                    
                    ForEach(level.prerequisites, id: \.self) { prerequisite in
                        Text("• \(prerequisite)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func learningOutcomesCard(_ outcomes: [TamilWritingCurriculumService.WritingCurriculumLevel.LearningOutcome]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Learning Outcomes")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.green)
            
            ForEach(outcomes, id: \.id) { outcome in
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(outcome.category.displayName)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Image(systemName: "target")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                    
                    Text(outcome.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    // Measurable goals
                    ForEach(outcome.measurableGoals.prefix(2), id: \.self) { goal in
                        HStack(alignment: .top, spacing: 6) {
                            Image(systemName: "checkmark.circle")
                                .font(.caption)
                                .foregroundColor(.green)
                            
                            Text(goal)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.green.opacity(0.05))
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func assessmentCriteriaCard(_ criteria: TamilWritingCurriculumService.WritingCurriculumLevel.AssessmentCriteria) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Assessment Standards")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
            
            VStack(spacing: 8) {
                criteriaRow("Accuracy", criteria.accuracy)
                criteriaRow("Fluency", criteria.fluency)
                criteriaRow("Complexity", criteria.complexity)
                criteriaRow("Cultural Appropriateness", criteria.culturalAppropriatenesss)
                criteriaRow("Communication", criteria.communicativeEffectiveness)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func criteriaRow(_ label: String, _ value: Double) -> some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text("\(Int(value))%")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
            
            ProgressView(value: value / 100.0, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                .frame(width: 60)
        }
    }
    
    // MARK: - Actions
    
    private func loadCurriculum() {
        Task {
            if curriculumService.curriculumLevels.isEmpty {
                await curriculumService.generateCompleteCurriculum()
            }
            
            // Load user progress
            userProgress = TamilWritingCurriculumService.CurriculumProgress(
                userId: userId,
                currentLevel: .a1,
                completedModules: [],
                currentModule: nil,
                overallProgress: 0.15,
                skillProgress: [:],
                assessmentScores: [:],
                timeSpent: 180, // 3 hours
                lastActivity: Date(),
                achievements: [],
                nextRecommendations: ["Complete A1 Module 1: Basic Characters"]
            )
        }
    }
    
    private func isLevelUnlocked(_ level: CEFRLevel) -> Bool {
        guard let progress = userProgress else { return level == .a1 }
        
        switch level {
        case .a1: return true
        case .a2: return progress.currentLevel.rawValue >= CEFRLevel.a1.rawValue
        case .b1: return progress.currentLevel.rawValue >= CEFRLevel.a2.rawValue
        case .b2: return progress.currentLevel.rawValue >= CEFRLevel.b1.rawValue
        case .c1: return progress.currentLevel.rawValue >= CEFRLevel.b2.rawValue
        case .c2: return progress.currentLevel.rawValue >= CEFRLevel.c1.rawValue
        }
    }
    
    private func getLevelProgress(_ level: CEFRLevel) -> Double {
        guard let progress = userProgress else { return 0.0 }
        
        if level == progress.currentLevel {
            return progress.overallProgress
        } else if level.rawValue < progress.currentLevel.rawValue {
            return 1.0
        } else {
            return 0.0
        }
    }
    
    private func getModuleProgress(_ moduleId: String) -> Double {
        return userProgress?.skillProgress[moduleId] ?? 0.0
    }
}

// MARK: - CEFR Level Chip

struct CEFRLevelChip: View {
    let level: CEFRLevel
    let isSelected: Bool
    let isUnlocked: Bool
    let progress: Double
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Text(level.rawValue.uppercased())
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(isSelected ? .white : (isUnlocked ? .primary : .secondary))
                
                if isUnlocked {
                    ProgressView(value: progress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: isSelected ? .white : .indigo))
                        .frame(width: 30, height: 2)
                } else {
                    Image(systemName: "lock.fill")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.indigo : Color(.secondarySystemBackground))
                    .opacity(isUnlocked ? 1.0 : 0.5)
            )
        }
        .disabled(!isUnlocked)
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Writing Module Card

struct WritingModuleCard: View {
    let module: TamilWritingCurriculumService.WritingCurriculumLevel.WritingModule
    let progress: Double
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Module \(module.moduleNumber)")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.indigo)
                        
                        Text(module.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        Text("\(module.estimatedTime)h")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if progress > 0 {
                            Text("\(Int(progress * 100))%")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.indigo)
                        }
                    }
                }
                
                Text(module.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                // Focus areas
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(module.focusAreas, id: \.self) { area in
                            Text(area.displayName)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(6)
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Progress bar
                if progress > 0 {
                    ProgressView(value: progress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .indigo))
                }
                
                HStack {
                    Text("\(module.writingUnits.count) units")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.indigo)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Module Detail View

struct ModuleDetailView: View {
    let module: TamilWritingCurriculumService.WritingCurriculumLevel.WritingModule
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text(module.description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                    
                    // Writing units
                    ForEach(module.writingUnits, id: \.id) { unit in
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Unit \(unit.unitNumber): \(unit.title)")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            ForEach(unit.objectives, id: \.self) { objective in
                                HStack(alignment: .top, spacing: 6) {
                                    Image(systemName: "target")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                    
                                    Text(objective)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.secondarySystemBackground))
                        )
                        .padding(.horizontal)
                    }
                }
                .padding(.vertical)
            }
            .navigationTitle(module.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    WritingCurriculumView()
}
