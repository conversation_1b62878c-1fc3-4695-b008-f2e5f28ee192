//
//  DetailedFeedbackAnalysisView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct DetailedFeedbackAnalysisView: View {
    let feedback: TamilWritingAIFeedbackService.WritingFeedbackResult
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedTab: AnalysisTab = .overview
    @State private var showingActionPlan = false
    
    enum AnalysisTab: String, CaseIterable {
        case overview = "Overview"
        case categories = "Categories"
        case suggestions = "Suggestions"
        case strengths = "Strengths"
        case nextSteps = "Next Steps"
        
        var icon: String {
            switch self {
            case .overview: return "chart.pie"
            case .categories: return "list.bullet.rectangle"
            case .suggestions: return "lightbulb"
            case .strengths: return "star"
            case .nextSteps: return "arrow.right.circle"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with overall score
                analysisHeader
                
                // Tab selector
                tabSelector
                
                // Content based on selected tab
                ScrollView {
                    VStack(spacing: 20) {
                        switch selectedTab {
                        case .overview:
                            overviewContent
                        case .categories:
                            categoriesContent
                        case .suggestions:
                            suggestionsContent
                        case .strengths:
                            strengthsContent
                        case .nextSteps:
                            nextStepsContent
                        }
                    }
                    .padding()
                }
            }
            .background(
                LinearGradient(
                    colors: [Color.purple.opacity(0.03), Color.blue.opacity(0.03)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Action Plan") {
                        showingActionPlan = true
                    }
                    .foregroundColor(.blue)
                }
            }
        }
        .sheet(isPresented: $showingActionPlan) {
            ActionPlanView(feedback: feedback)
        }
    }
    
    private var analysisHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("AI Analysis Report")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Detailed feedback and recommendations")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Text("\(Int(feedback.overallScore * 100))%")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(overallScoreColor)
                    
                    Text("Overall Score")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Confidence and timestamp
            HStack {
                HStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .font(.caption)
                        .foregroundColor(.purple)
                    
                    Text("AI Confidence: \(Int(feedback.confidenceLevel * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text("Analyzed \(feedback.analysisTimestamp, style: .relative)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var tabSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(AnalysisTab.allCases, id: \.self) { tab in
                    TabButton(
                        tab: tab,
                        isSelected: selectedTab == tab,
                        action: { selectedTab = tab }
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    private var overviewContent: some View {
        VStack(spacing: 20) {
            // Score breakdown chart
            ScoreBreakdownChart(categories: feedback.feedbackCategories)
            
            // Quick insights
            QuickInsightsCard(feedback: feedback)
            
            // Performance summary
            PerformanceSummaryCard(feedback: feedback)
        }
    }
    
    private var categoriesContent: some View {
        VStack(spacing: 16) {
            ForEach(feedback.feedbackCategories, id: \.id) { category in
                CategoryDetailCard(category: category)
            }
        }
    }
    
    private var suggestionsContent: some View {
        VStack(spacing: 16) {
            if feedback.improvementSuggestions.isEmpty {
                EmptyStateCard(
                    icon: "lightbulb",
                    title: "No Suggestions",
                    description: "Your writing is excellent! Keep practicing to maintain your skills."
                )
            } else {
                ForEach(feedback.improvementSuggestions, id: \.id) { suggestion in
                    ImprovementSuggestionCard(suggestion: suggestion)
                }
            }
        }
    }
    
    private var strengthsContent: some View {
        VStack(spacing: 16) {
            if feedback.strengthAreas.isEmpty {
                EmptyStateCard(
                    icon: "star",
                    title: "Building Strengths",
                    description: "Continue practicing to develop your strong areas."
                )
            } else {
                ForEach(feedback.strengthAreas, id: \.id) { strength in
                    StrengthAreaCard(strength: strength)
                }
            }
        }
    }
    
    private var nextStepsContent: some View {
        VStack(spacing: 16) {
            ForEach(feedback.nextSteps, id: \.id) { step in
                NextStepCard(step: step)
            }
        }
    }
    
    private var overallScoreColor: Color {
        if feedback.overallScore >= 0.8 {
            return .green
        } else if feedback.overallScore >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Tab Button

struct TabButton: View {
    let tab: DetailedFeedbackAnalysisView.AnalysisTab
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: tab.icon)
                    .font(.caption)
                
                Text(tab.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.purple : Color(.tertiarySystemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Score Breakdown Chart

struct ScoreBreakdownChart: View {
    let categories: [TamilWritingAIFeedbackService.WritingFeedbackResult.FeedbackCategory]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Score Breakdown")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                ForEach(categories, id: \.id) { category in
                    HStack {
                        HStack(spacing: 8) {
                            Image(systemName: category.category.icon)
                                .font(.caption)
                                .foregroundColor(category.category.color)
                                .frame(width: 16)
                            
                            Text(category.category.displayName)
                                .font(.subheadline)
                                .foregroundColor(.primary)
                        }
                        
                        Spacer()
                        
                        Text("\(Int(category.score * 100))%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(category.category.color)
                    }
                    
                    ProgressView(value: category.score, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: category.category.color))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Quick Insights Card

struct QuickInsightsCard: View {
    let feedback: TamilWritingAIFeedbackService.WritingFeedbackResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                InsightRow(
                    icon: "target",
                    title: "Top Priority",
                    value: feedback.improvementSuggestions.first?.title ?? "Keep practicing",
                    color: .red
                )
                
                InsightRow(
                    icon: "star.fill",
                    title: "Strongest Area",
                    value: feedback.strengthAreas.first?.area ?? "Building skills",
                    color: .green
                )
                
                InsightRow(
                    icon: "clock",
                    title: "Next Focus",
                    value: feedback.nextSteps.first?.title ?? "Continue practice",
                    color: .blue
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

struct InsightRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
                .frame(width: 16)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
            }
            
            Spacer()
        }
    }
}

// MARK: - Performance Summary Card

struct PerformanceSummaryCard: View {
    let feedback: TamilWritingAIFeedbackService.WritingFeedbackResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance Summary")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(generateSummaryText())
                .font(.body)
                .foregroundColor(.secondary)
                .lineSpacing(4)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func generateSummaryText() -> String {
        let score = feedback.overallScore
        
        if score >= 0.9 {
            return "Excellent work! Your Tamil writing shows mastery across all areas. You demonstrate strong understanding of stroke order, character formation, and cultural nuances. Continue practicing to maintain this high level of proficiency."
        } else if score >= 0.7 {
            return "Good progress in your Tamil writing journey. You show solid fundamentals with room for improvement in specific areas. Focus on the highlighted suggestions to enhance your skills further."
        } else if score >= 0.5 {
            return "You're building your Tamil writing foundation. There are several areas that need attention, but with focused practice on the recommended areas, you'll see significant improvement."
        } else {
            return "Keep practicing! Tamil writing takes time to master. Focus on the basic fundamentals first, use guided mode frequently, and practice regularly to build your skills."
        }
    }
}

// MARK: - Category Detail Card

struct CategoryDetailCard: View {
    let category: TamilWritingAIFeedbackService.WritingFeedbackResult.FeedbackCategory
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: category.category.icon)
                    .font(.title3)
                    .foregroundColor(category.category.color)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(category.category.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("\(Int(category.score * 100))% Score")
                        .font(.subheadline)
                        .foregroundColor(category.category.color)
                }
                
                Spacer()
            }
            
            Text(category.analysis)
                .font(.body)
                .foregroundColor(.secondary)
            
            if !category.specificIssues.isEmpty {
                VStack(alignment: .leading, spacing: 6) {
                    Text("Specific Issues:")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.red)
                    
                    ForEach(category.specificIssues, id: \.self) { issue in
                        HStack(alignment: .top, spacing: 6) {
                            Image(systemName: "exclamationmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.red)
                            
                            Text(issue)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            
            if !category.recommendations.isEmpty {
                VStack(alignment: .leading, spacing: 6) {
                    Text("Recommendations:")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    
                    ForEach(category.recommendations, id: \.self) { recommendation in
                        HStack(alignment: .top, spacing: 6) {
                            Image(systemName: "lightbulb.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text(recommendation)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Improvement Suggestion Card

struct ImprovementSuggestionCard: View {
    let suggestion: TamilWritingAIFeedbackService.WritingFeedbackResult.ImprovementSuggestion
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(suggestion.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Priority: \(suggestion.priority.rawValue.capitalized)")
                        .font(.caption)
                        .foregroundColor(suggestion.priority.color)
                }
                
                Spacer()
                
                Text("\(Int(suggestion.estimatedImpact * 100))% Impact")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
            }
            
            Text(suggestion.description)
                .font(.body)
                .foregroundColor(.secondary)
            
            if !suggestion.actionSteps.isEmpty {
                VStack(alignment: .leading, spacing: 6) {
                    Text("Action Steps:")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    
                    ForEach(Array(suggestion.actionSteps.enumerated()), id: \.offset) { index, step in
                        HStack(alignment: .top, spacing: 8) {
                            Text("\(index + 1).")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.blue)
                            
                            Text(step)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Strength Area Card

struct StrengthAreaCard: View {
    let strength: TamilWritingAIFeedbackService.WritingFeedbackResult.StrengthArea
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "star.fill")
                    .font(.title3)
                    .foregroundColor(.yellow)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(strength.area)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("\(Int(strength.score * 100))% Proficiency")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                
                Spacer()
            }
            
            Text(strength.description)
                .font(.body)
                .foregroundColor(.secondary)
            
            Text(strength.encouragement)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.green)
                .italic()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.green.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Next Step Card

struct NextStepCard: View {
    let step: TamilWritingAIFeedbackService.WritingFeedbackResult.NextStep
    
    var body: some View {
        HStack(spacing: 12) {
            Text("\(step.stepNumber)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 32, height: 32)
                .background(Circle().fill(Color.blue))
            
            VStack(alignment: .leading, spacing: 4) {
                Text(step.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(step.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                
                HStack {
                    Text("\(step.estimatedTime) min")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Text("•")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(step.difficulty.rawValue.capitalized)
                        .font(.caption)
                        .foregroundColor(step.difficulty.color)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Empty State Card

struct EmptyStateCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 40))
                .foregroundColor(.gray)
            
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Action Plan View

struct ActionPlanView: View {
    let feedback: TamilWritingAIFeedbackService.WritingFeedbackResult
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Your Personalized Action Plan")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.top)
                    
                    // Implementation for action plan
                    Text("Action plan content coming soon...")
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    DetailedFeedbackAnalysisView(
        feedback: TamilWritingAIFeedbackService.WritingFeedbackResult(
            characterId: UUID(),
            analysisTimestamp: Date(),
            overallScore: 0.75,
            feedbackCategories: [],
            improvementSuggestions: [],
            strengthAreas: [],
            nextSteps: [],
            confidenceLevel: 0.85
        )
    )
}
