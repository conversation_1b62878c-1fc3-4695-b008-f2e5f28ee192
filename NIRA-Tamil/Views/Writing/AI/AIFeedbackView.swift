//
//  AIFeedbackView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct AIFeedbackView: View {
    @StateObject private var aiService = TamilWritingAIFeedbackService.shared
    @State private var selectedFeedback: TamilWritingAIFeedbackService.WritingFeedbackResult?
    @State private var showingDetailedAnalysis = false
    @State private var selectedCategory: FeedbackCategory = .all
    
    enum FeedbackCategory: String, CaseIterable {
        case all = "All"
        case strokeOrder = "Stroke Order"
        case characterShape = "Character Shape"
        case proportions = "Proportions"
        case consistency = "Consistency"
        case fluency = "Fluency"
        case cultural = "Cultural"
        
        var systemImage: String {
            switch self {
            case .all: return "list.bullet"
            case .strokeOrder: return "arrow.triangle.turn.up.right.diamond"
            case .characterShape: return "scribble.variable"
            case .proportions: return "rectangle.ratio.3.to.4"
            case .consistency: return "equal"
            case .fluency: return "speedometer"
            case .cultural: return "globe.asia.australia"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with AI status
                aiStatusHeader
                
                // Category filter
                categoryFilterView
                
                // Main content
                if aiService.isAnalyzing {
                    analyzingView
                } else if aiService.feedbackResults.isEmpty {
                    emptyStateView
                } else {
                    feedbackListView
                }
            }
            .background(
                LinearGradient(
                    colors: [Color.purple.opacity(0.03), Color.blue.opacity(0.03)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingDetailedAnalysis) {
            if let feedback = selectedFeedback {
                DetailedFeedbackAnalysisView(feedback: feedback)
            }
        }
    }
    
    private var aiStatusHeader: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("AI Writing Coach")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Personalized feedback powered by machine learning")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .font(.title2)
                        .foregroundColor(.purple)
                    
                    Text("AI Coach")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
            
            // AI insights summary
            if !aiService.personalizedSuggestions.isEmpty || !aiService.learningInsights.isEmpty {
                AIInsightsSummaryCard(
                    suggestions: aiService.personalizedSuggestions,
                    insights: aiService.learningInsights
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var categoryFilterView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(FeedbackCategory.allCases, id: \.self) { category in
                    CategoryFilterChip(
                        category: category,
                        isSelected: selectedCategory == category,
                        action: { selectedCategory = category }
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    private var analyzingView: some View {
        VStack(spacing: 20) {
            LottieView(name: "ai-analyzing", loopMode: .loop)
                .frame(width: 120, height: 120)
            
            VStack(spacing: 8) {
                Text("AI Analysis in Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("Analyzing your writing with advanced machine learning")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            ProgressView()
                .scaleEffect(1.2)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 80))
                .foregroundColor(.purple.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("No AI Feedback Yet")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Start writing Tamil characters to receive personalized AI feedback and insights")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("Start Writing Practice") {
                // Navigate to writing practice
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    private var feedbackListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredFeedbackResults, id: \.id) { feedback in
                    FeedbackResultCard(
                        feedback: feedback,
                        onTap: {
                            selectedFeedback = feedback
                            showingDetailedAnalysis = true
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    private var filteredFeedbackResults: [TamilWritingAIFeedbackService.WritingFeedbackResult] {
        if selectedCategory == .all {
            return aiService.feedbackResults
        } else {
            return aiService.feedbackResults.filter { feedback in
                feedback.feedbackCategories.contains { category in
                    switch selectedCategory {
                    case .strokeOrder: return category.category == .strokeOrder
                    case .characterShape: return category.category == .characterShape
                    case .proportions: return category.category == .proportions
                    case .consistency: return category.category == .consistency
                    case .fluency: return category.category == .fluency
                    case .cultural: return category.category == .culturalAccuracy
                    default: return true
                    }
                }
            }
        }
    }
}

// MARK: - AI Insights Summary Card

struct AIInsightsSummaryCard: View {
    let suggestions: [TamilWritingAIFeedbackService.PersonalizedSuggestion]
    let insights: [TamilWritingAIFeedbackService.LearningInsight]
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("AI Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(suggestions.count + insights.count) insights")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(suggestions.prefix(3), id: \.id) { suggestion in
                        InsightChip(
                            title: suggestion.title,
                            type: suggestion.suggestionType.rawValue,
                            confidence: suggestion.confidence,
                            color: .blue
                        )
                    }
                    
                    ForEach(insights.prefix(2), id: \.id) { insight in
                        InsightChip(
                            title: insight.title,
                            type: insight.insightType.rawValue,
                            confidence: insight.confidence,
                            color: .green
                        )
                    }
                }
                .padding(.horizontal, 4)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

struct InsightChip: View {
    let title: String
    let type: String
    let confidence: Double
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.caption)
                .fontWeight(.semibold)
                .lineLimit(2)
            
            HStack {
                Text(type.replacingOccurrences(of: "_", with: " ").capitalized)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(Int(confidence * 100))%")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }
        }
        .padding(8)
        .frame(width: 120, height: 60)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

// MARK: - Category Filter Chip

struct CategoryFilterChip: View {
    let category: AIFeedbackView.FeedbackCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: category.systemImage)
                    .font(.caption)
                
                Text(category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.purple : Color(.tertiarySystemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Feedback Result Card

struct FeedbackResultCard: View {
    let feedback: TamilWritingAIFeedbackService.WritingFeedbackResult
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Character Analysis")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(feedback.analysisTimestamp, style: .relative)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        Text("\(Int(feedback.overallScore * 100))%")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(scoreColor)
                        
                        Text("Overall")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Category scores
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(feedback.feedbackCategories, id: \.id) { category in
                            CategoryScoreChip(category: category)
                        }
                    }
                    .padding(.horizontal, 4)
                }
                
                // Key insights
                if !feedback.improvementSuggestions.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Key Insights")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                        
                        Text(feedback.improvementSuggestions.first?.title ?? "")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                // Confidence indicator
                HStack {
                    Text("AI Confidence")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(Int(feedback.confidenceLevel * 100))%")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                    
                    Image(systemName: "chevron.right")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var scoreColor: Color {
        if feedback.overallScore >= 0.8 {
            return .green
        } else if feedback.overallScore >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Category Score Chip

struct CategoryScoreChip: View {
    let category: TamilWritingAIFeedbackService.WritingFeedbackResult.FeedbackCategory
    
    var body: some View {
        VStack(spacing: 2) {
            Text(category.category.displayName)
                .font(.caption2)
                .fontWeight(.medium)
                .lineLimit(1)
            
            Text("\(Int(category.score * 100))%")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(category.category.color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(category.category.color.opacity(0.1))
        )
    }
}

// MARK: - Lottie View (Mock)

struct LottieView: View {
    let name: String
    let loopMode: LoopMode
    
    enum LoopMode {
        case loop
        case playOnce
    }
    
    var body: some View {
        // Mock Lottie animation with system image
        Image(systemName: "brain.head.profile")
            .font(.system(size: 60))
            .foregroundColor(.purple)
            .scaleEffect(1.2)
            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: true)
    }
}

#Preview {
    AIFeedbackView()
}
