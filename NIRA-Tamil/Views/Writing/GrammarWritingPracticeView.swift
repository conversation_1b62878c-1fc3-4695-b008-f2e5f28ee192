//
//  GrammarWritingPracticeView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct GrammarWritingPracticeView: View {
    @StateObject private var grammarService = GrammarWritingIntegrationService.shared
    @StateObject private var scriptService = TamilScriptService.shared
    
    @State private var selectedExercise: GrammarWritingIntegrationService.GrammarWritingExercise?
    @State private var currentStepIndex = 0
    @State private var showingWritingCanvas = false
    @State private var completedSteps: Set<Int> = []
    @State private var exerciseProgress: Double = 0.0
    
    let lessonId: String
    let level: CEFRLevel
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            grammarPracticeHeader
            
            // Main Content
            if let exercise = selectedExercise {
                grammarExerciseView(exercise)
            } else {
                exerciseSelectionView
            }
        }
        .background(
            LinearGradient(
                colors: [Color.purple.opacity(0.05), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .onAppear {
            loadGrammarExercises()
        }
    }
    
    private var grammarPracticeHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Grammar Writing Practice")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Learn Tamil grammar through writing")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: "book.fill")
                        .font(.title2)
                        .foregroundColor(.purple)
                    
                    Text("Grammar")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
            
            if let exercise = selectedExercise {
                // Progress indicator
                VStack(spacing: 8) {
                    HStack {
                        Text(exercise.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Text("\(currentStepIndex + 1)/\(exercise.writingSteps.count)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    ProgressView(value: exerciseProgress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.secondarySystemBackground))
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var exerciseSelectionView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Select a Grammar Topic")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .padding(.top)
                
                ForEach(grammarService.grammarWritingExercises.filter { $0.lessonId == lessonId }, id: \.id) { exercise in
                    GrammarExerciseCard(
                        exercise: exercise,
                        onSelect: {
                            selectedExercise = exercise
                            currentStepIndex = 0
                            updateProgress()
                        }
                    )
                }
                
                if grammarService.grammarWritingExercises.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "book.closed")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("No grammar exercises available")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("Grammar writing exercises will be generated for this lesson")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                }
            }
            .padding()
        }
    }
    
    private func grammarExerciseView(_ exercise: GrammarWritingIntegrationService.GrammarWritingExercise) -> some View {
        VStack(spacing: 0) {
            // Current step content
            if currentStepIndex < exercise.writingSteps.count {
                let currentStep = exercise.writingSteps[currentStepIndex]
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Step content
                        grammarStepView(currentStep, exercise: exercise)
                        
                        // Navigation controls
                        stepNavigationControls(exercise: exercise)
                    }
                    .padding()
                }
            }
        }
        .fullScreenCover(isPresented: $showingWritingCanvas) {
            if let step = exercise.writingSteps[safe: currentStepIndex],
               let character = getCharacterForStep(step) {
                TamilWritingCanvasContainer(
                    character: character,
                    writingMode: .guided,
                    onComplete: handleStepComplete
                )
            }
        }
    }
    
    private func grammarStepView(_ step: GrammarWritingIntegrationService.GrammarWritingExercise.GrammarWritingStep, exercise: GrammarWritingIntegrationService.GrammarWritingExercise) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Step header
            HStack {
                Text("Step \(step.stepNumber)")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.purple.opacity(0.2))
                    .foregroundColor(.purple)
                    .cornerRadius(8)
                
                Text(step.stepType.rawValue.replacingOccurrences(of: "_", with: " ").capitalized)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if completedSteps.contains(step.stepNumber) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                }
            }
            
            // Step instruction
            Text(step.instruction)
                .font(.headline)
                .fontWeight(.semibold)
            
            // Grammar explanation
            if !step.grammarExplanation.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Grammar Focus")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                    
                    Text(step.grammarExplanation)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.purple.opacity(0.1))
                        )
                }
            }
            
            // Target text display
            if !step.targetText.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Practice Text")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text(step.targetText)
                        .font(.title)
                        .fontWeight(.medium)
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .center)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.secondarySystemBackground))
                        )
                }
            }
            
            // Writing guidance
            if !step.writingGuidance.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Writing Tips")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    
                    ForEach(step.writingGuidance, id: \.self) { tip in
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "lightbulb.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text(tip)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.05))
                )
            }
            
            // Grammar highlights for sentences
            if let sentence = exercise.targetSentences.first(where: { $0.tamil == step.targetText }) {
                grammarHighlightsView(sentence)
            }
            
            // Action button
            stepActionButton(step: step)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func grammarHighlightsView(_ sentence: GrammarWritingIntegrationService.GrammarWritingExercise.TargetSentence) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Grammar Breakdown")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.green)
            
            ForEach(sentence.grammarHighlights, id: \.text) { highlight in
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(highlight.text)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Text(highlight.grammarFunction)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.green.opacity(0.2))
                            .foregroundColor(.green)
                            .cornerRadius(6)
                    }
                    
                    Text(highlight.explanation)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.green.opacity(0.05))
                )
            }
        }
    }
    
    private func stepActionButton(step: GrammarWritingIntegrationService.GrammarWritingExercise.GrammarWritingStep) -> some View {
        Button(action: {
            handleStepAction(step)
        }) {
            HStack {
                Image(systemName: getStepActionIcon(step.stepType))
                    .font(.title3)
                
                Text(getStepActionText(step.stepType))
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [.purple, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
        }
        .disabled(completedSteps.contains(step.stepNumber))
    }
    
    private func stepNavigationControls(exercise: GrammarWritingIntegrationService.GrammarWritingExercise) -> some View {
        HStack(spacing: 16) {
            Button("Previous") {
                if currentStepIndex > 0 {
                    currentStepIndex -= 1
                    updateProgress()
                }
            }
            .disabled(currentStepIndex == 0)
            .buttonStyle(.bordered)
            
            Spacer()
            
            Button("Next") {
                if currentStepIndex < exercise.writingSteps.count - 1 {
                    currentStepIndex += 1
                    updateProgress()
                }
            }
            .disabled(currentStepIndex >= exercise.writingSteps.count - 1)
            .buttonStyle(.bordered)
            
            Button("Complete Exercise") {
                completeExercise(exercise)
            }
            .disabled(completedSteps.count < exercise.writingSteps.count)
            .buttonStyle(.borderedProminent)
        }
    }
    
    // MARK: - Actions
    
    private func loadGrammarExercises() {
        Task {
            await grammarService.generateGrammarWritingExercises(for: lessonId, level: level)
        }
    }
    
    private func handleStepAction(_ step: GrammarWritingIntegrationService.GrammarWritingExercise.GrammarWritingStep) {
        switch step.stepType {
        case .characterPractice, .wordFormation, .sentenceConstruction:
            showingWritingCanvas = true
        case .grammarIntroduction, .grammarApplication, .freeWriting:
            // Mark as completed for non-writing steps
            completedSteps.insert(step.stepNumber)
            updateProgress()
        }
    }
    
    private func handleStepComplete(_ result: WritingResult) {
        completedSteps.insert(currentStepIndex + 1)
        updateProgress()
        
        // Auto-advance to next step
        if let exercise = selectedExercise, currentStepIndex < exercise.writingSteps.count - 1 {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                currentStepIndex += 1
                updateProgress()
            }
        }
    }
    
    private func updateProgress() {
        if let exercise = selectedExercise {
            exerciseProgress = Double(completedSteps.count) / Double(exercise.writingSteps.count)
        }
    }
    
    private func completeExercise(_ exercise: GrammarWritingIntegrationService.GrammarWritingExercise) {
        // Handle exercise completion
        print("Grammar exercise completed: \(exercise.title)")
        selectedExercise = nil
        currentStepIndex = 0
        completedSteps.removeAll()
        exerciseProgress = 0.0
    }
    
    private func getCharacterForStep(_ step: GrammarWritingIntegrationService.GrammarWritingExercise.GrammarWritingStep) -> TamilCharacter? {
        // Extract first character from target text for writing practice
        guard !step.targetText.isEmpty else { return nil }
        let firstChar = String(step.targetText.first!)
        return scriptService.allCharacters.first { $0.character == firstChar }
    }
    
    private func getStepActionIcon(_ stepType: GrammarWritingIntegrationService.GrammarWritingExercise.GrammarWritingStep.StepType) -> String {
        switch stepType {
        case .grammarIntroduction: return "book.fill"
        case .characterPractice: return "pencil"
        case .wordFormation: return "textformat.abc"
        case .sentenceConstruction: return "text.alignleft"
        case .grammarApplication: return "checkmark.circle"
        case .freeWriting: return "pencil.and.outline"
        }
    }
    
    private func getStepActionText(_ stepType: GrammarWritingIntegrationService.GrammarWritingExercise.GrammarWritingStep.StepType) -> String {
        switch stepType {
        case .grammarIntroduction: return "Continue"
        case .characterPractice: return "Practice Writing"
        case .wordFormation: return "Write Word"
        case .sentenceConstruction: return "Write Sentence"
        case .grammarApplication: return "Apply Grammar"
        case .freeWriting: return "Free Write"
        }
    }
}

// MARK: - Grammar Exercise Card

struct GrammarExerciseCard: View {
    let exercise: GrammarWritingIntegrationService.GrammarWritingExercise
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(exercise.grammarTopic.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(exercise.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        Text("\(exercise.estimatedTime) min")
                            .font(.caption)
                            .foregroundColor(.purple)
                        
                        Text("Level \(exercise.difficultyLevel)")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                // Grammar focus preview
                if let focus = exercise.grammarFocus.first {
                    Text(focus.explanation)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .italic()
                        .lineLimit(2)
                }
                
                // Target sentences preview
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(exercise.targetSentences.prefix(2), id: \.id) { sentence in
                            Text(sentence.tamil)
                                .font(.subheadline)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color(.tertiarySystemBackground))
                                )
                        }
                    }
                    .padding(.horizontal)
                }
                
                HStack {
                    Text(exercise.exerciseType.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.purple.opacity(0.2))
                        .foregroundColor(.purple)
                        .cornerRadius(8)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.purple)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Array Extension

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

#Preview {
    GrammarWritingPracticeView(lessonId: "A1_BASIC_GREETINGS", level: .a1)
}
