//
//  EnhancedIPadWritingView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI
import PencilKit

struct EnhancedIPadWritingView: View {
    @StateObject private var scriptService = TamilScriptService.shared
    @StateObject private var writingViewModel = IPadWritingViewModel()
    @StateObject private var assessmentEngine = TamilWritingAssessmentEngine.shared
    
    @State private var selectedCharacter: TamilCharacter?
    @State private var writingMode: WritingMode = .guided
    @State private var showingSidebar = true
    @State private var showingInspector = true
    @State private var selectedTool: WritingTool = .pencil
    @State private var canvasZoom: CGFloat = 1.0
    @State private var showingMultiCharacterMode = false
    
    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // Sidebar
                if showingSidebar {
                    iPadSidebarView
                        .frame(width: geometry.size.width * 0.25)
                }
                
                // Main content area
                VStack(spacing: 0) {
                    // Toolbar
                    iPadToolbarView
                    
                    // Canvas area
                    iPadCanvasArea(geometry: geometry)
                }
                .frame(maxWidth: .infinity)
                
                // Inspector panel
                if showingInspector {
                    iPadInspectorView
                        .frame(width: geometry.size.width * 0.25)
                }
            }
        }
        .background(Color(.systemGroupedBackground))
        .onAppear {
            setupIPadInterface()
        }
    }
    
    private var iPadSidebarView: some View {
        VStack(spacing: 0) {
            // Sidebar header
            HStack {
                Text("Characters")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: { showingSidebar = false }) {
                    Image(systemName: "sidebar.left")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            
            // Character categories
            CharacterCategoryView(
                selectedCharacter: $selectedCharacter,
                onCharacterSelected: { character in
                    selectedCharacter = character
                    writingViewModel.selectCharacter(character)
                }
            )
        }
        .background(Color(.systemBackground))
    }
    
    private var iPadToolbarView: some View {
        HStack {
            // Sidebar toggle
            if !showingSidebar {
                Button(action: { showingSidebar = true }) {
                    Image(systemName: "sidebar.left")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            }
            
            // Writing mode selector
            IPadWritingModeSelector(selectedMode: $writingMode)
            
            Spacer()
            
            // Tool selector
            IPadToolSelector(selectedTool: $selectedTool)
            
            // Zoom controls
            IPadZoomControls(zoom: $canvasZoom)
            
            // Multi-character mode toggle
            Button(action: { showingMultiCharacterMode.toggle() }) {
                Image(systemName: showingMultiCharacterMode ? "rectangle.split.3x1" : "square")
                    .font(.title3)
                    .foregroundColor(showingMultiCharacterMode ? .blue : .secondary)
            }
            
            // Inspector toggle
            if !showingInspector {
                Button(action: { showingInspector = true }) {
                    Image(systemName: "sidebar.right")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private func iPadCanvasArea(geometry: GeometryProxy) -> some View {
        ZStack {
            if showingMultiCharacterMode {
                MultiCharacterCanvasView(
                    characters: writingViewModel.practiceCharacters,
                    writingMode: writingMode,
                    selectedTool: selectedTool,
                    zoom: canvasZoom,
                    onCharacterComplete: handleCharacterComplete
                )
            } else if let character = selectedCharacter {
                EnhancedSingleCanvasView(
                    character: character,
                    writingMode: writingMode,
                    selectedTool: selectedTool,
                    zoom: canvasZoom,
                    onComplete: handleWritingComplete
                )
            } else {
                iPadWelcomeView
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    private var iPadInspectorView: some View {
        VStack(spacing: 0) {
            // Inspector header
            HStack {
                Text("Details")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: { showingInspector = false }) {
                    Image(systemName: "sidebar.right")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            
            // Inspector content
            ScrollView {
                VStack(spacing: 16) {
                    if let character = selectedCharacter {
                        CharacterDetailView(character: character)
                        StrokeOrderDetailView(character: character)
                        WritingTipsView(character: character)
                        ProgressDetailView(character: character)
                    } else {
                        Text("Select a character to see details")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding()
                    }
                }
                .padding()
            }
        }
        .background(Color(.systemBackground))
    }
    
    private var iPadWelcomeView: some View {
        VStack(spacing: 24) {
            Image(systemName: "pencil.and.outline")
                .font(.system(size: 80))
                .foregroundColor(.blue)
            
            VStack(spacing: 8) {
                Text("Tamil Writing Practice")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Select a character from the sidebar to begin")
                    .font(.title3)
                    .foregroundColor(.secondary)
            }
            
            VStack(spacing: 12) {
                Text("iPad Features")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(alignment: .leading, spacing: 8) {
                    FeatureRow(icon: "apple.logo", text: "Optimized for Apple Pencil")
                    FeatureRow(icon: "rectangle.split.3x1", text: "Multi-character practice mode")
                    FeatureRow(icon: "magnifyingglass", text: "Zoom and precision controls")
                    FeatureRow(icon: "sidebar.left", text: "Organized character library")
                    FeatureRow(icon: "chart.line.uptrend.xyaxis", text: "Detailed progress tracking")
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.secondarySystemBackground))
            )
        }
        .padding()
    }
    
    // MARK: - Actions
    
    private func setupIPadInterface() {
        writingViewModel.configureForIPad()
    }
    
    private func handleCharacterComplete(_ character: TamilCharacter, result: WritingResult) {
        writingViewModel.recordCharacterCompletion(character, result: result)
    }
    
    private func handleWritingComplete(_ result: WritingResult) {
        writingViewModel.recordWritingResult(result)
        
        Task {
            if let userId = UUID(uuidString: "user-id") {
                let assessment = await assessmentEngine.assessWriting(
                    character: result.character,
                    userStrokes: result.strokes,
                    writingMode: result.writingMode,
                    userId: userId,
                    startTime: Date().addingTimeInterval(-result.completionTime)
                )
                
                writingViewModel.updateAssessment(assessment)
            }
        }
    }
}

// MARK: - Character Category View

struct CharacterCategoryView: View {
    @Binding var selectedCharacter: TamilCharacter?
    let onCharacterSelected: (TamilCharacter) -> Void
    
    @StateObject private var scriptService = TamilScriptService.shared
    @State private var selectedCategory: CharacterCategory = .vowels
    @State private var searchText = ""
    
    enum CharacterCategory: String, CaseIterable {
        case vowels = "Vowels"
        case consonants = "Consonants"
        case combined = "Combined"
        case favorites = "Favorites"
        
        var icon: String {
            switch self {
            case .vowels: return "a.circle.fill"
            case .consonants: return "textformat"
            case .combined: return "textformat.abc"
            case .favorites: return "heart.fill"
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Search
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
            }
            .padding()
            .background(Color(.secondarySystemBackground))
            
            // Category tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(CharacterCategory.allCases, id: \.self) { category in
                        CategoryTab(
                            category: category,
                            isSelected: selectedCategory == category,
                            action: { selectedCategory = category }
                        )
                    }
                }
                .padding(.horizontal)
            }
            .padding(.vertical, 8)
            
            // Character list
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                    ForEach(filteredCharacters, id: \.id) { character in
                        IPadCharacterCell(
                            character: character,
                            isSelected: selectedCharacter?.id == character.id,
                            onTap: {
                                selectedCharacter = character
                                onCharacterSelected(character)
                            }
                        )
                    }
                }
                .padding()
            }
        }
    }
    
    private var filteredCharacters: [TamilCharacter] {
        let characters: [TamilCharacter]
        
        switch selectedCategory {
        case .vowels:
            characters = scriptService.vowels
        case .consonants:
            characters = scriptService.consonants
        case .combined:
            characters = scriptService.allCharacters.filter { $0.characterType == .combined }
        case .favorites:
            characters = Array(scriptService.allCharacters.prefix(9)) // Mock favorites
        }
        
        if searchText.isEmpty {
            return characters
        } else {
            return characters.filter { character in
                character.character.contains(searchText) ||
                character.romanization.lowercased().contains(searchText.lowercased()) ||
                character.characterNameEnglish.lowercased().contains(searchText.lowercased())
            }
        }
    }
}

struct CategoryTab: View {
    let category: CharacterCategoryView.CharacterCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: category.icon)
                    .font(.caption)
                
                Text(category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue : Color(.tertiarySystemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct IPadCharacterCell: View {
    let character: TamilCharacter
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(character.character)
                    .font(.title)
                    .foregroundColor(.primary)
                
                Text(character.romanization)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(character.characterNameEnglish)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                // Difficulty indicator
                HStack(spacing: 2) {
                    ForEach(1...character.difficultyLevel, id: \.self) { _ in
                        Circle()
                            .fill(character.writingComplexity.color)
                            .frame(width: 4, height: 4)
                    }
                }
            }
            .padding()
            .frame(height: 100)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.2) : Color(.secondarySystemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - iPad Writing Mode Selector

struct IPadWritingModeSelector: View {
    @Binding var selectedMode: WritingMode
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(WritingMode.allCases, id: \.self) { mode in
                Button(action: { selectedMode = mode }) {
                    HStack(spacing: 6) {
                        Image(systemName: mode.icon)
                            .font(.caption)
                        
                        Text(mode.displayName)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedMode == mode ? .white : .primary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedMode == mode ? Color.blue : Color(.tertiarySystemBackground))
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

// MARK: - iPad Tool Selector

enum WritingTool: String, CaseIterable {
    case pencil = "Apple Pencil"
    case pen = "Pen"
    case marker = "Marker"
    case finger = "Finger"
    
    var icon: String {
        switch self {
        case .pencil: return "pencil.tip"
        case .pen: return "pencil"
        case .marker: return "paintbrush.pointed"
        case .finger: return "hand.point.up"
        }
    }
    
    var color: Color {
        switch self {
        case .pencil: return .primary
        case .pen: return .blue
        case .marker: return .green
        case .finger: return .orange
        }
    }
}

struct IPadToolSelector: View {
    @Binding var selectedTool: WritingTool
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(WritingTool.allCases, id: \.self) { tool in
                Button(action: { selectedTool = tool }) {
                    Image(systemName: tool.icon)
                        .font(.title3)
                        .foregroundColor(selectedTool == tool ? tool.color : .secondary)
                        .padding(8)
                        .background(
                            Circle()
                                .fill(selectedTool == tool ? tool.color.opacity(0.2) : Color.clear)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

// MARK: - iPad Zoom Controls

struct IPadZoomControls: View {
    @Binding var zoom: CGFloat
    
    var body: some View {
        HStack(spacing: 8) {
            Button(action: { zoom = max(0.5, zoom - 0.25) }) {
                Image(systemName: "minus.magnifyingglass")
                    .font(.title3)
                    .foregroundColor(.blue)
            }
            
            Text("\(Int(zoom * 100))%")
                .font(.caption)
                .fontWeight(.medium)
                .frame(width: 40)
            
            Button(action: { zoom = min(3.0, zoom + 0.25) }) {
                Image(systemName: "plus.magnifyingglass")
                    .font(.title3)
                    .foregroundColor(.blue)
            }
        }
    }
}

// MARK: - Feature Row

struct FeatureRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

// MARK: - iPad Writing View Model

@MainActor
class IPadWritingViewModel: ObservableObject {
    @Published var practiceCharacters: [TamilCharacter] = []
    @Published var currentAssessment: TamilWritingAssessmentEngine.WritingAssessment?
    @Published var writingHistory: [WritingResult] = []
    
    private let scriptService = TamilScriptService.shared
    
    func configureForIPad() {
        // Load practice characters optimized for iPad
        practiceCharacters = Array(scriptService.allCharacters.prefix(6))
    }
    
    func selectCharacter(_ character: TamilCharacter) {
        // Handle character selection
        print("Selected character: \(character.character)")
    }
    
    func recordCharacterCompletion(_ character: TamilCharacter, result: WritingResult) {
        writingHistory.append(result)
    }
    
    func recordWritingResult(_ result: WritingResult) {
        writingHistory.append(result)
    }
    
    func updateAssessment(_ assessment: TamilWritingAssessmentEngine.WritingAssessment) {
        currentAssessment = assessment
    }
}

// MARK: - Enhanced Single Canvas View

struct EnhancedSingleCanvasView: View {
    let character: TamilCharacter
    let writingMode: WritingMode
    let selectedTool: WritingTool
    let zoom: CGFloat
    let onComplete: (WritingResult) -> Void

    @StateObject private var scriptService = TamilScriptService.shared
    @State private var canvasView = PKCanvasView()
    @State private var isDirty = false
    @State private var currentStrokes: [PKStroke] = []
    @State private var showingGuidance = true
    @State private var recognitionResults: [CharacterRecognitionResult] = []
    @State private var writingStartTime = Date()

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background guidance
                if showingGuidance && writingMode == .guided {
                    EnhancedCharacterGuidanceView(
                        character: character,
                        strokeOrders: scriptService.getStrokeOrder(for: character.id),
                        zoom: zoom
                    )
                }

                // Main writing canvas
                EnhancedTamilWritingCanvas(
                    canvasView: $canvasView,
                    isDirty: $isDirty,
                    character: character,
                    writingMode: writingMode,
                    selectedTool: selectedTool,
                    zoom: zoom,
                    onStrokeAdded: handleStrokeAdded,
                    onDrawingChanged: handleDrawingChanged
                )

                // Real-time feedback overlay
                if !recognitionResults.isEmpty && writingMode != .assessment {
                    IPadRecognitionFeedbackView(
                        results: recognitionResults,
                        geometry: geometry
                    )
                }

                // Canvas controls overlay
                VStack {
                    Spacer()

                    HStack {
                        Spacer()

                        IPadCanvasControlsView(
                            isDirty: isDirty,
                            showingGuidance: $showingGuidance,
                            onClear: clearCanvas,
                            onUndo: undoLastStroke,
                            onComplete: completeWriting
                        )
                    }
                    .padding()
                }
            }
        }
        .onAppear {
            setupCanvas()
        }
    }

    // MARK: - Canvas Actions

    private func setupCanvas() {
        canvasView.drawing = PKDrawing()
        currentStrokes = []
        writingStartTime = Date()

        // Configure canvas for selected tool
        configureCanvasForTool()
    }

    private func configureCanvasForTool() {
        switch selectedTool {
        case .pencil:
            canvasView.tool = PKInkingTool(.pencil, color: .black, width: 2.0)
        case .pen:
            canvasView.tool = PKInkingTool(.pen, color: .blue, width: 3.0)
        case .marker:
            canvasView.tool = PKInkingTool(.marker, color: .green, width: 8.0)
        case .finger:
            canvasView.tool = PKInkingTool(.pen, color: .orange, width: 4.0)
        }
    }

    private func handleStrokeAdded(_ stroke: PKStroke) {
        currentStrokes.append(stroke)

        Task {
            await performCharacterRecognition()
        }
    }

    private func handleDrawingChanged(_ drawing: PKDrawing) {
        currentStrokes = drawing.strokes
    }

    private func performCharacterRecognition() async {
        let image = canvasView.drawing.image(from: canvasView.bounds, scale: 1.0)

        // Mock recognition for now
        recognitionResults = [
            CharacterRecognitionResult(
                recognizedCharacter: character.character,
                confidence: 0.88,
                boundingBox: .zero,
                alternativeMatches: []
            )
        ]
    }

    private func clearCanvas() {
        canvasView.drawing = PKDrawing()
        currentStrokes = []
        recognitionResults = []
        isDirty = false
    }

    private func undoLastStroke() {
        guard !currentStrokes.isEmpty else { return }

        currentStrokes.removeLast()

        var newDrawing = PKDrawing()
        for stroke in currentStrokes {
            newDrawing.strokes.append(stroke)
        }
        canvasView.drawing = newDrawing

        isDirty = !currentStrokes.isEmpty
    }

    private func completeWriting() {
        let completionTime = Date().timeIntervalSince(writingStartTime)
        let accuracy = recognitionResults.first?.confidence ?? 0.0

        let result = WritingResult(
            character: character,
            writingMode: writingMode,
            strokes: currentStrokes,
            recognitionResults: recognitionResults,
            accuracy: accuracy,
            completionTime: completionTime,
            strokeAccuracies: [],
            isCompleted: true
        )

        onComplete(result)
    }
}

// MARK: - Multi-Character Canvas View

struct MultiCharacterCanvasView: View {
    let characters: [TamilCharacter]
    let writingMode: WritingMode
    let selectedTool: WritingTool
    let zoom: CGFloat
    let onCharacterComplete: (TamilCharacter, WritingResult) -> Void

    @State private var currentCharacterIndex = 0
    @State private var completedCharacters: Set<Int> = []

    var body: some View {
        VStack(spacing: 0) {
            // Multi-character header
            MultiCharacterHeaderView(
                characters: characters,
                currentIndex: currentCharacterIndex,
                completedIndices: completedCharacters,
                onCharacterTap: { index in
                    currentCharacterIndex = index
                }
            )

            // Canvas grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(Array(characters.enumerated()), id: \.offset) { index, character in
                    MultiCharacterCanvasCell(
                        character: character,
                        writingMode: writingMode,
                        selectedTool: selectedTool,
                        zoom: zoom,
                        isActive: index == currentCharacterIndex,
                        isCompleted: completedCharacters.contains(index),
                        onComplete: { result in
                            completedCharacters.insert(index)
                            onCharacterComplete(character, result)

                            // Auto-advance to next character
                            if index < characters.count - 1 {
                                currentCharacterIndex = index + 1
                            }
                        }
                    )
                }
            }
            .padding()
        }
    }
}

struct MultiCharacterHeaderView: View {
    let characters: [TamilCharacter]
    let currentIndex: Int
    let completedIndices: Set<Int>
    let onCharacterTap: (Int) -> Void

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(Array(characters.enumerated()), id: \.offset) { index, character in
                    Button(action: { onCharacterTap(index) }) {
                        VStack(spacing: 4) {
                            Text(character.character)
                                .font(.title2)
                                .foregroundColor(.primary)

                            Circle()
                                .fill(completedIndices.contains(index) ? Color.green :
                                      index == currentIndex ? Color.blue : Color.gray.opacity(0.3))
                                .frame(width: 8, height: 8)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(index == currentIndex ? Color.blue.opacity(0.1) : Color.clear)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.secondarySystemBackground))
    }
}

struct MultiCharacterCanvasCell: View {
    let character: TamilCharacter
    let writingMode: WritingMode
    let selectedTool: WritingTool
    let zoom: CGFloat
    let isActive: Bool
    let isCompleted: Bool
    let onComplete: (WritingResult) -> Void

    @State private var canvasView = PKCanvasView()
    @State private var isDirty = false

    var body: some View {
        VStack(spacing: 8) {
            // Character header
            HStack {
                Text(character.character)
                    .font(.title)
                    .fontWeight(.semibold)

                Spacer()

                if isCompleted {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else if isActive {
                    Image(systemName: "pencil.circle.fill")
                        .foregroundColor(.blue)
                }
            }

            // Mini canvas
            EnhancedTamilWritingCanvas(
                canvasView: $canvasView,
                isDirty: $isDirty,
                character: character,
                writingMode: writingMode,
                selectedTool: selectedTool,
                zoom: zoom * 0.8, // Smaller zoom for multi-character mode
                onStrokeAdded: { _ in },
                onDrawingChanged: { _ in }
            )
            .frame(height: 200)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isActive ? Color.blue : Color.gray.opacity(0.3), lineWidth: 2)
            )

            // Quick controls
            HStack {
                Button("Clear") {
                    canvasView.drawing = PKDrawing()
                    isDirty = false
                }
                .font(.caption)
                .foregroundColor(.red)

                Spacer()

                Button("Done") {
                    let result = WritingResult(
                        character: character,
                        writingMode: writingMode,
                        strokes: canvasView.drawing.strokes,
                        recognitionResults: [],
                        accuracy: 0.85, // Mock accuracy
                        completionTime: 30.0,
                        strokeAccuracies: [],
                        isCompleted: true
                    )
                    onComplete(result)
                }
                .font(.caption)
                .foregroundColor(.blue)
                .disabled(!isDirty)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isActive ? Color.blue.opacity(0.05) : Color(.secondarySystemBackground))
        )
        .opacity(isCompleted ? 0.7 : 1.0)
    }
}

// MARK: - Enhanced Tamil Writing Canvas

struct EnhancedTamilWritingCanvas: UIViewRepresentable {
    @Binding var canvasView: PKCanvasView
    @Binding var isDirty: Bool

    let character: TamilCharacter?
    let writingMode: WritingMode
    let selectedTool: WritingTool
    let zoom: CGFloat
    let onStrokeAdded: (PKStroke) -> Void
    let onDrawingChanged: (PKDrawing) -> Void

    func makeUIView(context: Context) -> PKCanvasView {
        canvasView.delegate = context.coordinator
        canvasView.drawingPolicy = .anyInput
        canvasView.backgroundColor = UIColor.clear

        // Configure for iPad
        if #available(iOS 14.0, *) {
            canvasView.drawingPolicy = selectedTool == .finger ? .anyInput : .pencilOnly
        } else {
            canvasView.allowsFingerDrawing = selectedTool == .finger
        }
        canvasView.isRulerActive = false

        // Set zoom
        canvasView.zoomScale = zoom

        return canvasView
    }

    func updateUIView(_ uiView: PKCanvasView, context: Context) {
        uiView.zoomScale = zoom
        if #available(iOS 14.0, *) {
            uiView.drawingPolicy = selectedTool == .finger ? .anyInput : .pencilOnly
        } else {
            uiView.allowsFingerDrawing = selectedTool == .finger
        }

        // Update tool based on selection
        switch selectedTool {
        case .pencil:
            uiView.tool = PKInkingTool(.pencil, color: .black, width: 2.0)
        case .pen:
            uiView.tool = PKInkingTool(.pen, color: .blue, width: 3.0)
        case .marker:
            uiView.tool = PKInkingTool(.marker, color: .green, width: 8.0)
        case .finger:
            uiView.tool = PKInkingTool(.pen, color: .orange, width: 4.0)
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, PKCanvasViewDelegate {
        let parent: EnhancedTamilWritingCanvas

        init(_ parent: EnhancedTamilWritingCanvas) {
            self.parent = parent
        }

        func canvasViewDrawingDidChange(_ canvasView: PKCanvasView) {
            parent.isDirty = !canvasView.drawing.strokes.isEmpty
            parent.onDrawingChanged(canvasView.drawing)
        }

        func canvasViewDidEndUsingTool(_ canvasView: PKCanvasView) {
            if let latestStroke = canvasView.drawing.strokes.last {
                parent.onStrokeAdded(latestStroke)
            }
        }
    }
}

// MARK: - Enhanced Character Guidance View

struct EnhancedCharacterGuidanceView: View {
    let character: TamilCharacter
    let strokeOrders: [TamilStrokeOrder]
    let zoom: CGFloat

    var body: some View {
        Text(character.character)
            .font(.system(size: 200 * zoom, weight: .ultraLight))
            .foregroundColor(.gray.opacity(0.2))
            .scaleEffect(zoom)
    }
}

// MARK: - iPad Recognition Feedback View

struct IPadRecognitionFeedbackView: View {
    let results: [CharacterRecognitionResult]
    let geometry: GeometryProxy

    var body: some View {
        VStack {
            HStack {
                Spacer()

                VStack(alignment: .trailing, spacing: 8) {
                    ForEach(Array(results.prefix(3).enumerated()), id: \.offset) { index, result in
                        HStack(spacing: 8) {
                            Text(result.recognizedCharacter)
                                .font(.title2)

                            Text("\(Int(result.confidence * 100))%")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemBackground))
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                        )
                        .opacity(index == 0 ? 1.0 : 0.7)
                    }
                }
            }

            Spacer()
        }
        .padding()
    }
}

// MARK: - iPad Canvas Controls View

struct IPadCanvasControlsView: View {
    let isDirty: Bool
    @Binding var showingGuidance: Bool
    let onClear: () -> Void
    let onUndo: () -> Void
    let onComplete: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            // Guidance toggle
            Button(action: { showingGuidance.toggle() }) {
                Image(systemName: showingGuidance ? "eye.slash.fill" : "eye.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.orange))
            }

            // Clear
            Button(action: onClear) {
                Image(systemName: "trash.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.red))
            }

            // Undo
            Button(action: onUndo) {
                Image(systemName: "arrow.uturn.backward")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.blue))
            }
            .disabled(!isDirty)

            // Complete
            Button(action: onComplete) {
                Image(systemName: "checkmark")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.green))
            }
            .disabled(!isDirty)
        }
        .background(
            RoundedRectangle(cornerRadius: 22)
                .fill(Color.black.opacity(0.1))
                .blur(radius: 10)
        )
    }
}

// MARK: - Character Detail Views

struct CharacterDetailView: View {
    let character: TamilCharacter

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Character Details")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 8) {
                WritingDetailRow(label: "Character", value: character.character)
                WritingDetailRow(label: "Name", value: character.characterNameEnglish)
                WritingDetailRow(label: "Romanization", value: character.romanization)
                WritingDetailRow(label: "Type", value: character.characterType.rawValue.capitalized)
                WritingDetailRow(label: "Difficulty", value: "\(character.difficultyLevel)/5")
                WritingDetailRow(label: "Strokes", value: "\(character.strokeCount)")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

struct StrokeOrderDetailView: View {
    let character: TamilCharacter

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Stroke Order")
                .font(.headline)
                .fontWeight(.semibold)

            Text("Follow the numbered sequence to write this character correctly.")
                .font(.caption)
                .foregroundColor(.secondary)

            // Stroke order visualization would go here
            Text("Stroke order animation coming soon")
                .font(.caption)
                .foregroundColor(.blue)
                .italic()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

struct WritingTipsView: View {
    let character: TamilCharacter

    private var tips: [String] {
        switch character.writingComplexity {
        case .simple:
            return [
                "Start with light pressure",
                "Follow the stroke order",
                "Keep consistent size"
            ]
        case .moderate:
            return [
                "Pay attention to curves",
                "Maintain proper proportions",
                "Practice slowly first"
            ]
        case .complex:
            return [
                "Break into smaller parts",
                "Focus on each stroke",
                "Practice individual components"
            ]
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Writing Tips")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(tips, id: \.self) { tip in
                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "lightbulb.fill")
                        .font(.caption)
                        .foregroundColor(.yellow)

                    Text(tip)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

struct ProgressDetailView: View {
    let character: TamilCharacter

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Your Progress")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 8) {
                ProgressRow(label: "Accuracy", value: 0.85, color: .green)
                ProgressRow(label: "Speed", value: 0.72, color: .blue)
                ProgressRow(label: "Consistency", value: 0.68, color: .orange)
            }

            Text("Practice more to improve your scores!")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

struct WritingDetailRow: View {
    let label: String
    let value: String

    var body: some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
}

struct ProgressRow: View {
    let label: String
    let value: Double
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(label)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(Int(value * 100))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }

            ProgressView(value: value, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
        }
    }
}

#Preview {
    EnhancedIPadWritingView()
}
