import SwiftUI
import AVFoundation

// MARK: - Main Lesson View with Enhanced Interactivity

struct LessonView: View {
    let lesson: Lesson
    @ObservedObject var lessonService = LessonService.shared
    @State private var currentExerciseIndex = 0
    @State private var userAnswers: [String] = []
    @State private var exerciseResults: [LessonExerciseResult] = []
    @State private var showingResults = false
    @State private var sessionStartTime = Date()
    @State private var showingHints = false
    @State private var hintsUsed = 0
    @State private var timeRemaining: Int = 0
    @State private var timer: Timer?
    @State private var showingCelebration = false
    @StateObject private var audioService = AudioPlayerService.shared
    
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.dismiss) var dismiss
    
    var currentExercise: Exercise? {
        guard currentExerciseIndex < lesson.exercises.count else { return nil }
        return lesson.exercises[currentExerciseIndex]
    }
    
    var progress: Double {
        guard !lesson.exercises.isEmpty else { return 0.0 }
        let calculatedProgress = Double(currentExerciseIndex) / Double(lesson.exercises.count)
        // Ensure we never return NaN or infinity
        guard calculatedProgress.isFinite else { return 0.0 }
        return min(max(calculatedProgress, 0.0), 1.0)
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Dynamic background
                backgroundGradient
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header with progress
                    lessonHeader
                        .padding(.horizontal)
                    
                    // Main content
                    if let exercise = currentExercise {
                        ScrollView {
                            VStack(spacing: 24) {
                                exerciseCard(exercise, geometry: geometry)
                                    .transition(.asymmetric(
                                        insertion: .move(edge: .trailing).combined(with: .opacity),
                                        removal: .move(edge: .leading).combined(with: .opacity)
                                    ))
                            }
                            .padding(.horizontal)
                        }
                    } else if showingResults {
                        lessonCompletionView
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupLesson()
        }
        .onDisappear {
            timer?.invalidate()
        }
        .alert("Lesson Complete!", isPresented: $showingCelebration) {
            Button("Continue") {
                dismiss()
            }
        } message: {
            Text("Congratulations! You've completed the lesson with a great score!")
        }
    }
    
    // MARK: - Header
    
    private var lessonHeader: some View {
        VStack(spacing: 16) {
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "xmark")
                        .font(.title2)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(Color.black.opacity(0.3))
                        .clipShape(Circle())
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Text(lesson.title)
                        .font(.headline)
                        .foregroundColor(.white)
                        .lineLimit(1)
                    
                    if let exercise = currentExercise, exercise.timeLimit != nil {
                        Text("\(timeRemaining)s")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(Color.black.opacity(0.3))
                            .clipShape(Capsule())
                    }
                }
                
                Spacer()
                
                if let exercise = currentExercise {
                    Button(action: { showingHints.toggle() }) {
                        Image(systemName: "lightbulb.fill")
                            .font(.title2)
                            .foregroundColor(.yellow)
                            .frame(width: 44, height: 44)
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                    .disabled(exercise.hints.isEmpty)
                }
            }
            
            // Progress bar
            GeometryReader { geo in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.white.opacity(0.3))
                        .frame(height: 8)
                    
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [Color.emojiOrange, Color.emojiPink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geo.size.width * progress, height: 8)
                        .animation(.easeInOut(duration: 0.3), value: progress)
                }
            }
            .frame(height: 8)
            
            HStack {
                Text("\(currentExerciseIndex + 1) of \(lesson.exercises.count)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                Text("\(Int(progress * 100))% Complete")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.top, 8)
    }
    
    // MARK: - Exercise Card
    
    private func exerciseCard(_ exercise: Exercise, geometry: GeometryProxy) -> some View {
        VStack(spacing: 24) {
            // Exercise content
            VStack(spacing: 20) {
                exerciseHeader(exercise)
                exerciseQuestion(exercise)
                exerciseInteraction(exercise)
                
                if showingHints && !exercise.hints.isEmpty {
                    hintsView(exercise.hints)
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 24)
                    .fill(.ultraThinMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
            )
            
            // Action buttons
            actionButtons(exercise)
        }
    }
    
    private func exerciseHeader(_ exercise: Exercise) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(exercise.type.displayName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                    Text("\(exercise.points) points")
                        .font(.caption)
                        .fontWeight(.medium)
                }
            }
            
            Spacer()
            
            categoryIcon(lesson.category)
        }
    }
    
    private func exerciseQuestion(_ exercise: Exercise) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(exercise.question)
                .font(.title2)
                .fontWeight(.semibold)
                .multilineTextAlignment(.leading)
            
            if let culturalNote = exercise.culturalNote {
                HStack {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(.blue)
                    Text(culturalNote)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(12)
                .background(Color.blue.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
        }
    }
    
    @ViewBuilder
    private func exerciseInteraction(_ exercise: Exercise) -> some View {
        switch exercise.type {
        case .multipleChoice:
            multipleChoiceView(exercise)
        case .fillInBlank:
            fillInTheBlankView(exercise)
        case .matching:
            matchingView(exercise)
        case .pronunciation:
            pronunciationView(exercise)
        case .listening:
            listeningView(exercise)
        case .dragAndDrop:
            dragAndDropView(exercise)
        default:
            Text("Exercise type not implemented")
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Exercise Types
    
    private func multipleChoiceView(_ exercise: Exercise) -> some View {
        VStack(spacing: 12) {
            ForEach(Array(exercise.options.enumerated()), id: \.offset) { index, option in
                Button(action: {
                    selectAnswer(String(index))
                }) {
                    HStack {
                        Text(option)
                            .font(.body)
                            .foregroundColor(colorScheme == .dark ? .white : .black)
                            .multilineTextAlignment(.leading)
                        
                        Spacer()
                        
                        if userAnswers.indices.contains(currentExerciseIndex) && userAnswers[currentExerciseIndex] == String(index) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                        }
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(userAnswers.indices.contains(currentExerciseIndex) && userAnswers[currentExerciseIndex] == String(index) ? 
                                  Color.green.opacity(0.2) : Color.gray.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(userAnswers.indices.contains(currentExerciseIndex) && userAnswers[currentExerciseIndex] == String(index) ? 
                                           Color.green : Color.clear, lineWidth: 2)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    private func fillInTheBlankView(_ exercise: Exercise) -> some View {
        VStack(spacing: 16) {
            TextField("Type your answer here...", text: Binding(
                get: { userAnswers.indices.contains(currentExerciseIndex) ? userAnswers[currentExerciseIndex] : "" },
                set: { selectAnswer($0) }
            ))
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .font(.body)
            .autocapitalization(.none)
            .disableAutocorrection(true)
        }
    }
    
    private func pronunciationView(_ exercise: Exercise) -> some View {
        VStack(spacing: 16) {
            Text("Tap and hold to record your pronunciation")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: {
                // Implement pronunciation recording
            }) {
                VStack {
                    Image(systemName: "mic.fill")
                        .font(.title)
                        .foregroundColor(.white)
                    Text("Hold to Record")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .frame(width: 120, height: 120)
                .background(
                    Circle()
                        .fill(Color.red.gradient)
                )
            }
        }
    }
    
    private func listeningView(_ exercise: Exercise) -> some View {
        VStack(spacing: 16) {
            Button(action: {
                playAudio(exercise.audioURL)
            }) {
                VStack {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.title)
                        .foregroundColor(.white)
                    Text("Play Audio")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .frame(width: 100, height: 100)
                .background(
                    Circle()
                        .fill(Color.blue.gradient)
                )
            }
            
            TextField("What did you hear?", text: Binding(
                get: { userAnswers.indices.contains(currentExerciseIndex) ? userAnswers[currentExerciseIndex] : "" },
                set: { selectAnswer($0) }
            ))
            .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
    
    private func matchingView(_ exercise: Exercise) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Match the items")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            if exercise.options.count >= 2 {
                // Create pairs from options
                let leftItems = Array(exercise.options.prefix(exercise.options.count / 2))
                let rightItems = Array(exercise.options.suffix(exercise.options.count / 2))
                
                VStack(spacing: 12) {
                    ForEach(Array(zip(leftItems.indices, leftItems)), id: \.0) { index, leftItem in
                        HStack(spacing: 20) {
                            // Left item
                            Text(leftItem)
                                .font(.body)
                                .foregroundColor(.primary)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.blue.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                                        )
                                )
                            
                            // Connector line
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 40, height: 2)
                            
                            // Right item (if available)
                            if index < rightItems.count {
                                Text(rightItems[index])
                                    .font(.body)
                                    .foregroundColor(.primary)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.green.opacity(0.1))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                            )
                                    )
                            }
                        }
                    }
                }
                
                // Simple feedback mechanism
                Button(action: {
                    selectAnswer("matched")
                }) {
                    Text("Complete Matching")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, minHeight: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue.gradient)
                        )
                }
                .padding(.top, 8)
            } else {
                Text("Not enough items to match")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
        .padding(.vertical, 8)
    }
    
    private func dragAndDropView(_ exercise: Exercise) -> some View {
        Text("Drag and drop exercise (to be implemented)")
            .foregroundColor(.secondary)
    }
    
    // MARK: - Helper Views
    
    private func hintsView(_ hints: [String]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("Hints")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            ForEach(Array(hints.enumerated()), id: \.offset) { index, hint in
                HStack(alignment: .top) {
                    Text("\(index + 1).")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(hint)
                        .font(.body)
                        .multilineTextAlignment(.leading)
                }
            }
        }
        .padding(16)
        .background(Color.yellow.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    private func categoryIcon(_ category: LessonCategory) -> some View {
        Image(systemName: category.icon)
            .font(.title2)
            .foregroundColor(.white)
            .frame(width: 40, height: 40)
            .background(
                Circle()
                    .fill(Color(category.primaryColor).gradient)
            )
    }
    
    private func actionButtons(_ exercise: Exercise) -> some View {
        HStack(spacing: 16) {
            if currentExerciseIndex > 0 {
                Button(action: previousExercise) {
                    Text("Previous")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, minHeight: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.gray.opacity(0.2))
                        )
                }
            }
            
            Button(action: nextExercise) {
                Text(currentExerciseIndex == lesson.exercises.count - 1 ? "Finish" : "Next")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, minHeight: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(hasAnswered ? Color.green.gradient : Color.gray.gradient)
                    )
            }
            .disabled(!hasAnswered)
        }
    }
    
    private var lessonCompletionView: some View {
        VStack(spacing: 24) {
            Text("Lesson Complete!")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            // Results summary
            VStack(spacing: 16) {
                resultCard("Score", value: "\(exerciseResults.reduce(0) { $0 + $1.score }) points")
                resultCard("Accuracy", value: "\(Int(Double(exerciseResults.filter { $0.isCorrect }.count) / Double(exerciseResults.count) * 100))%")
                resultCard("Time", value: formatTime(exerciseResults.reduce(0) { $0 + $1.timeSpent }))
            }
            
            Button(action: { dismiss() }) {
                Text("Continue")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, minHeight: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.green.gradient)
                    )
            }
            .padding(.horizontal)
        }
        .padding()
    }
    
    private func resultCard(_ title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
            
            Spacer()
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Computed Properties
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(lesson.category.primaryColor),
                Color(lesson.category.primaryColor).opacity(0.7)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var hasAnswered: Bool {
        userAnswers.indices.contains(currentExerciseIndex) && !userAnswers[currentExerciseIndex].isEmpty
    }
    
    // MARK: - Methods
    
    private func setupLesson() {
        userAnswers = Array(repeating: "", count: lesson.exercises.count)
        startTimer()
    }
    
    private func selectAnswer(_ answer: String) {
        if userAnswers.indices.contains(currentExerciseIndex) {
            userAnswers[currentExerciseIndex] = answer
        } else {
            userAnswers.append(answer)
        }
    }
    
    private func nextExercise() {
        guard let exercise = currentExercise else { return }
        
        // Record result for current exercise
        let timeSpent = Int(Date().timeIntervalSince(sessionStartTime))
        let isCorrect = validateAnswer(exercise: exercise, userAnswer: userAnswers[currentExerciseIndex])
        
        let result = LessonExerciseResult(
            exerciseId: exercise.id,
            userId: UUID(), // This should come from the current user
            userAnswer: userAnswers[currentExerciseIndex],
            isCorrect: isCorrect,
            score: isCorrect ? exercise.points : 0,
            timeSpent: timeSpent,
            hintsUsed: hintsUsed,
            completedAt: Date()
        )
        
        exerciseResults.append(result)
        
        if currentExerciseIndex < lesson.exercises.count - 1 {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentExerciseIndex += 1
            }
            startTimer()
        } else {
            // Lesson complete
            showingResults = true
            showingCelebration = true
        }
    }
    
    private func previousExercise() {
        guard currentExerciseIndex > 0 else { return }
        withAnimation(.easeInOut(duration: 0.3)) {
            currentExerciseIndex -= 1
        }
        startTimer()
    }
    
    private func startTimer() {
        timer?.invalidate()
        
        guard let exercise = currentExercise, let timeLimit = exercise.timeLimit else { return }
        
        timeRemaining = timeLimit
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if timeRemaining > 0 {
                timeRemaining -= 1
            } else {
                timer?.invalidate()
                // Auto-submit when time runs out
                if !hasAnswered {
                    selectAnswer("")
                }
                nextExercise()
            }
        }
    }
    
    private func validateAnswer(exercise: Exercise, userAnswer: String) -> Bool {
        switch exercise.type {
        case .multipleChoice:
            return Int(userAnswer) == exercise.correctAnswer
        case .fillInBlank:
            return userAnswer.lowercased().trimmingCharacters(in: .whitespacesAndNewlines) == 
                   exercise.options.first?.lowercased()
        default:
            return false
        }
    }
    
    private func playAudio(_ audioURL: String?) {
        guard let audioURL = audioURL else {
            print("⚠️ No audio URL provided")
            return
        }

        audioService.playAudio(from: audioURL)
    }
    
    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}

// MARK: - Supporting Models

struct LessonExerciseResult {
    let exerciseId: UUID
    let userId: UUID
    let userAnswer: String
    let isCorrect: Bool
    let score: Int
    let timeSpent: Int
    let hintsUsed: Int
    let completedAt: Date
}

struct ViewLearningSession {
    let userId: UUID
    let language: Language
    let sessionType: String
    let startTime: Date
    var endTime: Date?
    var totalScore: Int = 0
    var exercisesCompleted: Int = 0
}

// MARK: - Preview

#Preview {
    LessonView(lesson: Lesson(
        title: "French Greetings",
        lessonDescription: "Learn basic French greetings",
        language: .french,
        difficulty: .beginner,
        category: .vocabulary,
        estimatedDuration: 15,
        exercises: [
            Exercise(
                type: .multipleChoice,
                question: "How do you say 'Hello' in French?",
                options: ["Bonjour", "Bonsoir", "Salut", "Au revoir"],
                correctAnswer: 0,
                explanation: "Bonjour is the most common way to say hello in French.",
                points: 10,
                timeLimit: 30,
                hints: ["Think about morning greetings", "It starts with 'Bon'"]
            )
        ]
    ))
} 