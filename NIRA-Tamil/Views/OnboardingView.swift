//
//  OnboardingView.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

// MARK: - Extended Brand System for Onboarding

extension NIRABrandSystem.Spacing {
    static let xxxl: CGFloat = 64
}

extension NIRABrandSystem.Typography {
    static let titleLarge = Font.system(size: 22, weight: .semibold, design: .rounded)
    static let titleMedium = Font.system(size: 18, weight: .medium, design: .rounded)
    static let titleSmall = Font.system(size: 16, weight: .medium, design: .rounded)
}

extension NIRABrandSystem.CornerRadius {
    static let xl: CGFloat = 20
    static let lg: CGFloat = 16
    static let md: CGFloat = 12
}

struct OnboardingView: View {
    let onComplete: () -> Void
    @State private var currentPage = 0
    @State private var animateElements = false
    @State private var showContent = false
    @State private var backgroundOffset: CGFloat = 0

    private let pages = [
        PremiumOnboardingPage(
            title: "Welcome to NIRA Tamil",
            subtitle: "Master Tamil through intelligent conversations",
            icon: "brain.head.profile",
            description: "Experience the future of Tamil language learning with AI-powered tutors and immersive cultural simulations.",
            gradient: [Color(hex: "667eea"), Color(hex: "764ba2")],
            features: ["Tamil Language", "AI Tutors", "Cultural Immersion"]
        ),
        PremiumOnboardingPage(
            title: "AI-Powered Learning",
            subtitle: "Personalized tutoring that adapts to you",
            icon: "sparkles",
            description: "Our advanced AI understands your learning style, pace, and goals to create a truly personalized experience.",
            gradient: [Color(hex: "f093fb"), Color(hex: "f5576c")],
            features: ["Smart Analytics", "Adaptive Learning", "Real-time Feedback"]
        ),
        PremiumOnboardingPage(
            title: "Cultural Simulations",
            subtitle: "Practice in authentic scenarios",
            icon: "globe.europe.africa.fill",
            description: "Immerse yourself in real-world situations and build confidence through interactive cultural experiences.",
            gradient: [Color(hex: "4facfe"), Color(hex: "00f2fe")],
            features: ["Real Scenarios", "Cultural Context", "Confidence Building"]
        ),
        PremiumOnboardingPage(
            title: "Ready to Begin?",
            subtitle: "Join millions learning with NIRA",
            icon: "star.fill",
            description: "Start your journey to fluency with the most advanced language learning platform available.",
            gradient: [Color(hex: "43e97b"), Color(hex: "38f9d7")],
            features: ["Premium Quality", "Proven Results", "Global Community"]
        )
    ]

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Premium Animated Background
                PremiumAnimatedBackground(
                    colors: pages[currentPage].gradient,
                    animateElements: $animateElements,
                    geometry: geometry
                )

                // Main Content
                VStack(spacing: 0) {
                    // Premium Page Content
                    TabView(selection: $currentPage) {
                        ForEach(0..<pages.count, id: \.self) { index in
                            PremiumOnboardingPageView(
                                page: pages[index],
                                isActive: currentPage == index,
                                geometry: geometry
                            )
                            .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .onChange(of: currentPage) {
                        withAnimation(.easeInOut(duration: 0.8)) {
                            animateElements.toggle()
                        }
                    }

                    // Premium Controls
                    PremiumOnboardingControls(
                        currentPage: $currentPage,
                        totalPages: pages.count,
                        onComplete: onComplete
                    )
                    .padding(.bottom, geometry.safeAreaInsets.bottom + 20)
                }
            }
        }
        .ignoresSafeArea()
        .onAppear {
            withAnimation(.easeOut(duration: 1.2)) {
                showContent = true
                animateElements = true
            }
        }
    }
}

// MARK: - Premium Onboarding Components

struct PremiumOnboardingPage {
    let title: String
    let subtitle: String
    let icon: String
    let description: String
    let gradient: [Color]
    let features: [String]
}

struct PremiumAnimatedBackground: View {
    let colors: [Color]
    @Binding var animateElements: Bool
    let geometry: GeometryProxy

    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: colors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            // Animated orbs
            ForEach(0..<12, id: \.self) { index in
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.15),
                                Color.white.opacity(0.05),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 10,
                            endRadius: 80
                        )
                    )
                    .frame(width: CGFloat.random(in: 60...120))
                    .position(
                        x: CGFloat.random(in: 0...geometry.size.width),
                        y: CGFloat.random(in: 0...geometry.size.height)
                    )
                    .scaleEffect(animateElements ? 1.2 : 0.8)
                    .opacity(animateElements ? 0.8 : 0.4)
                    .animation(
                        .easeInOut(duration: Double.random(in: 3...6))
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.3),
                        value: animateElements
                    )
            }

            // Subtle mesh gradient overlay
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color.black.opacity(0.1),
                            Color.clear,
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .ignoresSafeArea()
        }
    }
}

struct PremiumOnboardingPageView: View {
    let page: PremiumOnboardingPage
    let isActive: Bool
    let geometry: GeometryProxy
    @State private var showElements = false

    var body: some View {
        VStack(spacing: 0) {
            Spacer()

            // Hero Section
            VStack(spacing: 32) {
                // Premium Icon with glassmorphism
                ZStack {
                    // Outer glow
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color.white.opacity(0.3),
                                    Color.white.opacity(0.1),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 40,
                                endRadius: 80
                            )
                        )
                        .frame(width: 160, height: 160)
                        .scaleEffect(showElements ? 1.0 : 0.8)
                        .opacity(showElements ? 1.0 : 0.0)

                    // Glassmorphism container
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 120, height: 120)
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.6),
                                            Color.white.opacity(0.2)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 2
                                )
                        )
                        .scaleEffect(showElements ? 1.0 : 0.6)
                        .opacity(showElements ? 1.0 : 0.0)

                    // Icon
                    Image(systemName: page.icon)
                        .font(.system(size: 48, weight: .light))
                        .foregroundColor(.white)
                        .scaleEffect(showElements ? 1.0 : 0.4)
                        .opacity(showElements ? 1.0 : 0.0)
                }
                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: showElements)

                // Premium Typography
                VStack(spacing: 16) {
                    Text(page.title)
                        .font(.system(size: 36, weight: .light, design: .rounded))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .opacity(showElements ? 1.0 : 0.0)
                        .offset(y: showElements ? 0 : 30)
                        .animation(.easeOut(duration: 0.8).delay(0.4), value: showElements)

                    Text(page.subtitle)
                        .font(.system(size: 18, weight: .regular))
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 32)
                        .opacity(showElements ? 1.0 : 0.0)
                        .offset(y: showElements ? 0 : 20)
                        .animation(.easeOut(duration: 0.8).delay(0.6), value: showElements)
                }
            }

            Spacer()

            // Premium Feature Cards
            VStack(spacing: 16) {
                Text(page.description)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
                    .opacity(showElements ? 1.0 : 0.0)
                    .offset(y: showElements ? 0 : 20)
                    .animation(.easeOut(duration: 0.8).delay(0.8), value: showElements)

                // Feature highlights
                HStack(spacing: 12) {
                    ForEach(Array(page.features.enumerated()), id: \.offset) { index, feature in
                        Text(feature)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(.ultraThinMaterial)
                            .cornerRadius(20)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                            .opacity(showElements ? 1.0 : 0.0)
                            .offset(y: showElements ? 0 : 15)
                            .animation(.easeOut(duration: 0.6).delay(1.0 + Double(index) * 0.1), value: showElements)
                    }
                }
                .padding(.horizontal, 20)
            }

            Spacer()
        }
        .padding()
        .onChange(of: isActive) {
            if isActive {
                showElements = false
                withAnimation {
                    showElements = true
                }
            }
        }
        .onAppear {
            if isActive {
                withAnimation {
                    showElements = true
                }
            }
        }
    }
}

struct PremiumOnboardingControls: View {
    @Binding var currentPage: Int
    let totalPages: Int
    let onComplete: () -> Void
    @State private var showControls = false

    var body: some View {
        VStack(spacing: 24) {
            // Premium Page Indicator
            HStack(spacing: 12) {
                ForEach(0..<totalPages, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 4)
                        .fill(currentPage == index ? Color.white : Color.white.opacity(0.3))
                        .frame(width: currentPage == index ? 32 : 8, height: 8)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: currentPage)
                }
            }
            .opacity(showControls ? 1.0 : 0.0)
            .offset(y: showControls ? 0 : 20)
            .animation(.easeOut(duration: 0.8).delay(0.2), value: showControls)

            // Premium Action Buttons
            HStack(spacing: 16) {
                // Back Button
                if currentPage > 0 {
                    Button(action: {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            currentPage -= 1
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .medium))
                            Text("Back")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(.ultraThinMaterial)
                        .cornerRadius(25)
                        .overlay(
                            RoundedRectangle(cornerRadius: 25)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                    }
                    .transition(.asymmetric(
                        insertion: .move(edge: .leading).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                }

                Spacer()

                // Next/Get Started Button
                Button(action: {
                    if currentPage == totalPages - 1 {
                        UserDefaults.standard.set(true, forKey: "hasCompletedOnboarding")
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            onComplete()
                        }
                    } else {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            currentPage += 1
                        }
                    }
                }) {
                    HStack(spacing: 8) {
                        Text(currentPage == totalPages - 1 ? "Get Started" : "Next")
                            .font(.system(size: 16, weight: .semibold))

                        if currentPage < totalPages - 1 {
                            Image(systemName: "chevron.right")
                                .font(.system(size: 16, weight: .medium))
                        } else {
                            Image(systemName: "arrow.right")
                                .font(.system(size: 16, weight: .medium))
                        }
                    }
                    .foregroundColor(.black)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            colors: [Color.white, Color.white.opacity(0.9)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
                }
                .scaleEffect(currentPage == totalPages - 1 ? 1.05 : 1.0)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: currentPage)
            }
            .padding(.horizontal, 32)
            .opacity(showControls ? 1.0 : 0.0)
            .offset(y: showControls ? 0 : 30)
            .animation(.easeOut(duration: 0.8).delay(0.4), value: showControls)
        }
        .onAppear {
            withAnimation {
                showControls = true
            }
        }
    }
}



#Preview {
    OnboardingView {
        print("Onboarding completed")
    }
}