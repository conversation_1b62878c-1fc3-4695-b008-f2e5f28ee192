import SwiftUI

struct MicroAssessmentView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var assessmentService = MicroAssessmentService()
    @StateObject private var supabaseClient = NIRASupabaseClient.shared
    @State private var assessmentItems: [AssessmentItem] = []
    @State private var currentItemIndex = 0
    @State private var userResponses: [String: String] = [:]
    @State private var isLoading = true
    @State private var isCompleted = false
    @State private var assessmentReport: AssessmentReport?
    @State private var selectedAnswer: String?
    @State private var showResults = false
    
    let userId: String
    let skillCategories: [SkillCategory]
    let assessmentTrigger: AssessmentTrigger
    
    var body: some View {
        NavigationView {
            ZStack {
                // Gradient background
                LinearGradient(
                    gradient: Gradient(colors: [Color.green.opacity(0.1), Color.blue.opacity(0.1)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                if isLoading {
                    loadingView
                } else if showResults, let report = assessmentReport {
                    resultsView(report: report)
                } else if isCompleted {
                    completionView
                } else {
                    assessmentContentView
                }
            }
            .navigationTitle("Quick Assessment")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Skip") {
                        dismiss()
                    }
                    .foregroundColor(.secondary)
                }
            }
            .task {
                await loadAssessmentItems()
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Preparing your assessment...")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("This will only take a few minutes")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Assessment Content View
    private var assessmentContentView: some View {
        VStack(spacing: 20) {
            progressIndicator
            
            if currentItemIndex < assessmentItems.count {
                let currentItem = assessmentItems[currentItemIndex]
                
                Spacer()
                assessmentCard(for: currentItem)
                Spacer()
                navigationButtons
            }
        }
        .padding()
    }
    
    // MARK: - Progress Indicator
    private var progressIndicator: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Question \(currentItemIndex + 1) of \(assessmentItems.count)")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if currentItemIndex < assessmentItems.count {
                    skillBadge(for: assessmentItems[currentItemIndex].skill)
                }
            }
            
            ProgressView(value: Double(currentItemIndex), total: Double(assessmentItems.count))
                .progressViewStyle(LinearProgressViewStyle())
                .scaleEffect(y: 2)
        }
    }
    
    private func skillBadge(for skill: SkillCategory) -> some View {
        Text(skill.displayName)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(skill.color.opacity(0.2))
            )
            .foregroundColor(skill.color)
    }
    
    // MARK: - Assessment Card
    private func assessmentCard(for item: AssessmentItem) -> some View {
        VStack(spacing: 24) {
            Text(item.question)
                .font(.title2)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Group {
                switch item.type {
                case .multipleChoice:
                    multipleChoiceOptions(for: item)
                case .fillInBlank:
                    fillInBlankInput(for: item)
                case .speaking:
                    speakingAssessment(for: item)
                case .writing:
                    writingAssessment(for: item)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }
    
    // MARK: - Answer Type Views
    private func multipleChoiceOptions(for item: AssessmentItem) -> some View {
        VStack(spacing: 12) {
            ForEach(item.options, id: \.self) { option in
                Button(action: {
                    selectedAnswer = option
                }) {
                    HStack {
                        Text(option)
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        if selectedAnswer == option {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedAnswer == option ? Color.blue.opacity(0.1) : Color(.secondarySystemBackground))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(selectedAnswer == option ? Color.blue : Color.clear, lineWidth: 2)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    private func fillInBlankInput(for item: AssessmentItem) -> some View {
        VStack(spacing: 16) {
            TextField("Type your answer here...", text: Binding(
                get: { selectedAnswer ?? "" },
                set: { selectedAnswer = $0 }
            ))
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .font(.body)
            .autocapitalization(.none)
            .disableAutocorrection(true)
            
            Text("Fill in the blank with the most appropriate word or phrase")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func speakingAssessment(for item: AssessmentItem) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "mic.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            Text("Speak the Tamil phrase")
                .font(.headline)
                .foregroundColor(.primary)
            
            Button(action: {
                selectedAnswer = "recorded_audio_placeholder"
            }) {
                Text("Start Recording")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.red)
                    )
            }
        }
    }
    
    private func writingAssessment(for item: AssessmentItem) -> some View {
        VStack(spacing: 16) {
            TextEditor(text: Binding(
                get: { selectedAnswer ?? "" },
                set: { selectedAnswer = $0 }
            ))
            .frame(height: 120)
            .padding(8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
            )
            
            Text("Write a short response (2-3 sentences)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Navigation Buttons
    private var navigationButtons: some View {
        HStack(spacing: 20) {
            if currentItemIndex > 0 {
                Button("Previous") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        currentItemIndex -= 1
                        selectedAnswer = userResponses[assessmentItems[currentItemIndex].id]
                    }
                }
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(currentItemIndex == assessmentItems.count - 1 ? "Finish" : "Next") {
                saveCurrentResponse()
                
                if currentItemIndex == assessmentItems.count - 1 {
                    Task {
                        await completeAssessment()
                    }
                } else {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        currentItemIndex += 1
                        selectedAnswer = userResponses[assessmentItems[currentItemIndex].id]
                    }
                }
            }
            .disabled(selectedAnswer?.isEmpty ?? true)
            .font(.headline)
            .foregroundColor(.white)
            .padding(.horizontal, 30)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(selectedAnswer?.isEmpty ?? true ? Color.gray : Color.blue)
            )
        }
    }
    
    // MARK: - Completion View
    private var completionView: some View {
        VStack(spacing: 24) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("Assessment Complete!")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("Processing your results...")
                .font(.body)
                .foregroundColor(.secondary)
            
            ProgressView()
                .scaleEffect(1.2)
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                withAnimation {
                    showResults = true
                }
            }
        }
    }
    
    // MARK: - Results View
    private func resultsView(report: AssessmentReport) -> some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(spacing: 8) {
                    Text("Overall Score")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("\(Int(report.overallScore * 100))%")
                        .font(.system(size: 48, weight: .bold))
                        .foregroundColor(scoreColor(report.overallScore))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.secondarySystemBackground))
                )
                
                Button("Continue Learning") {
                    dismiss()
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 40)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color.blue)
                )
            }
            .padding()
        }
    }
    
    // MARK: - Helper Functions
    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 0.8...: return .green
        case 0.6..<0.8: return .yellow
        default: return .red
        }
    }
    
    private func saveCurrentResponse() {
        guard let answer = selectedAnswer, !answer.isEmpty else { return }
        userResponses[assessmentItems[currentItemIndex].id] = answer
    }
    
    private func loadAssessmentItems() async {
        isLoading = true
        defer { isLoading = false }
        
        // For now, create mock assessment items
        // TODO: Replace with actual service call once implemented
        assessmentItems = [
            AssessmentItem(
                id: "1",
                type: .multipleChoice,
                skill: .vocabulary,
                question: "What is the Tamil word for 'hello'?",
                correctAnswer: "வணக்கம்",
                options: ["வணக்கம்", "நன்றி", "போடு", "வா"],
                audioUrl: nil,
                difficultyLevel: 1,
                timeLimit: 30,
                metadata: [:]
            ),
            AssessmentItem(
                id: "2",
                type: .fillInBlank,
                skill: .grammar,
                question: "Complete: நான் _____ போகிறேன்",
                correctAnswer: "வீட்டுக்கு",
                options: [],
                audioUrl: nil,
                difficultyLevel: 1,
                timeLimit: 30,
                metadata: [:]
            )
        ]
    }
    
    private func completeAssessment() async {
        isCompleted = true
        
        let _ = userResponses.map { (itemId, response) in
            MicroAssessmentResponse(
                id: UUID().uuidString,
                assessmentId: UUID().uuidString,
                itemId: itemId,
                userAnswer: response,
                correctAnswer: "", // Will be filled by service
                isCorrect: false, // Will be evaluated by service
                timeSpent: 30.0,
                timestamp: Date()
            )
        }

        // For now, create a mock assessment report
        // TODO: Replace with actual service call once implemented
        assessmentReport = AssessmentReport(
            assessmentId: UUID().uuidString,
            completedAt: Date(),
            overallScore: 0.75, // Mock score
            skillBreakdown: [:],
            recommendations: [],
            timeSpent: 300,
            improvedAreas: [],
            areasForFocus: []
        )
    }
    

}

// MARK: - Supporting Types
struct AssessmentTrigger {
    let completedUnits: Int
    let lastAssessmentDate: Date?
    let shouldTrigger: Bool
}

extension SkillCategory {
    var color: Color {
        switch self {
        case .vocabulary: return .blue
        case .grammar: return .green
        case .listening: return .purple
        case .speaking: return .red
        case .reading: return .orange
        case .writing: return .pink
        case .culture: return .indigo
        case .pronunciation: return .teal
        }
    }
    
    var displayName: String {
        switch self {
        case .vocabulary: return "Vocabulary"
        case .grammar: return "Grammar"
        case .listening: return "Listening"
        case .speaking: return "Speaking"
        case .reading: return "Reading"
        case .writing: return "Writing"
        case .culture: return "Culture"
        case .pronunciation: return "Pronunciation"
        }
    }
}

#Preview {
    MicroAssessmentView(
        userId: "test-user-id",
        skillCategories: [.vocabulary, .grammar, .culture],
        assessmentTrigger: AssessmentTrigger(
            completedUnits: 4,
            lastAssessmentDate: nil,
            shouldTrigger: true
        )
    )
} 