import SwiftUI
import AVFoundation

// MARK: - Reading Detail View

struct ReadingDetailView: View {
    let content: ReadingContent
    @StateObject private var readingService = ReadingContentService.shared
    @StateObject private var audioService = ReadingAudioService.shared
    @StateObject private var statsService = ReadingStatisticsService.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var fontSize: CGFloat = 18
    private let showRomanization = true // Always show romanization
    @State private var showTranslation = false
    @State private var readingProgress: Double = 0.0
    @State private var currentPosition: Int = 0
    @State private var selectedVoice: String = "female"
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Reading controls bar
                readingControlsBar
                
                // Main reading content
                ScrollView {
                    VStack(alignment: .leading, spacing: 24) {
                        // Content header
                        contentHeader
                        
                        // Main text content
                        mainTextContent
                        
                        // Cultural context (if available)
                        if let context = content.culturalContext {
                            culturalContextSection(context)
                        }
                        
                        // Progress section
                        progressSection
                    }
                    .padding()
                }
                
                // Bottom action bar
                bottomActionBar
            }
            .background(
                LinearGradient(
                    colors: [Color.blue.opacity(0.02), Color.purple.opacity(0.02)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .navigationTitle("Reading Practice")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        markAsCompleted()
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Complete") {
                        markAsCompleted()
                        dismiss()
                    }
                    .foregroundColor(.green)
                }
            }
        }
        .onAppear {
            readingService.startReadingSession(for: content)
            loadUserPreferences()
            HapticFeedbackManager.shared.mediumImpact()
        }
    }
    
    // MARK: - Reading Controls Bar
    
    private var readingControlsBar: some View {
        HStack {
            // Font size controls
            HStack(spacing: 8) {
                Button {
                    if fontSize > 14 {
                        fontSize -= 2
                        savePreferences()
                    }
                } label: {
                    Image(systemName: "textformat.size.smaller")
                        .foregroundColor(.blue)
                }
                
                Text("\(Int(fontSize))pt")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Button {
                    if fontSize < 32 {
                        fontSize += 2
                        savePreferences()
                    }
                } label: {
                    Image(systemName: "textformat.size.larger")
                        .foregroundColor(.blue)
                }
            }
            
            Spacer()
            
            // Display toggles and voice selector
            HStack(spacing: 16) {


                Button {
                    showTranslation.toggle()
                    savePreferences()
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: showTranslation ? "eye.fill" : "eye.slash")
                        Text("English")
                    }
                    .font(.caption)
                    .foregroundColor(showTranslation ? .blue : .secondary)
                }

                // Voice selector
                Button {
                    selectedVoice = selectedVoice == "female" ? "male" : "female"
                    savePreferences()
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: selectedVoice == "female" ? "person.fill" : "person.2.fill")
                        Text(selectedVoice == "female" ? "♀" : "♂")
                    }
                    .font(.caption)
                    .foregroundColor(.purple)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 0)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 1, x: 0, y: 1)
        )
    }
    
    // MARK: - Content Header
    
    private var contentHeader: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(content.titleEnglish)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(content.titleTamil)
                        .font(.title3)
                        .foregroundColor(.secondary)
                    
                    if let romanization = content.titleRomanization {
                        Text(romanization)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }
                
                Spacer()
                
                VStack(spacing: 8) {
                    // Difficulty badge
                    Text(content.difficultyLevel.rawValue.uppercased())
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(content.difficultyColor)
                        )
                    
                    // Category
                    HStack(spacing: 4) {
                        Image(systemName: content.category.icon)
                            .font(.caption)
                        Text(content.category.displayName)
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                }
            }
            
            // Reading time and progress
            HStack {
                Text("Estimated time: \(content.readingTimeDisplay)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if readingProgress > 0 {
                    Text("Progress: \(Int(readingProgress))%")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [Color.white.opacity(0.2), Color.clear],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Main Text Content
    
    private var mainTextContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Tamil text
            Text(content.contentTamil)
                .font(.system(size: fontSize))
                .lineSpacing(8)
                .foregroundColor(.primary)
                .textSelection(.enabled)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.secondarySystemBackground))
                )
            
            // Romanization (if enabled and available)
            if showRomanization, let romanization = content.contentRomanization {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Romanization")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Image(systemName: "textformat.abc")
                            .foregroundColor(.blue)
                    }
                    
                    Text(romanization)
                        .font(.system(size: fontSize - 2))
                        .lineSpacing(6)
                        .foregroundColor(.secondary)
                        .textSelection(.enabled)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue.opacity(0.05))
                        )
                }
            }
            
            // Translation (if enabled and available)
            if showTranslation, let translation = content.contentTranslation {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Translation")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Image(systemName: "globe")
                            .foregroundColor(.green)
                    }
                    
                    Text(translation)
                        .font(.system(size: fontSize - 2))
                        .lineSpacing(6)
                        .foregroundColor(.secondary)
                        .textSelection(.enabled)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green.opacity(0.05))
                        )
                }
            }
        }
    }
    
    // MARK: - Cultural Context Section
    
    private func culturalContextSection(_ context: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Cultural Context")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Image(systemName: "info.circle")
                    .foregroundColor(.orange)
            }
            
            Text(context)
                .font(.body)
                .lineSpacing(4)
                .foregroundColor(.secondary)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.05))
                )
        }
    }
    
    // MARK: - Progress Section
    
    private var progressSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Reading Progress")
                .font(.headline)
                .fontWeight(.semibold)
            
            ProgressView(value: readingProgress / 100.0)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            
            HStack {
                Text("Progress: \(Int(readingProgress))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if let session = readingService.currentSession {
                    Text("Time: \(Int(session.elapsedTime))s")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Audio status display
            if audioService.isGenerating || !audioService.statusMessage.isEmpty {
                HStack {
                    if audioService.isGenerating {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                    Text(audioService.statusMessage)
                        .font(.caption)
                        .foregroundColor(.blue)

                    Spacer()
                }
            }

            // Audio error display
            if let error = audioService.playbackError {
                HStack {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.red)
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)

                    Spacer()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.tertiarySystemBackground))
        )
    }
    
    // MARK: - Bottom Action Bar
    
    private var bottomActionBar: some View {
        HStack {
            // Audio controls with real implementation
            Button {
                HapticFeedbackManager.shared.buttonTap()
                if audioService.isPlaying && audioService.currentlyPlaying == content.contentId {
                    audioService.stopAudio()
                } else {
                    Task {
                        await audioService.playReadingAudio(for: content)
                    }
                }
            } label: {
                HStack(spacing: 8) {
                    if audioService.isGenerating && audioService.currentlyPlaying == content.contentId {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: (audioService.isPlaying && audioService.currentlyPlaying == content.contentId) ? "pause.circle.fill" : "play.circle.fill")
                            .font(.title2)
                    }

                    Text((audioService.isPlaying && audioService.currentlyPlaying == content.contentId) ? "Pause" : "Play Audio")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.blue)
            }
            .disabled(audioService.isGenerating)
            
            Spacer()
            
            // Mark as read button
            Button {
                markAsCompleted()
            } label: {
                HStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                    Text("Mark as Read")
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color.green.gradient)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 0)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: -1)
        )
    }
    
    // MARK: - Helper Methods
    
    private func loadUserPreferences() {
        let preferences = readingService.readingPreferences
        fontSize = preferences.fontSize
        // showRomanization is always true, no need to load
        showTranslation = preferences.showTranslation
        selectedVoice = preferences.preferredVoice
    }
    
    private func savePreferences() {
        readingService.readingPreferences.fontSize = fontSize
        readingService.readingPreferences.showRomanization = true // Always true
        readingService.readingPreferences.showTranslation = showTranslation
        readingService.readingPreferences.preferredVoice = selectedVoice
        readingService.saveReadingPreferences()
    }
    
    private func markAsCompleted() {
        readingProgress = 100.0
        readingService.updateReadingProgress(position: content.contentTamil.count)

        // Haptic feedback for completion
        HapticFeedbackManager.shared.simulationComplete()

        Task {
            await readingService.completeReadingSession()
            // Update statistics after completion
            statsService.updateStatistics()
        }
    }
}

#Preview {
    ReadingDetailView(
        content: ReadingContent(
            id: UUID(),
            contentId: "preview_content",
            titleEnglish: "Sample Reading",
            titleTamil: "மாதிரி வாசிப்பு",
            titleRomanization: "Maathiri Vaasippu",
            category: .simpleReading,
            difficultyLevel: .a1,
            contentTamil: "வணக்கம்! நான் ராம். நீங்கள் எப்படி இருக்கிறீர்கள்?",
            contentRomanization: "Vanakkam! Naan Raam. Neengal eppadi irukkireergal?",
            contentTranslation: "Hello! I am Ram. How are you?",
            audioUrl: nil,
            estimatedReadingTime: 3,
            prerequisiteLessons: [],
            tags: ["greetings"],
            culturalContext: "வணக்கம் is the universal Tamil greeting.",
            source: .custom,
            sourceReference: nil,
            isActive: true,
            createdAt: Date(),
            updatedAt: Date()
        )
    )
}
