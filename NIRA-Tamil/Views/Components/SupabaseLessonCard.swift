//
//  SupabaseLessonCard.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

struct SupabaseLessonCard: View {
    let lesson: SupabaseLesson
    @State private var isPressed = false

    var body: some View {
        NavigationLink(destination: LessonDetailView(lesson: lesson)) {
            lessonCardContent
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private var lessonCardContent: some View {
        VStack(alignment: .leading, spacing: 16) {
                // Header with title and difficulty
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        if let description = lesson.description {
                            Text(description)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }
                    }

                    Spacer()

                    // Difficulty badge
                    Text(lesson.difficultyText)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(lesson.difficultyColor)
                        .cornerRadius(8)
                }

                // Content preview
                if !lesson.content.isEmpty {
                    Text(lesson.content)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .multilineTextAlignment(.leading)
                }

                // Audio player (if available)
                if lesson.hasAudio == true, let audioUrl = lesson.audioUrl {
                    LessonAudioPlayer(
                        audioUrl: audioUrl,
                        audioMetadata: lesson.audioMetadata?.value as? [String: Any]
                    )
                }

                // Metadata row
                HStack {
                    // Duration
                    Label(lesson.formattedDuration, systemImage: "clock")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    // Audio indicator
                    if lesson.hasAudio == true {
                        Label("Audio", systemImage: "speaker.wave.2")
                            .font(.caption)
                            .foregroundColor(.niraPrimary)
                    }

                    Spacer()

                    // Category
                    if !lesson.lessonType.isEmpty {
                        Text(lesson.lessonType.capitalized)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.niraPrimary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.niraPrimary.opacity(0.1))
                            .cornerRadius(6)
                    }
                }

                // Topics and objectives
                if !(lesson.vocabularyFocus?.isEmpty ?? true) || !(lesson.learningObjectives?.isEmpty ?? true) {
                    VStack(alignment: .leading, spacing: 8) {
                        if !(lesson.vocabularyFocus?.isEmpty ?? true) {
                            HStack {
                                Text("Topics:")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.secondary)

                                Text((lesson.vocabularyFocus ?? []).prefix(3).joined(separator: ", "))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                        }

                        if let learningObjectives = lesson.learningObjectives, !learningObjectives.isEmpty {
                            HStack {
                                Text("Goals:")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.secondary)

                                Text(learningObjectives.prefix(2).joined(separator: ", "))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                        }
                    }
                }

                // Vocabulary and exercises count
                HStack {
                    if !(lesson.vocabularyFocus?.isEmpty ?? true) {
                        Label("\(lesson.vocabularyFocus?.count ?? 0) topics", systemImage: "book")
                            .font(.caption)
                            .foregroundColor(.niraSecondary)
                    }

                    // TODO: Add exercises count when Supabase integration is complete
                    Label("Exercises", systemImage: "pencil")
                        .font(.caption)
                        .foregroundColor(.niraAccent)

                    Spacer()

                    // Premium Start button
                    HStack(spacing: 6) {
                        Image(systemName: "play.fill")
                            .font(.system(size: 10, weight: .semibold))
                        Text("Start")
                            .font(.system(size: 12, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            colors: [Color.niraPrimary, Color.niraPrimary.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: Color.niraPrimary.opacity(0.3), radius: 4, x: 0, y: 2)
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(
                        color: isPressed ? .niraPrimary.opacity(0.3) : .black.opacity(0.08),
                        radius: isPressed ? 12 : 6,
                        x: 0,
                        y: isPressed ? 6 : 3
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.3),
                                Color.clear,
                                Color.black.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .scaleEffect(isPressed ? 0.97 : 1.0)
            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isPressed)
    }
}

// MARK: - Content Statistics Item

struct ContentStatItem: View {
    let icon: String
    let count: Int
    let label: String
    let color: Color

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption2)
                .foregroundColor(color)

            Text("\(count)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(color)

            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    let sampleLesson = SupabaseLesson(
        id: UUID(),
        pathId: UUID(),
        title: "French Café Conversation",
        description: "Learn how to order coffee and pastries in a French café",
        lessonType: "conversation",
        difficultyLevel: 2,
        estimatedDuration: 25,
        sequenceOrder: 1,
        learningObjectives: ["Order coffee in French", "Use polite expressions", "Understand café vocabulary"],
        vocabularyFocus: ["café", "s'il vous plaît", "merci"],
        grammarConcepts: ["Politeness expressions"],
        culturalNotes: "French café culture emphasizes taking time to enjoy your drink",
        prerequisiteLessons: [],
        contentMetadata: SupabaseAnyCodable([
            "vocabulary": [
                [
                    "word": "café",
                    "translation": "coffee",
                    "partOfSpeech": "noun",
                    "context": "Je voudrais un café, s'il vous plaît",
                    "difficulty": "beginner",
                    "pronunciation": "ka-FEH"
                ]
            ],
            "exercises": [
                [
                    "type": "multiple_choice",
                    "question": "How do you say 'coffee' in French?",
                    "options": ["café", "thé", "eau", "lait"],
                    "correctAnswer": "café",
                    "explanation": "Café is the French word for coffee",
                    "points": 10,
                    "difficulty": 1
                ]
            ]
        ]),
        isActive: true,
        createdAt: Date(),
        updatedAt: Date(),
        audioUrl: nil,
        hasAudio: false,
        audioMetadata: nil
    )

    SupabaseLessonCard(lesson: sampleLesson)
        .padding()
}