//
//  ModernAgentCard.swift
//  NIRA
//
//  Created by NIRA Team on 1/2025.
//

import SwiftUI

struct ModernAgentCard: View {
    let agent: LearningAgent
    let onSelect: () -> Void
    @State private var isPressed = false
    @State private var showingDetails = false

    private var personalityColor: Color {
        switch agent.persona {
        case .beginnerEnthusiast: return .emojiGreen
        case .busyProfessional: return .niraThemeBlue
        case .traveler: return .emojiOrange
        case .culturalSeeker: return .niraThemeIndigo
        case .socialLearner: return .emojiYellow
        }
    }

    private var personalityGradient: [Color] {
        [personalityColor, personalityColor.opacity(0.7)]
    }

    private var compatibilityLevel: String {
        // This would be calculated based on user level vs agent specialization
        return "High"
    }

    private var compatibilityColor: Color {
        switch compatibilityLevel {
        case "High": return .niraThemeSuccess
        case "Medium": return .niraThemeWarning
        case "Low": return .red
        default: return .gray
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with avatar and basic info
            HStack(spacing: 12) {
                // Agent avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: personalityGradient,
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)

                    Text(agent.name.prefix(2).uppercased())
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primaryText)

                    Text(agent.persona.rawValue)
                        .font(.subheadline)
                        .foregroundColor(personalityColor)
                        .fontWeight(.medium)

                    // Compatibility indicator
                    HStack(spacing: 4) {
                        Circle()
                            .fill(compatibilityColor)
                            .frame(width: 8, height: 8)

                        Text("\(compatibilityLevel) Match")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }
                }

                Spacer()

                // More info button
                Button(action: { showingDetails.toggle() }) {
                    Image(systemName: "info.circle")
                        .font(.title3)
                        .foregroundColor(.secondaryText)
                }
            }

            // Language specializations
            VStack(alignment: .leading, spacing: 8) {
                Text("Languages")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondaryText)
                    .textCase(.uppercase)

                HStack(spacing: 8) {
                    Text(agent.language.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(getLanguageColor(agent.language.displayName))
                        .cornerRadius(6)

                    // Tier indicator
                    Text(agent.tier.displayName)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(agent.tier.gradient)
                        .cornerRadius(4)
                }
            }

            // Specialties
            VStack(alignment: .leading, spacing: 8) {
                Text("Specialties")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondaryText)
                    .textCase(.uppercase)

                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 6) {
                    ForEach(agent.specialties.prefix(4), id: \.self) { specialty in
                        HStack(spacing: 4) {
                            Image(systemName: getSpecialtyIcon(specialty))
                                .font(.caption2)
                                .foregroundColor(personalityColor)

                            Text(specialty)
                                .font(.caption2)
                                .foregroundColor(.secondaryText)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
            }

            // Voice sample and start button
            HStack(spacing: 12) {
                // Voice sample button
                Button(action: {
                    // Play voice sample
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.caption)

                        Text("Voice Sample")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(personalityColor)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(personalityColor.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())

                Spacer()

                // Start conversation button
                Button(action: onSelect) {
                    HStack(spacing: 6) {
                        Image(systemName: "message.fill")
                            .font(.caption)

                        Text("Chat")
                            .font(.caption)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            colors: personalityGradient,
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(
            color: personalityColor.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .sheet(isPresented: $showingDetails) {
            // AgentDetailView - replaced with new design in AgentsView
        }
    }

    // MARK: - Helper Functions

    private func getLanguageColor(_ language: String) -> Color {
        switch language.lowercased() {
        case "english": return .englishColor
        case "spanish": return .spanishColor
        case "french": return .frenchColor
        case "german": return .germanColor
        case "italian": return .italianColor
        case "portuguese": return .portugueseColor
        case "japanese": return .japaneseColor
        case "tamil": return .tamilColor
        default: return .niraThemeTeal
        }
    }

    private func getSpecialtyIcon(_ specialty: String) -> String {
        switch specialty.lowercased() {
        case "business": return "briefcase.fill"
        case "travel": return "airplane"
        case "academic": return "graduationcap.fill"
        case "casual": return "message.fill"
        case "formal": return "person.fill.badge.plus"
        case "slang": return "quote.bubble.fill"
        default: return "star.fill"
        }
    }
}

// MARK: - Agent Detail View (using existing AgentDetailView from AgentCard.swift)

// MARK: - Preview

#Preview {
    ModernAgentCard(
        agent: LearningAgent(
            persona: .traveler,
            language: .spanish,
            name: "Sofia",
            avatar: "✈️",
            description: "A warm and encouraging Spanish tutor from Barcelona specializing in travel scenarios",
            specialties: ["Travel Phrases", "Cultural Tips", "Navigation", "Local Customs"],
            systemPrompt: "You are a specialized travel companion for Spanish learning..."
        ),
        onSelect: {}
    )
    .padding()
}
