import SwiftUI

struct CompactThemeToggle: View {
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showingThemeSelector = false
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            showingThemeSelector = true
        }) {
            ZStack {
                // Subtle background circle
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(.systemGray6).opacity(0.6),
                                Color(.systemGray5).opacity(0.4)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 36, height: 36)
                    .shadow(
                        color: Color.black.opacity(isPressed ? 0.15 : 0.08),
                        radius: isPressed ? 2 : 4,
                        x: 0,
                        y: isPressed ? 1 : 2
                    )
                
                // Theme icon
                Image(systemName: themeManager.currentMode.icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                    .scaleEffect(isPressed ? 0.9 : 1.0)
            }
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .sheet(isPresented: $showingThemeSelector) {
            ModernThemeSelectorSheet()
                .environmentObject(themeManager)
        }
    }
}

#Preview {
    CompactThemeToggle()
        .environmentObject(ThemeManager())
} 