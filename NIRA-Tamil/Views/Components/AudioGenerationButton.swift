import SwiftUI
import AVFoundation

struct AudioGenerationButton: View {
    let text: String
    let onAudioGenerated: (URL) -> Void

    @State private var isGenerating = false
    @State private var synthesizer = AVSpeechSynthesizer()
    
    var body: some View {
        Button(action: {
            Task {
                await generateAudio()
            }
        }) {
            HStack(spacing: 8) {
                if isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Image(systemName: "waveform.badge.plus")
                        .font(.system(size: 16, weight: .medium))
                }
                
                Text(isGenerating ? "Generating..." : "Generate Audio")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isGenerating ? Color.gray : Color.blue)
            )
        }
        .disabled(isGenerating || text.isEmpty)
    }
    
    private func generateAudio() async {
        isGenerating = true

        // Use Google Text-to-Speech via AVSpeechSynthesizer
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "ta-IN") // Tamil (India)
        utterance.rate = 0.5 // Slower rate for learning
        utterance.pitchMultiplier = 1.0

        // For now, just play the audio directly
        // In a full implementation, you would record the output to a file
        await MainActor.run {
            synthesizer.speak(utterance)
            print("🔊 Playing Tamil audio with Google TTS: \(text)")
        }

        isGenerating = false
    }
}

#Preview {
    AudioGenerationButton(text: "வணக்கம்") { url in
        print("Generated audio: \(url)")
    }
}
