import SwiftUI

struct FSRSReviewCard: View {
    let card: FSRSCard
    let isAnswerVisible: Bool
    let onToggleAnswer: () -> Void
    let onRating: (ReviewRating) -> Void
    
    @State private var content: FSRSReviewContent?
    @State private var isLoading = true
    
    var body: some View {
        VStack(spacing: 20) {
            // Card content area
            cardContentView
            
            // Show/Hide answer button
            toggleAnswerButton
            
            // Rating buttons (only when answer is visible)
            if isAnswerVisible {
                ratingButtonsView
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .task {
            await loadContent()
        }
    }
    
    // MARK: - Card Content View
    private var cardContentView: some View {
        VStack(spacing: 16) {
            if isLoading {
                ProgressView("Loading content...")
                    .frame(height: 100)
            } else if let content = content {
                // Question side (always visible)
                VStack(spacing: 8) {
                    Text(content.question)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.primary)
                    
                    if let context = content.context {
                        Text(context)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
                
                // Answer side (conditionally visible)
                if isAnswerVisible {
                    Divider()
                        .padding(.vertical, 8)
                    
                    VStack(spacing: 8) {
                        Text(content.answer)
                            .font(.title3)
                            .fontWeight(.medium)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.primary)
                        
                        if let example = content.example {
                            Text("Example: \(example)")
                                .font(.body)
                                .italic()
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.top, 4)
                        }
                        
                        if let pronunciation = content.pronunciation {
                            HStack {
                                Image(systemName: "speaker.wave.2")
                                    .foregroundColor(.blue)
                                Text(pronunciation)
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                            .padding(.top, 4)
                        }
                    }
                }
            } else {
                Text("Content not available")
                    .foregroundColor(.secondary)
                    .frame(height: 100)
            }
        }
        .frame(minHeight: 120)
    }
    
    // MARK: - Toggle Answer Button
    private var toggleAnswerButton: some View {
        Button(action: onToggleAnswer) {
            HStack {
                Image(systemName: isAnswerVisible ? "eye.slash" : "eye")
                    .font(.headline)
                
                Text(isAnswerVisible ? "Hide Answer" : "Show Answer")
                    .font(.headline)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.blue)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isAnswerVisible ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isAnswerVisible)
    }
    
    // MARK: - Rating Buttons View
    private var ratingButtonsView: some View {
        VStack(spacing: 16) {
            Text("How well did you know this?")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack(spacing: 12) {
                ForEach(ReviewRating.allCases, id: \.self) { rating in
                    ratingButton(for: rating)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.secondarySystemBackground))
        )
        .transition(.opacity.combined(with: .slide))
    }
    
    private func ratingButton(for rating: ReviewRating) -> some View {
        let buttonColor = colorForRating(rating)
        
        return Button(action: {
            onRating(rating)
        }) {
            VStack(spacing: 6) {
                Text(emojiForRating(rating))
                    .font(.title2)
                
                Text(titleForRating(rating))
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .frame(width: 70, height: 60)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(buttonColor.opacity(0.15))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(buttonColor, lineWidth: 2)
                    )
            )
            .foregroundColor(buttonColor)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
    }
    
    // MARK: - Helper Functions
    private func emojiForRating(_ rating: ReviewRating) -> String {
        switch rating {
        case .again: return "😓"
        case .hard: return "😐"
        case .good: return "😊"
        case .easy: return "😄"
        }
    }
    
    private func titleForRating(_ rating: ReviewRating) -> String {
        switch rating {
        case .again: return "Again"
        case .hard: return "Hard"
        case .good: return "Good"
        case .easy: return "Easy"
        }
    }
    
    private func colorForRating(_ rating: ReviewRating) -> Color {
        switch rating {
        case .again: return .red
        case .hard: return .orange
        case .good: return .green
        case .easy: return .blue
        }
    }
    
    // MARK: - Data Loading
    private func loadContent() async {
        isLoading = true
        defer { isLoading = false }
        
        // Simulate loading delay
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // TODO: Load actual content from database using card.learningItemId
        // For now, using mock data based on content type
        content = generateMockContent(for: card)
    }
    
    private func generateMockContent(for card: FSRSCard) -> FSRSReviewContent {
        // Mock content generation based on card type or learning item ID
        let mockData = [
            FSRSReviewContent(
                id: card.learningItemId.uuidString,
                question: "வணக்கம்",
                answer: "Hello (formal greeting)",
                example: "வணக்கம், எப்படி இருக்கீங்க?",
                context: "Common greeting in Tamil",
                pronunciation: "van-ak-kam"
            ),
            FSRSReviewContent(
                id: card.learningItemId.uuidString,
                question: "நன்றி",
                answer: "Thank you",
                example: "உங்க உதவிக்கு நன்றி",
                context: "Expression of gratitude",
                pronunciation: "nan-ri"
            ),
            FSRSReviewContent(
                id: card.learningItemId.uuidString,
                question: "Complete: நான் _____ போகிறேன்",
                answer: "வீட்டுக்கு (to home)",
                example: "நான் வீட்டுக்கு போகிறேன் (I am going home)",
                context: "Grammar: Direction/Location",
                pronunciation: "veet-tuk-ku"
            )
        ]
        
        return mockData.randomElement() ?? mockData[0]
    }
}

// MARK: - Supporting Types
struct FSRSReviewContent {
    let id: String
    let question: String
    let answer: String
    let example: String?
    let context: String?
    let pronunciation: String?
}





#Preview {
    VStack {
        FSRSReviewCard(
            card: FSRSCard(
                id: UUID(),
                learningItemId: UUID(),
                due: Date().addingTimeInterval(24*60*60),
                stability: 5.0,
                difficulty: 3.0,
                elapsedDays: 1,
                scheduledDays: 1,
                reps: 3,
                lapses: 0,
                state: .review,
                lastReview: Date(),
                retrievability: 0.8
            ),
            isAnswerVisible: false,
            onToggleAnswer: {},
            onRating: { _ in }
        )
        .padding()
        
        Spacer()
    }
    .background(Color(.systemGroupedBackground))
} 