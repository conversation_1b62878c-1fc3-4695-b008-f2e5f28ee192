//
//  EnhancedAgentCard.swift
//  NIRA
//
//  Enhanced AI Agent Card with Modern UX and Adaptive Features
//  Based on Language Learning Content Development Standards v2.0
//

import SwiftUI

struct EnhancedAgentCard: View {
    let agent: EnhancedLearningAgent
    let biometricData: BiometricMetrics?
    let adaptiveMetrics: AdaptiveLearningMetrics?
    let socialMode: Bool
    let onTap: () -> Void
    
    @State private var isAnimating = false
    @State private var showingCulturalDetails = false
    @State private var pulseAnimation = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Main card content
            mainCardContent
            
            // Expandable cultural section
            if showingCulturalDetails {
                culturalDetailsSection
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.8).combined(with: .opacity),
                        removal: .scale(scale: 0.8).combined(with: .opacity)
                    ))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 20, style: .continuous)
                .fill(
                    LinearGradient(
                        colors: [
                            Color(.systemBackground),
                            agent.adaptiveLearningCapability > 8.5 ? 
                                Color.niraThemeIndigo.opacity(0.05) : Color(.systemGray6).opacity(0.3)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20, style: .continuous)
                        .stroke(
                            agent.biometricIntegration ? 
                                Color.red.opacity(0.3) : Color.clear,
                            lineWidth: pulseAnimation ? 2 : 0
                        )
                        .animation(.easeInOut(duration: 1.5).repeatForever(), value: pulseAnimation)
                )
        )
        .shadow(
            color: Color.black.opacity(0.1),
            radius: isAnimating ? 15 : 8,
            x: 0,
            y: isAnimating ? 8 : 4
        )
        .scaleEffect(isAnimating ? 1.02 : 1.0)
        .onTapGesture {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                isAnimating = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    isAnimating = false
                }
                onTap()
            }
        }
        .onAppear {
            if agent.biometricIntegration {
                pulseAnimation = true
            }
        }
    }
    
    // MARK: - Main Card Content
    
    private var mainCardContent: some View {
        VStack(spacing: 16) {
            // Header with avatar and real-time indicators
            headerSection
            
            // Agent info and specializations
            agentInfoSection
            
            // Capabilities and metrics
            capabilitiesSection
            
            // Action buttons
            actionButtonsSection
        }
        .padding(20)
    }
    
    private var headerSection: some View {
        HStack(spacing: 16) {
            // Enhanced avatar with status indicators
            ZStack {
                // Avatar background with adaptive color
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                agent.persona.color.opacity(0.8),
                                agent.persona.color.opacity(0.4)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 70, height: 70)
                
                // Avatar image or emoji
                if let avatarEmoji = agent.avatarEmoji {
                    Text(avatarEmoji)
                        .font(.system(size: 30))
                } else {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 35))
                        .foregroundColor(.white)
                }
                
                // Status indicators
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        
                        // Biometric integration indicator
                        if agent.biometricIntegration {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 12, height: 12)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 2)
                                )
                        }
                        
                        // Adaptive learning indicator
                        if agent.adaptiveLearningCapability > 8.5 {
                            Circle()
                                .fill(Color.niraThemeIndigo)
                                .frame(width: 12, height: 12)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 2)
                                )
                                .offset(x: agent.biometricIntegration ? -8 : 0)
                        }
                    }
                }
            }
            
            VStack(alignment: .leading, spacing: 4) {
                // Agent name with real-time status
                HStack(spacing: 8) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    // Real-time availability indicator
                    Circle()
                        .fill(agent.isAvailable ? Color.green : Color.gray)
                        .frame(width: 8, height: 8)
                }
                
                // Persona and expertise level
                HStack(spacing: 8) {
                    Text(agent.persona.rawValue)
                        .font(.subheadline)
                        .foregroundColor(agent.persona.color)
                        .fontWeight(.medium)
                    
                    Text("•")
                        .foregroundColor(.secondary)
                    
                    Text("Expert Level \(Int(agent.expertiseLevel))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Cultural specialization score
                if agent.culturalSpecialization.score > 7.0 {
                    HStack(spacing: 4) {
                        Image(systemName: "globe.americas.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                        
                        Text("Cultural Expert")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .fontWeight(.medium)
                    }
                }
            }
            
            Spacer()
            
            // Adaptive learning score
            VStack {
                Text("\(Int(agent.adaptiveLearningCapability * 10))")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.niraThemeIndigo)
                
                Text("AI Score")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var agentInfoSection: some View {
        VStack(spacing: 12) {
            // Specializations with visual indicators
            VStack(alignment: .leading, spacing: 8) {
                Text("Specializations")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                    ForEach(agent.specializations.prefix(4), id: \.self) { specialization in
                        HStack(spacing: 6) {
                            Image(systemName: getSpecializationIcon(specialization))
                                .font(.caption)
                                .foregroundColor(.niraThemeIndigo)
                            
                            Text(specialization)
                                .font(.caption)
                                .foregroundColor(.primary)
                                .lineLimit(1)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
            }
            
            // Cultural expertise areas
            if !agent.culturalExpertise.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Cultural Expertise")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Button(action: { 
                            withAnimation(.spring()) {
                                showingCulturalDetails.toggle()
                            }
                        }) {
                            Image(systemName: showingCulturalDetails ? "chevron.up" : "chevron.down")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(agent.culturalExpertise.prefix(3), id: \.self) { expertise in
                                Text(expertise)
                                    .font(.caption)
                                    .foregroundColor(.orange)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.orange.opacity(0.1))
                                    .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 1)
                    }
                }
            }
        }
    }
    
    private var capabilitiesSection: some View {
        VStack(spacing: 12) {
            // Learning capabilities with progress bars
            VStack(spacing: 8) {
                CapabilityRow(
                    title: "Adaptive Learning",
                    score: agent.adaptiveLearningCapability,
                    icon: "brain.head.profile",
                    color: .niraThemeIndigo
                )
                
                CapabilityRow(
                    title: "Cultural Context",
                    score: agent.culturalSpecialization.score / 10.0,
                    icon: "globe.americas",
                    color: .orange
                )
                
                if socialMode {
                    CapabilityRow(
                        title: "Social Learning",
                        score: Double(agent.socialLearningFeatures.count) / 5.0,
                        icon: "person.2",
                        color: .green
                    )
                }
            }
            
            // Real-time metrics (if available)
            if let adaptiveMetrics = adaptiveMetrics {
                realTimeMetricsView(metrics: adaptiveMetrics)
            }
        }
    }
    
    private var actionButtonsSection: some View {
        HStack(spacing: 12) {
            // Start conversation button
            Button(action: onTap) {
                HStack(spacing: 8) {
                    Image(systemName: "message.fill")
                        .font(.subheadline)
                    
                    Text("Start Learning")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    LinearGradient(
                        colors: [agent.persona.color, agent.persona.color.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(12)
            }
            
            Spacer()
            
            // Quick actions
            HStack(spacing: 8) {
                // Voice practice
                if agent.voicePracticeAvailable {
                    QuickActionIcon(
                        icon: "mic.fill",
                        color: .blue,
                        isActive: true
                    ) {
                        // Start voice practice
                    }
                }
                
                // Cultural insights
                if agent.culturalSpecialization.score > 7.0 {
                    QuickActionIcon(
                        icon: "globe.americas.fill",
                        color: .orange,
                        isActive: true
                    ) {
                        withAnimation(.spring()) {
                            showingCulturalDetails.toggle()
                        }
                    }
                }
                
                // Biometric integration
                if agent.biometricIntegration {
                    QuickActionIcon(
                        icon: "heart.fill",
                        color: .red,
                        isActive: biometricData != nil
                    ) {
                        // Toggle biometric monitoring
                    }
                }
            }
        }
    }
    
    // MARK: - Cultural Details Section
    
    private var culturalDetailsSection: some View {
        VStack(spacing: 12) {
            Divider()
                .padding(.horizontal)
            
            VStack(alignment: .leading, spacing: 12) {
                Text("Cultural Expertise Details")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: 8) {
                    ForEach(agent.culturalExpertise, id: \.self) { expertise in
                        HStack {
                            Image(systemName: getCulturalExpertiseIcon(expertise))
                                .font(.subheadline)
                                .foregroundColor(.orange)
                                .frame(width: 20)
                            
                            Text(expertise)
                                .font(.subheadline)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            // Expertise level indicator
                            HStack(spacing: 2) {
                                ForEach(0..<5) { index in
                                    Circle()
                                        .fill(index < 4 ? Color.orange : Color(.systemGray4))
                                        .frame(width: 6, height: 6)
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                
                // Cultural context examples
                Text("\"I help learners understand not just the language, but the cultural nuances that make communication truly effective.\"")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
        }
        .padding(.vertical, 16)
    }
    
    // MARK: - Supporting Views
    
    private func realTimeMetricsView(metrics: AdaptiveLearningMetrics) -> some View {
        VStack(spacing: 8) {
            Text("Real-Time Learning State")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
            
            HStack(spacing: 12) {
                // Cognitive load indicator
                VStack(spacing: 4) {
                    Circle()
                        .fill(metrics.cognitiveLoadLevel.color)
                        .frame(width: 12, height: 12)
                    
                    Text("Load")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                // Learning state
                VStack(spacing: 4) {
                    Text(metrics.learningState.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text("State")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                // Session effectiveness
                VStack(spacing: 4) {
                    Text("\(Int(metrics.sessionEffectiveness * 100))%")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.niraThemeIndigo)
                    
                    Text("Effective")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Helper Functions
    
    private func getSpecializationIcon(_ specialization: String) -> String {
        switch specialization.lowercased() {
        case let s where s.contains("grammar"):
            return "textformat"
        case let s where s.contains("conversation"):
            return "message"
        case let s where s.contains("pronunciation"):
            return "mic"
        case let s where s.contains("writing"):
            return "pencil"
        case let s where s.contains("reading"):
            return "book"
        case let s where s.contains("business"):
            return "briefcase"
        case let s where s.contains("travel"):
            return "airplane"
        default:
            return "star"
        }
    }
    
    private func getCulturalExpertiseIcon(_ expertise: String) -> String {
        switch expertise.lowercased() {
        case let s where s.contains("etiquette"):
            return "hand.raised"
        case let s where s.contains("tradition"):
            return "building.columns"
        case let s where s.contains("festival"):
            return "party.popper"
        case let s where s.contains("food"):
            return "fork.knife"
        case let s where s.contains("business"):
            return "building.2"
        case let s where s.contains("family"):
            return "house"
        default:
            return "globe.americas"
        }
    }
}

// MARK: - Supporting Components

struct CapabilityRow: View {
    let title: String
    let score: Double
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
            
            // Progress bar
            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 60, height: 6)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(color)
                    .frame(width: 60 * score, height: 6)
            }
            
            Text("\(Int(score * 100))%")
                .font(.caption2)
                .foregroundColor(.secondary)
                .frame(width: 30, alignment: .trailing)
        }
    }
}

struct QuickActionIcon: View {
    let icon: String
    let color: Color
    let isActive: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(isActive ? color : .gray)
                .frame(width: 32, height: 32)
                .background(Color(.systemGray6))
                .clipShape(Circle())
        }
    }
}

// MARK: - BiometricMetrics Type Definition

struct BiometricMetrics {
    let heartRate: Double
    let heartRateVariability: Double
    let stressLevel: Double
    let attentionLevel: Double
    let cognitiveLoad: BiometricLearningService.CognitiveLoadLevel
    let timestamp: Date
    
    init(heartRate: Double = 75.0, 
         heartRateVariability: Double = 50.0, 
         stressLevel: Double = 0.3, 
         attentionLevel: Double = 0.75, 
         cognitiveLoad: BiometricLearningService.CognitiveLoadLevel = .optimal) {
        self.heartRate = heartRate
        self.heartRateVariability = heartRateVariability
        self.stressLevel = stressLevel
        self.attentionLevel = attentionLevel
        self.cognitiveLoad = cognitiveLoad
        self.timestamp = Date()
    }
}

#Preview {
    EnhancedAgentCard(
        agent: EnhancedLearningAgent.sampleAgent,
        biometricData: nil,
        adaptiveMetrics: nil,
        socialMode: false
    ) {
        print("Agent tapped")
    }
    .padding()
} 