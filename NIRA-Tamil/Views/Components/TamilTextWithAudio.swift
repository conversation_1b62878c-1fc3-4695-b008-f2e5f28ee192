//
//  TamilTextWithAudio.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import SwiftUI

// MARK: - Tamil Text with Audio Component

struct TamilTextWithAudio: View {
    let tamilText: String
    let romanization: String?
    let contentId: UUID
    let contentType: ExploreContentType
    let voiceType: VoiceType
    let showRomanization: Bool
    let fontSize: Font
    let alignment: TextAlignment
    let maxLines: Int?
    
    @StateObject private var audioService = ExploreAudioService.shared
    @StateObject private var romanizationService = TamilRomanizationService.shared
    
    @State private var computedRomanization: String = ""
    @State private var isAudioLoading = false
    
    init(
        tamilText: String,
        romanization: String? = nil,
        contentId: UUID,
        contentType: ExploreContentType,
        voiceType: VoiceType = .female,
        showRomanization: Bool = true,
        fontSize: Font = .body,
        alignment: TextAlignment = .leading,
        maxLines: Int? = nil
    ) {
        self.tamilText = tamilText
        self.romanization = romanization
        self.contentId = contentId
        self.contentType = contentType
        self.voiceType = voiceType
        self.showRomanization = showRomanization
        self.fontSize = fontSize
        self.alignment = alignment
        self.maxLines = maxLines
    }
    
    var body: some View {
        VStack(alignment: alignmentGuide, spacing: 4) {
            // Tamil text with audio button
            HStack(alignment: .top, spacing: 8) {
                // Tamil text
                Text(tamilText)
                    .font(fontSize)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(alignment)
                    .lineLimit(maxLines)
                
                Spacer()
                
                // Audio button
                audioButton
            }
            
            // Romanization (always visible if enabled)
            if showRomanization && !computedRomanization.isEmpty {
                HStack {
                    Text(computedRomanization)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .italic()
                        .multilineTextAlignment(alignment)
                        .lineLimit(maxLines)
                    
                    Spacer()
                }
            }
        }
        .onAppear {
            setupRomanization()
        }
    }
    
    // MARK: - Audio Button
    
    private var audioButton: some View {
        Button(action: {
            playAudio()
        }) {
            ZStack {
                // Background circle with gradient
                Circle()
                    .fill(audioButtonGradient)
                    .frame(width: 32, height: 32)
                    .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
                
                // Audio icon or loading indicator
                if isAudioLoading {
                    ProgressView()
                        .scaleEffect(0.7)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else if audioService.currentlyPlaying?.contains(contentId.uuidString) == true {
                    Image(systemName: "pause.fill")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                } else {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isAudioLoading ? 0.9 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isAudioLoading)
    }
    
    private var audioButtonGradient: LinearGradient {
        if audioService.currentlyPlaying?.contains(contentId.uuidString) == true {
            return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        } else {
            return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
    
    // MARK: - Helper Properties
    
    private var alignmentGuide: HorizontalAlignment {
        switch alignment {
        case .leading: return .leading
        case .center: return .center
        case .trailing: return .trailing
        }
    }
    
    // MARK: - Methods
    
    private func setupRomanization() {
        if let providedRomanization = romanization, !providedRomanization.isEmpty {
            computedRomanization = providedRomanization
        } else {
            // Generate romanization on-demand
            computedRomanization = romanizationService.romanize(tamilText)
        }
    }
    
    private func playAudio() {
        if audioService.currentlyPlaying?.contains(contentId.uuidString) == true {
            // Stop if currently playing this audio
            audioService.stopAudio()
        } else {
            // Play audio
            isAudioLoading = true
            
            Task {
                await audioService.playAudio(
                    contentId: contentId,
                    contentType: contentType,
                    voiceType: voiceType
                )
                
                await MainActor.run {
                    isAudioLoading = false
                }
            }
        }
    }
}

// MARK: - Compact Tamil Text with Audio

struct CompactTamilTextWithAudio: View {
    let tamilText: String
    let romanization: String?
    let contentId: UUID
    let contentType: ExploreContentType
    let voiceType: VoiceType
    
    @StateObject private var audioService = ExploreAudioService.shared
    @StateObject private var romanizationService = TamilRomanizationService.shared
    
    @State private var computedRomanization: String = ""
    @State private var showRomanization = false
    
    init(
        tamilText: String,
        romanization: String? = nil,
        contentId: UUID,
        contentType: ExploreContentType,
        voiceType: VoiceType = .female
    ) {
        self.tamilText = tamilText
        self.romanization = romanization
        self.contentId = contentId
        self.contentType = contentType
        self.voiceType = voiceType
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            // Tamil text with controls
            HStack(spacing: 8) {
                Text(tamilText)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Spacer()
                
                // Romanization toggle
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        showRomanization.toggle()
                    }
                }) {
                    Image(systemName: showRomanization ? "textformat.abc" : "textformat.abc.dottedunderline")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(width: 20, height: 20)
                }
                
                // Audio button
                Button(action: {
                    playAudio()
                }) {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.caption)
                        .foregroundColor(.white)
                        .frame(width: 20, height: 20)
                        .background(
                            Circle()
                                .fill(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                        )
                }
            }
            
            // Romanization (collapsible)
            if showRomanization && !computedRomanization.isEmpty {
                Text(computedRomanization)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
                    .lineLimit(1)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .onAppear {
            setupRomanization()
        }
    }
    
    private func setupRomanization() {
        if let providedRomanization = romanization, !providedRomanization.isEmpty {
            computedRomanization = providedRomanization
        } else {
            computedRomanization = romanizationService.romanize(tamilText)
        }
    }
    
    private func playAudio() {
        Task {
            await audioService.playAudio(
                contentId: contentId,
                contentType: contentType,
                voiceType: voiceType
            )
        }
    }
}

// MARK: - Audio Control Panel

struct AudioControlPanel: View {
    @StateObject private var audioService = ExploreAudioService.shared
    @StateObject private var romanizationService = TamilRomanizationService.shared
    
    @State private var selectedVoiceType: VoiceType = .female
    @State private var showingGenerationProgress = false
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                Text("Audio & Romanization")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                // Voice type selector
                Picker("Voice", selection: $selectedVoiceType) {
                    ForEach(VoiceType.allCases, id: \.self) { voice in
                        Text(voice.displayName)
                            .tag(voice)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 200)
            }
            
            // Generation controls
            VStack(spacing: 12) {
                // Generate all audio button
                Button(action: {
                    generateAllAudio()
                }) {
                    HStack {
                        Image(systemName: "speaker.wave.3.fill")
                            .font(.title3)
                        
                        Text("Generate All Audio")
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                .disabled(audioService.isGenerating)
                
                // Update romanization button
                Button(action: {
                    updateRomanization()
                }) {
                    HStack {
                        Image(systemName: "textformat.abc")
                            .font(.title3)
                        
                        Text("Update Romanization")
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        LinearGradient(colors: [.green, .mint], startPoint: .leading, endPoint: .trailing)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                .disabled(romanizationService.isProcessing)
            }
            
            // Progress indicators
            if audioService.isGenerating || romanizationService.isProcessing {
                VStack(spacing: 8) {
                    ProgressView(value: audioService.isGenerating ? audioService.generationProgress : romanizationService.processingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    
                    Text(audioService.isGenerating ? audioService.statusMessage : romanizationService.statusMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
    }
    
    private func generateAllAudio() {
        Task {
            await audioService.generateAllExploreAudio()
        }
    }
    
    private func updateRomanization() {
        Task {
            await romanizationService.updateAllExploreContentRomanization()
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        TamilTextWithAudio(
            tamilText: "வணக்கம்",
            romanization: "Vanakkam",
            contentId: UUID(),
            contentType: .literature,
            voiceType: .female
        )
        
        CompactTamilTextWithAudio(
            tamilText: "நன்றி",
            romanization: "Nandri",
            contentId: UUID(),
            contentType: .festival
        )
        
        AudioControlPanel()
    }
    .padding()
}
