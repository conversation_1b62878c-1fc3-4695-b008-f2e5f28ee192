import SwiftUI

struct PracticeDetailModal: View {
    let practiceExercises: [TamilSupabasePracticeExercise]
    @Binding var selectedIndex: Int
    let level: CEFRLevel
    @Environment(\.dismiss) private var dismiss
    @State private var showingInteractivePractice = false
    
    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
    
    private var levelGradient: LinearGradient {
        LinearGradient(
            colors: [levelColor, levelColor.opacity(0.7)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background with glassmorphism
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        levelColor.opacity(0.05)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Floating geometric patterns
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(levelColor.opacity(0.03))
                        .frame(width: CGFloat.random(in: 100...200))
                        .position(
                            x: CGFloat.random(in: 50...350),
                            y: CGFloat.random(in: 100...600)
                        )
                        .animation(
                            .easeInOut(duration: Double.random(in: 4...8))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.5),
                            value: selectedIndex
                        )
                }
                
                VStack(spacing: 0) {
                    // Header
                    VStack(spacing: 16) {
                        HStack {
                            Button("Close") {
                                dismiss()
                            }
                            .foregroundColor(levelColor)
                            .fontWeight(.medium)

                            Spacer()

                            Text("Practice")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Spacer()

                            Text("\(selectedIndex + 1)/\(practiceExercises.count)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }

                        // Start Practice Session Button
                        Button {
                            showingInteractivePractice = true
                        } label: {
                            HStack(spacing: 12) {
                                Image(systemName: "play.circle.fill")
                                    .font(.title3)

                                Text("Start Practice Session")
                                    .font(.headline)
                                    .fontWeight(.semibold)

                                Spacer()

                                Text("All \(practiceExercises.count) exercises")
                                    .font(.caption)
                                    .opacity(0.8)

                                Image(systemName: "arrow.right.circle.fill")
                                    .font(.title3)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(levelGradient)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 16)
                                            .stroke(
                                                LinearGradient(
                                                    colors: [Color.white.opacity(0.4), Color.white.opacity(0.1)],
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                ),
                                                lineWidth: 1
                                            )
                                    )
                            )
                            .shadow(color: levelColor.opacity(0.3), radius: 8, x: 0, y: 4)
                        }
                        .padding(.horizontal, 4)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                    
                    // Content with TabView for swipe navigation
                    TabView(selection: $selectedIndex) {
                        ForEach(Array(practiceExercises.enumerated()), id: \.offset) { index, practice in
                            PracticeDetailCard(
                                practice: practice,
                                level: level,
                                levelColor: levelColor,
                                levelGradient: levelGradient
                            )
                            .tag(index)
                        }
                    }
                    .tabViewStyle(.page(indexDisplayMode: .never))
                    .animation(.easeInOut(duration: 0.3), value: selectedIndex)
                    
                    // Progress indicator
                    HStack(spacing: 8) {
                        ForEach(0..<practiceExercises.count, id: \.self) { index in
                            Circle()
                                .fill(index == selectedIndex ? levelColor : Color.secondary.opacity(0.3))
                                .frame(width: 8, height: 8)
                                .scaleEffect(index == selectedIndex ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.2), value: selectedIndex)
                        }
                    }
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingInteractivePractice) {
            InteractivePracticeView(
                exercises: practiceExercises,
                level: level
            )
        }
    }
}

struct PracticeDetailCard: View {
    let practice: TamilSupabasePracticeExercise
    let level: CEFRLevel
    let levelColor: Color
    let levelGradient: LinearGradient
    @State private var showingInstructions = false
    @State private var showingPreview = false

    private var exerciseTypeColor: Color {
        switch practice.exerciseType.lowercased() {
        case "multiple_choice": return .blue
        case "true_false": return .green
        case "fill_blank": return .orange
        case "match_following": return .purple
        case "audio_comprehension": return .cyan
        default: return .gray
        }
    }

    private var exerciseTypeIcon: String {
        switch practice.exerciseType.lowercased() {
        case "multiple_choice": return "list.bullet.circle"
        case "true_false": return "checkmark.circle"
        case "fill_blank": return "text.cursor"
        case "match_following": return "arrow.triangle.2.circlepath"
        case "audio_comprehension": return "speaker.wave.2"
        default: return "questionmark.circle"
        }
    }

    private var exerciseTypeDisplayName: String {
        switch practice.exerciseType.lowercased() {
        case "multiple_choice": return "Multiple Choice"
        case "true_false": return "True/False"
        case "fill_blank": return "Fill in the Blank"
        case "match_following": return "Matching"
        case "audio_comprehension": return "Audio Comprehension"
        default: return practice.exerciseType.capitalized
        }
    }

    private var exercisePreviewItems: [String] {
        switch practice.exerciseType.lowercased() {
        case "multiple_choice":
            return ["Basic Tamil greetings", "Vocabulary recognition", "Cultural context"]
        case "true_false":
            return ["Statement accuracy", "Grammar understanding", "Cultural appropriateness"]
        case "fill_blank":
            return ["Sentence completion", "Vocabulary usage", "Grammar application"]
        case "match_following":
            return ["Word associations", "Translation pairs", "Context matching"]
        case "audio_comprehension":
            return ["Listening skills", "Pronunciation recognition", "Audio understanding"]
        default:
            return ["Basic Tamil greetings", "Appropriate responses", "Cultural context"]
        }
    }

    private var exerciseFormatDescription: String {
        switch practice.exerciseType.lowercased() {
        case "multiple_choice":
            return "Select the correct answer from multiple options"
        case "true_false":
            return "Determine if statements are true or false"
        case "fill_blank":
            return "Complete sentences by filling in missing words"
        case "match_following":
            return "Match related items or concepts together"
        case "audio_comprehension":
            return "Listen to audio and answer comprehension questions"
        default:
            return "Follow the exercise instructions to complete tasks"
        }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Enhanced main exercise card with gradient glass design
                VStack(spacing: 20) {
                    // Exercise type badge with enhanced design
                    HStack(spacing: 8) {
                        Image(systemName: exerciseTypeIcon)
                            .font(.title3)
                            .foregroundColor(.white)

                        Text(exerciseTypeDisplayName)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                LinearGradient(
                                    colors: [exerciseTypeColor, exerciseTypeColor.opacity(0.8)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
                    .shadow(color: exerciseTypeColor.opacity(0.3), radius: 8, x: 0, y: 4)
                    
                    // Enhanced exercise titles - English only for practice exercises
                    VStack(spacing: 12) {
                        Text(practice.titleEnglish)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)

                        // Exercise ID for reference
                        Text(practice.exerciseId)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                            .multilineTextAlignment(.center)
                    }
                    
                    // Exercise metadata
                    HStack(spacing: 16) {
                        VStack(spacing: 4) {
                            Text("Difficulty")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            HStack(spacing: 2) {
                                ForEach(1...5, id: \.self) { level in
                                    Circle()
                                        .fill(level <= practice.difficultyLevel ? levelColor : Color.secondary.opacity(0.3))
                                        .frame(width: 6, height: 6)
                                }
                            }
                        }
                        
                        Divider()
                            .frame(height: 30)
                        
                        VStack(spacing: 4) {
                            Text("Points")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text("\(practice.pointsValue)")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.orange)
                        }
                        
                        if let timeLimit = practice.timeLimitSeconds {
                            Divider()
                                .frame(height: 30)
                            
                            VStack(spacing: 4) {
                                Text("Time Limit")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                Text("\(timeLimit)s")
                                    .font(.headline)
                                    .fontWeight(.bold)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }
                .padding(24)
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 24)
                                .stroke(
                                    LinearGradient(
                                        colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                )
                .shadow(color: levelColor.opacity(0.1), radius: 12, x: 0, y: 6)
                
                // Instructions section
                VStack(alignment: .leading, spacing: 16) {
                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingInstructions.toggle()
                        }
                    } label: {
                        HStack {
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(levelColor)
                            
                            Text("Instructions")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            Image(systemName: showingInstructions ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                .foregroundColor(levelColor)
                                .font(.title3)
                        }
                    }
                    
                    if showingInstructions {
                        VStack(alignment: .leading, spacing: 12) {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("English:")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.secondary)
                                
                                Text(practice.instructionsEnglish)
                                    .font(.body)
                                    .foregroundColor(.primary)
                            }
                            
                            Divider()
                            
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Tamil:")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.secondary)
                                
                                Text(practice.instructionsTamil)
                                    .font(.body)
                                    .fontWeight(.medium)
                                    .foregroundColor(levelColor)
                            }
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(levelColor.opacity(0.2), lineWidth: 1)
                        )
                )
                .shadow(color: levelColor.opacity(0.05), radius: 8, x: 0, y: 4)
                
                // Exercise preview section
                VStack(alignment: .leading, spacing: 16) {
                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingPreview.toggle()
                        }
                    } label: {
                        HStack {
                            Image(systemName: "eye.fill")
                                .foregroundColor(exerciseTypeColor)
                            
                            Text("Exercise Preview")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            Image(systemName: showingPreview ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                .foregroundColor(exerciseTypeColor)
                                .font(.title3)
                        }
                    }
                    
                    if showingPreview {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("This exercise will test your understanding of:")
                                .font(.callout)
                                .foregroundColor(.secondary)

                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(exercisePreviewItems, id: \.self) { item in
                                    HStack {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                        Text(item)
                                            .font(.callout)
                                            .foregroundColor(.primary)
                                    }
                                }
                            }

                            // Exercise format preview
                            if !exerciseFormatDescription.isEmpty {
                                Divider()

                                VStack(alignment: .leading, spacing: 6) {
                                    Text("Format:")
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.secondary)

                                    Text(exerciseFormatDescription)
                                        .font(.callout)
                                        .foregroundColor(.primary)
                                        .italic()
                                }
                            }
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(exerciseTypeColor.opacity(0.2), lineWidth: 1)
                        )
                )
                .shadow(color: exerciseTypeColor.opacity(0.05), radius: 8, x: 0, y: 4)
                
                // Exercise preview only - no individual start button
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
    }
}
