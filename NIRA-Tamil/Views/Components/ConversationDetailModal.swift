import SwiftUI

struct ConversationDetailModal: View {
    let conversations: [TamilSupabaseConversation]
    @Binding var selectedIndex: Int
    let level: CEFRLevel
    @Environment(\.dismiss) private var dismiss
    @StateObject private var audioManager = AudioContentManager.shared
    
    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
    
    private var levelGradient: LinearGradient {
        LinearGradient(
            colors: [levelColor, levelColor.opacity(0.7)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background with glassmorphism
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        levelColor.opacity(0.05)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Floating geometric patterns
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(levelColor.opacity(0.03))
                        .frame(width: CGFloat.random(in: 100...200))
                        .position(
                            x: CGFloat.random(in: 50...350),
                            y: CGFloat.random(in: 100...600)
                        )
                        .animation(
                            .easeInOut(duration: Double.random(in: 4...8))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.5),
                            value: selectedIndex
                        )
                }
                
                VStack(spacing: 0) {
                    // Header
                    HStack {
                        Button("Close") {
                            dismiss()
                        }
                        .foregroundColor(levelColor)
                        .fontWeight(.medium)
                        
                        Spacer()
                        
                        Text("Conversations")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Text("\(selectedIndex + 1)/\(conversations.count)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                    
                    // Content with TabView for swipe navigation
                    TabView(selection: $selectedIndex) {
                        ForEach(Array(conversations.enumerated()), id: \.offset) { index, conversation in
                            ConversationDetailCard(
                                conversation: conversation,
                                level: level,
                                levelColor: levelColor,
                                levelGradient: levelGradient
                            )
                            .tag(index)
                        }
                    }
                    .tabViewStyle(.page(indexDisplayMode: .never))
                    .animation(.easeInOut(duration: 0.3), value: selectedIndex)
                    
                    // Progress indicator
                    HStack(spacing: 8) {
                        ForEach(0..<conversations.count, id: \.self) { index in
                            Circle()
                                .fill(index == selectedIndex ? levelColor : Color.secondary.opacity(0.3))
                                .frame(width: 8, height: 8)
                                .scaleEffect(index == selectedIndex ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.2), value: selectedIndex)
                        }
                    }
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                }
            }
        }
        .navigationBarHidden(true)
    }
}

struct ConversationDetailCard: View {
    let conversation: TamilSupabaseConversation
    let level: CEFRLevel
    let levelColor: Color
    let levelGradient: LinearGradient
    @StateObject private var audioManager = AudioContentManager.shared
    @State private var showingContext = false
    @State private var showingCultural = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                mainHeaderSection
                contextSection
                culturalSection
                learningObjectivesSection
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
    }

    private var mainHeaderSection: some View {
        VStack(spacing: 20) {
            conversationTitles
            metadataBadges
            audioButton
        }
        .padding(24)
        .background(glassmorphismBackground)
        .shadow(color: levelColor.opacity(0.1), radius: 12, x: 0, y: 6)
    }

    private var conversationTitles: some View {
        VStack(spacing: 12) {
            Text(conversation.titleEnglish)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)

            Text(conversation.titleTamil)
                .font(.title)
                .fontWeight(.medium)
                .foregroundColor(levelColor)
                .multilineTextAlignment(.center)
        }
    }

    private var metadataBadges: some View {
        HStack(spacing: 12) {
            if let participants = conversation.participants, !participants.isEmpty {
                Label(participants, systemImage: "person.2.fill")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(levelColor)
                    .cornerRadius(12)
            }

            if let formality = conversation.formalityLevel, !formality.isEmpty {
                Label(formality.capitalized, systemImage: "star.fill")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.orange)
                    .cornerRadius(12)
            }
        }
    }

    @ViewBuilder
    private var audioButton: some View {
        if let audioUrl = conversation.audioFullUrl {
            Button {
                if let url = URL(string: audioUrl) {
                    audioManager.playAudio(url: url, filename: "conversation")
                }
            } label: {
                HStack(spacing: 12) {
                    Image(systemName: "play.circle.fill")
                        .font(.title2)

                    Text("Play Full Conversation")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 16)
                .background(audioButtonBackground)
                .shadow(color: levelColor.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .scaleEffect(audioManager.isPlaying ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: audioManager.isPlaying)
        }
    }

    private var audioButtonBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(levelGradient)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.4), Color.white.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 2
                    )
            )
    }

    private var glassmorphismBackground: some View {
        RoundedRectangle(cornerRadius: 24)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 24)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }

    @ViewBuilder
    private var contextSection: some View {
        if let contextDescription = conversation.contextDescription, !contextDescription.isEmpty {
            VStack(alignment: .leading, spacing: 16) {
                contextHeader

                if showingContext {
                    Text(contextDescription)
                        .font(.body)
                        .foregroundColor(.primary)
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(20)
            .background(sectionBackground(levelColor))
            .shadow(color: levelColor.opacity(0.05), radius: 8, x: 0, y: 4)
        }
    }

    private var contextHeader: some View {
        Button {
            withAnimation(.easeInOut(duration: 0.3)) {
                showingContext.toggle()
            }
        } label: {
            HStack {
                Image(systemName: "info.circle.fill")
                    .foregroundColor(levelColor)

                Text("Context & Setting")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: showingContext ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                    .foregroundColor(levelColor)
                    .font(.title3)
            }
        }
    }

    @ViewBuilder
    private var culturalSection: some View {
        if let culturalSetting = conversation.culturalSetting, !culturalSetting.isEmpty {
            VStack(alignment: .leading, spacing: 16) {
                culturalHeader

                if showingCultural {
                    Text(culturalSetting)
                        .font(.body)
                        .foregroundColor(.primary)
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(20)
            .background(sectionBackground(.orange))
            .shadow(color: Color.orange.opacity(0.05), radius: 8, x: 0, y: 4)
        }
    }

    private var culturalHeader: some View {
        Button {
            withAnimation(.easeInOut(duration: 0.3)) {
                showingCultural.toggle()
            }
        } label: {
            HStack {
                Image(systemName: "globe.asia.australia.fill")
                    .foregroundColor(.orange)

                Text("Cultural Context")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: showingCultural ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                    .foregroundColor(.orange)
                    .font(.title3)
            }
        }
    }

    private var learningObjectivesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Learning Objectives")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 8) {
                objectiveItem("Practice common greetings and responses")
                objectiveItem("Learn appropriate formality levels")
                objectiveItem("Understand cultural context")
            }
        }
        .padding(20)
        .background(sectionBackground(.green))
        .shadow(color: Color.green.opacity(0.05), radius: 8, x: 0, y: 4)
    }

    private func objectiveItem(_ text: String) -> some View {
        HStack {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
            Text(text)
                .font(.callout)
                .foregroundColor(.primary)
        }
    }

    private func sectionBackground(_ color: Color) -> some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(color.opacity(0.2), lineWidth: 1)
            )
    }
}
