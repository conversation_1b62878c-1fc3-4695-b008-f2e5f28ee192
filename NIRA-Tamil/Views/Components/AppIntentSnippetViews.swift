// //
// //  AppIntentSnippetViews.swift
// //  NIRA
// //
// //  Created by NIRA Team on 1/22/25.
// //  Snippet Views for App Intents Integration
// //
// 
// import SwiftUI
// import AppIntents
// 
// // MARK: - Lesson Start Snippet View
// 
// struct LessonStartSnippetView: View {
//     let lessonType: LessonTypeEntity.LessonType
//     let difficulty: DifficultyEntity.DifficultyLevel
//     
//     var body: some View {
//         VStack(alignment: .leading, spacing: 12) {
//             HStack {
//                 Image(systemName: iconForLessonType(lessonType))
//                     .font(.title2)
//                     .foregroundColor(.blue)
//                 
//                 VStack(alignment: .leading, spacing: 4) {
//                     Text(lessonType.displayName)
//                         .font(.headline)
//                         .fontWeight(.semibold)
//                     
//                     Text("\(difficulty.displayName) Level")
//                         .font(.subheadline)
//                         .foregroundColor(.secondary)
//                 }
//                 
//                 Spacer()
//                 
//                 Image(systemName: "arrow.right.circle.fill")
//                     .font(.title2)
//                     .foregroundColor(.green)
//             }
//             
//             HStack(spacing: 16) {
//                 LessonStatChip(
//                     icon: "clock",
//                     value: estimatedDuration(for: lessonType),
//                     label: "Duration"
//                 )
//                 
//                 LessonStatChip(
//                     icon: "target",
//                     value: "\(estimatedWords(for: lessonType))",
//                     label: "Words"
//                 )
//                 
//                 LessonStatChip(
//                     icon: "star.fill",
//                     value: difficultyStars(for: difficulty),
//                     label: "Level"
//                 )
//             }
//         }
//         .padding()
//         .background(Color(.systemBackground))
//         .cornerRadius(12)
//         .shadow(radius: 2)
//     }
//     
//     private func iconForLessonType(_ type: LessonTypeEntity.LessonType) -> String {
//         switch type {
//         case .vocabulary: return "text.book.closed.fill"
//         case .grammar: return "textformat.abc"
//         case .conversation: return "bubble.left.and.bubble.right.fill"
//         case .pronunciation: return "mic.fill"
//         case .cultural: return "globe.asia.australia.fill"
//         }
//     }
//     
//     private func estimatedDuration(for type: LessonTypeEntity.LessonType) -> String {
//         switch type {
//         case .vocabulary: return "15 min"
//         case .grammar: return "20 min"
//         case .conversation: return "25 min"
//         case .pronunciation: return "10 min"
//         case .cultural: return "30 min"
//         }
//     }
//     
//     private func estimatedWords(for type: LessonTypeEntity.LessonType) -> Int {
//         switch type {
//         case .vocabulary: return 25
//         case .grammar: return 15
//         case .conversation: return 20
//         case .pronunciation: return 10
//         case .cultural: return 30
//         }
//     }
//     
//     private func difficultyStars(for difficulty: DifficultyEntity.DifficultyLevel) -> String {
//         switch difficulty {
//         case .beginner: return "⭐"
//         case .intermediate: return "⭐⭐"
//         case .advanced: return "⭐⭐⭐"
//         }
//     }
// }
// 
// // MARK: - Vocabulary Snippet View
// 
// struct VocabularySnippetView: View {
//     let category: VocabCategoryEntity.VocabCategory
//     
//     var body: some View {
//         VStack(alignment: .leading, spacing: 12) {
//             HStack {
//                 Image(systemName: iconForCategory(category))
//                     .font(.title2)
//                     .foregroundColor(.purple)
//                 
//                 VStack(alignment: .leading, spacing: 4) {
//                     Text(category.displayName)
//                         .font(.headline)
//                         .fontWeight(.semibold)
//                     
//                     Text("Vocabulary Review")
//                         .font(.subheadline)
//                         .foregroundColor(.secondary)
//                 }
//                 
//                 Spacer()
//                 
//                 Text("🇮🇳")
//                     .font(.title)
//             }
//             
//             // Sample vocabulary preview
//             VStack(alignment: .leading, spacing: 8) {
//                 Text("Today's Words:")
//                     .font(.caption)
//                     .fontWeight(.medium)
//                     .foregroundColor(.secondary)
//                 
//                 HStack(spacing: 12) {
//                     ForEach(sampleWords(for: category), id: \.self) { word in
//                         VocabWordChip(word: word)
//                     }
//                 }
//             }
//         }
//         .padding()
//         .background(Color(.systemBackground))
//         .cornerRadius(12)
//         .shadow(radius: 2)
//     }
//     
//     private func iconForCategory(_ category: VocabCategoryEntity.VocabCategory) -> String {
//         switch category {
//         case .daily: return "house.fill"
//         case .family: return "person.3.fill"
//         case .food: return "fork.knife"
//         case .travel: return "airplane"
//         case .business: return "briefcase.fill"
//         case .cultural: return "theatermasks.fill"
//         }
//     }
//     
//     private func sampleWords(for category: VocabCategoryEntity.VocabCategory) -> [String] {
//         switch category {
//         case .daily: return ["வணக்கம்", "நன்றி", "மன்னிக்கவும்"]
//         case .family: return ["அம்மா", "அப்பா", "அண்ணன்"]
//         case .food: return ["சாதம்", "சாம்பார்", "ரசம்"]
//         case .travel: return ["வண்டி", "ரயில்", "விமானம்"]
//         case .business: return ["வேலை", "அலுவலகம்", "கூட்டம்"]
//         case .cultural: return ["திருவிழா", "கோயில்", "நடனம்"]
//         }
//     }
// }
// 
// // MARK: - Progress Snippet View
// 
// struct ProgressSnippetView: View {
//     let progress: ProgressData
//     
//     var body: some View {
//         VStack(alignment: .leading, spacing: 16) {
//             HStack {
//                 Image(systemName: "chart.line.uptrend.xyaxis")
//                     .font(.title2)
//                     .foregroundColor(.green)
//                 
//                 VStack(alignment: .leading, spacing: 4) {
//                     Text("Learning Progress")
//                         .font(.headline)
//                         .fontWeight(.semibold)
//                     
//                     Text("Tamil Language")
//                         .font(.subheadline)
//                         .foregroundColor(.secondary)
//                 }
//                 
//                 Spacer()
//                 
//                 Text("\(Int(progress.progressPercentage))%")
//                     .font(.title2)
//                     .fontWeight(.bold)
//                     .foregroundColor(.green)
//             }
//             
//             // Progress bar
//             ProgressView(value: progress.progressPercentage, total: 100)
//                 .progressViewStyle(LinearProgressViewStyle(tint: .green))
//                 .scaleEffect(x: 1, y: 2, anchor: .center)
//             
//             // Stats grid
//             HStack(spacing: 16) {
//                 ProgressStatView(
//                     icon: "book.fill",
//                     value: "\(progress.completedLessons)/\(progress.totalLessons)",
//                     label: "Lessons"
//                 )
//                 
//                 ProgressStatView(
//                     icon: "flame.fill",
//                     value: "\(progress.currentStreak)",
//                     label: "Day Streak"
//                 )
//                 
//                 ProgressStatView(
//                     icon: "clock.fill",
//                     value: "\(progress.totalStudyTime/60)h",
//                     label: "Study Time"
//                 )
//             }
//         }
//         .padding()
//         .background(Color(.systemBackground))
//         .cornerRadius(12)
//         .shadow(radius: 2)
//     }
// }
// 
// // MARK: - Supporting Components
// 
// struct LessonStatChip: View {
//     let icon: String
//     let value: String
//     let label: String
//     
//     var body: some View {
//         VStack(spacing: 4) {
//             Image(systemName: icon)
//                 .font(.caption)
//                 .foregroundColor(.blue)
//             
//             Text(value)
//                 .font(.caption2)
//                 .fontWeight(.semibold)
//             
//             Text(label)
//                 .font(.caption2)
//                 .foregroundColor(.secondary)
//         }
//         .frame(maxWidth: .infinity)
//         .padding(.vertical, 8)
//         .background(Color(.systemGray6))
//         .cornerRadius(8)
//     }
// }
// 
// struct VocabWordChip: View {
//     let word: String
//     
//     var body: some View {
//         Text(word)
//             .font(.caption)
//             .fontWeight(.medium)
//             .padding(.horizontal, 8)
//             .padding(.vertical, 4)
//             .background(Color.purple.opacity(0.1))
//             .foregroundColor(.purple)
//             .cornerRadius(6)
//     }
// }
// 
// struct ProgressStatView: View {
//     let icon: String
//     let value: String
//     let label: String
//     
//     var body: some View {
//         VStack(spacing: 4) {
//             Image(systemName: icon)
//                 .font(.caption)
//                 .foregroundColor(.orange)
//             
//             Text(value)
//                 .font(.caption)
//                 .fontWeight(.semibold)
//             
//             Text(label)
//                 .font(.caption2)
//                 .foregroundColor(.secondary)
//         }
//         .frame(maxWidth: .infinity)
//     }
// }
// 
// // MARK: - Preview Providers
// 
// #Preview("Lesson Start Snippet") {
//     LessonStartSnippetView(
//         lessonType: .vocabulary,
//         difficulty: .beginner
//     )
//     .padding()
// }
// 
// #Preview("Vocabulary Snippet") {
//     VocabularySnippetView(category: .family)
//         .padding()
// }
// 
// #Preview("Progress Snippet") {
//     ProgressSnippetView(
//         progress: ProgressData(
//             completedLessons: 15,
//             totalLessons: 30,
//             averageScore: 87,
//             currentStreak: 7,
//             totalStudyTime: 1250
//         )
//     )
//     .padding()
// }
