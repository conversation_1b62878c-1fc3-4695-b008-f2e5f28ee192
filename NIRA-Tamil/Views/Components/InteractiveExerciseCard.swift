//
//  InteractiveExerciseCard.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 25/06/2025.
//

import SwiftUI
import AVFoundation
import AVFoundation

struct InteractiveExerciseCard: View {
    let exercise: TamilSupabasePracticeExercise
    let selectedAnswer: Int?
    let showResult: Bool
    let level: CEFRLevel
    let onAnswerSelected: (Int) -> Void
    let onSubmit: () -> Void

    // Audio support for Tamil pronunciation (basic learners need this!)
    @StateObject private var audioManager = AudioContentManager.shared
    
    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
    
    private var exerciseTypeColor: Color {
        switch exercise.exerciseType {
        case "multiple_choice": return .blue
        case "true_false": return .green
        case "fill_blank": return .orange
        case "match_following": return .purple
        default: return .gray
        }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Exercise header
                exerciseHeader
                
                // Question content
                if let questionData = exercise.questionData {
                    questionSection(questionData)

                    // Answer options
                    answerSection(questionData)

                    // Submit button (if not submitted yet)
                    if selectedAnswer != nil && !showResult {
                        submitButton
                    }

                    // Result feedback (if submitted)
                    if showResult {
                        resultSection(questionData)
                    }
                } else {
                    // Fallback for exercises without detailed question data
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.largeTitle)
                            .foregroundColor(.orange)

                        Text("Exercise content not available")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Text("Exercise ID: \(exercise.exerciseId)")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("Type: \(exercise.exerciseType)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial)
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
    }
    
    private var exerciseHeader: some View {
        VStack(spacing: 12) {
            // Exercise type badge
            HStack(spacing: 8) {
                Image(systemName: exerciseTypeIcon)
                    .font(.title3)
                    .foregroundColor(.white)
                
                Text(exerciseTypeDisplayName)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [exerciseTypeColor, exerciseTypeColor.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            
            // Exercise title - English only for practice exercises
            Text(exercise.titleEnglish)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .minimumScaleFactor(0.8)
            
            // Points and difficulty
            HStack(spacing: 16) {
                Label("\(exercise.pointsValue) pts", systemImage: "star.fill")
                    .foregroundColor(.orange)
                
                Label("Level \(exercise.difficultyLevel)", systemImage: "chart.bar.fill")
                    .foregroundColor(levelColor)
            }
            .font(.caption)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    private func questionSection(_ questionData: ExerciseQuestionData) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Question")
                .font(.headline)
                .foregroundColor(levelColor)

            // Only English question - no Tamil translation or audio for practice exercises
            Text(questionData.question)
                .font(.body)
                .foregroundColor(.primary)
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                )
        }
    }
    
    private func answerSection(_ questionData: ExerciseQuestionData) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Choose your answer")
                .font(.headline)
                .foregroundColor(levelColor)
            
            if exercise.exerciseType == "match_following" {
                // Matching exercise layout
                matchingAnswerLayout(questionData)
            } else {
                // Multiple choice, true/false, fill blank layout
                multipleChoiceLayout(questionData)
            }
        }
    }
    
    private func multipleChoiceLayout(_ questionData: ExerciseQuestionData) -> some View {
        VStack(spacing: 12) {
            ForEach(Array(questionData.options.enumerated()), id: \.offset) { index, option in
                Button {
                    if !showResult {
                        onAnswerSelected(index)
                    }
                } label: {
                    HStack {
                        HStack(spacing: 12) {
                            VStack(alignment: .leading, spacing: 4) {
                                // Tamil option (main text - basic learners need to see this!)
                                Text(option)
                                    .font(.body)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)

                                // Romanization (basic learners need this to read Tamil!)
                                if let romanization = questionData.optionsRomanization?[safe: index] {
                                    Text(romanization)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .italic()
                                }

                                // Pronunciation guide (basic learners need this!)
                                if let pronunciation = questionData.optionsPronunciation?[safe: index] {
                                    Text("[\(pronunciation)]")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }

                            // Audio button for Tamil pronunciation (basic learners need this!)
                            // Only show audio for Tamil text options, not English options
                            if index < questionData.optionsTamil.count &&
                               isTamilText(questionData.optionsTamil[index]) {
                                Button {
                                    playTamilOptionAudio(option: questionData.optionsTamil[index], index: index)
                                } label: {
                                    Image(systemName: "speaker.wave.2.fill")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                        .frame(width: 28, height: 28)
                                        .background(levelColor)
                                        .clipShape(Circle())
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        
                        Spacer()
                        
                        // Selection indicator
                        if selectedAnswer == index {
                            let _ = {
                                // 🐛 DEBUG: Log UI display logic
                                print("🎨 UI Display Debug - Option \(index):")
                                print("   Selected: \(selectedAnswer == index)")
                                print("   Show Result: \(showResult)")
                                print("   Is Correct Answer: \(index == questionData.correctAnswer)")
                                print("   Will Show: \(showResult ? (index == questionData.correctAnswer ? "✅ Green Check" : "❌ Red X") : "🔵 Blue Circle")")
                            }()

                            Image(systemName: showResult ?
                                (index == questionData.correctAnswer ? "checkmark.circle.fill" : "xmark.circle.fill") :
                                "circle.fill"
                            )
                            .foregroundColor(showResult ?
                                (index == questionData.correctAnswer ? .green : .red) :
                                levelColor
                            )
                        } else if showResult && index == questionData.correctAnswer {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(getOptionBackgroundColor(index: index, questionData: questionData))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(getOptionBorderColor(index: index, questionData: questionData), lineWidth: 2)
                            )
                    )
                }
                .disabled(showResult)
            }
        }
    }
    
    private func matchingAnswerLayout(_ questionData: ExerciseQuestionData) -> some View {
        VStack(spacing: 16) {
            if let matchPairs = questionData.matchPairs {
                ForEach(Array(matchPairs.enumerated()), id: \.offset) { index, pair in
                    HStack(spacing: 16) {
                        // Left side
                        Text(pair.left)
                            .font(.body)
                            .padding(12)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.ultraThinMaterial)
                            )
                        
                        Image(systemName: "arrow.right")
                            .foregroundColor(levelColor)
                        
                        // Right side
                        Text(pair.right)
                            .font(.body)
                            .padding(12)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.ultraThinMaterial)
                            )
                    }
                }
            }
        }
    }
    
    private var submitButton: some View {
        Button {
            onSubmit()
        } label: {
            HStack(spacing: 12) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.title2)
                
                Text("Submit Answer")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [levelColor, levelColor.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .shadow(color: levelColor.opacity(0.3), radius: 8, x: 0, y: 4)
        }
    }
    
    private func resultSection(_ questionData: ExerciseQuestionData) -> some View {
        // 🐛 DEBUG: Log result message logic
        let isCorrectResult: Bool
        if let selectedAnswer = selectedAnswer {
            isCorrectResult = selectedAnswer == questionData.correctAnswer
            print("📝 Result Message Debug:")
            print("   Selected Answer: \(selectedAnswer)")
            print("   Correct Answer: \(questionData.correctAnswer)")
            print("   Is Correct: \(isCorrectResult)")
            print("   Message: \(isCorrectResult ? "Correct!" : "Incorrect")")
        } else {
            isCorrectResult = false
            print("⚠️ CRITICAL ERROR: selectedAnswer is nil in result section!")
            print("   This should never happen if showResult is true")
            print("   Correct Answer: \(questionData.correctAnswer)")
            print("   Options: \(questionData.options)")
        }

        return VStack(alignment: .leading, spacing: 12) {
            // Result indicator
            HStack(spacing: 8) {
                Image(systemName: isCorrectResult ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(isCorrectResult ? .green : .red)

                Text(isCorrectResult ? "Correct!" : "Incorrect")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(isCorrectResult ? .green : .red)
            }
            
            // Explanation
            VStack(alignment: .leading, spacing: 8) {
                Text("Explanation")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(levelColor)
                
                Text(questionData.explanation)
                    .font(.body)
                    .foregroundColor(.primary)
                
                if !questionData.explanationTamil.isEmpty {
                    Text(questionData.explanationTamil)
                        .font(.body)
                        .foregroundColor(.secondary)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Helper Methods
    
    private var exerciseTypeIcon: String {
        switch exercise.exerciseType {
        case "multiple_choice": return "list.bullet.circle.fill"
        case "true_false": return "checkmark.circle.fill"
        case "fill_blank": return "text.cursor"
        case "match_following": return "arrow.left.arrow.right.circle.fill"
        default: return "questionmark.circle.fill"
        }
    }
    
    private var exerciseTypeDisplayName: String {
        switch exercise.exerciseType {
        case "multiple_choice": return "Multiple Choice"
        case "true_false": return "True or False"
        case "fill_blank": return "Fill in the Blank"
        case "match_following": return "Match the Following"
        default: return "Exercise"
        }
    }
    
    private func getOptionBackgroundColor(index: Int, questionData: ExerciseQuestionData) -> Color {
        if showResult {
            if index == questionData.correctAnswer {
                return .green.opacity(0.1)
            } else if selectedAnswer == index && index != questionData.correctAnswer {
                return .red.opacity(0.1)
            }
        } else if selectedAnswer == index {
            return levelColor.opacity(0.1)
        }
        return .clear
    }
    
    private func getOptionBorderColor(index: Int, questionData: ExerciseQuestionData) -> Color {
        if showResult {
            if index == questionData.correctAnswer {
                return .green
            } else if selectedAnswer == index && index != questionData.correctAnswer {
                return .red
            }
        } else if selectedAnswer == index {
            return levelColor
        }
        return Color.secondary.opacity(0.3)
    }

    // MARK: - Romanization Support (Audio removed from practice exercises)

    private func romanizeTamilText(_ tamilText: String) -> String {
        // Basic Tamil to Roman transliteration
        // This is a simplified version - in production, use a proper transliteration library
        let romanizationMap: [String: String] = [
            "வணக்கம்": "Vanakkam",
            "நல்ல": "Nalla",
            "காலை": "Kaalai",
            "மாலை": "Maalai",
            "இரவு": "Iravu",
            "நன்றி": "Nandri",
            "மன்னிக்கவும்": "Mannikkavum",
            "எப்படி": "Eppadi",
            "இருக்கிறீர்கள்": "Irukkireergal",
            "நான்": "Naan",
            "தமிழ்": "Tamil",
            "பேசுகிறேன்": "Pesugireen",
            "உண்மை": "Unmai",
            "பொய்": "Poy",
            "என்ன": "Enna",
            "எது": "Ethu",
            "எப்போது": "Eppothu",
            "எங்கே": "Enge",
            "யார்": "Yaar",
            "என்பது": "Enpathu",
            "சரியான": "Sariyaana",
            "தேர்ந்தெடு": "Thernthedu",
            "வாக்கியம்": "Vaakkiyam",
            "நிறைவு": "Niraivu",
            "செய்": "Sey"
        ]

        // Try to find exact matches first
        if let romanized = romanizationMap[tamilText] {
            return romanized
        }

        // For partial matches, try to romanize word by word
        let words = tamilText.components(separatedBy: " ")
        let romanizedWords = words.map { word in
            romanizationMap[word] ?? word
        }

        return romanizedWords.joined(separator: " ")
    }

    // MARK: - Audio Support for Tamil Pronunciation (Basic learners need this!)

    private func playTamilOptionAudio(option: String, index: Int) {
        // Use pre-generated Google TTS audio from Supabase following the implementation guide
        // Following A1_BASIC_GREETINGS_IMPLEMENTATION_GUIDE.md approach:
        // 1. Audio is pre-generated using Google Cloud TTS with approved voices
        // 2. Stored in Supabase Storage with public URLs
        // 3. Played using download-then-play approach (not real-time TTS)
        let tamilText = option
        print("🔊 Playing Tamil audio for option \(index): \(tamilText)")

        // Check if we have a pre-generated audio URL for this Tamil text
        if let audioURL = getAudioURLForTamilText(tamilText) {
            // Use AudioContentManager's playAudioWithFallback for pre-generated audio
            Task {
                await audioManager.playAudioWithFallback(
                    text: tamilText,
                    audioURL: audioURL,
                    context: "practice_exercise_option"
                )
            }
        } else {
            print("⚠️ No pre-generated audio found for Tamil text: \(tamilText)")
            print("💡 Audio should be generated using: python generate_audio.py --lesson-id <lesson-uuid>")
            print("💡 Following A1_BASIC_GREETINGS_IMPLEMENTATION_GUIDE.md process")
        }
    }

    /// Get pre-generated audio URL for Tamil text from practice exercise data
    private func getAudioURLForTamilText(_ tamilText: String) -> String? {
        // Check if the exercise question data has audio URLs for options
        guard let questionData = exercise.questionData,
              let optionsAudioUrls = questionData.optionsAudioUrls else {
            return nil
        }

        let tamilOptions = questionData.optionsTamil

        // Find the index of this Tamil text in the options
        if let optionIndex = tamilOptions.firstIndex(of: tamilText),
           optionIndex < optionsAudioUrls.count {
            return optionsAudioUrls[optionIndex]
        }

        return nil
    }

    // MARK: - Helper Functions

    /// Check if text contains Tamil characters (basic learners need audio for Tamil text only)
    private func isTamilText(_ text: String) -> Bool {
        // Tamil Unicode range: U+0B80 to U+0BFF
        let tamilRange = CharacterSet(charactersIn: "\u{0B80}"..."\u{0BFF}")
        return text.rangeOfCharacter(from: tamilRange) != nil
    }
}
