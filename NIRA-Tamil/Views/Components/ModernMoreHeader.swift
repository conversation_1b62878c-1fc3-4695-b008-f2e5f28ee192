import SwiftUI

struct ModernMoreHeader: View {
    let userName: String
    let currentStreak: Int
    @Environment(\.colorScheme) var colorScheme

    private var currentTimeGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Good morning"
        case 12..<17: return "Good afternoon"
        case 17..<22: return "Good evening"
        default: return "Good night"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Status bar background
            Rectangle()
                .fill(colorScheme == .dark ? Color.black : Color.white)
                .frame(height: 0)
                .ignoresSafeArea(edges: .top)

            // Main header content
            VStack(spacing: 16) {
                // Top row with greeting and theme toggle
                HStack(alignment: .center) {
                    // Greeting and streak section
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(currentTimeGreeting), \(userName)!")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)

                        HStack(spacing: 8) {
                            Image(systemName: "flame.fill")
                                .font(.caption)
                                .foregroundColor(.orange)

                            Text("\(currentStreak) day streak")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.orange)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // Compact Theme Toggle for Header
                    CompactThemeToggle()
                }
                .padding(.horizontal, 20)
            }
            .padding(.top, 12)
            .padding(.bottom, 16)
            .background(colorScheme == .dark ? Color.black : Color.white)
        }
    }
}



#Preview {
    ModernMoreHeader(
        userName: "Alex",
        currentStreak: 7
    )
    .background(Color.gray.opacity(0.1))
}
