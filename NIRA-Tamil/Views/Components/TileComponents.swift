import SwiftUI

// MARK: - Shared Tile Components

struct TileItem {
    let title: String
    let subtitle: String
    let color: Color
    let onTap: (() -> Void)?

    init(title: String, subtitle: String, color: Color, onTap: (() -> Void)? = nil) {
        self.title = title
        self.subtitle = subtitle
        self.color = color
        self.onTap = onTap
    }
}

enum TileViewMode {
    case carousel
    case list
}

struct CategoryTileSection: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let items: [TileItem]
    @State private var showingItems = false
    @State private var viewMode: TileViewMode = .carousel
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Category header with illustration
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text(title)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }

                Spacer()

                // View mode toggle
                if showingItems {
                    Button(action: {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            viewMode = viewMode == .carousel ? .list : .carousel
                        }
                    }) {
                        Image(systemName: viewMode == .carousel ? "square.grid.2x2" : "rectangle.stack")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(color)
                            .frame(width: 44, height: 44)
                            .background(color.opacity(0.1))
                            .cornerRadius(12)
                    }
                }

                // Colorful illustration circle
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [color, color.opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            
            // Tiles display (carousel or list)
            if showingItems {
                Group {
                    if viewMode == .carousel {
                        // Carousel view
                        VStack(spacing: 12) {
                            ScrollView(.horizontal, showsIndicators: false) {
                                LazyHStack(spacing: 16) {
                                    ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                                        TileView(item: item)
                                            .frame(width: 280) // Fixed width for carousel
                                    }
                                }
                                .padding(.horizontal, 20)
                            }
                            .scrollTargetBehavior(.viewAligned)

                            // Page indicator for carousel
                            if items.count > 1 {
                                HStack(spacing: 8) {
                                    ForEach(0..<min(items.count, 5), id: \.self) { index in
                                        Circle()
                                            .fill(color.opacity(index < 3 ? 0.8 : 0.3))
                                            .frame(width: 6, height: 6)
                                    }
                                    if items.count > 5 {
                                        Text("+\(items.count - 5)")
                                            .font(.caption2)
                                            .foregroundColor(color)
                                    }
                                }
                            }
                        }
                    } else {
                        // List view (grid)
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                            ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                                TileView(item: item)
                            }
                        }
                    }
                }
                .transition(.opacity.combined(with: .scale))
            }
            
            // Hide/Show toggle button
            Button(action: {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: showingItems ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                    Text(showingItems ? "Hide \(title.lowercased())" : "Show \(title.lowercased())")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.secondary)
            }
        }
        .onAppear {
            // Auto-show first category and set appropriate view mode
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingItems = true
                    // Use carousel for many items, list for few items
                    viewMode = items.count > 4 ? .carousel : .list
                }
            }
        }
    }
}

struct TileView: View {
    let item: TileItem
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(item.title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            if !item.subtitle.isEmpty {
                Text(item.subtitle)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.8))
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .frame(minHeight: 80) // Ensure consistent height
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [item.color, item.color.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
        .shadow(color: item.color.opacity(0.3), radius: 8, x: 0, y: 4)
        .onTapGesture {
            item.onTap?()
        }
    }
}

#Preview {
    VStack(spacing: 24) {
        CategoryTileSection(
            title: "Vocabulary Practice",
            subtitle: "Learn essential words and phrases",
            icon: "book.fill",
            color: .emojiGreen,
            items: [
                TileItem(title: "Hello", subtitle: "Basic greeting", color: .emojiGreen),
                TileItem(title: "Thank you", subtitle: "Expression of gratitude", color: .emojiGreen),
                TileItem(title: "Please", subtitle: "Polite request", color: .emojiGreen),
                TileItem(title: "Excuse me", subtitle: "Getting attention", color: .emojiGreen),
                TileItem(title: "Good morning", subtitle: "Morning greeting", color: .emojiGreen),
                TileItem(title: "How are you?", subtitle: "Common question", color: .emojiGreen),
                TileItem(title: "I'm fine", subtitle: "Standard response", color: .emojiGreen),
                TileItem(title: "See you later", subtitle: "Farewell phrase", color: .emojiGreen)
            ]
        )
    }
    .padding()
    .background(Color(.systemBackground))
}
