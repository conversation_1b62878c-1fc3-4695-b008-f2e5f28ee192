//
//  TamilVowelsLearningView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 05/07/2025.
//

import SwiftUI

/// Tamil Vowels Learning Module - Phase 1 of TN Curriculum
struct TamilVowelsLearningView: View {
    @ObservedObject var scriptService: TamilScriptService
    @State private var selectedVowel: TamilCharacter?
    @State private var showingWritingCanvas = false
    @State private var currentLevel = 1
    @State private var completedVowels: Set<UUID> = []
    @State private var showingStrokeOrder = false
    
    // Vowels organized by TN curriculum progression
    private var vowelsByLevel: [Int: [TamilCharacter]] {
        let vowels = scriptService.allCharacters.filter { $0.characterType == .vowel }
        return Dictionary(grouping: vowels) { character in
            // Group by learning progression
            switch character.learningOrder ?? 999 {
            case 3, 6: return 1      // அ, ஆ - First vowels introduced
            case 17, 18: return 2    // இ, ஈ - Second wave
            case 19, 20: return 3    // உ, ஊ - Third wave  
            case 21, 22: return 4    // எ, ஏ - Fourth wave
            case 23, 24, 25, 26: return 5  // ஐ, ஒ, ஓ, ஔ - Advanced vowels
            default: return 6        // Any remaining vowels
            }
        }
    }
    
    private var currentLevelVowels: [TamilCharacter] {
        return vowelsByLevel[currentLevel]?.sorted { 
            ($0.learningOrder ?? 999) < ($1.learningOrder ?? 999) 
        } ?? []
    }
    
    private var levelProgress: Double {
        let totalVowels = currentLevelVowels.count
        let completedCount = currentLevelVowels.filter { completedVowels.contains($0.id) }.count
        return totalVowels > 0 ? Double(completedCount) / Double(totalVowels) : 0
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Header
                headerSection
                
                // Level Progress
                levelProgressSection
                
                // Level Navigation
                levelNavigationSection
                
                // Vowels Grid
                vowelsGridSection
                
                // Practice Tips
                practiceTipsSection
            }
            .padding()
        }
        .navigationTitle("Tamil Vowels")
        .navigationBarTitleDisplayMode(.large)
        .sheet(isPresented: $showingWritingCanvas) {
            if let vowel = selectedVowel {
                TamilWritingCanvasView(
                    character: vowel,
                    scriptService: scriptService,
                    onComplete: { result in
                        handleWritingComplete(result)
                    }
                )
            }
        }
        .sheet(isPresented: $showingStrokeOrder) {
            if let vowel = selectedVowel {
                TamilStrokeOrderView(character: vowel, scriptService: scriptService)
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "textformat.abc")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                Text("உயிர் எழுத்துகள்")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Text("Vowels")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Text("Master the 12 Tamil vowels with proper stroke order and pronunciation")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private var levelProgressSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Level \(currentLevel) Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(Int(levelProgress * 100))%")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
            }
            
            ProgressView(value: levelProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            
            Text("\(currentLevelVowels.filter { completedVowels.contains($0.id) }.count) of \(currentLevelVowels.count) vowels mastered")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
    
    private var levelNavigationSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(1...5, id: \.self) { level in
                    Button(action: { currentLevel = level }) {
                        VStack(spacing: 4) {
                            Text("Level \(level)")
                                .font(.caption)
                                .fontWeight(.medium)
                            
                            Text(levelTitle(for: level))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(currentLevel == level ? Color.blue : Color(.tertiarySystemBackground))
                        )
                        .foregroundColor(currentLevel == level ? .white : .primary)
                    }
                }
            }
            .padding(.horizontal)
        }
    }
    
    private var vowelsGridSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            ForEach(currentLevelVowels, id: \.id) { vowel in
                VowelPracticeCard(
                    vowel: vowel,
                    isCompleted: completedVowels.contains(vowel.id),
                    onPractice: {
                        selectedVowel = vowel
                        showingWritingCanvas = true
                    },
                    onStrokeOrder: {
                        selectedVowel = vowel
                        showingStrokeOrder = true
                    }
                )
            }
        }
    }
    
    private var practiceTipsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Practice Tips")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                TipRow(icon: "hand.draw", text: "Follow the stroke order for proper character formation")
                TipRow(icon: "speaker.wave.2", text: "Practice pronunciation while writing")
                TipRow(icon: "repeat", text: "Repeat each vowel 5-10 times for muscle memory")
                TipRow(icon: "graduationcap", text: "Master each level before moving to the next")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
    
    private func levelTitle(for level: Int) -> String {
        switch level {
        case 1: return "Basic (அ, ஆ)"
        case 2: return "Short/Long I (இ, ஈ)"
        case 3: return "Short/Long U (உ, ஊ)"
        case 4: return "Short/Long E (எ, ஏ)"
        case 5: return "Advanced (ஐ, ஒ, ஓ, ஔ)"
        default: return "Extra"
        }
    }
    
    private func handleWritingComplete(_ result: VowelWritingResult) {
        if let vowel = selectedVowel, result.accuracy >= 70 {
            completedVowels.insert(vowel.id)
            
            // Check if level is complete
            if levelProgress >= 1.0 && currentLevel < 5 {
                // Auto-advance to next level after a delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    currentLevel += 1
                }
            }
        }
        showingWritingCanvas = false
    }
}

struct VowelPracticeCard: View {
    let vowel: TamilCharacter
    let isCompleted: Bool
    let onPractice: () -> Void
    let onStrokeOrder: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // Character Display
            VStack(spacing: 4) {
                Text(vowel.character)
                    .font(.system(size: 48, weight: .medium, design: .default))
                    .foregroundColor(.primary)
                
                Text(vowel.romanization)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                Text(vowel.characterNameTamil)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // Action Buttons
            HStack(spacing: 8) {
                Button("Stroke Order") {
                    onStrokeOrder()
                }
                .buttonStyle(.bordered)
                .font(.caption)
                
                Button("Practice") {
                    onPractice()
                }
                .buttonStyle(.borderedProminent)
                .font(.caption)
            }
            
            // Completion Status
            if isCompleted {
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    
                    Text("Mastered")
                        .font(.caption2)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isCompleted ? Color.green.opacity(0.3) : Color.clear, lineWidth: 2)
        )
    }
}

struct TipRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.blue)
                .frame(width: 16)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
}

// Placeholder views for missing components
struct TamilWritingCanvasView: View {
    let character: TamilCharacter
    let scriptService: TamilScriptService
    let onComplete: (VowelWritingResult) -> Void
    
    var body: some View {
        VStack {
            Text("Writing Canvas for \(character.character)")
                .font(.title)
            
            Text("PencilKit integration will be implemented here")
                .foregroundColor(.secondary)
            
            Button("Complete Practice") {
                onComplete(VowelWritingResult(character: character, accuracy: 85.0, timeSpent: 30))
            }
            .buttonStyle(.borderedProminent)
            .padding()
        }
        .padding()
    }
}

struct TamilStrokeOrderView: View {
    let character: TamilCharacter
    let scriptService: TamilScriptService
    
    var body: some View {
        VStack {
            Text("Stroke Order for \(character.character)")
                .font(.title)
            
            Text("Animated stroke order will be shown here")
                .foregroundColor(.secondary)
            
            Button("Close") {
                // Dismiss
            }
            .buttonStyle(.bordered)
            .padding()
        }
        .padding()
    }
}

struct VowelWritingResult {
    let character: TamilCharacter
    let accuracy: Double
    let timeSpent: TimeInterval
}

#Preview {
    NavigationView {
        TamilVowelsLearningView(scriptService: TamilScriptService())
    }
}
