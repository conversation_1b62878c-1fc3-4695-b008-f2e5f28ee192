//
//  PracticeResultsView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 25/06/2025.
//

import SwiftUI

struct PracticeResultsView: View {
    let results: [PracticeExerciseResult]
    let totalTime: TimeInterval
    let level: CEFRLevel
    let onDismiss: () -> Void
    
    private var correctCount: Int {
        results.filter { $0.isCorrect }.count
    }
    
    private var totalPoints: Int {
        results.reduce(0) { $0 + $1.points }
    }
    
    private var maxPoints: Int {
        results.reduce(0) { $0 + ($1.isCorrect ? $1.points : 0) } + 
        results.filter { !$0.isCorrect }.reduce(0) { $0 + $1.points }
    }
    
    private var accuracy: Double {
        guard !results.isEmpty else { return 0 }
        return Double(correctCount) / Double(results.count)
    }
    
    private var averageResponseTime: TimeInterval {
        guard !results.isEmpty else { return 0 }
        return results.reduce(0) { $0 + $1.responseTime } / Double(results.count)
    }
    
    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
    
    private var performanceGrade: String {
        switch accuracy {
        case 0.9...1.0: return "Excellent!"
        case 0.8..<0.9: return "Great!"
        case 0.7..<0.8: return "Good"
        case 0.6..<0.7: return "Fair"
        default: return "Keep Practicing"
        }
    }
    
    private var performanceColor: Color {
        switch accuracy {
        case 0.9...1.0: return .green
        case 0.8..<0.9: return .blue
        case 0.7..<0.8: return .orange
        case 0.6..<0.7: return .yellow
        default: return .red
        }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header
                resultsHeader
                
                // Performance summary
                performanceSummary
                
                // Detailed stats
                detailedStats
                
                // Exercise breakdown
                exerciseBreakdown
                
                // Action buttons
                actionButtons
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(
            LinearGradient(
                colors: [
                    Color(.systemBackground),
                    levelColor.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
        )
    }
    
    private var resultsHeader: some View {
        VStack(spacing: 16) {
            // Celebration icon
            Image(systemName: accuracy >= 0.8 ? "star.circle.fill" : "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(performanceColor)
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: 0.5).repeatCount(3, autoreverses: true), value: accuracy)
            
            // Performance grade
            Text(performanceGrade)
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(performanceColor)
            
            // Score summary
            VStack(spacing: 4) {
                Text("\(correctCount) out of \(results.count) correct")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("\(Int(accuracy * 100))% accuracy")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
        )
        .shadow(color: performanceColor.opacity(0.2), radius: 12, x: 0, y: 6)
    }
    
    private var performanceSummary: some View {
        HStack(spacing: 16) {
            // Points earned
            PracticeStatCard(
                title: "Points",
                value: "\(totalPoints)",
                subtitle: "earned",
                color: .orange,
                icon: "star.fill"
            )

            // Time taken
            PracticeStatCard(
                title: "Time",
                value: formatTime(totalTime),
                subtitle: "total",
                color: .blue,
                icon: "clock.fill"
            )

            // Average response
            PracticeStatCard(
                title: "Avg Response",
                value: formatTime(averageResponseTime),
                subtitle: "per question",
                color: .purple,
                icon: "timer"
            )
        }
    }
    
    private var detailedStats: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Performance Breakdown")
                .font(.headline)
                .foregroundColor(levelColor)
            
            VStack(spacing: 12) {
                // Accuracy bar
                ProgressBarView(
                    title: "Accuracy",
                    value: accuracy,
                    color: performanceColor,
                    displayValue: "\(Int(accuracy * 100))%"
                )
                
                // Exercise type breakdown
                let exerciseTypes = Dictionary(grouping: results, by: { $0.exerciseType })
                ForEach(Array(exerciseTypes.keys.sorted()), id: \.self) { type in
                    let typeResults = exerciseTypes[type] ?? []
                    let typeAccuracy = Double(typeResults.filter { $0.isCorrect }.count) / Double(typeResults.count)
                    
                    ProgressBarView(
                        title: formatExerciseType(type),
                        value: typeAccuracy,
                        color: getExerciseTypeColor(type),
                        displayValue: "\(typeResults.filter { $0.isCorrect }.count)/\(typeResults.count)"
                    )
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    private var exerciseBreakdown: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Exercise Details")
                .font(.headline)
                .foregroundColor(levelColor)
            
            LazyVStack(spacing: 8) {
                ForEach(Array(results.enumerated()), id: \.offset) { index, result in
                    ExerciseResultRow(result: result, index: index + 1)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Primary action - Continue learning
            Button {
                onDismiss()
            } label: {
                HStack(spacing: 12) {
                    Image(systemName: "arrow.right.circle.fill")
                        .font(.title2)
                    
                    Text("Continue Learning")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [levelColor, levelColor.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
                .shadow(color: levelColor.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            
            // Secondary action - Review mistakes (if any)
            if correctCount < results.count {
                Button {
                    // TODO: Implement review functionality
                } label: {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.clockwise")
                        Text("Review Mistakes")
                    }
                    .foregroundColor(levelColor)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(levelColor, lineWidth: 2)
                    )
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
    
    private func formatExerciseType(_ type: String) -> String {
        switch type {
        case "multiple_choice": return "Multiple Choice"
        case "true_false": return "True/False"
        case "fill_blank": return "Fill Blanks"
        case "match_following": return "Matching"
        default: return type.capitalized
        }
    }
    
    private func getExerciseTypeColor(_ type: String) -> Color {
        switch type {
        case "multiple_choice": return .blue
        case "true_false": return .green
        case "fill_blank": return .orange
        case "match_following": return .purple
        default: return .gray
        }
    }
}

// MARK: - Supporting Views

struct PracticeStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct ProgressBarView: View {
    let title: String
    let value: Double
    let color: Color
    let displayValue: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text(displayValue)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.secondary.opacity(0.2))
                        .frame(height: 8)
                    
                    RoundedRectangle(cornerRadius: 4)
                        .fill(color)
                        .frame(width: geometry.size.width * value, height: 8)
                        .animation(.easeInOut(duration: 0.8), value: value)
                }
            }
            .frame(height: 8)
        }
    }
}

struct ExerciseResultRow: View {
    let result: PracticeExerciseResult
    let index: Int
    
    var body: some View {
        HStack(spacing: 12) {
            // Exercise number
            Text("\(index)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(Color.secondary.opacity(0.1))
                )
            
            // Exercise info
            VStack(alignment: .leading, spacing: 2) {
                Text(formatExerciseType(result.exerciseType))
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                
                Text(result.question)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            // Result indicator
            HStack(spacing: 8) {
                Text(formatTime(result.responseTime))
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Image(systemName: result.isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(result.isCorrect ? .green : .red)
            }
        }
        .padding(.vertical, 8)
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        return "\(Int(time))s"
    }
    
    private func formatExerciseType(_ type: String) -> String {
        switch type {
        case "multiple_choice": return "MC"
        case "true_false": return "T/F"
        case "fill_blank": return "Fill"
        case "match_following": return "Match"
        default: return type.prefix(4).uppercased()
        }
    }
}
