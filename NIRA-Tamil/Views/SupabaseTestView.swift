//
//  SupabaseTestView.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import SwiftUI

struct SupabaseTestView: View {
    @StateObject private var testService = SupabaseTestService.shared
    @State private var showingResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "server.rack")
                        .font(.system(size: 50))
                        .foregroundColor(.niraPrimary)
                    
                    Text("Supabase Integration Test")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Test database connectivity and integration")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Connection Status
                HStack {
                    Circle()
                        .fill(testService.isConnected ? Color.green : Color.red)
                        .frame(width: 12, height: 12)
                    
                    Text(testService.isConnected ? "Connected" : "Disconnected")
                        .font(.headline)
                        .foregroundColor(testService.isConnected ? .green : .red)
                    
                    Spacer()
                }
                .padding(.horizontal)
                
                // Test Button
                Button(action: {
                    Task {
                        await testService.runIntegrationTests()
                        showingResults = true
                    }
                }) {
                    HStack {
                        if testService.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "play.circle.fill")
                        }
                        
                        Text(testService.isLoading ? "Running Tests..." : "Run Integration Tests")
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(testService.isLoading ? Color.gray : Color.niraPrimary)
                    )
                }
                .disabled(testService.isLoading)
                .padding(.horizontal)
                
                // Quick Tests
                VStack(alignment: .leading, spacing: 12) {
                    Text("Quick Tests")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        QuickTestButton(
                            title: "Database Connection",
                            icon: "network",
                            action: {
                                Task {
                                    await testService.testDatabaseConnection()
                                }
                            }
                        )
                        
                        QuickTestButton(
                            title: "Local Cache",
                            icon: "internaldrive",
                            action: {
                                Task {
                                    await testService.testLocalCaching()
                                }
                            }
                        )
                        
                        QuickTestButton(
                            title: "Fallback Data",
                            icon: "arrow.triangle.2.circlepath",
                            action: {
                                Task {
                                    await testService.testFallbackMechanism()
                                }
                            }
                        )
                        
                        QuickTestButton(
                            title: "Clear Results",
                            icon: "trash",
                            action: {
                                testService.clearResults()
                            }
                        )
                    }
                    .padding(.horizontal)
                }
                
                // Results Preview
                if !testService.testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Test Results")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        ScrollView {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(testService.testResults, id: \.self) { result in
                                    HStack {
                                        Text(result)
                                            .font(.caption)
                                            .foregroundColor(result.contains("✅") ? .green : result.contains("❌") ? .red : .orange)
                                        Spacer()
                                    }
                                }
                            }
                            .padding()
                        }
                        .frame(maxHeight: 150)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                        .padding(.horizontal)
                    }
                }
                
                Spacer()
            }
            .navigationTitle("Supabase Test")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingResults) {
            TestResultsDetailView(results: testService.testResults)
        }
    }
}

struct QuickTestButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.niraPrimary)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )
        }
    }
}

struct TestResultsDetailView: View {
    let results: [String]
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(results, id: \.self) { result in
                        HStack {
                            Text(result)
                                .font(.body)
                                .foregroundColor(result.contains("✅") ? .green : result.contains("❌") ? .red : .orange)
                            Spacer()
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
                .padding()
            }
            .navigationTitle("Test Results")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") { dismiss() })
        }
    }
}

#Preview {
    SupabaseTestView()
}
