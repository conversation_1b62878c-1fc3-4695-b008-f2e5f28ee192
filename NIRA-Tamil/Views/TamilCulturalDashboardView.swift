import SwiftUI

struct TamilCulturalDashboardView: View {
    // Removed selectedCulturalTab - Dashboard is now a single view
    @State private var isLoading = false
    @State private var scrollOffset: CGFloat = 0
    @Environment(\.colorScheme) var colorScheme

    // Service instances
    @StateObject private var thirukkuralService = ThirukkuralService.shared
    @StateObject private var tamilCalendarService = TamilCalendarService.shared
    @StateObject private var tamilNewsService = TamilNewsService.shared
    @StateObject private var culturalInsightsService = CulturalInsightsService.shared

    // Use app's standard color system for consistency
    private var primaryGradient: LinearGradient {
        LinearGradient(
            colors: [Color("NiraPrimary"), Color("NiraAccent")],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var secondaryGradient: LinearGradient {
        LinearGradient(
            colors: [Color.tamilColor, Color.niraThemeTeal],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var accentGradient: LinearGradient {
        LinearGradient(
            colors: [Color("NiraAccent"), Color.niraThemeBlue],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // Learning Progress Data (from original HomeView)
    @State private var currentStreak = 7
    @State private var goals = [
        Goal(id: "1", title: "Complete daily lesson", isCompleted: true),
        Goal(id: "2", title: "Practice speaking for 10 minutes", isCompleted: true),
        Goal(id: "3", title: "Review 20 vocabulary words", isCompleted: false),
        Goal(id: "4", title: "Learn 5 Tamil cultural facts", isCompleted: false)
    ]
    @State private var showingAddGoal = false
    @State private var newGoalText = ""

    // Removed old todayMetrics - using new TodayMetric model instead
    
    // Removed tabs - Dashboard is now a single comprehensive view

    // MARK: - TodayMetric Model

    struct TodayMetric: Identifiable {
        let id = UUID()
        let title: String
        let subtitle: String
        let value: String
        let icon: String
        let gradient: LinearGradient
    }

    // MARK: - Sample Data

    private var todayMetrics: [TodayMetric] {
        [
            TodayMetric(
                title: "Lessons",
                subtitle: "Completed today",
                value: "3",
                icon: "book.fill",
                gradient: LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
            ),
            TodayMetric(
                title: "Words",
                subtitle: "Learned today",
                value: "12",
                icon: "textformat.abc",
                gradient: LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
            ),
            TodayMetric(
                title: "Streak",
                subtitle: "Days in a row",
                value: "7",
                icon: "flame.fill",
                gradient: LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing)
            ),
            TodayMetric(
                title: "Practice",
                subtitle: "Minutes today",
                value: "25",
                icon: "timer.circle.fill",
                gradient: LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
            )
        ]
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Dark mode compatible background
                Color(.systemBackground)
                    .ignoresSafeArea()

                ScrollView {
                    LazyVStack(spacing: 24) {
                        // Modern Tamil Cultural Header
                        modernTamilCulturalHeader

                        // Comprehensive Dashboard Content
                        VStack(spacing: 24) {
                            // Learning Progress & Goals
                            modernDailyGoalsCard

                            // Today's Progress Metrics
                            modernTodayProgressSection

                            // Removed duplicate Today's Highlights - content covered in other sections

                            // Today's Thirukkural
                            modernThirukkuralPreviewCard

                            // Tamil Calendar & Events
                            modernTamilCalendarPreviewCard

                            // Latest Tamil News
                            modernTamilNewsPreviewCard

                            // Cultural Insights
                            modernCulturalInsightsPreviewCard

                            // Removed missing cards - will be implemented later
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 100)
                    }
                }
                .background(Color(.systemBackground))
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .task {
                await loadCulturalContent()
            }
            .refreshable {
                await refreshCulturalContent()
            }
            .sheet(isPresented: $showingAddGoal) {
                addGoalSheet
            }
        }
        .preferredColorScheme(nil) // Support system dark mode
    }
    
    // MARK: - Modern Tamil Cultural Header

    private var modernTamilCulturalHeader: some View {
        VStack(spacing: 20) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 8) {
                        Text("வணக்கம்!")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundStyle(primaryGradient)

                        Text("🙏")
                            .font(.title2)
                    }

                    Text("Tamil Cultural Dashboard")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .fontWeight(.medium)
                }

                Spacer()

                // Modern Tamil Date Display with glassmorphism
                VStack(alignment: .trailing, spacing: 4) {
                    Text("௧௫ சித்திரை")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(secondaryGradient)

                    Text("சித்திரை மாதம்")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray4), lineWidth: 1)
                        )
                )
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)

            // Modern Cultural Status Bar
            modernCulturalStatusBar
        }
        .background(
            Rectangle()
                .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                .overlay(
                    Rectangle()
                        .fill(primaryGradient.opacity(0.05))
                )
        )
    }
    
    private var modernCulturalStatusBar: some View {
        HStack(spacing: 20) {
            // Today's Festival/Event with modern styling
            HStack(spacing: 8) {
                Image(systemName: "star.circle.fill")
                    .foregroundStyle(accentGradient)
                    .font(.title3)

                VStack(alignment: .leading, spacing: 2) {
                    Text("Tamil New Year")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text("Today's Festival")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                    .overlay(
                        Capsule()
                            .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray4), lineWidth: 1)
                    )
            )

            Spacer()

            // Learning Streak with modern styling
            HStack(spacing: 8) {
                Image(systemName: "flame.fill")
                    .foregroundStyle(LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .font(.title3)

                VStack(alignment: .trailing, spacing: 2) {
                    Text("7 நாள்")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text("தொடர்ச்சி")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                    .overlay(
                        Capsule()
                            .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray4), lineWidth: 1)
                    )
            )
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
    }

    // MARK: - Modern Daily Goals Card

    private var modernDailyGoalsCard: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                HStack(spacing: 12) {
                    Image(systemName: "target")
                        .foregroundStyle(primaryGradient)
                        .font(.title2)
                        .frame(width: 32, height: 32)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Today's Goals")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        Text("Stay on track with your learning")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                Text("\(completedGoalsCount)/\(goals.count)")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundStyle(primaryGradient)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                            .overlay(
                                Capsule()
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                    )
            }

            // Modern Progress Bar
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Progress")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Spacer()

                    Text("\(Int(goalProgress))%")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundStyle(primaryGradient)
                }

                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemGray5))
                        .frame(height: 8)

                    RoundedRectangle(cornerRadius: 8)
                        .fill(primaryGradient)
                        .frame(width: max(0, CGFloat(goalProgress) / 100 * 300), height: 8)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8), value: goalProgress)
                }
            }

            // Goals List with modern styling
            VStack(spacing: 12) {
                ForEach(goals) { goal in
                    modernGoalRow(goal)
                }
            }

            // Modern Add Goal / Completion Status
            HStack {
                if completedGoalsCount == goals.count {
                    HStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundStyle(LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .font(.title3)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("All goals completed!")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)

                            Text("Great job today! 🎉")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing), lineWidth: 1)
                            )
                    )
                } else {
                    Button(action: { showingAddGoal = true }) {
                        HStack(spacing: 8) {
                            Image(systemName: "plus.circle.fill")
                                .foregroundStyle(secondaryGradient)
                                .font(.title3)

                            Text("Add New Goal")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray4), lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                Spacer()
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray4), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
    }

    // MARK: - Today's Progress Section (enhanced)

    private var modernTodayProgressSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundStyle(primaryGradient)
                    .font(.title2)
                    .frame(width: 32, height: 32)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Today's Progress")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Your learning journey today")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                NavigationLink(destination: LessonsView()) {
                    Image(systemName: "arrow.up.right")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                ForEach(todayMetrics) { metric in
                    modernMetricCard(metric)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray4), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
    }

    private func modernMetricCard(_ metric: TodayMetric) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: metric.icon)
                    .foregroundStyle(metric.gradient)
                    .font(.title3)

                Spacer()

                Text(metric.value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundStyle(metric.gradient)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(metric.title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(metric.subtitle)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray5), lineWidth: 1)
                )
        )
    }

    // MARK: - Removed Tab Navigation - Dashboard is now a single comprehensive view

    // MARK: - Thirukkural Preview Card

    private var modernThirukkuralPreviewCard: some View {
        VStack(alignment: .leading, spacing: 20) {
            thirukkuralCardHeader
            thirukkuralCardContent
        }
        .padding(24)
        .background(cardBackground)
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
    }

    private var thirukkuralCardHeader: some View {
        HStack {
            Image(systemName: "book.closed.fill")
                .foregroundStyle(LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing))
                .font(.title2)
                .frame(width: 32, height: 32)

            VStack(alignment: .leading, spacing: 4) {
                Text("Today's Thirukkural")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Ancient wisdom for modern life")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            NavigationLink(destination: ThirukkuralExploreView()) {
                Image(systemName: "arrow.up.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
    }

    private var thirukkuralCardContent: some View {
        Group {
            if let todayKural = thirukkuralService.todayKural {
                thirukkuralContent(todayKural)
            } else {
                thirukkuralLoadingContent
            }
        }
    }

    private func thirukkuralContent(_ kural: Thirukkural) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(kural.tamilText)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)

            Text(kural.englishTranslation)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)

            HStack {
                Text("Kural \(kural.number)")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundStyle(LinearGradient(colors: [.purple, .pink], startPoint: .leading, endPoint: .trailing))

                Spacer()

                Text(kural.category.displayName)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(16)
        .background(thirukkuralContentBackground)
    }

    private var thirukkuralLoadingContent: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Loading today's wisdom...")
                .font(.subheadline)
                .foregroundColor(.secondary)

            ProgressView()
                .scaleEffect(0.8)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
        )
    }

    private var thirukkuralContentBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing), lineWidth: 1)
            )
    }

    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 24)
            .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
            .overlay(
                RoundedRectangle(cornerRadius: 24)
                    .stroke(colorScheme == .dark ? Color.white.opacity(0.1) : Color(.systemGray4), lineWidth: 1)
            )
    }

    // MARK: - Tamil Calendar Preview Card

    private var modernTamilCalendarPreviewCard: some View {
        VStack(alignment: .leading, spacing: 20) {
            tamilCalendarHeader
            tamilCalendarContent
        }
        .padding(24)
        .background(cardBackground)
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
    }

    private var tamilCalendarHeader: some View {
        HStack {
            Image(systemName: "calendar")
                .foregroundStyle(LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing))
                .font(.title2)
                .frame(width: 32, height: 32)

            VStack(alignment: .leading, spacing: 4) {
                Text("Tamil Calendar")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Traditional Tamil calendar system")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            NavigationLink(destination: CalendarExploreView()) {
                Image(systemName: "arrow.up.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
    }

    private var tamilCalendarContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            tamilDateRow
            tamilEventSection
        }
    }

    private var tamilDateRow: some View {
        HStack {
            Text("Today:")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            Spacer()

            Text(tamilCalendarService.currentTamilDate)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundStyle(LinearGradient(colors: [.green, .mint], startPoint: .leading, endPoint: .trailing))
        }
    }

    private var tamilEventSection: some View {
        Group {
            if let todayEvent = tamilCalendarService.todayEvent {
                tamilEventCard(todayEvent, title: "Today's Festival", isToday: true)
            } else if let nextEvent = tamilCalendarService.upcomingEvents.first {
                tamilEventCard(nextEvent, title: "Next Festival", isToday: false)
            }
        }
    }

    private func tamilEventCard(_ event: TamilEvent, title: String, isToday: Bool) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            HStack {
                Text(event.tamilName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Spacer()

                Text(isToday ? event.name : "in \(daysUntil(event.date)) days")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(tamilEventBackground(isToday: isToday))
    }

    private func tamilEventBackground(isToday: Bool) -> some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        isToday ? AnyShapeStyle(LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)) : AnyShapeStyle(colorScheme == .dark ? Color.white.opacity(0.1) : Color.clear),
                        lineWidth: 1
                    )
            )
    }

    // MARK: - Tamil News Preview Card

    private var modernTamilNewsPreviewCard: some View {
        VStack(alignment: .leading, spacing: 20) {
            tamilNewsHeader
            tamilNewsContent
        }
        .padding(24)
        .background(cardBackground)
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
    }

    private var tamilNewsHeader: some View {
        HStack {
            Image(systemName: "newspaper.fill")
                .foregroundStyle(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                .font(.title2)
                .frame(width: 32, height: 32)

            VStack(alignment: .leading, spacing: 4) {
                Text("Tamil Nadu News")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Latest news from Tamil Nadu")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            NavigationLink(destination: NewsExploreView()) {
                Image(systemName: "arrow.up.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
    }

    private var tamilNewsContent: some View {
        VStack(spacing: 12) {
            if tamilNewsService.latestNews.isEmpty {
                tamilNewsLoadingView
            } else {
                tamilNewsArticlesList
            }
        }
    }

    private var tamilNewsLoadingView: some View {
        HStack {
            ProgressView()
                .scaleEffect(0.8)

            Text("Loading latest news...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 8)
    }

    private var tamilNewsArticlesList: some View {
        ForEach(tamilNewsService.latestNews.prefix(3), id: \.id) { article in
            tamilNewsArticleRow(article)
        }
    }

    private func tamilNewsArticleRow(_ article: TamilNewsArticle) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Circle()
                .fill(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                .frame(width: 8, height: 8)
                .padding(.top, 6)

            VStack(alignment: .leading, spacing: 4) {
                Text(article.title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(2)

                Text(tamilNewsService.formatPublishedDate(article.publishedDate))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }

    // MARK: - Cultural Insights Preview Card

    private var modernCulturalInsightsPreviewCard: some View {
        VStack(alignment: .leading, spacing: 20) {
            culturalInsightsHeader
            culturalInsightsContent
        }
        .padding(24)
        .background(cardBackground)
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
    }

    private var culturalInsightsHeader: some View {
        HStack {
            Image(systemName: "sparkles")
                .foregroundStyle(LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing))
                .font(.title2)
                .frame(width: 32, height: 32)

            VStack(alignment: .leading, spacing: 4) {
                Text("Cultural Insights")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Explore Tamil heritage & traditions")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            NavigationLink(destination: CultureExploreView()) {
                Image(systemName: "arrow.up.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
    }

    private var culturalInsightsContent: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(culturalInsightsService.todayInsight.title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .lineLimit(2)

            Text(culturalInsightsService.todayInsight.description)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(3)

            culturalInsightsFooter
        }
        .padding(16)
        .background(culturalInsightsBackground)
    }

    private var culturalInsightsFooter: some View {
        HStack {
            Text(culturalInsightsService.todayInsight.category.displayName)
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundStyle(LinearGradient(colors: [.purple, .pink], startPoint: .leading, endPoint: .trailing))

            Spacer()

            Text("Read More")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }

    private var culturalInsightsBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing), lineWidth: 1)
            )
    }

    // MARK: - Modern Overview Section

    // MARK: - Removed Overview Section - Content is now directly in main body
    
    // MARK: - Removed modernTodayHighlightsCard - duplicate content covered in individual sections
    
    // MARK: - Modern Quick Cultural Access Grid

    private var modernQuickCulturalAccessGrid: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Explore Tamil Culture")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Discover heritage, wisdom & traditions")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Navigate to Explore tab indicator
                HStack(spacing: 4) {
                    Text("View All")
                        .font(.caption)
                        .foregroundColor(.niraPrimary)
                    Image(systemName: "arrow.right")
                        .font(.caption)
                        .foregroundColor(.niraPrimary)
                }
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                exploreAccessCard(
                    title: "திருக்குறள்",
                    subtitle: "Daily Wisdom",
                    icon: "book.closed.fill",
                    gradient: LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
                )

                exploreAccessCard(
                    title: "தமிழ் நாள்காட்டி",
                    subtitle: "Tamil Calendar",
                    icon: "calendar",
                    gradient: LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
                )

                exploreAccessCard(
                    title: "தமிழ்நாடு செய்திகள்",
                    subtitle: "Tamil Nadu News",
                    icon: "newspaper.fill",
                    gradient: LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
                )

                exploreAccessCard(
                    title: "கலாச்சாரம்",
                    subtitle: "Culture & Arts",
                    icon: "theatermasks.fill",
                    gradient: LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
                )
            }
        }
    }
    
    private func exploreAccessCard(title: String, subtitle: String, icon: String, gradient: LinearGradient) -> some View {
        Button(action: {
            // Navigate to Explore tab - this would be handled by the main tab view
            // For now, just provide visual feedback
        }) {
            VStack(spacing: 16) {
                // Icon with gradient background
                Image(systemName: icon)
                    .foregroundColor(.white)
                    .font(.title)
                    .frame(width: 50, height: 50)
                    .background(gradient)
                    .clipShape(Circle())

                VStack(spacing: 6) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    Text(subtitle)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                }

                // Small indicator that this goes to Explore
                HStack(spacing: 4) {
                    Text("Explore")
                        .font(.caption2)
                        .fontWeight(.medium)
                    Image(systemName: "arrow.right")
                        .font(.caption2)
                }
                .foregroundColor(.niraPrimary)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 140)
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(colorScheme == .dark ? Color.black : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(gradient.opacity(0.3), lineWidth: 1)
                    )
            )
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Thirukkural Card

    private func thirukkuralCard(_ kural: Thirukkural) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Image(systemName: "book.closed.fill")
                    .foregroundColor(.purple)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    Text("Today's Thirukkural")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("Kural \(kural.number) - \(kural.chapterEnglish)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button(action: {
                    thirukkuralService.toggleFavorite(kural)
                }) {
                    Image(systemName: thirukkuralService.isFavorite(kural) ? "heart.fill" : "heart")
                        .foregroundColor(thirukkuralService.isFavorite(kural) ? .red : .secondary)
                }
            }

            // Tamil Text
            Text(kural.tamilText)
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
                .padding(.vertical, 8)

            // Transliteration
            Text(kural.transliteration)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .italic()

            // English Translation
            Text(kural.englishTranslation)
                .font(.body)
                .foregroundColor(.primary)
                .padding(.vertical, 4)

            // Meaning
            Text(kural.meaning)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.top, 4)

            // Modern Relevance
            if !kural.modernRelevance.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Modern Relevance")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)

                    Text(kural.modernRelevance)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .purple.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Placeholder Sections (simplified for compilation)

    private var thirukkuralSection: some View {
        VStack(spacing: 20) {
            if let todayKural = thirukkuralService.todayKural {
                thirukkuralCard(todayKural)
            } else if thirukkuralService.isLoading {
                ProgressView("Loading today's Thirukkural...")
                    .frame(maxWidth: .infinity, minHeight: 200)
            } else {
                VStack {
                    Text("Thirukkural")
                        .font(.title2)
                        .padding()
                    Text("Loading daily wisdom...")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            }
        }
        .cornerRadius(16)
    }
    
    private var tamilCalendarSection: some View {
        VStack(spacing: 20) {
            // Tamil Date Header
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "calendar")
                        .foregroundColor(.green)
                        .font(.title2)

                    Text("Tamil Calendar")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Spacer()

                    Text(tamilCalendarService.currentTamilDate)
                        .font(.subheadline)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                }

                // Today's Event
                if let todayEvent = tamilCalendarService.todayEvent {
                    tamilEventCard(todayEvent)
                } else {
                    Text("No special events today")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                }

                // Upcoming Events
                if !tamilCalendarService.upcomingEvents.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Upcoming Events")
                            .font(.subheadline)
                            .fontWeight(.semibold)

                        ForEach(tamilCalendarService.upcomingEvents.prefix(3), id: \.id) { event in
                            upcomingEventRow(event)
                        }
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .green.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
    }

    // MARK: - Tamil Calendar Helper Views

    private func tamilEventCard(_ event: TamilEvent) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(event.tamilName)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Text(event.type.rawValue.capitalized)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .foregroundColor(.green)
                    .cornerRadius(8)
            }

            Text(event.name)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Text(event.description)
                .font(.body)
                .foregroundColor(.primary)
                .lineLimit(3)

            if !event.traditions.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Traditions:")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)

                    Text(event.traditions.joined(separator: " • "))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.green.opacity(0.05))
        .cornerRadius(12)
    }

    private func upcomingEventRow(_ event: TamilEvent) -> some View {
        HStack(spacing: 12) {
            VStack {
                Text("\(Calendar.current.component(.day, from: event.date))")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.green)

                Text(DateFormatter().monthSymbols[Calendar.current.component(.month, from: event.date) - 1].prefix(3))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(width: 40)

            VStack(alignment: .leading, spacing: 2) {
                Text(event.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(event.tamilName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }

    private var tamilNewsSection: some View {
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "newspaper.fill")
                        .foregroundColor(.blue)
                        .font(.title2)

                    Text("Tamil Nadu News")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Spacer()

                    if tamilNewsService.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }

                if !tamilNewsService.latestNews.isEmpty {
                    ForEach(tamilNewsService.latestNews.prefix(3), id: \.id) { article in
                        newsArticleCard(article)
                    }
                } else {
                    Text("Loading latest Tamil Nadu news...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .blue.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
    }

    // MARK: - News Article Card

    private func newsArticleCard(_ article: TamilNewsArticle) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(article.category.displayName)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(8)

                Spacer()

                Text(tamilNewsService.formatPublishedDate(article.publishedDate))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Text(article.title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .lineLimit(2)

            if let tamilTitle = article.tamilTitle {
                Text(tamilTitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            Text(article.summary)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(3)

            HStack {
                Text(article.source)
                    .font(.caption)
                    .foregroundColor(.blue)

                Spacer()

                HStack(spacing: 4) {
                    Image(systemName: "clock")
                        .font(.caption2)
                    Text("\(article.readingTime) min read")
                        .font(.caption2)
                }
                .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(12)
    }

    private var culturalInsightsSection: some View {
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "sparkles")
                        .foregroundColor(.orange)
                        .font(.title2)

                    Text("Cultural Insights")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Spacer()

                    if culturalInsightsService.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }

                if !culturalInsightsService.isLoading {
                    culturalInsightCard(culturalInsightsService.todayInsight)

                    if !culturalInsightsService.featuredInsights.isEmpty {
                        culturalInsightCard(culturalInsightsService.featuredInsights.first!)
                    }
                } else {
                    Text("Loading cultural insights...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .orange.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
    }

    // MARK: - Cultural Insight Card

    private func culturalInsightCard(_ insight: TamilCulturalInsight) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: insight.category.icon)
                    .foregroundColor(.orange)
                    .font(.title3)

                VStack(alignment: .leading, spacing: 2) {
                    Text(insight.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(insight.category.displayName)
                        .font(.caption)
                        .foregroundColor(.orange)
                }

                Spacer()

                Text(insight.difficulty.displayName)
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(insight.difficulty.color.opacity(0.2))
                    .foregroundColor(insight.difficulty.color)
                    .cornerRadius(6)
            }

            Text(insight.content)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(4)

            if !insight.tags.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Tags:")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)

                    Text(insight.tags.prefix(3).joined(separator: " • "))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.orange.opacity(0.05))
        .cornerRadius(12)
    }

    private var learningIntegrationSection: some View {
        VStack {
            Text("Cultural Learning")
                .font(.title2)
                .padding()
            Text("Learn Tamil through cultural context and real-world applications")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .background(Color(.systemBackground))
        .cornerRadius(16)
    }
    
    private var culturalMapSection: some View {
        VStack {
            Text("Cultural Map")
                .font(.title2)
                .padding()
            Text("Explore Tamil Nadu's cultural sites and heritage locations")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .background(Color(.systemBackground))
        .cornerRadius(16)
    }
    
    private var culturalLearningProgressCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "graduationcap.fill")
                    .foregroundColor(.niraPrimary)
                    .font(.title2)
                
                Text("Cultural Learning Journey")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            // Progress indicators for different cultural aspects
            VStack(spacing: 12) {
                culturalProgressRow(title: "Tamil Literature", progress: 0.65, color: .purple)
                culturalProgressRow(title: "Cultural Traditions", progress: 0.45, color: .orange)
                culturalProgressRow(title: "Historical Knowledge", progress: 0.30, color: .blue)
                culturalProgressRow(title: "Festival Understanding", progress: 0.80, color: .green)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .niraPrimary.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    private func culturalProgressRow(title: String, progress: Double, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
                .scaleEffect(y: 1.5)
        }
    }

    // MARK: - Tamil Language Portfolio Card

    private var tamilLanguagePortfolioCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "person.crop.circle.badge.checkmark")
                    .foregroundColor(.niraPrimary)
                    .font(.title2)

                Text("Tamil Learning Portfolio")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            HStack(spacing: 20) {
                portfolioStat(value: "\(currentStreak)", label: "Day Streak", icon: "flame.fill", color: .emojiOrange)
                portfolioStat(value: "156", label: "Tamil Words\nLearned", icon: "textformat.abc", color: .emojiBlue)
                portfolioStat(value: "23", label: "Cultural\nInsights", icon: "sparkles", color: .emojiPurple)
                portfolioStat(value: "8", label: "Festivals\nExplored", icon: "party.popper.fill", color: .emojiGreen)
            }

            // Tamil Script Progress
            VStack(alignment: .leading, spacing: 12) {
                Text("Tamil Script Mastery")
                    .font(.subheadline)
                    .fontWeight(.medium)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                    scriptFeatureCard(title: "அ", subtitle: "Vowels", icon: "a.circle.fill", color: .emojiBlue)
                    scriptFeatureCard(title: "க", subtitle: "Consonants", icon: "k.circle.fill", color: .emojiPurple)
                    scriptFeatureCard(title: "கா", subtitle: "Combinations", icon: "textformat", color: .emojiOrange)
                    scriptFeatureCard(title: "௧", subtitle: "Numbers", icon: "number.circle.fill", color: .emojiGreen)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }

    // MARK: - Helper Methods (from original HomeView)

    private func modernGoalRow(_ goal: Goal) -> some View {
        HStack(spacing: 16) {
            Button(action: {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    toggleGoal(goal.id)
                }
            }) {
                Image(systemName: goal.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(goal.isCompleted ? .green : .secondary)
                    .font(.title3)
                    .scaleEffect(goal.isCompleted ? 1.1 : 1.0)
            }
            .buttonStyle(PlainButtonStyle())

            Text(goal.title)
                .font(.subheadline)
                .fontWeight(.medium)
                .strikethrough(goal.isCompleted)
                .foregroundColor(goal.isCompleted ? .secondary : .primary)
                .animation(.easeInOut(duration: 0.3), value: goal.isCompleted)

            Spacer()

            if goal.isCompleted {
                Image(systemName: "sparkles")
                    .foregroundStyle(accentGradient)
                    .font(.caption)
                    .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(goal.isCompleted ? Color.green.opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(goal.isCompleted ? Color.green.opacity(0.3) : Color.clear, lineWidth: 1)
                )
        )
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: goal.isCompleted)
    }

    // Removed old metricCard function - using modernMetricCard instead

    private func portfolioStat(value: String, label: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(color.opacity(0.1))
                .clipShape(Circle())

            Text(value)
                .font(.title2)
                .fontWeight(.bold)

            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
    }

    private func scriptFeatureCard(title: String, subtitle: String, icon: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
                .frame(width: 30, height: 30)

            Text(subtitle)
                .font(.caption)
                .fontWeight(.medium)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 10)
        .background(color.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }

    // MARK: - Helper Properties and Methods

    private var completedGoalsCount: Int {
        goals.filter { $0.isCompleted }.count
    }

    private var goalProgress: Double {
        guard !goals.isEmpty else { return 0 }
        return Double(completedGoalsCount) / Double(goals.count) * 100
    }

    private func toggleGoal(_ id: String) {
        if let index = goals.firstIndex(where: { $0.id == id }) {
            goals[index].isCompleted.toggle()
        }
    }

    private func addGoal() {
        let newGoal = Goal(
            id: UUID().uuidString,
            title: newGoalText,
            isCompleted: false
        )
        goals.append(newGoal)
        newGoalText = ""
        showingAddGoal = false
    }

    // MARK: - Add Goal Sheet

    private var addGoalSheet: some View {
        NavigationView {
            VStack(spacing: 20) {
                TextField("Enter new goal", text: $newGoalText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                Spacer()
            }
            .padding()
            .navigationTitle("Add Goal")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    showingAddGoal = false
                    newGoalText = ""
                },
                trailing: Button("Add") {
                    addGoal()
                }
                .disabled(newGoalText.isEmpty)
            )
        }
    }

    // MARK: - Helper Functions

    private func daysUntil(_ date: Date) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let eventDate = calendar.startOfDay(for: date)
        let components = calendar.dateComponents([.day], from: today, to: eventDate)
        return max(0, components.day ?? 0)
    }

    // MARK: - Removed duplicate and placeholder sections

    // MARK: - Data Loading

    private func loadCulturalContent() async {
        isLoading = true

        // Load data from all services
        await thirukkuralService.loadTodayKural()
        await tamilCalendarService.loadTodayEvents()
        await tamilNewsService.loadLatestNews()
        await culturalInsightsService.loadTodayInsight()

        isLoading = false
    }

    private func refreshCulturalContent() async {
        await loadCulturalContent()
    }
}

// MARK: - Supporting Extensions

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Supporting Data Models
// Note: Goal and LearningMetric are defined in HomeView.swift

// MARK: - Preview

struct TamilCulturalDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        TamilCulturalDashboardView()
    }
}
