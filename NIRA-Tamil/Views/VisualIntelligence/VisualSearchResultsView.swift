//
//  VisualSearchResultsView.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  Visual Intelligence Search Results Detail View
//

import SwiftUI

struct VisualSearchResultsView: View {
    let results: [VisualSearchResult]
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedTab = 0
    
    private var tamilScriptResults: [TamilScriptResult] {
        results.compactMap {
            if case .tamilScript(let result) = $0 { return result }
            return nil
        }
    }
    
    private var culturalResults: [CulturalContentResult] {
        results.compactMap {
            if case .cultural(let result) = $0 { return result }
            return nil
        }
    }
    
    private var learningMaterialResults: [LearningMaterialResult] {
        results.compactMap {
            if case .learningMaterial(let result) = $0 { return result }
            return nil
        }
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if results.isEmpty {
                    emptyStateView
                } else {
                    TabView(selection: $selectedTab) {
                        // All Results Tab
                        allResultsView
                            .tabItem {
                                Image(systemName: "list.bullet")
                                Text("All")
                            }
                            .tag(0)
                        
                        // Tamil Text Tab
                        if !tamilScriptResults.isEmpty {
                            tamilTextResultsView
                                .tabItem {
                                    Image(systemName: "text.viewfinder")
                                    Text("Tamil Text")
                                }
                                .tag(1)
                        }
                        
                        // Cultural Content Tab
                        if !culturalResults.isEmpty {
                            culturalContentResultsView
                                .tabItem {
                                    Image(systemName: "building.columns")
                                    Text("Culture")
                                }
                                .tag(2)
                        }
                        
                        // Learning Materials Tab
                        if !learningMaterialResults.isEmpty {
                            learningMaterialsResultsView
                                .tabItem {
                                    Image(systemName: "book")
                                    Text("Learning")
                                }
                                .tag(3)
                        }
                    }
                }
            }
            .navigationTitle("Search Results")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
    
    // MARK: - Empty State
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Results Found")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Try analyzing a different image or adjusting your search criteria.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - All Results View
    
    private var allResultsView: some View {
        List {
            ForEach(Array(results.enumerated()), id: \.offset) { index, result in
                VisualSearchResultRow(result: result)
                    .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
            }
        }
        .listStyle(PlainListStyle())
    }
    
    // MARK: - Tamil Text Results View
    
    private var tamilTextResultsView: some View {
        List {
            ForEach(Array(tamilScriptResults.enumerated()), id: \.offset) { index, result in
                TamilTextResultRow(result: result)
                    .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
            }
        }
        .listStyle(PlainListStyle())
    }
    
    // MARK: - Cultural Content Results View
    
    private var culturalContentResultsView: some View {
        List {
            ForEach(Array(culturalResults.enumerated()), id: \.offset) { index, result in
                CulturalContentResultRow(result: result)
                    .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
            }
        }
        .listStyle(PlainListStyle())
    }
    
    // MARK: - Learning Materials Results View
    
    private var learningMaterialsResultsView: some View {
        List {
            ForEach(Array(learningMaterialResults.enumerated()), id: \.offset) { index, result in
                LearningMaterialResultRow(result: result)
                    .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
            }
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - Result Row Components

struct VisualSearchResultRow: View {
    let result: VisualSearchResult
    
    var body: some View {
        HStack(spacing: 12) {
            resultIcon
            
            VStack(alignment: .leading, spacing: 4) {
                Text(resultTitle)
                    .font(.headline)
                    .lineLimit(2)
                
                Text(resultSubtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                HStack {
                    confidenceIndicator
                    Spacer()
                    actionButton
                }
            }
        }
        .padding(.vertical, 4)
    }
    
    private var resultIcon: some View {
        Group {
            switch result {
            case .tamilScript:
                Image(systemName: "text.viewfinder")
                    .foregroundColor(.blue)
            case .cultural:
                Image(systemName: "building.columns")
                    .foregroundColor(.orange)
            case .learningMaterial:
                Image(systemName: "book")
                    .foregroundColor(.green)
            }
        }
        .font(.title2)
        .frame(width: 40, height: 40)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    private var resultTitle: String {
        switch result {
        case .tamilScript(let tamilResult):
            return tamilResult.text
        case .cultural(let culturalResult):
            return culturalResult.name
        case .learningMaterial(let materialResult):
            return materialResult.topic
        }
    }
    
    private var resultSubtitle: String {
        switch result {
        case .tamilScript(let tamilResult):
            return tamilResult.translation
        case .cultural(let culturalResult):
            return culturalResult.description
        case .learningMaterial(let materialResult):
            return "Level: \(materialResult.level) • \(materialResult.type)"
        }
    }
    
    private var confidenceIndicator: some View {
        Text("\(Int(result.relevanceScore * 100))% match")
            .font(.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.blue.opacity(0.2))
            .cornerRadius(6)
    }
    
    private var actionButton: some View {
        Button("Explore") {
            // Handle result exploration
        }
        .font(.caption)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.blue)
        .foregroundColor(.white)
        .cornerRadius(6)
    }
}

struct TamilTextResultRow: View {
    let result: TamilScriptResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "text.viewfinder")
                    .foregroundColor(.blue)
                Text("Tamil Text")
                    .font(.caption)
                    .foregroundColor(.blue)
                Spacer()
                Text("\(Int(result.confidence * 100))%")
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(4)
            }
            
            Text(result.text)
                .font(.title3)
                .fontWeight(.semibold)
            
            Text(result.translation)
                .font(.body)
                .foregroundColor(.secondary)
            
            if !result.lessonRecommendations.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(result.lessonRecommendations.prefix(3), id: \.id) { lesson in
                            LessonRecommendationChip(lesson: lesson)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(12)
    }
}

struct CulturalContentResultRow: View {
    let result: CulturalContentResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "building.columns")
                    .foregroundColor(.orange)
                Text("Cultural Element")
                    .font(.caption)
                    .foregroundColor(.orange)
                Spacer()
                if result.arExperience {
                    Image(systemName: "arkit")
                        .foregroundColor(.purple)
                }
            }
            
            Text(result.name)
                .font(.title3)
                .fontWeight(.semibold)
            
            Text(result.description)
                .font(.body)
                .foregroundColor(.secondary)
            
            Text(result.culturalSignificance)
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
            
            if result.arExperience {
                Button("Explore in AR") {
                    // Launch AR experience
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.purple)
                .foregroundColor(.white)
                .cornerRadius(6)
            }
        }
        .padding()
        .background(Color.orange.opacity(0.05))
        .cornerRadius(12)
    }
}

struct LearningMaterialResultRow: View {
    let result: LearningMaterialResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "book")
                    .foregroundColor(.green)
                Text("Learning Material")
                    .font(.caption)
                    .foregroundColor(.green)
                Spacer()
                if result.audioAvailable {
                    Image(systemName: "speaker.wave.2")
                        .foregroundColor(.blue)
                }
            }
            
            Text(result.topic)
                .font(.title3)
                .fontWeight(.semibold)
            
            HStack {
                Text("Level: \(result.level)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .cornerRadius(6)
                
                Text("\(result.type)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(6)
            }
            
            Text(result.content)
                .font(.body)
                .foregroundColor(.secondary)
                .lineLimit(3)
            
            HStack {
                if result.audioAvailable {
                    Button("Play Audio") {
                        // Play audio
                    }
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(6)
                }
                
                Button("Start Learning") {
                    // Start learning session
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.green)
                .foregroundColor(.white)
                .cornerRadius(6)
            }
        }
        .padding()
        .background(Color.green.opacity(0.05))
        .cornerRadius(12)
    }
}

struct LessonRecommendationChip: View {
    let lesson: LessonRecommendation
    
    var body: some View {
        Button(lesson.title) {
            // Navigate to lesson
        }
        .font(.caption)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.blue.opacity(0.2))
        .cornerRadius(6)
    }
}

#Preview {
    VisualSearchResultsView(results: [])
}
