//
//  VisualIntelligenceView.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  iOS 26 Visual Intelligence UI
//

import SwiftUI
import PhotosUI

struct VisualIntelligenceView: View {
    @StateObject private var visualIntelligenceService = VisualIntelligenceService.shared
    @State private var selectedImage: UIImage?
    @State private var showingImagePicker = false
    @State private var showingCamera = false
    @State private var searchType: VisualSearchType = .all
    @State private var showingResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                headerSection
                
                if let image = selectedImage {
                    selectedImageSection(image)
                } else {
                    imageSelectionSection
                }
                
                searchTypeSelector
                
                if !visualIntelligenceService.searchResults.isEmpty {
                    searchResultsSection
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Visual Intelligence")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingImagePicker) {
                ImagePicker(image: $selectedImage)
            }
            .sheet(isPresented: $showingCamera) {
                CameraView(image: $selectedImage)
            }
            .sheet(isPresented: $showingResults) {
                VisualSearchResultsView(results: visualIntelligenceService.searchResults)
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "eye.circle.fill")
                .font(.system(size: 60))
                .foregroundStyle(.blue.gradient)
            
            Text("Visual Intelligence")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Discover Tamil content in any image")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top)
    }
    
    // MARK: - Image Selection Section
    
    private var imageSelectionSection: some View {
        VStack(spacing: 16) {
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
                .frame(height: 200)
                .overlay(
                    VStack(spacing: 12) {
                        Image(systemName: "photo.badge.plus")
                            .font(.system(size: 40))
                            .foregroundColor(.gray)
                        
                        Text("Select an image to analyze")
                            .font(.headline)
                            .foregroundColor(.gray)
                    }
                )
            
            HStack(spacing: 16) {
                Button(action: { showingCamera = true }) {
                    Label("Camera", systemImage: "camera.fill")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                
                Button(action: { showingImagePicker = true }) {
                    Label("Photos", systemImage: "photo.fill")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
            }
        }
    }
    
    // MARK: - Selected Image Section
    
    private func selectedImageSection(_ image: UIImage) -> some View {
        VStack(spacing: 16) {
            Image(uiImage: image)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxHeight: 200)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue, lineWidth: 2)
                )
            
            HStack(spacing: 12) {
                Button("Change Image") {
                    selectedImage = nil
                }
                .foregroundColor(.blue)
                
                Spacer()
                
                Button(action: performVisualSearch) {
                    HStack {
                        if visualIntelligenceService.isProcessing {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "magnifyingglass")
                        }
                        Text("Analyze")
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .disabled(visualIntelligenceService.isProcessing)
            }
        }
    }
    
    // MARK: - Search Type Selector
    
    private var searchTypeSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Search For:")
                .font(.headline)
            
            Picker("Search Type", selection: $searchType) {
                Text("All Content").tag(VisualSearchType.all)
                Text("Tamil Text").tag(VisualSearchType.textOnly)
                Text("Cultural Elements").tag(VisualSearchType.culturalOnly)
                Text("Learning Materials").tag(VisualSearchType.learningMaterialsOnly)
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Search Results Section
    
    private var searchResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Results")
                    .font(.headline)
                
                Spacer()
                
                Button("View All") {
                    showingResults = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(Array(visualIntelligenceService.searchResults.prefix(3).enumerated()), id: \.offset) { index, result in
                        VisualSearchResultCard(result: result)
                            .frame(width: 200)
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Actions
    
    private func performVisualSearch() {
        guard let image = selectedImage else { return }
        
        let request = VisualSearchRequest(
            image: image,
            includeTextRecognition: searchType.includesTextRecognition,
            includeCulturalContent: searchType.includesCulturalContent,
            includeLearningMaterials: searchType.includesLearningMaterials,
            userContext: nil
        )
        
        Task {
            await visualIntelligenceService.handleVisualSearchRequest(request)
        }
    }
}

// MARK: - Visual Search Result Card

struct VisualSearchResultCard: View {
    let result: VisualSearchResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                resultIcon
                Spacer()
                confidenceIndicator
            }
            
            Text(resultTitle)
                .font(.headline)
                .lineLimit(2)
            
            Text(resultDescription)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(3)
            
            Spacer()
            
            actionButton
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var resultIcon: some View {
        Group {
            switch result {
            case .tamilScript:
                Image(systemName: "text.viewfinder")
                    .foregroundColor(.blue)
            case .cultural:
                Image(systemName: "building.columns")
                    .foregroundColor(.orange)
            case .learningMaterial:
                Image(systemName: "book")
                    .foregroundColor(.green)
            }
        }
        .font(.title2)
    }
    
    private var confidenceIndicator: some View {
        Text("\(Int(result.relevanceScore * 100))%")
            .font(.caption)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.blue.opacity(0.2))
            .cornerRadius(4)
    }
    
    private var resultTitle: String {
        switch result {
        case .tamilScript(let tamilResult):
            return tamilResult.text
        case .cultural(let culturalResult):
            return culturalResult.name
        case .learningMaterial(let materialResult):
            return materialResult.topic
        }
    }
    
    private var resultDescription: String {
        switch result {
        case .tamilScript(let tamilResult):
            return tamilResult.translation
        case .cultural(let culturalResult):
            return culturalResult.description
        case .learningMaterial(let materialResult):
            return "Level: \(materialResult.level)"
        }
    }
    
    private var actionButton: some View {
        Button("Learn More") {
            // Handle result selection
        }
        .font(.caption)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.blue)
        .foregroundColor(.white)
        .cornerRadius(6)
    }
}

// MARK: - Supporting Views

struct ImagePicker: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var config = PHPickerConfiguration()
        config.filter = .images
        config.selectionLimit = 1
        
        let picker = PHPickerViewController(configuration: config)
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            parent.presentationMode.wrappedValue.dismiss()
            
            guard let provider = results.first?.itemProvider else { return }
            
            if provider.canLoadObject(ofClass: UIImage.self) {
                provider.loadObject(ofClass: UIImage.self) { image, _ in
                    DispatchQueue.main.async {
                        self.parent.image = image as? UIImage
                    }
                }
            }
        }
    }
}

struct CameraView: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraView
        
        init(_ parent: CameraView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let uiImage = info[.originalImage] as? UIImage {
                parent.image = uiImage
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

#Preview {
    VisualIntelligenceView()
}
