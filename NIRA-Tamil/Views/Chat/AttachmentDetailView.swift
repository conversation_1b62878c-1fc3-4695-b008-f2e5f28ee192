import SwiftUI
import QuickLook

// MARK: - Attachment Detail View
struct AttachmentDetailView: View {
    let attachment: ChatAttachment
    @StateObject private var attachmentService = FileAttachmentService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var showingQuickLook = false
    @State private var showingDeleteAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // File Preview
                    filePreviewSection
                    
                    // File Information
                    fileInfoSection
                    
                    // Actions
                    actionsSection
                    
                    // AI Analysis (if available)
                    if let extractedText = attachment.extractedText {
                        aiAnalysisSection(extractedText)
                    }
                }
                .padding()
            }
            .navigationTitle("Attachment")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: { showingQuickLook = true }) {
                            Label("Quick Look", systemImage: "eye")
                        }
                        
                        But<PERSON>(action: shareAttachment) {
                            Label("Share", systemImage: "square.and.arrow.up")
                        }
                        
                        Button(role: .destructive, action: { showingDeleteAlert = true }) {
                            Label("Delete", systemImage: "trash")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        }
        .sheet(isPresented: $showingQuickLook) {
            if let url = URL(string: attachment.url) {
                QuickLookView(url: url)
            }
        }
        .alert("Delete Attachment", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteAttachment()
            }
        } message: {
            Text("Are you sure you want to delete this attachment? This action cannot be undone.")
        }
    }
    
    // MARK: - File Preview Section
    
    private var filePreviewSection: some View {
        VStack(spacing: 12) {
            // Large preview
            Group {
                if attachment.mimeType.hasPrefix("image/") {
                    imagePreview
                } else if attachment.mimeType.hasPrefix("video/") {
                    videoPreview
                } else {
                    genericFilePreview
                }
            }
            .frame(maxHeight: 300)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            // File name and type
            VStack(spacing: 4) {
                Text(attachment.fileName)
                    .font(.headline)
                    .multilineTextAlignment(.center)
                
                Text(attachment.mimeType)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var imagePreview: some View {
        AsyncImage(url: URL(string: attachment.url)) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fit)
        } placeholder: {
            ProgressView()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }
    
    private var videoPreview: some View {
        VStack {
            if let thumbnailUrl = attachment.thumbnailUrl {
                AsyncImage(url: URL(string: thumbnailUrl)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                } placeholder: {
                    ProgressView()
                }
            } else {
                Image(systemName: "video.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.secondary)
            }
            
            Button("Play Video") {
                showingQuickLook = true
            }
            .buttonStyle(.borderedProminent)
        }
    }
    
    private var genericFilePreview: some View {
        VStack(spacing: 16) {
            Image(systemName: attachment.fileTypeIcon)
                .font(.system(size: 60))
                .foregroundColor(attachment.fileTypeColor)
            
            Button("Open File") {
                showingQuickLook = true
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - File Info Section
    
    private var fileInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("File Information")
                .font(.headline)
            
            VStack(spacing: 8) {
                InfoRow(label: "Size", value: formatFileSize(attachment.size))
                InfoRow(label: "Type", value: attachment.mimeType)
                InfoRow(label: "Uploaded", value: formatDate(attachment.uploadedAt))
                
                if let metadata = attachment.metadata {
                    ForEach(metadata.keys.sorted(), id: \.self) { key in
                        if let value = metadata[key] {
                            InfoRow(label: key.capitalized, value: String(describing: value))
                        }
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Actions Section
    
    private var actionsSection: some View {
        VStack(spacing: 12) {
            Text("Actions")
                .font(.headline)
            
            VStack(spacing: 8) {
                ActionButton(
                    title: "Download",
                    icon: "arrow.down.circle",
                    color: .blue
                ) {
                    downloadAttachment()
                }
                
                ActionButton(
                    title: "Share",
                    icon: "square.and.arrow.up",
                    color: .green
                ) {
                    shareAttachment()
                }
                
                if attachment.mimeType.hasPrefix("text/") || attachment.mimeType == "application/pdf" {
                    ActionButton(
                        title: "Analyze with AI",
                        icon: "brain",
                        color: .purple
                    ) {
                        analyzeWithAI()
                    }
                }
            }
        }
    }
    
    // MARK: - AI Analysis Section
    
    private func aiAnalysisSection(_ extractedText: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("AI Analysis")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Extracted Text:")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(extractedText)
                    .font(.body)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                
                Button("Ask AI about this content") {
                    // TODO: Integrate with AI chat
                }
                .buttonStyle(.borderedProminent)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Actions
    
    private func downloadAttachment() {
        guard let url = URL(string: attachment.url) else { return }
        
        // Open in Safari or system handler
        UIApplication.shared.open(url)
    }
    
    private func shareAttachment() {
        guard let url = URL(string: attachment.url) else { return }
        
        let activityVC = UIActivityViewController(
            activityItems: [url],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
    
    private func analyzeWithAI() {
        // TODO: Implement AI analysis
        print("Analyzing attachment with AI...")
    }
    
    private func deleteAttachment() {
        Task {
            do {
                try await attachmentService.deleteAttachment(attachment)
                dismiss()
            } catch {
                // Handle error
                print("Failed to delete attachment: \(error)")
            }
        }
    }
    
    // MARK: - Helper Functions
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Supporting Views

struct InfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .fontWeight(.regular)
        }
    }
}

struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                
                Text(title)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Quick Look View

struct QuickLookView: UIViewControllerRepresentable {
    let url: URL
    
    func makeUIViewController(context: Context) -> QLPreviewController {
        let controller = QLPreviewController()
        controller.dataSource = context.coordinator
        return controller
    }
    
    func updateUIViewController(_ uiViewController: QLPreviewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(url: url)
    }
    
    class Coordinator: NSObject, QLPreviewControllerDataSource {
        let url: URL
        
        init(url: URL) {
            self.url = url
        }
        
        func numberOfPreviewItems(in controller: QLPreviewController) -> Int {
            return 1
        }
        
        func previewController(_ controller: QLPreviewController, previewItemAt index: Int) -> QLPreviewItem {
            return url as QLPreviewItem
        }
    }
}
