import SwiftUI
import UniformTypeIdentifiers

// MARK: - File Attachment View
struct FileAttachmentView: View {
    @StateObject private var attachmentService = FileAttachmentService.shared
    @State private var showingFilePicker = false
    @State private var showingImagePicker = false
    @State private var showingDocumentPicker = false
    @State private var selectedAttachment: ChatAttachment?
    
    let conversationId: UUID
    let onAttachmentAdded: (ChatAttachment) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // Attachment Options
            attachmentOptionsView
            
            // Upload Progress
            if attachmentService.isUploading {
                uploadProgressView
            }
            
            // Recent Attachments
            if !attachmentService.recentAttachments.isEmpty {
                recentAttachmentsView
            }
            
            // Error Message
            if let errorMessage = attachmentService.errorMessage {
                errorView(errorMessage)
            }
        }
        .sheet(isPresented: $showingImagePicker) {
            ChatImagePicker { image in
                handleImageSelection(image)
            }
        }
        .sheet(isPresented: $showingDocumentPicker) {
            ChatDocumentPicker { url in
                handleDocumentSelection(url)
            }
        }
        .sheet(item: $selectedAttachment) { attachment in
            AttachmentDetailView(attachment: attachment)
        }
    }
    
    // MARK: - Attachment Options
    
    private var attachmentOptionsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                // Camera/Photo Library
                AttachmentOptionButton(
                    icon: "camera.fill",
                    title: "Photo",
                    color: .blue
                ) {
                    showingImagePicker = true
                }
                
                // Documents
                AttachmentOptionButton(
                    icon: "doc.fill",
                    title: "Document",
                    color: .green
                ) {
                    showingDocumentPicker = true
                }
                
                // Audio Recording
                AttachmentOptionButton(
                    icon: "mic.fill",
                    title: "Audio",
                    color: .red
                ) {
                    // TODO: Implement audio recording
                }
                
                // File Browser
                AttachmentOptionButton(
                    icon: "folder.fill",
                    title: "Files",
                    color: .orange
                ) {
                    showingDocumentPicker = true
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
    }
    
    // MARK: - Upload Progress
    
    private var uploadProgressView: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "arrow.up.circle.fill")
                    .foregroundColor(.blue)
                
                Text("Uploading...")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(Int(attachmentService.uploadProgress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: attachmentService.uploadProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    // MARK: - Recent Attachments
    
    private var recentAttachmentsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recent Attachments")
                .font(.headline)
                .padding(.horizontal)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(attachmentService.recentAttachments.prefix(5)) { attachment in
                        AttachmentThumbnailView(attachment: attachment) {
                            selectedAttachment = attachment
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical)
    }
    
    // MARK: - Error View
    
    private func errorView(_ message: String) -> some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
            
            Text(message)
                .font(.caption)
                .foregroundColor(.red)
            
            Spacer()
            
            Button("Dismiss") {
                attachmentService.errorMessage = nil
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .padding()
        .background(Color.red.opacity(0.1))
        .cornerRadius(8)
        .padding(.horizontal)
    }
    
    // MARK: - File Handling
    
    private func handleImageSelection(_ image: UIImage) {
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            attachmentService.errorMessage = "Failed to process image"
            return
        }
        
        let fileName = "image_\(Date().timeIntervalSince1970).jpg"
        
        Task {
            do {
                let attachment = try await attachmentService.uploadFile(
                    data: imageData,
                    fileName: fileName,
                    mimeType: "image/jpeg",
                    conversationId: conversationId
                )
                
                onAttachmentAdded(attachment)
                
            } catch {
                attachmentService.errorMessage = error.localizedDescription
            }
        }
    }
    
    private func handleDocumentSelection(_ url: URL) {
        guard url.startAccessingSecurityScopedResource() else {
            attachmentService.errorMessage = "Cannot access selected file"
            return
        }
        
        defer {
            url.stopAccessingSecurityScopedResource()
        }
        
        do {
            let data = try Data(contentsOf: url)
            let fileName = url.lastPathComponent
            let mimeType = url.mimeType()
            
            // Validate file
            let validation = attachmentService.validateFile(
                data: data,
                fileName: fileName,
                mimeType: mimeType
            )
            
            guard validation.isValid else {
                attachmentService.errorMessage = validation.errors.joined(separator: ", ")
                return
            }
            
            Task {
                do {
                    let attachment = try await attachmentService.uploadFile(
                        data: data,
                        fileName: fileName,
                        mimeType: mimeType,
                        conversationId: conversationId
                    )
                    
                    onAttachmentAdded(attachment)
                    
                } catch {
                    attachmentService.errorMessage = error.localizedDescription
                }
            }
            
        } catch {
            attachmentService.errorMessage = "Failed to read file: \(error.localizedDescription)"
        }
    }
}

// MARK: - Attachment Option Button

struct AttachmentOptionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
            }
            .frame(width: 60, height: 60)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Attachment Thumbnail View

struct AttachmentThumbnailView: View {
    let attachment: ChatAttachment
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                // Thumbnail or Icon
                Group {
                    if let thumbnailUrl = attachment.thumbnailUrl {
                        AsyncImage(url: URL(string: thumbnailUrl)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            ProgressView()
                        }
                    } else {
                        Image(systemName: attachment.fileTypeIcon)
                            .font(.title)
                            .foregroundColor(attachment.fileTypeColor)
                    }
                }
                .frame(width: 50, height: 50)
                .background(Color(.systemGray5))
                .cornerRadius(8)
                
                // File Name
                Text(attachment.fileName)
                    .font(.caption2)
                    .lineLimit(1)
                    .truncationMode(.middle)
                    .frame(width: 60)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Views

struct ChatImagePicker: UIViewControllerRepresentable {
    let onImageSelected: (UIImage) -> Void

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(onImageSelected: onImageSelected)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let onImageSelected: (UIImage) -> Void

        init(onImageSelected: @escaping (UIImage) -> Void) {
            self.onImageSelected = onImageSelected
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                onImageSelected(image)
            }
            picker.dismiss(animated: true)
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            picker.dismiss(animated: true)
        }
    }
}

struct ChatDocumentPicker: UIViewControllerRepresentable {
    let onDocumentSelected: (URL) -> Void

    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: [
            .pdf, .text, .plainText, .rtf, .image, .audio, .video, .zip, .json
        ])
        picker.delegate = context.coordinator
        picker.allowsMultipleSelection = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(onDocumentSelected: onDocumentSelected)
    }

    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let onDocumentSelected: (URL) -> Void

        init(onDocumentSelected: @escaping (URL) -> Void) {
            self.onDocumentSelected = onDocumentSelected
        }

        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            if let url = urls.first {
                onDocumentSelected(url)
            }
        }
    }
}

// MARK: - Extensions

extension URL {
    func mimeType() -> String {
        if let mimeType = UTType(filenameExtension: self.pathExtension)?.preferredMIMEType {
            return mimeType
        }
        return "application/octet-stream"
    }
}

extension ChatAttachment {
    var fileTypeIcon: String {
        if mimeType.hasPrefix("image/") {
            return "photo.fill"
        } else if mimeType.hasPrefix("audio/") {
            return "music.note"
        } else if mimeType.hasPrefix("video/") {
            return "video.fill"
        } else if mimeType == "application/pdf" {
            return "doc.text.fill"
        } else if mimeType.hasPrefix("text/") {
            return "text.alignleft"
        } else {
            return "doc.fill"
        }
    }
    
    var fileTypeColor: Color {
        if mimeType.hasPrefix("image/") {
            return .blue
        } else if mimeType.hasPrefix("audio/") {
            return .purple
        } else if mimeType.hasPrefix("video/") {
            return .red
        } else if mimeType == "application/pdf" {
            return .orange
        } else if mimeType.hasPrefix("text/") {
            return .green
        } else {
            return .gray
        }
    }
}
