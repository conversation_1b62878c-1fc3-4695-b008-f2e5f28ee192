import SwiftUI

/// Admin view for managing Google TTS audio generation
struct TTSManagementView: View {
    @StateObject private var ttsService = GoogleTTSService.shared
    @StateObject private var supabaseService = SupabaseContentService.shared
    
    @State private var vocabularyItems: [TamilSupabaseVocabulary] = []
    @State private var isLoadingVocabulary = false
    @State private var selectedVocabulary: [TamilSupabaseVocabulary] = []
    @State private var showingResults = false
    @State private var audioResults: [VocabularyAudioResult] = []
    @State private var errorMessage: String?
    
    // Basic Greetings lesson ID
    private let basicGreetingsLessonId = "7b8c60af-dd2f-4754-9363-ab09a5bcea95"
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                headerSection
                
                if isLoadingVocabulary {
                    loadingSection
                } else {
                    vocabularyListSection
                }
                
                if ttsService.isGenerating {
                    generationProgressSection
                } else {
                    actionButtonsSection
                }
                
                if !ttsService.statusMessage.isEmpty {
                    statusSection
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("TTS Audio Generation")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadBasicGreetingsVocabulary()
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") { errorMessage = nil }
            } message: {
                if let error = errorMessage {
                    Text(error)
                }
            }
            .sheet(isPresented: $showingResults) {
                AudioResultsView(results: audioResults)
            }
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "speaker.wave.3.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                Text("Google TTS Audio Generation")
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            
            Text("Generate Tamil audio for Basic Greetings vocabulary using Google's premium female voice")
                .font(.callout)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var loadingSection: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading vocabulary...")
                .font(.callout)
                .foregroundColor(.secondary)
        }
        .frame(height: 100)
    }
    
    private var vocabularyListSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Basic Greetings Vocabulary (\(vocabularyItems.count) items)")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(selectedVocabulary.count == vocabularyItems.count ? "Deselect All" : "Select All") {
                    if selectedVocabulary.count == vocabularyItems.count {
                        selectedVocabulary.removeAll()
                    } else {
                        selectedVocabulary = vocabularyItems
                    }
                }
                .font(.callout)
                .foregroundColor(.blue)
            }
            
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(vocabularyItems, id: \.id) { vocabulary in
                        VocabularyRowView(
                            vocabulary: vocabulary,
                            isSelected: selectedVocabulary.contains { $0.id == vocabulary.id }
                        ) { isSelected in
                            if isSelected {
                                selectedVocabulary.append(vocabulary)
                            } else {
                                selectedVocabulary.removeAll { $0.id == vocabulary.id }
                            }
                        }
                    }
                }
            }
            .frame(maxHeight: 300)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var generationProgressSection: some View {
        VStack(spacing: 16) {
            Text("Generating Audio...")
                .font(.headline)
                .fontWeight(.semibold)
            
            ProgressView(value: ttsService.generationProgress)
                .progressViewStyle(LinearProgressViewStyle())
                .scaleEffect(y: 2)
            
            Text("\(Int(ttsService.generationProgress * 100))% Complete")
                .font(.callout)
                .foregroundColor(.secondary)
            
            Text("Generated: \(ttsService.generatedAudioCount) files")
                .font(.callout)
                .foregroundColor(.blue)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            Button {
                generateSelectedAudio()
            } label: {
                HStack {
                    Image(systemName: "waveform.circle.fill")
                    Text("Generate Audio for Selected (\(selectedVocabulary.count))")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(selectedVocabulary.isEmpty ? Color.gray : Color.blue)
                )
            }
            .disabled(selectedVocabulary.isEmpty)
            
            Button {
                testSingleVocabulary()
            } label: {
                HStack {
                    Image(systemName: "play.circle.fill")
                    Text("Test with First Item")
                }
                .font(.callout)
                .foregroundColor(.blue)
            }
            .disabled(vocabularyItems.isEmpty)
        }
    }
    
    private var statusSection: some View {
        VStack(spacing: 8) {
            Text("Status")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(ttsService.statusMessage)
                .font(.callout)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Actions
    
    private func loadBasicGreetingsVocabulary() {
        isLoadingVocabulary = true
        
        Task {
            let vocabulary = await supabaseService.fetchVocabulary(for: basicGreetingsLessonId)

            await MainActor.run {
                self.vocabularyItems = vocabulary
                self.isLoadingVocabulary = false
            }
        }
    }
    
    private func generateSelectedAudio() {
        guard !selectedVocabulary.isEmpty else { return }
        
        let vocabularyItems = selectedVocabulary.map { vocab in
            TTSVocabularyItem(
                vocabId: vocab.vocabId,
                englishWord: vocab.englishWord,
                tamilTranslation: vocab.tamilTranslation,
                exampleSentenceTamil: vocab.exampleSentenceTamil
            )
        }
        
        Task {
            do {
                let results = try await ttsService.generateLessonAudio(vocabularyItems: vocabularyItems)
                
                await MainActor.run {
                    self.audioResults = results
                    self.showingResults = true
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to generate audio: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testSingleVocabulary() {
        guard let firstVocab = vocabularyItems.first else { return }
        
        selectedVocabulary = [firstVocab]
        generateSelectedAudio()
    }
}

// MARK: - Supporting Views

struct VocabularyRowView: View {
    let vocabulary: TamilSupabaseVocabulary
    let isSelected: Bool
    let onSelectionChanged: (Bool) -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Button {
                onSelectionChanged(!isSelected)
            } label: {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .blue : .gray)
                    .font(.title3)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(vocabulary.englishWord)
                    .font(.callout)
                    .fontWeight(.medium)
                
                Text(vocabulary.tamilTranslation)
                    .font(.callout)
                    .foregroundColor(.blue)
                
                if let example = vocabulary.exampleSentenceTamil, !example.isEmpty {
                    Text("Example: \(example.prefix(30))...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(vocabulary.vocabId)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                if vocabulary.audioWordUrl != nil {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
        )
    }
}

struct AudioResultsView: View {
    let results: [VocabularyAudioResult]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List(results, id: \.vocabId) { result in
                VStack(alignment: .leading, spacing: 8) {
                    Text("Vocab ID: \(result.vocabId)")
                        .font(.headline)
                    
                    Text("Word Audio: \(result.wordAudioURL)")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    if let sentenceURL = result.sentenceAudioURL {
                        Text("Sentence Audio: \(sentenceURL)")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
                .padding(.vertical, 4)
            }
            .navigationTitle("Generated Audio")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    TTSManagementView()
}
