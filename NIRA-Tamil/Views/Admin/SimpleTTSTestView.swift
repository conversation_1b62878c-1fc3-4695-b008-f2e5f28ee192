import SwiftUI

/// Simple TTS Test View for Basic Greetings vocabulary
/// Uses types from GoogleTTSService: TTSVocabularyItem, VocabularyAudioResult
struct SimpleTTSTestView: View {
    @StateObject private var ttsService = SimpleTTSService.shared
    @StateObject private var supabaseService = SupabaseContentService.shared
    @StateObject private var quickFixService = QuickAudioFixService.shared
    @StateObject private var realFixService = RealAudioFixService.shared
    @StateObject private var ttsGenerator = GoogleTTSGenerator.shared
    @StateObject private var audioUploader = SupabaseAudioUploader.shared
    @StateObject private var databaseService = AudioDatabaseService.shared

    @State private var vocabularyItems: [TamilSupabaseVocabulary] = []
    @State private var isLoadingVocabulary = false
    @State private var audioResults: [VocabularyAudioResult] = []
    @State private var showingResults = false
    @State private var errorMessage: String?
    @State private var verificationResult: AudioVerificationResult?
    @State private var isWorkflowRunning = false
    @State private var generatedFiles: [GeneratedAudioFile] = []
    @State private var uploadedFiles: [UploadedAudioFile] = []
    
    // Basic Greetings lesson ID
    private let basicGreetingsLessonId = "7b8c60af-dd2f-4754-9363-ab09a5bcea95"
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    headerSection
                    
                    if isLoadingVocabulary {
                        loadingSection
                    } else {
                        vocabularySection
                    }
                    
                    if ttsService.isGenerating || databaseService.isUpdating {
                        progressSection
                    } else {
                        actionSection
                    }
                    
                    if !ttsService.statusMessage.isEmpty || !databaseService.statusMessage.isEmpty {
                        statusSection
                    }
                    
                    if !audioResults.isEmpty {
                        resultsSection
                    }
                }
                .padding()
            }
            .navigationTitle("TTS Test")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadVocabulary()
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") { errorMessage = nil }
            } message: {
                if let error = errorMessage {
                    Text(error)
                }
            }
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "speaker.wave.3.fill")
                    .font(.title)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading) {
                    Text("Tamil TTS Test")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Basic Greetings Audio Generation")
                        .font(.callout)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            Text("Test Google TTS with Tamil female voice (ta-IN-Chirp3-HD-Erinome)")
                .font(.callout)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var loadingSection: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading vocabulary...")
                .font(.callout)
                .foregroundColor(.secondary)
        }
        .frame(height: 120)
    }
    
    private var vocabularySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Vocabulary Items (\(vocabularyItems.count))")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVStack(spacing: 12) {
                ForEach(vocabularyItems.prefix(5), id: \.id) { vocab in
                    VocabPreviewCard(vocabulary: vocab)
                }
                
                if vocabularyItems.count > 5 {
                    Text("... and \(vocabularyItems.count - 5) more items")
                        .font(.callout)
                        .foregroundColor(.secondary)
                        .padding()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var progressSection: some View {
        VStack(spacing: 16) {
            if ttsService.isGenerating {
                Text("Generating Audio...")
                    .font(.headline)
                    .fontWeight(.semibold)

                ProgressView(value: ttsService.generationProgress)
                    .progressViewStyle(LinearProgressViewStyle())
                    .scaleEffect(y: 2)

                HStack {
                    Text("\(Int(ttsService.generationProgress * 100))%")
                        .font(.callout)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("\(ttsService.generatedAudioCount) files")
                        .font(.callout)
                        .foregroundColor(.blue)
                }
            } else if databaseService.isUpdating {
                Text("Updating Database...")
                    .font(.headline)
                    .fontWeight(.semibold)

                ProgressView(value: databaseService.updateProgress)
                    .progressViewStyle(LinearProgressViewStyle())
                    .scaleEffect(y: 2)

                HStack {
                    Text("\(Int(databaseService.updateProgress * 100))%")
                        .font(.callout)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("\(databaseService.updatedCount) updated")
                        .font(.callout)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var actionSection: some View {
        VStack(spacing: 16) {
            // Quick Fix Section
            if !vocabularyItems.isEmpty {
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text("Audio Playback Issues?")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }

                    Text("If you're getting audio playback errors, use this real fix to update the database with working audio URLs")
                        .font(.callout)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)

                    Button {
                        realFixAudio()
                    } label: {
                        HStack {
                            Image(systemName: "wrench.and.screwdriver.fill")
                            Text("Fix Audio URLs in Database")
                        }
                        .font(.callout)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(realFixService.isFixing ? Color.gray : Color.red)
                        )
                    }
                    .disabled(realFixService.isFixing)

                    if realFixService.isFixing {
                        VStack(spacing: 8) {
                            ProgressView(value: realFixService.fixProgress)
                                .progressViewStyle(LinearProgressViewStyle())

                            Text(realFixService.statusMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.orange.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(.orange.opacity(0.3), lineWidth: 1)
                        )
                )
            }

            // Production Audio Generation Section
            if !vocabularyItems.isEmpty {
                VStack(spacing: 16) {
                    // Header
                    HStack {
                        Image(systemName: "waveform.circle.fill")
                            .foregroundColor(.blue)
                        Text("Production Audio Generation")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }

                    Text("Generate high-quality MP3 files using Google Cloud TTS with Tamil Premium voice")
                        .font(.callout)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)

                    // Complete Workflow Button
                    Button {
                        completeAudioWorkflow()
                    } label: {
                        HStack {
                            Image(systemName: "play.circle.fill")
                            Text("Generate → Upload → Update Database")
                        }
                        .font(.callout)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(isWorkflowRunning ? Color.gray : Color.blue)
                        )
                    }
                    .disabled(isWorkflowRunning)

                    // Individual Step Buttons
                    HStack(spacing: 12) {
                        Button("1. Generate") {
                            generateAudioFiles()
                        }
                        .buttonStyle(StepButtonStyle(color: .green, isActive: ttsGenerator.isGenerating))
                        .disabled(ttsGenerator.isGenerating)

                        Button("2. Upload") {
                            uploadAudioFiles()
                        }
                        .buttonStyle(StepButtonStyle(color: .orange, isActive: audioUploader.isUploading))
                        .disabled(audioUploader.isUploading)

                        Button("3. Validate") {
                            validateAudioURLs()
                        }
                        .buttonStyle(StepButtonStyle(color: .purple, isActive: false))
                    }

                    // Progress Display
                    if isWorkflowRunning {
                        VStack(spacing: 8) {
                            if ttsGenerator.isGenerating {
                                ProgressView(value: ttsGenerator.generationProgress)
                                    .progressViewStyle(LinearProgressViewStyle())
                                Text(ttsGenerator.statusMessage)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            } else if audioUploader.isUploading {
                                ProgressView(value: audioUploader.uploadProgress)
                                    .progressViewStyle(LinearProgressViewStyle())
                                Text(audioUploader.statusMessage)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.blue.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(.blue.opacity(0.3), lineWidth: 1)
                        )
                )
            }

            // Primary Action
            Button {
                generateAndUpdateAllAudio()
            } label: {
                HStack {
                    Image(systemName: "speaker.wave.3.fill")
                    Text("Generate & Update All (\(vocabularyItems.count) items)")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(vocabularyItems.isEmpty ? Color.gray : Color.green)
                )
            }
            .disabled(vocabularyItems.isEmpty)

            // Test Actions
            HStack(spacing: 12) {
                Button {
                    testSingleItem()
                } label: {
                    HStack {
                        Image(systemName: "play.circle")
                        Text("Test 1 Item")
                    }
                    .font(.callout)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.blue, lineWidth: 1)
                    )
                }
                .disabled(vocabularyItems.isEmpty)

                Button {
                    testFiveItems()
                } label: {
                    HStack {
                        Image(systemName: "play.circle.fill")
                        Text("Test 5 Items")
                    }
                    .font(.callout)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.orange, lineWidth: 1)
                    )
                }
                .disabled(vocabularyItems.count < 5)
            }

            // Database Actions
            if !audioResults.isEmpty {
                HStack(spacing: 12) {
                    Button {
                        updateDatabase()
                    } label: {
                        HStack {
                            Image(systemName: "square.and.arrow.down")
                            Text("Update DB")
                        }
                        .font(.callout)
                        .foregroundColor(.green)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.green, lineWidth: 1)
                        )
                    }

                    Button {
                        verifyAudioURLs()
                    } label: {
                        HStack {
                            Image(systemName: "checkmark.circle")
                            Text("Verify URLs")
                        }
                        .font(.callout)
                        .foregroundColor(.purple)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.purple, lineWidth: 1)
                        )
                    }
                }
            }
        }
    }
    
    private var statusSection: some View {
        VStack(spacing: 12) {
            Text("Status")
                .font(.headline)
                .fontWeight(.semibold)

            if !ttsService.statusMessage.isEmpty {
                VStack(spacing: 4) {
                    Text("TTS Service")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)

                    Text(ttsService.statusMessage)
                        .font(.callout)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }

            if !databaseService.statusMessage.isEmpty {
                VStack(spacing: 4) {
                    Text("Database Service")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.green)

                    Text(databaseService.statusMessage)
                        .font(.callout)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }

            if let verification = verificationResult {
                VStack(spacing: 4) {
                    Text("Verification Results")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.purple)

                    Text("Success Rate: \(Int(verification.successRate * 100))% (\(verification.totalSuccessful)/\(verification.totalSuccessful + verification.totalFailed))")
                        .font(.callout)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private var resultsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Generated Audio URLs")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVStack(spacing: 8) {
                ForEach(audioResults, id: \.vocabId) { result in
                    AudioResultCard(result: result)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Actions
    
    private func loadVocabulary() {
        isLoadingVocabulary = true
        
        Task {
            let vocabulary = await supabaseService.fetchVocabulary(for: basicGreetingsLessonId)

            await MainActor.run {
                self.vocabularyItems = vocabulary
                self.isLoadingVocabulary = false
            }
        }
    }
    
    private func generateAndUpdateAllAudio() {
        Task {
            do {
                let results = try await databaseService.generateAndUpdateBasicGreetingsAudio()

                await MainActor.run {
                    self.audioResults = results
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to generate and update audio: \(error.localizedDescription)"
                }
            }
        }
    }

    private func testSingleItem() {
        guard let firstItem = vocabularyItems.first else { return }
        generateAudio(for: [firstItem])
    }

    private func testFiveItems() {
        let fiveItems = Array(vocabularyItems.prefix(5))
        generateAudio(for: fiveItems)
    }

    private func updateDatabase() {
        guard !audioResults.isEmpty else { return }

        Task {
            do {
                try await databaseService.updateVocabularyWithAudioURLs(results: audioResults)
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to update database: \(error.localizedDescription)"
                }
            }
        }
    }

    private func verifyAudioURLs() {
        guard !audioResults.isEmpty else { return }

        Task {
            let result = await databaseService.verifyAudioURLs(results: audioResults)

            await MainActor.run {
                self.verificationResult = result
            }
        }
    }
    
    private func generateAudio(for vocabulary: [TamilSupabaseVocabulary]) {
        let vocabularyItems = vocabulary.map { vocab in
            TTSVocabularyItem(
                vocabId: vocab.vocabId,
                englishWord: vocab.englishWord,
                tamilTranslation: vocab.tamilTranslation,
                exampleSentenceTamil: vocab.exampleSentenceTamil
            )
        }

        Task {
            do {
                let results = try await ttsService.generateLessonAudio(vocabularyItems: vocabularyItems)

                await MainActor.run {
                    self.audioResults = results
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to generate audio: \(error.localizedDescription)"
                }
            }
        }
    }

    private func quickFixAudio() {
        Task {
            do {
                try await quickFixService.fixBasicGreetingsAudio()

                await MainActor.run {
                    // Reload vocabulary to see updated URLs
                    loadVocabulary()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to fix audio URLs: \(error.localizedDescription)"
                }
            }
        }
    }

    private func realFixAudio() {
        Task {
            do {
                try await realFixService.fixBasicGreetingsAudio()

                await MainActor.run {
                    // Reload vocabulary to see updated URLs
                    loadVocabulary()
                    self.errorMessage = "✅ Successfully updated audio URLs in database!"
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to update database: \(error.localizedDescription)"
                }
            }
        }
    }

    // MARK: - Production Audio Workflow Functions

    private func completeAudioWorkflow() {
        isWorkflowRunning = true

        Task {
            do {
                let result = try await audioUploader.generateUploadAndUpdateBasicGreetings()

                await MainActor.run {
                    self.isWorkflowRunning = false
                    self.errorMessage = "✅ Workflow completed! \(result.summary)"
                    // Reload vocabulary to see updated URLs
                    loadVocabulary()
                }
            } catch {
                await MainActor.run {
                    self.isWorkflowRunning = false
                    self.errorMessage = "❌ Workflow failed: \(error.localizedDescription)"
                }
            }
        }
    }

    private func generateAudioFiles() {
        Task {
            do {
                let files = try await ttsGenerator.generateBasicGreetingsAudio()

                await MainActor.run {
                    self.generatedFiles = files
                    self.errorMessage = "✅ Generated \(files.count) audio files"
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "❌ Generation failed: \(error.localizedDescription)"
                }
            }
        }
    }

    private func uploadAudioFiles() {
        guard !generatedFiles.isEmpty else {
            errorMessage = "No generated files to upload"
            return
        }

        Task {
            do {
                let uploaded = try await audioUploader.uploadGeneratedAudio(generatedFiles)

                await MainActor.run {
                    self.uploadedFiles = uploaded
                    self.errorMessage = "✅ Uploaded \(uploaded.count) files to Supabase"
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "❌ Upload failed: \(error.localizedDescription)"
                }
            }
        }
    }

    private func validateAudioURLs() {
        Task {
            let result = await audioUploader.validateUploadedAudio()

            await MainActor.run {
                self.errorMessage = "✅ Validation: \(result.summary)"
            }
        }
    }
}

// MARK: - Step Button Style

struct StepButtonStyle: ButtonStyle {
    let color: Color
    let isActive: Bool

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(isActive ? .white : color)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isActive ? color : color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(color, lineWidth: 1)
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Supporting Views

struct VocabPreviewCard: View {
    let vocabulary: TamilSupabaseVocabulary
    
    var body: some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(vocabulary.englishWord)
                    .font(.callout)
                    .fontWeight(.medium)
                
                Text(vocabulary.tamilTranslation)
                    .font(.callout)
                    .foregroundColor(.blue)
                
                if let example = vocabulary.exampleSentenceTamil, !example.isEmpty {
                    Text(example.prefix(40) + "...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Text(vocabulary.vocabId)
                .font(.caption2)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color(.systemGray5))
                .cornerRadius(4)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

struct AudioResultCard: View {
    let result: VocabularyAudioResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Vocab: \(result.vocabId)")
                .font(.callout)
                .fontWeight(.medium)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Word Audio:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(result.wordAudioURL)
                    .font(.caption2)
                    .foregroundColor(.blue)
                    .lineLimit(2)
            }
            
            if let sentenceURL = result.sentenceAudioURL {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Sentence Audio:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(sentenceURL)
                        .font(.caption2)
                        .foregroundColor(.green)
                        .lineLimit(2)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

#Preview {
    SimpleTTSTestView()
}
