//
//  ContentManagementDashboard.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import SwiftUI

struct ContentManagementDashboard: View {
    @StateObject private var dynamicContentService = DynamicContentService.shared
    @StateObject private var cachingService = ContentCachingService.shared
    @StateObject private var audioService = ExploreAudioService.shared
    @StateObject private var romanizationService = TamilRomanizationService.shared
    
    @State private var selectedTab = 0
    @State private var showingClearCacheAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(alignment: .leading, spacing: 8) {
                    Text("Content Management")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Manage dynamic content, cache, and integrations")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                
                // Tab Navigation
                Picker("Management Type", selection: $selectedTab) {
                    Text("Content").tag(0)
                    Text("Cache").tag(1)
                    Text("Audio").tag(2)
                    Text("APIs").tag(3)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                
                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    // Content Management
                    contentManagementView
                        .tag(0)
                    
                    // Cache Management
                    cacheManagementView
                        .tag(1)
                    
                    // Audio Management
                    audioManagementView
                        .tag(2)
                    
                    // API Status
                    apiStatusView
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationBarHidden(true)
        }
    }
    
    // MARK: - Content Management View
    
    private var contentManagementView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Update All Content Button
                Button(action: {
                    updateAllContent()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                            .font(.title3)
                        
                        VStack(alignment: .leading) {
                            Text("Update All Content")
                                .fontWeight(.semibold)
                            Text("Refresh from all sources")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if dynamicContentService.isUpdating {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                .disabled(dynamicContentService.isUpdating)
                
                // Content Status Cards
                ForEach(dynamicContentService.contentStatus, id: \.contentType) { status in
                    contentStatusCard(status: status)
                }
                
                // Update Progress
                if dynamicContentService.isUpdating {
                    VStack(spacing: 8) {
                        ProgressView(value: dynamicContentService.updateProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        
                        Text(dynamicContentService.statusMessage)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                }
                
                // Last Update Info
                if let lastUpdate = dynamicContentService.lastUpdateTime {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Last Updated")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(lastUpdate, style: .relative)
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                }
            }
            .padding()
        }
    }
    
    // MARK: - Cache Management View
    
    private var cacheManagementView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Cache Statistics
                VStack(spacing: 12) {
                    Text("Cache Statistics")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 20) {
                        VStack {
                            Text("\(cachingService.cachedItemCount)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.blue)
                            Text("Items")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack {
                            Text(formatBytes(cachingService.cacheSize))
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                            Text("Size")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                
                // Cache Actions
                VStack(spacing: 12) {
                    Button("Clear Expired Cache") {
                        cachingService.clearExpiredCache()
                    }
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.orange)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    
                    Button("Clear All Cache") {
                        showingClearCacheAlert = true
                    }
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.red)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
            }
            .padding()
        }
        .alert("Clear All Cache", isPresented: $showingClearCacheAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Clear", role: .destructive) {
                cachingService.clearCache()
            }
        } message: {
            Text("This will remove all cached content and may slow down the app temporarily.")
        }
    }
    
    // MARK: - Audio Management View
    
    private var audioManagementView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Audio Control Panel
                AudioControlPanel()
                
                // Audio Statistics
                VStack(alignment: .leading, spacing: 8) {
                    Text("Audio Generation Status")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    if audioService.isGenerating {
                        VStack(spacing: 8) {
                            ProgressView(value: audioService.generationProgress)
                                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            
                            Text("\(audioService.generatedAudioCount) of \(audioService.totalAudioToGenerate) files generated")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Text("Audio generation is idle")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                
                // Romanization Status
                VStack(alignment: .leading, spacing: 8) {
                    Text("Romanization Status")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    if romanizationService.isProcessing {
                        VStack(spacing: 8) {
                            ProgressView(value: romanizationService.processingProgress)
                                .progressViewStyle(LinearProgressViewStyle(tint: .green))
                            
                            Text(romanizationService.statusMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Text("Romanization is up to date")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .padding()
        }
    }
    
    // MARK: - API Status View
    
    private var apiStatusView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("API Integration Status")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                // API Status Cards
                apiStatusCard(
                    name: "Supabase",
                    status: .connected,
                    description: "Primary database and storage"
                )
                
                apiStatusCard(
                    name: "Google TTS",
                    status: .connected,
                    description: "Text-to-speech generation"
                )
                
                apiStatusCard(
                    name: "TMDB (Movies)",
                    status: .notConfigured,
                    description: "Tamil movie information"
                )
                
                apiStatusCard(
                    name: "News API",
                    status: .notConfigured,
                    description: "Cultural news and updates"
                )
                
                // Configuration Note
                Text("Configure API keys in the app settings to enable external integrations.")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .padding()
        }
    }
    
    // MARK: - Helper Views
    
    private func contentStatusCard(status: ContentUpdateStatus) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(status.contentType)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Refresh") {
                    refreshContent(type: status.contentType)
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            HStack {
                Text("\(status.recordCount) items")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(status.lastUpdated, style: .relative)
                    .font(.caption)
                    .foregroundColor(status.isStale ? .orange : .secondary)
            }
            
            // Source indicator
            HStack {
                Circle()
                    .fill(sourceColor(for: status.source))
                    .frame(width: 8, height: 8)
                
                Text(sourceDescription(for: status.source))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    private func apiStatusCard(name: String, status: APIStatus, description: String) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            HStack(spacing: 6) {
                Circle()
                    .fill(status.color)
                    .frame(width: 8, height: 8)
                
                Text(status.displayName)
                    .font(.caption)
                    .foregroundColor(status.color)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    // MARK: - Helper Methods
    
    private func updateAllContent() {
        Task {
            await dynamicContentService.updateAllContent()
        }
    }
    
    private func refreshContent(type: String) {
        Task {
            await dynamicContentService.refreshContent(type: type)
        }
    }
    
    private func sourceColor(for source: ContentSource) -> Color {
        switch source {
        case .supabase: return .blue
        case .publicAPI: return .green
        case .cached: return .orange
        }
    }
    
    private func sourceDescription(for source: ContentSource) -> String {
        switch source {
        case .supabase: return "Supabase Database"
        case .publicAPI(let name): return "API: \(name)"
        case .cached: return "Cached Data"
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - API Status Enum

enum APIStatus {
    case connected
    case disconnected
    case notConfigured
    case error
    
    var color: Color {
        switch self {
        case .connected: return .green
        case .disconnected: return .red
        case .notConfigured: return .orange
        case .error: return .red
        }
    }
    
    var displayName: String {
        switch self {
        case .connected: return "Connected"
        case .disconnected: return "Disconnected"
        case .notConfigured: return "Not Configured"
        case .error: return "Error"
        }
    }
}

#Preview {
    ContentManagementDashboard()
}
