import SwiftUI

/// Admin view for generating grammar audio files
struct GrammarAudioGeneratorView: View {
    @StateObject private var generator = GrammarAudioGenerator.shared
    @StateObject private var workflow = GrammarAudioWorkflow.shared
    @State private var showingResults = false
    @State private var workflowResult: GrammarWorkflowResult?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                headerSection
                statusSection
                actionButtons
                Spacer()
            }
            .padding(20)
            .navigationTitle("Grammar Audio Generator")
            .navigationBarTitleDisplayMode(.large)
        }
        .alert("Generation Complete", isPresented: $showingResults) {
            Button("OK") { showingResults = false }
        } message: {
            if let result = workflowResult {
                Text("Successfully generated \(result.generatedFiles) audio files for \(result.totalExamples) grammar examples!")
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "waveform.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("Grammar Audio Generator")
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text("Generate Tamil audio files for grammar examples using Google TTS with approved voices")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical)
    }
    
    private var statusSection: some View {
        VStack(spacing: 16) {
            if workflow.isProcessing {
                VStack(spacing: 12) {
                    ProgressView(value: workflow.overallProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    
                    Text(workflow.currentStep)
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    Text(workflow.statusMessage)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.blue.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                        )
                )
            } else {
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "info.circle.fill")
                            .foregroundColor(.blue)
                        Text("Ready to Generate")
                            .font(.headline)
                            .foregroundColor(.blue)
                        Spacer()
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("• Uses approved Tamil voices:")
                        Text("  - Female: ta-IN-Chirp3-HD-Erinome")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("  - Male: ta-IN-Chirp3-HD-Iapetus")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("• Generates MP3 files for all grammar examples")
                        Text("• Uploads to Supabase Storage")
                        Text("• Updates database with audio URLs")
                    }
                    .font(.body)
                    .foregroundColor(.primary)
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.green.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.green.opacity(0.3), lineWidth: 1)
                        )
                )
            }
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: 16) {
            Button(action: {
                Task {
                    await generateGrammarAudio()
                }
            }) {
                HStack {
                    if workflow.isProcessing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "play.fill")
                    }
                    
                    Text(workflow.isProcessing ? "Generating..." : "Generate Grammar Audio")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(workflow.isProcessing ? Color.gray : Color.blue)
                )
                .foregroundColor(.white)
            }
            .disabled(workflow.isProcessing)
            
            if !workflow.isProcessing {
                Button(action: {
                    Task {
                        await testSingleExample()
                    }
                }) {
                    HStack {
                        Image(systemName: "play.circle")
                        Text("Test Single Example")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue, lineWidth: 2)
                    )
                    .foregroundColor(.blue)
                }
            }
        }
    }
    
    // MARK: - Actions
    
    private func generateGrammarAudio() async {
        do {
            let result = try await workflow.executeGrammarAudioWorkflow()
            await MainActor.run {
                workflowResult = result
                showingResults = true
            }
        } catch {
            await MainActor.run {
                workflow.statusMessage = "Error: \(error.localizedDescription)"
            }
        }
    }
    
    private func testSingleExample() async {
        do {
            workflow.statusMessage = "Testing single example generation..."
            
            // Test with a simple example
            let testFiles = try await generator.generateGrammarAudio()
            
            await MainActor.run {
                workflow.statusMessage = "Test completed! Generated \(testFiles.count) files."
            }
        } catch {
            await MainActor.run {
                workflow.statusMessage = "Test failed: \(error.localizedDescription)"
            }
        }
    }
}

#Preview {
    GrammarAudioGeneratorView()
}
