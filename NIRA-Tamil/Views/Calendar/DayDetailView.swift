import SwiftUI
import Foundation

struct DayDetailView: View {
    let panchang: DailyPanchang
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    
    private let tabs = ["Panchang", "Festivals", "Muhurat"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Date Header
                dateHeader
                
                // Tab Selector
                tabSelector
                
                // Content
                TabView(selection: $selectedTab) {
                    panchangContent
                        .tag(0)
                    
                    festivalsContent
                        .tag(1)
                    
                    muhuratContent
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Daily Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - Date Header
    
    private var dateHeader: some View {
        VStack(spacing: 8) {
            // English Date
            Text(panchang.date.formatted(date: .complete, time: .omitted))
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            // Tamil Date
            Text(panchang.tamilDate.displayString)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // Location
            if let cityName = panchang.location.cityName {
                Text("📍 \(cityName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 16)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - Tab Selector
    
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(0..<tabs.count, id: \.self) { index in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = index
                    }
                }) {
                    Text(tabs[index])
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(selectedTab == index ? .white : .primary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedTab == index ? Color.niraPrimary : Color.clear)
                        )
                }
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - Panchang Content
    
    private var panchangContent: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Sun and Moon Times
                sunMoonTimesCard
                
                // Panchang Elements
                panchangElementsCard
                
                // Lunar Information
                lunarInformationCard
                
                // Significance
                if let significance = panchang.significance {
                    significanceCard(significance)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 100)
        }
    }
    
    private var sunMoonTimesCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Sun & Moon Times")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Label("Sunrise", systemImage: "sunrise.fill")
                        .font(.subheadline)
                        .foregroundColor(.orange)
                    
                    Text(panchang.sunTimes.formattedSunrise)
                        .font(.title3)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    Label("Sunset", systemImage: "sunset.fill")
                        .font(.subheadline)
                        .foregroundColor(.red)
                    
                    Text(panchang.sunTimes.formattedSunset)
                        .font(.title3)
                        .fontWeight(.medium)
                }
            }
            
            if let moonTimes = panchang.moonTimes {
                Divider()
                
                HStack(spacing: 20) {
                    if let moonrise = moonTimes.formattedMoonrise {
                        VStack(alignment: .leading, spacing: 8) {
                            Label("Moonrise", systemImage: "moon.fill")
                                .font(.subheadline)
                                .foregroundColor(.blue)
                            
                            Text(moonrise)
                                .font(.title3)
                                .fontWeight(.medium)
                        }
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 8) {
                        Text("Phase")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack(spacing: 4) {
                            Text(moonTimes.phase.emoji)
                                .font(.title2)
                            
                            Text(moonTimes.phase.tamilName)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    private var panchangElementsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Panchang Elements")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                PanchangElementRow(
                    title: "Tithi",
                    tamilTitle: "திதி",
                    value: panchang.tithi.name,
                    tamilValue: panchang.tithi.tamilName,
                    percentage: panchang.tithi.percentage,
                    isAuspicious: panchang.tithi.isAuspicious
                )
                
                Divider()
                
                PanchangElementRow(
                    title: "Nakshatra",
                    tamilTitle: "நட்சத்திரம்",
                    value: panchang.nakshatra.name,
                    tamilValue: panchang.nakshatra.tamilName,
                    percentage: panchang.nakshatra.percentage,
                    lord: panchang.nakshatra.lordDisplayName
                )
                
                Divider()
                
                PanchangElementRow(
                    title: "Yoga",
                    tamilTitle: "யோகம்",
                    value: panchang.yoga.name,
                    tamilValue: panchang.yoga.tamilName,
                    percentage: panchang.yoga.percentage
                )
                
                Divider()
                
                PanchangElementRow(
                    title: "Karana",
                    tamilTitle: "கரணம்",
                    value: panchang.karana.name,
                    tamilValue: panchang.karana.tamilName,
                    percentage: panchang.karana.percentage
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    private var lunarInformationCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Lunar Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                HStack {
                    Text("Lunar Month:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(panchang.lunarMonth.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Paksha:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(panchang.tamilDate.paksha.tamilName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Season:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(panchang.season.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    // MARK: - Festivals Content
    
    private var festivalsContent: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if panchang.festivals.isEmpty {
                    emptyFestivalsView
                } else {
                    ForEach(panchang.festivals, id: \.id) { festival in
                        FestivalCard(festival: festival)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 100)
        }
    }
    
    private var emptyFestivalsView: some View {
        VStack(spacing: 12) {
            Image(systemName: "calendar.badge.exclamationmark")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Festivals Today")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("This day has no major festivals or celebrations.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
    
    // MARK: - Muhurat Content
    
    private var muhuratContent: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Auspicious Times
                if !panchang.muhurat.isEmpty {
                    auspiciousTimesCard
                }
                
                // Inauspicious Times
                if !panchang.inauspiciousTimes.isEmpty {
                    inauspiciousTimesCard
                }
                
                if panchang.muhurat.isEmpty && panchang.inauspiciousTimes.isEmpty {
                    emptyMuhuratView
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 100)
        }
    }
    
    private var auspiciousTimesCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Auspicious Times")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.green)
            
            ForEach(panchang.muhurat.filter { $0.isAuspicious }, id: \.id) { muhurat in
                MuhuratRow(muhurat: muhurat, isAuspicious: true)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.green.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.green.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var inauspiciousTimesCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Inauspicious Times")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.red)
            
            ForEach(panchang.inauspiciousTimes, id: \.id) { inauspiciousTime in
                InauspiciousTimeRow(inauspiciousTime: inauspiciousTime)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var emptyMuhuratView: some View {
        VStack(spacing: 12) {
            Image(systemName: "clock.badge.questionmark")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Muhurat Data")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Muhurat timings are being calculated.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
    
    // MARK: - Helper Views
    
    private func significanceCard(_ significance: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Significance")
                .font(.headline)
                .fontWeight(.semibold)

            Text(significance)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(nil)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.niraPrimary.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.niraPrimary.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Supporting Views

struct PanchangElementRow: View {
    let title: String
    let tamilTitle: String
    let value: String
    let tamilValue: String
    let percentage: Double?
    let isAuspicious: Bool?
    let lord: String?

    init(title: String, tamilTitle: String, value: String, tamilValue: String, percentage: Double? = nil, isAuspicious: Bool? = nil, lord: String? = nil) {
        self.title = title
        self.tamilTitle = tamilTitle
        self.value = value
        self.tamilValue = tamilValue
        self.percentage = percentage
        self.isAuspicious = isAuspicious
        self.lord = lord
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(tamilTitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    HStack(spacing: 4) {
                        Text(value)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)

                        if let isAuspicious = isAuspicious {
                            Image(systemName: isAuspicious ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                                .font(.caption)
                                .foregroundColor(isAuspicious ? .green : .orange)
                        }
                    }

                    Text(tamilValue)
                        .font(.caption)
                        .foregroundColor(.niraPrimary)
                }
            }

            if let percentage = percentage {
                ProgressView(value: percentage / 100.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
                    .scaleEffect(y: 0.5)

                Text("\(Int(percentage))% remaining")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            if let lord = lord {
                Text("Lord: \(lord)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct FestivalCard: View {
    let festival: TamilFestival

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(festival.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(festival.nameTamil)
                        .font(.subheadline)
                        .foregroundColor(.niraPrimary)
                }

                Spacer()

                Text(festival.religion.capitalized)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(religionColor(festival.religion))
                    )
            }

            if !festival.description.isEmpty {
                Text(festival.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }

            if !festival.significance.isEmpty {
                Text("Significance: \(festival.significance)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    private func religionColor(_ religion: String) -> Color {
        switch religion.lowercased() {
        case "hindu":
            return .orange
        case "muslim":
            return .green
        case "christian":
            return .blue
        case "buddhist":
            return .yellow
        case "sikh":
            return .purple
        default:
            return .gray
        }
    }
}

struct MuhuratRow: View {
    let muhurat: Muhurat
    let isAuspicious: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(muhurat.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(muhurat.tamilName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Text(muhurat.formattedTimeRange)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isAuspicious ? .green : .red)
            }

            Text(muhurat.significance)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(2)

            if !muhurat.activities.isEmpty {
                Text("Good for: \(muhurat.activities.joined(separator: ", "))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

struct InauspiciousTimeRow: View {
    let inauspiciousTime: InauspiciousTime

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(inauspiciousTime.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(inauspiciousTime.tamilName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Text("\(DateFormatter.timeFormatter.string(from: inauspiciousTime.startTime)) - \(DateFormatter.timeFormatter.string(from: inauspiciousTime.endTime))")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.red)
            }

            Text(inauspiciousTime.warning)
                .font(.caption)
                .foregroundColor(.red)
                .lineLimit(2)

            if !inauspiciousTime.avoidActivities.isEmpty {
                Text("Avoid: \(inauspiciousTime.avoidActivities.joined(separator: ", "))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    // Create a sample panchang for preview
    let sampleLocation = LocationInfo(latitude: 13.0827, longitude: 80.2707, timezone: 5.5, cityName: "Chennai", countryName: "India")
    let sampleTamilDate = PanchangTamilDate(day: 15, month: .thai, year: 2025, paksha: .shukla, season: .winter, era: .sakaEra)
    let sampleSunTimes = SunTimes(sunrise: Date(), sunset: Date().addingTimeInterval(12*3600), noon: Date().addingTimeInterval(6*3600), twilightBegin: nil, twilightEnd: nil)
    let sampleTithi = Tithi(number: 15, name: "Purnima", tamilName: "பூர்ணிமை", paksha: .shukla, completionTime: nil, percentage: 85.0, lord: "Chandra", significance: "Full moon day")
    let sampleNakshatra = Nakshatra(number: 8, name: "Pushya", tamilName: "பூசம்", lord: "Saturn", tamilLord: "சனி", startTime: Date(), endTime: Date().addingTimeInterval(3600), percentage: 60.0, pada: 2, significance: "Auspicious nakshatra")
    let sampleYoga = Yoga(number: 1, name: "Vishkumbha", tamilName: "விஷ்கும்பா", completionTime: nil, percentage: 40.0, significance: "First yoga")
    let sampleKarana = Karana(number: 1, name: "Bava", tamilName: "பவ", completionTime: nil, percentage: 30.0, type: .movable)
    let sampleLunarMonth = LunarMonth(number: 10, name: "Pausha", tamilName: "தை", fullName: "Pausha Masa", isAdhika: false, isKshaya: false)
    let sampleSeason = Season(number: 6, name: "Shishir", tamilName: "குளிர் காலம்", description: "Winter season")

    let samplePanchang = DailyPanchang(
        date: Date(),
        location: sampleLocation,
        tamilDate: sampleTamilDate,
        sunTimes: sampleSunTimes,
        moonTimes: nil,
        tithi: sampleTithi,
        nakshatra: sampleNakshatra,
        yoga: sampleYoga,
        karana: sampleKarana,
        lunarMonth: sampleLunarMonth,
        season: sampleSeason,
        muhurat: [],
        inauspiciousTimes: [],
        festivals: [],
        significance: "This is a sample significance for preview purposes."
    )

    DayDetailView(panchang: samplePanchang)
}
