import SwiftUI
import Foundation

struct MonthlyCalendarView: View {
    @StateObject private var panchangService = RealTimePanchangService.shared
    @State private var selectedDate = Date()
    @State private var currentMonth = Date()
    @State private var showingDayDetail = false
    @State private var selectedPanchang: DailyPanchang?
    
    private let calendar = Calendar.current
    private let dateFormatter = DateFormatter()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Month Header
                monthHeader
                
                // Weekday Headers
                weekdayHeaders
                
                // Calendar Grid
                calendarGrid
                
                // Today's Summary
                todaySummary
                
                Spacer()
            }
            .navigationTitle("Tamil Calendar")
            .navigationBarTitleDisplayMode(.inline)
            .background(Color.systemBackground)
        }
        .sheet(isPresented: $showingDayDetail) {
            if let panchang = selectedPanchang {
                DayDetailView(panchang: panchang)
            }
        }
        .onAppear {
            Task {
                await panchangService.loadTodayPanchang()
                await panchangService.loadMonthPanchang(for: currentMonth)
            }
        }
        .onChange(of: currentMonth) { _, newMonth in
            Task {
                await panchangService.loadMonthPanchang(for: newMonth)
            }
        }
    }
    
    // MARK: - Month Header
    
    private var monthHeader: some View {
        HStack {
            Button(action: previousMonth) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.niraPrimary)
                    .padding(.horizontal, 8)
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Text(monthName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(tamilMonthName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(action: nextMonth) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.niraPrimary)
                    .padding(.horizontal, 8)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    // MARK: - Weekday Headers
    
    private var weekdayHeaders: some View {
        HStack(spacing: 0) {
            ForEach(Array(weekdaySymbols.enumerated()), id: \.offset) { index, weekday in
                VStack(spacing: 4) {
                    Text(weekday.english)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(weekday.tamil)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // MARK: - Calendar Grid
    
    private var calendarGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 2), count: 7), spacing: 2) {
            ForEach(monthDates, id: \.self) { date in
                CalendarDayCell(
                    date: date,
                    panchang: getPanchang(for: date),
                    isSelected: calendar.isDate(date, inSameDayAs: selectedDate),
                    isToday: Calendar.current.isDateInToday(date),
                    isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                )
                .onTapGesture {
                    selectedDate = date
                    selectedPanchang = getPanchang(for: date)
                    showingDayDetail = true
                }
            }
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Today's Summary
    
    private var todaySummary: some View {
        VStack(spacing: 12) {
            if let todayPanchang = panchangService.todayPanchang {
                TodayPanchangSummary(panchang: todayPanchang)
            } else if panchangService.isLoading {
                ProgressView("Loading today's panchang...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                Text("Tap any date to view details")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // MARK: - Helper Methods
    
    private func previousMonth() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) ?? currentMonth
        }
    }
    
    private func nextMonth() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) ?? currentMonth
        }
    }
    
    private func getPanchang(for date: Date) -> DailyPanchang? {
        return panchangService.currentMonthPanchang.first { panchang in
            calendar.isDate(panchang.date, inSameDayAs: date)
        }
    }
    
    // MARK: - Computed Properties
    
    private var monthName: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter.string(from: currentMonth)
    }
    
    private var tamilMonthName: String {
        let month = calendar.component(.month, from: currentMonth)
        let tamilMonths = [
            "தை", "மாசி", "பங்குனி", "சித்திரை", "வைகாசி", "ஆனி",
            "ஆடி", "ஆவணி", "புரட்டாசி", "ஐப்பசி", "கார்த்திகை", "மார்கழி"
        ]
        return tamilMonths[(month - 1) % 12]
    }
    
    private var weekdaySymbols: [(english: String, tamil: String)] {
        return [
            ("Sun", "ஞா"), ("Mon", "தி"), ("Tue", "செ"), ("Wed", "பு"),
            ("Thu", "வி"), ("Fri", "வெ"), ("Sat", "ச")
        ]
    }
    
    private var monthDates: [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else { return [] }
        
        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)
        
        // Calculate start date (including previous month's trailing days)
        let startDate = calendar.date(byAdding: .day, value: -(firstWeekday - 1), to: firstOfMonth) ?? firstOfMonth
        
        // Generate 42 days (6 weeks) to fill the calendar grid
        var dates: [Date] = []
        for i in 0..<42 {
            if let date = calendar.date(byAdding: .day, value: i, to: startDate) {
                dates.append(date)
            }
        }
        
        return dates
    }
}

// MARK: - Calendar Day Cell

struct CalendarDayCell: View {
    let date: Date
    let panchang: DailyPanchang?
    let isSelected: Bool
    let isToday: Bool
    let isCurrentMonth: Bool
    
    private let calendar = Calendar.current
    
    var body: some View {
        VStack(spacing: 2) {
            // Day number
            Text("\(calendar.component(.day, from: date))")
                .font(.system(size: 16, weight: isToday ? .bold : .medium))
                .foregroundColor(dayNumberColor)
            
            // Tithi indicator
            if let panchang = panchang, isCurrentMonth {
                Text(panchang.tithi.tamilName.prefix(2))
                    .font(.system(size: 8))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            // Festival indicator
            if let panchang = panchang, !panchang.festivals.isEmpty, isCurrentMonth {
                Circle()
                    .fill(festivalColor)
                    .frame(width: 4, height: 4)
            }
        }
        .frame(width: 44, height: 44)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(backgroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(borderColor, lineWidth: isSelected ? 2 : 0)
                )
        )
    }
    
    private var dayNumberColor: Color {
        if !isCurrentMonth {
            return .secondary.opacity(0.5)
        } else if isToday {
            return .white
        } else if isSelected {
            return .niraPrimary
        } else {
            return .primary
        }
    }
    
    private var backgroundColor: Color {
        if isToday {
            return .niraPrimary
        } else if isSelected {
            return .niraPrimary.opacity(0.1)
        } else {
            return .clear
        }
    }
    
    private var borderColor: Color {
        return isSelected ? .niraPrimary : .clear
    }
    
    private var festivalColor: Color {
        guard let panchang = panchang, !panchang.festivals.isEmpty else { return .clear }
        
        let festival = panchang.festivals.first!
        switch festival.religion.lowercased() {
        case "hindu":
            return .orange
        case "muslim":
            return .green
        case "christian":
            return .blue
        default:
            return .purple
        }
    }
}

// MARK: - Today's Panchang Summary

struct TodayPanchangSummary: View {
    let panchang: DailyPanchang
    
    var body: some View {
        VStack(spacing: 8) {
            Text("Today's Panchang")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 16) {
                PanchangItem(
                    title: "Tithi",
                    value: panchang.tithi.name,
                    tamilValue: panchang.tithi.tamilName
                )
                
                Divider()
                    .frame(height: 30)
                
                PanchangItem(
                    title: "Nakshatra",
                    value: panchang.nakshatra.name,
                    tamilValue: panchang.nakshatra.tamilName
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

struct PanchangItem: View {
    let title: String
    let value: String
    let tamilValue: String
    
    var body: some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text(tamilValue)
                .font(.caption)
                .foregroundColor(.niraPrimary)
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    MonthlyCalendarView()
}
