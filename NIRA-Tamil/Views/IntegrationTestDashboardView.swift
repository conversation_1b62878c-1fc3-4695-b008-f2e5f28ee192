//
//  IntegrationTestDashboardView.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import SwiftUI

struct IntegrationTestDashboardView: View {
    @StateObject private var testService = IntegrationTestService.shared
    @State private var showingTestDetails = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "checkmark.seal.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.niraPrimary)
                        
                        Text("Integration Tests")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Comprehensive testing of all Supabase integrations")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    
                    // Test Status Overview
                    TestStatusCard(
                        status: testService.overallStatus,
                        isRunning: testService.isRunning,
                        progress: testService.progress,
                        currentTest: testService.currentTest
                    )
                    
                    // Test Summary
                    if !testService.testResults.isEmpty {
                        TestSummaryCard(summary: testService.getTestSummary())
                    }
                    
                    // Run Tests Button
                    Button(action: {
                        Task {
                            await testService.runAllTests()
                        }
                    }) {
                        HStack {
                            if testService.isRunning {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "play.circle.fill")
                            }
                            
                            Text(testService.isRunning ? "Running Tests..." : "Run All Tests")
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(testService.isRunning ? Color.gray : Color.niraPrimary)
                        )
                    }
                    .disabled(testService.isRunning)
                    .padding(.horizontal)
                    
                    // Test Categories
                    if !testService.testResults.isEmpty {
                        TestCategoriesSection(testResults: testService.testResults)
                    }
                    
                    // Quick Actions
                    TestQuickActionsSection(
                        onClearResults: {
                            testService.clearResults()
                        },
                        onViewDetails: {
                            showingTestDetails = true
                        }
                    )
                    
                    Spacer(minLength: 50)
                }
            }
            .navigationTitle("Integration Tests")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingTestDetails) {
            TestDetailsView(testResults: testService.testResults)
        }
    }
}

struct TestStatusCard: View {
    let status: TestStatus
    let isRunning: Bool
    let progress: Double
    let currentTest: String
    
    var body: some View {
        VStack(spacing: 16) {
            // Status Icon and Text
            HStack {
                Image(systemName: statusIcon)
                    .font(.title2)
                    .foregroundColor(statusColor)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(statusText)
                        .font(.headline)
                        .foregroundColor(statusColor)
                    
                    if isRunning && !currentTest.isEmpty {
                        Text("Running: \(currentTest)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            
            // Progress Bar
            if isRunning {
                VStack(spacing: 8) {
                    ProgressView(value: progress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
                    
                    Text("\(Int(progress * 100))% Complete")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    private var statusIcon: String {
        switch status {
        case .notStarted:
            return "circle"
        case .running:
            return "arrow.clockwise"
        case .passed:
            return "checkmark.circle.fill"
        case .failed:
            return "xmark.circle.fill"
        case .partiallyPassed:
            return "exclamationmark.triangle.fill"
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .notStarted:
            return .gray
        case .running:
            return .blue
        case .passed:
            return .green
        case .failed:
            return .red
        case .partiallyPassed:
            return .orange
        }
    }
    
    private var statusText: String {
        switch status {
        case .notStarted:
            return "Ready to Run Tests"
        case .running:
            return "Running Tests..."
        case .passed:
            return "All Tests Passed"
        case .failed:
            return "Tests Failed"
        case .partiallyPassed:
            return "Some Tests Failed"
        }
    }
}

struct TestSummaryCard: View {
    let summary: TestSummary
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Test Summary")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                SummaryStatCard(
                    title: "Total Tests",
                    value: "\(summary.totalTests)",
                    icon: "list.bullet",
                    color: .blue
                )
                
                SummaryStatCard(
                    title: "Passed",
                    value: "\(summary.passedTests)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
                
                SummaryStatCard(
                    title: "Failed",
                    value: "\(summary.failedTests)",
                    icon: "xmark.circle.fill",
                    color: .red
                )
                
                SummaryStatCard(
                    title: "Avg Duration",
                    value: String(format: "%.2fs", summary.averageDuration),
                    icon: "clock.fill",
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct TestCategoriesSection: View {
    let testResults: [TestResult]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Test Categories")
                .font(.headline)
                .padding(.horizontal)
            
            let groupedResults = Dictionary(grouping: testResults) { $0.suiteName }
            
            ForEach(Array(groupedResults.keys.sorted()), id: \.self) { suiteName in
                TestCategoryCard(
                    suiteName: suiteName,
                    results: groupedResults[suiteName] ?? []
                )
            }
            .padding(.horizontal)
        }
    }
}

struct TestCategoryCard: View {
    let suiteName: String
    let results: [TestResult]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(suiteName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(passedCount)/\(results.count)")
                    .font(.caption)
                    .foregroundColor(passedCount == results.count ? .green : .red)
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(results) { result in
                    TestResultRow(result: result)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    private var passedCount: Int {
        results.filter { $0.status == .passed }.count
    }
}

struct TestResultRow: View {
    let result: TestResult
    
    var body: some View {
        HStack {
            Image(systemName: result.status == .passed ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(result.status == .passed ? .green : .red)
                .font(.caption)
            
            Text(result.testName)
                .font(.caption)
                .lineLimit(1)
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct TestQuickActionsSection: View {
    let onClearResults: () -> Void
    let onViewDetails: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .padding(.horizontal)
            
            HStack(spacing: 12) {
                Button(action: onClearResults) {
                    HStack {
                        Image(systemName: "trash")
                        Text("Clear Results")
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.red)
                    .cornerRadius(8)
                }
                
                Button(action: onViewDetails) {
                    HStack {
                        Image(systemName: "info.circle")
                        Text("View Details")
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.niraPrimary)
                    .cornerRadius(8)
                }
            }
            .padding(.horizontal)
        }
    }
}

struct SummaryStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.white.opacity(0.5))
        .cornerRadius(8)
    }
}

struct TestDetailsView: View {
    let testResults: [TestResult]
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    ForEach(testResults) { result in
                        TestDetailCard(result: result)
                    }
                }
                .padding()
            }
            .navigationTitle("Test Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") { dismiss() })
        }
    }
}

struct TestDetailCard: View {
    let result: TestResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: result.status == .passed ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(result.status == .passed ? .green : .red)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(result.suiteName): \(result.testName)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text("Duration: \(String(format: "%.3f", result.duration))s")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            if !result.message.isEmpty {
                Text(result.message)
                    .font(.caption)
                    .foregroundColor(result.status == .passed ? .green : .red)
                    .padding(.top, 4)
            }
            
            Text("Executed: \(DateFormatter.localizedString(from: result.timestamp, dateStyle: .none, timeStyle: .medium))")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    IntegrationTestDashboardView()
}
