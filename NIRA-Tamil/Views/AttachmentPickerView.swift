import SwiftUI
import PhotosUI
import UniformTypeIdentifiers

// MARK: - Attachment Picker View

struct AttachmentPickerView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedItems: [PhotosPickerItem] = []
    @State private var showingDocumentPicker = false
    @State private var showingCamera = false
    @State private var showingAudioRecorder = false
    
    let onAttachmentSelected: (MessageAttachment) -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Attachment Options
                ScrollView {
                    LazyVStack(spacing: 16) {
                        attachmentOptionsSection
                        
                        // Recent Photos Section
                        if !selectedItems.isEmpty {
                            recentPhotosSection
                        }
                    }
                    .padding()
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationBarHidden(true)
        }
        .photosPicker(
            isPresented: .constant(false),
            selection: $selectedItems,
            maxSelectionCount: 1,
            matching: .any(of: [.images, .videos])
        )
        .sheet(isPresented: $showingDocumentPicker) {
            DocumentPickerView { url in
                handleDocumentSelection(url)
            }
        }
        .sheet(isPresented: $showingCamera) {
            AttachmentCameraView { image in
                handleCameraCapture(image)
            }
        }
        .sheet(isPresented: $showingAudioRecorder) {
            AudioRecorderView { audioURL in
                handleAudioRecording(audioURL)
            }
        }
        .onChange(of: selectedItems) { _, items in
            handlePhotoSelection(items)
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(.niraPrimary)
                
                Spacer()
                
                Text("Add Attachment")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                // Placeholder for balance
                Text("Cancel")
                    .opacity(0)
            }
            .padding(.horizontal)
            .padding(.top)
            
            Text("Share documents, images, audio, or videos with your AI tutor")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - Attachment Options Section
    
    private var attachmentOptionsSection: some View {
        VStack(spacing: 12) {
            Text("Choose Attachment Type")
                .font(.title3)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                // Camera
                AttachmentOptionCard(
                    icon: "camera.fill",
                    title: "Camera",
                    subtitle: "Take a photo",
                    color: .blue
                ) {
                    showingCamera = true
                }
                
                // Photo Library
                AttachmentOptionCard(
                    icon: "photo.on.rectangle",
                    title: "Photos",
                    subtitle: "Choose from library",
                    color: .green
                ) {
                    // Trigger photos picker
                    selectedItems = []
                }
                
                // Documents
                AttachmentOptionCard(
                    icon: "doc.fill",
                    title: "Documents",
                    subtitle: "PDF, Word, Text files",
                    color: .orange
                ) {
                    showingDocumentPicker = true
                }
                
                // Audio Recording
                AttachmentOptionCard(
                    icon: "mic.fill",
                    title: "Audio",
                    subtitle: "Record voice message",
                    color: .red
                ) {
                    showingAudioRecorder = true
                }
            }
        }
    }
    
    // MARK: - Recent Photos Section
    
    private var recentPhotosSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Recent Photos")
                    .font(.title3)
                    .fontWeight(.semibold)
                
                Spacer()
                
                PhotosPicker(
                    selection: $selectedItems,
                    maxSelectionCount: 1,
                    matching: .any(of: [.images, .videos])
                ) {
                    Text("Browse All")
                        .font(.subheadline)
                        .foregroundColor(.niraPrimary)
                }
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack(spacing: 12) {
                    ForEach(selectedItems, id: \.itemIdentifier) { item in
                        PhotoThumbnailView(item: item) {
                            // Handle selection
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func handlePhotoSelection(_ items: [PhotosPickerItem]) {
        guard let item = items.first else { return }
        
        Task {
            if let data = try? await item.loadTransferable(type: Data.self) {
                let attachment = MessageAttachment(
                    type: .image,
                    url: "data:image/jpeg;base64,\(data.base64EncodedString())",
                    name: "Photo",
                    size: Int64(data.count)
                )
                
                await MainActor.run {
                    onAttachmentSelected(attachment)
                    presentationMode.wrappedValue.dismiss()
                }
            }
        }
    }
    
    private func handleDocumentSelection(_ url: URL) {
        do {
            let data = try Data(contentsOf: url)
            let attachment = MessageAttachment(
                type: .document,
                url: url.absoluteString,
                name: url.lastPathComponent,
                size: Int64(data.count)
            )
            
            onAttachmentSelected(attachment)
            presentationMode.wrappedValue.dismiss()
        } catch {
            print("Error loading document: \(error)")
        }
    }
    
    private func handleCameraCapture(_ image: UIImage) {
        guard let data = image.jpegData(compressionQuality: 0.8) else { return }
        
        let attachment = MessageAttachment(
            type: .image,
            url: "data:image/jpeg;base64,\(data.base64EncodedString())",
            name: "Camera Photo",
            size: Int64(data.count)
        )
        
        onAttachmentSelected(attachment)
        presentationMode.wrappedValue.dismiss()
    }
    
    private func handleAudioRecording(_ url: URL) {
        do {
            let data = try Data(contentsOf: url)
            let attachment = MessageAttachment(
                type: .audio,
                url: url.absoluteString,
                name: "Voice Message",
                size: Int64(data.count)
            )
            
            onAttachmentSelected(attachment)
            presentationMode.wrappedValue.dismiss()
        } catch {
            print("Error loading audio: \(error)")
        }
    }
}

// MARK: - Attachment Option Card

struct AttachmentOptionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(color.opacity(0.1))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(color)
                }
                
                VStack(spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Photo Thumbnail View

struct PhotoThumbnailView: View {
    let item: PhotosPickerItem
    let onTap: () -> Void
    
    @State private var image: UIImage?
    
    var body: some View {
        Button(action: onTap) {
            Group {
                if let image = image {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } else {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                        )
                }
            }
            .frame(width: 80, height: 80)
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        Task {
            if let data = try? await item.loadTransferable(type: Data.self),
               let uiImage = UIImage(data: data) {
                await MainActor.run {
                    self.image = uiImage
                }
            }
        }
    }
}

// MARK: - Document Picker

struct DocumentPickerView: UIViewControllerRepresentable {
    let onDocumentSelected: (URL) -> Void
    
    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: [
            .pdf,
            .plainText,
            .rtf,
            .item,
            UTType(filenameExtension: "doc") ?? .data,
            UTType(filenameExtension: "docx") ?? .data
        ])
        
        picker.delegate = context.coordinator
        picker.allowsMultipleSelection = false
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPickerView
        
        init(_ parent: DocumentPickerView) {
            self.parent = parent
        }
        
        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            guard let url = urls.first else { return }
            parent.onDocumentSelected(url)
        }
    }
}

// MARK: - Camera View

struct AttachmentCameraView: UIViewControllerRepresentable {
    let onImageCaptured: (UIImage) -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: AttachmentCameraView
        
        init(_ parent: AttachmentCameraView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImageCaptured(image)
            }
            picker.dismiss(animated: true)
        }
    }
}

// MARK: - Audio Recorder View

struct AudioRecorderView: View {
    let onAudioRecorded: (URL) -> Void
    @Environment(\.presentationMode) var presentationMode
    
    @State private var isRecording = false
    @State private var recordingTime: TimeInterval = 0
    @State private var timer: Timer?
    @State private var audioRecorder: AVAudioRecorder?
    @State private var recordingURL: URL?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 40) {
                Spacer()
                
                // Recording Visualization
                VStack(spacing: 20) {
                    ZStack {
                        Circle()
                            .fill(isRecording ? Color.red.opacity(0.2) : Color.gray.opacity(0.2))
                            .frame(width: 200, height: 200)
                            .scaleEffect(isRecording ? 1.1 : 1.0)
                            .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isRecording)
                        
                        Image(systemName: "mic.fill")
                            .font(.system(size: 60))
                            .foregroundColor(isRecording ? .red : .gray)
                    }
                    
                    Text(formatTime(recordingTime))
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(isRecording ? .red : .primary)
                }
                
                // Instructions
                Text(isRecording ? "Recording... Tap to stop" : "Tap to start recording")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // Controls
                HStack(spacing: 60) {
                    Button("Cancel") {
                        stopRecording()
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.secondary)
                    
                    Button(action: toggleRecording) {
                        Circle()
                            .fill(isRecording ? Color.red : Color.niraPrimary)
                            .frame(width: 80, height: 80)
                            .overlay(
                                Image(systemName: isRecording ? "stop.fill" : "mic.fill")
                                    .font(.title)
                                    .foregroundColor(.white)
                            )
                    }
                    
                    Button("Done") {
                        if let url = recordingURL {
                            onAudioRecorded(url)
                        }
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.niraPrimary)
                    .disabled(recordingURL == nil)
                }
            }
            .padding()
            .navigationTitle("Record Audio")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            setupAudioSession()
        }
    }
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default)
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioURL = documentsPath.appendingPathComponent("recording_\(Date().timeIntervalSince1970).m4a")
        
        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 2,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]
        
        do {
            audioRecorder = try AVAudioRecorder(url: audioURL, settings: settings)
            audioRecorder?.record()
            
            recordingURL = audioURL
            isRecording = true
            recordingTime = 0
            
            timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
                recordingTime += 0.1
            }
        } catch {
            print("Failed to start recording: \(error)")
        }
    }
    
    private func stopRecording() {
        audioRecorder?.stop()
        timer?.invalidate()
        timer = nil
        isRecording = false
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        let milliseconds = Int((time.truncatingRemainder(dividingBy: 1)) * 10)
        return String(format: "%02d:%02d.%01d", minutes, seconds, milliseconds)
    }
}

// MARK: - Preview

struct AttachmentPickerView_Previews: PreviewProvider {
    static var previews: some View {
        AttachmentPickerView { attachment in
            print("Selected attachment: \(attachment.name)")
        }
    }
} 