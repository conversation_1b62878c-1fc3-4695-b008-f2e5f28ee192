//
//  ARCulturalImmersionView.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  iOS 18 AR Cultural Immersion Experience
//

import SwiftUI
import RealityKit
import ARKit

struct ARCulturalImmersionView: View {
    @StateObject private var realityService = NIRARealityKitService.shared
    @State private var selectedEnvironment: CulturalEnvironment?
    @State private var showingEnvironmentPicker = false
    @State private var isARActive = false
    @State private var showingVocabularyCard = false
    @State private var selectedObject: InteractiveObject?
    @State private var guideExplanation: String = ""
    @State private var showingGuideDialog = false
    
    var body: some View {
        ZStack {
            if isARActive && realityService.isARSupported {
                // AR View
                NIRAARView()
                    .ignoresSafeArea()
                    .onReceive(NotificationCenter.default.publisher(for: .showARVocabulary)) { notification in
                        if let object = notification.userInfo?["object"] as? InteractiveObject {
                            selectedObject = object
                            showingVocabularyCard = true
                        }
                    }
                    .onReceive(NotificationCenter.default.publisher(for: .guideExplanation)) { notification in
                        if let explanation = notification.userInfo?["explanation"] as? String {
                            guideExplanation = explanation
                            showingGuideDialog = true
                        }
                    }
                
                // AR Overlay UI
                VStack {
                    // Top Controls
                    HStack {
                        Button(action: {
                            realityService.stopARSession()
                            isARActive = false
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                        
                        Spacer()
                        
                        if let environment = selectedEnvironment {
                            VStack(alignment: .trailing, spacing: 4) {
                                Text(environment.name)
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                
                                Text(environment.difficulty.rawValue.capitalized)
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 2)
                                    .background(Color.blue.opacity(0.8))
                                    .foregroundColor(.white)
                                    .cornerRadius(4)
                            }
                            .padding()
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(12)
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            showingEnvironmentPicker = true
                        }) {
                            Image(systemName: "globe.asia.australia.fill")
                                .font(.title2)
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                    }
                    .padding()
                    
                    Spacer()
                    
                    // Bottom Controls
                    VStack(spacing: 16) {
                        // Interactive Objects List
                        if !realityService.interactiveObjects.isEmpty {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(realityService.interactiveObjects) { object in
                                        InteractiveObjectButton(object: object) {
                                            Task {
                                                await realityService.handleObjectInteraction(object.id)
                                            }
                                        }
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                        
                        // Cultural Guide Info
                        if let guide = realityService.culturalGuide {
                            CulturalGuideCard(guide: guide)
                        }
                    }
                    .padding()
                }
                
            } else {
                // Environment Selection View
                EnvironmentSelectionView(
                    environments: realityService.availableEnvironments,
                    selectedEnvironment: $selectedEnvironment,
                    onStartAR: startARExperience
                )
            }
        }
        .sheet(isPresented: $showingEnvironmentPicker) {
            EnvironmentPickerSheet(
                environments: realityService.availableEnvironments,
                selectedEnvironment: $selectedEnvironment,
                onEnvironmentSelected: loadSelectedEnvironment
            )
        }
        .sheet(isPresented: $showingVocabularyCard) {
            if let object = selectedObject {
                VocabularyCardView(object: object)
            }
        }
        .alert("Cultural Guide", isPresented: $showingGuideDialog) {
            Button("Continue Learning") { }
        } message: {
            Text(guideExplanation)
        }
        .navigationTitle("Cultural Immersion")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            checkARSupport()
        }
    }
    
    private func checkARSupport() {
        if !realityService.isARSupported {
            // Show AR not supported message
        }
    }
    
    private func startARExperience() {
        guard let environment = selectedEnvironment else { return }
        
        Task {
            do {
                // This would be handled by the NIRAARView
                isARActive = true
                try await realityService.loadEnvironment(environment)
            } catch {
                print("Failed to start AR experience: \(error)")
                isARActive = false
            }
        }
    }
    
    private func loadSelectedEnvironment() {
        guard let environment = selectedEnvironment else { return }
        
        Task {
            do {
                try await realityService.loadEnvironment(environment)
            } catch {
                print("Failed to load environment: \(error)")
            }
        }
    }
}

// MARK: - Environment Selection View

struct EnvironmentSelectionView: View {
    let environments: [CulturalEnvironment]
    @Binding var selectedEnvironment: CulturalEnvironment?
    let onStartAR: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 12) {
                Text("🏛️")
                    .font(.system(size: 60))
                
                Text("Tamil Cultural Immersion")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text("Explore Tamil culture through immersive AR experiences")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding()
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(environments) { environment in
                    EnvironmentCard(
                        environment: environment,
                        isSelected: selectedEnvironment?.id == environment.id
                    ) {
                        selectedEnvironment = environment
                    }
                }
            }
            .padding(.horizontal)
            
            if selectedEnvironment != nil {
                Button(action: onStartAR) {
                    HStack {
                        Image(systemName: "arkit")
                            .font(.title3)
                        
                        Text("Start AR Experience")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                }
                .padding(.horizontal)
            }
            
            Spacer()
        }
        .background(Color(.systemBackground))
    }
}

// MARK: - Environment Card

struct EnvironmentCard: View {
    let environment: CulturalEnvironment
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                Text(environmentEmoji(for: environment.id))
                    .font(.system(size: 40))
                
                VStack(spacing: 4) {
                    Text(environment.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                    
                    Text(environment.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                }
                
                HStack {
                    Text(environment.difficulty.rawValue.capitalized)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(difficultyColor(for: environment.difficulty).opacity(0.2))
                        .foregroundColor(difficultyColor(for: environment.difficulty))
                        .cornerRadius(4)
                    
                    Spacer()
                    
                    Text("\(environment.vocabulary.count) words")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func environmentEmoji(for id: String) -> String {
        switch id {
        case "tamil-temple": return "🏛️"
        case "market-scene": return "🏪"
        case "traditional-home": return "🏠"
        case "festival-celebration": return "🎊"
        default: return "🌍"
        }
    }
    
    private func difficultyColor(for difficulty: CulturalEnvironment.Difficulty) -> Color {
        switch difficulty {
        case .beginner: return .green
        case .intermediate: return .orange
        case .advanced: return .red
        }
    }
}

// MARK: - Interactive Object Button

struct InteractiveObjectButton: View {
    let object: InteractiveObject
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 6) {
                Text(objectEmoji(for: object.name))
                    .font(.title2)
                
                Text(object.tamilName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(object.name)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.white.opacity(0.9))
            .cornerRadius(8)
            .shadow(radius: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func objectEmoji(for name: String) -> String {
        switch name.lowercased() {
        case "bell": return "🔔"
        case "statue": return "🗿"
        case "offerings": return "🌸"
        case "inscriptions": return "📜"
        case "fruits": return "🍎"
        case "vegetables": return "🥕"
        case "spices": return "🌶️"
        case "vendor": return "👨‍💼"
        case "kitchen": return "🍳"
        case "prayer room": return "🕉️"
        case "courtyard": return "🌿"
        case "family": return "👨‍👩‍👧‍👦"
        case "decorations": return "🎨"
        case "music": return "🎵"
        case "dance": return "💃"
        case "food": return "🍛"
        default: return "📍"
        }
    }
}

// MARK: - Cultural Guide Card

struct CulturalGuideCard: View {
    let guide: CulturalGuideCharacter
    
    var body: some View {
        HStack(spacing: 12) {
            Text("👩🏽‍🏫")
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(guide.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text("Cultural Guide • \(guide.specialization)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
            
            Button(action: {
                // Trigger guide interaction
            }) {
                Image(systemName: "bubble.left.and.bubble.right.fill")
                    .font(.title3)
                    .foregroundColor(.white)
            }
        }
        .padding()
        .background(Color.black.opacity(0.7))
        .cornerRadius(12)
    }
}

// MARK: - Vocabulary Card View

struct VocabularyCardView: View {
    let object: InteractiveObject
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text(objectEmoji(for: object.name))
                    .font(.system(size: 80))
                
                VStack(spacing: 12) {
                    Text(object.tamilName)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(object.name)
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    Text("[\(object.tamilName)]") // Pronunciation would be added
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        .italic()
                }
                
                VStack(spacing: 16) {
                    Button(action: {
                        // Play pronunciation
                    }) {
                        HStack {
                            Image(systemName: "speaker.wave.2.fill")
                            Text("Play Pronunciation")
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                    
                    Button(action: {
                        // Practice pronunciation
                    }) {
                        HStack {
                            Image(systemName: "mic.fill")
                            Text("Practice Speaking")
                        }
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Vocabulary")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func objectEmoji(for name: String) -> String {
        switch name.lowercased() {
        case "bell": return "🔔"
        case "statue": return "🗿"
        case "offerings": return "🌸"
        case "inscriptions": return "📜"
        case "fruits": return "🍎"
        case "vegetables": return "🥕"
        case "spices": return "🌶️"
        case "vendor": return "👨‍💼"
        case "kitchen": return "🍳"
        case "prayer room": return "🕉️"
        case "courtyard": return "🌿"
        case "family": return "👨‍👩‍👧‍👦"
        case "decorations": return "🎨"
        case "music": return "🎵"
        case "dance": return "💃"
        case "food": return "🍛"
        default: return "📍"
        }
    }
}

// MARK: - Environment Picker Sheet

struct EnvironmentPickerSheet: View {
    let environments: [CulturalEnvironment]
    @Binding var selectedEnvironment: CulturalEnvironment?
    let onEnvironmentSelected: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List(environments) { environment in
                Button(action: {
                    selectedEnvironment = environment
                    onEnvironmentSelected()
                    dismiss()
                }) {
                    HStack {
                        Text(environmentEmoji(for: environment.id))
                            .font(.title2)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(environment.name)
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text(environment.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if selectedEnvironment?.id == environment.id {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }
                    .padding(.vertical, 4)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .navigationTitle("Choose Environment")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func environmentEmoji(for id: String) -> String {
        switch id {
        case "tamil-temple": return "🏛️"
        case "market-scene": return "🏪"
        case "traditional-home": return "🏠"
        case "festival-celebration": return "🎊"
        default: return "🌍"
        }
    }
}

#Preview {
    ARCulturalImmersionView()
}
