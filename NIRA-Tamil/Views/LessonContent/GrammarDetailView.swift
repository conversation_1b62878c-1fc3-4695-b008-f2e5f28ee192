import SwiftUI

struct GrammarDetailView: View {
    let grammarPoints: [LessonGrammarPoint]
    @Environment(\.dismiss) private var dismiss
    @State private var currentIndex = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Main content
                if !grammarPoints.isEmpty {
                    TabView(selection: $currentIndex) {
                        ForEach(Array(grammarPoints.enumerated()), id: \.offset) { index, grammar in
                            GrammarDetailCard(grammarPoint: grammar)
                                .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                    .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                } else {
                    emptyStateView
                }
                
                // Bottom controls
                bottomControls
            }
        }
        .navigationBarHidden(true)
    }
    
    private var headerView: some View {
        VStack(spacing: 0) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text("Grammar")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                // Placeholder for symmetry
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                    Text("Back")
                        .font(.system(size: 16, weight: .medium))
                }
                .opacity(0)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            .padding(.bottom, 20)
            
            if !grammarPoints.isEmpty {
                Text("\(currentIndex + 1) of \(grammarPoints.count)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.bottom, 20)
            }
        }
        .background(Color(.systemBackground))
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "textformat.abc")
                .font(.system(size: 60))
                .foregroundColor(.blue.opacity(0.3))
            
            Text("No Grammar Points")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.primary)
            
            Text("Grammar content will be available soon")
                .font(.system(size: 16))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    private var bottomControls: some View {
        VStack(spacing: 16) {
            if !grammarPoints.isEmpty {
                HStack(spacing: 20) {
                    Button(action: {
                        withAnimation {
                            currentIndex = max(0, currentIndex - 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                            Text("Previous")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex > 0 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex <= 0)
                    
                    Button(action: {
                        withAnimation {
                            currentIndex = min(grammarPoints.count - 1, currentIndex + 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Text("Next")
                            Image(systemName: "chevron.right")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex < grammarPoints.count - 1 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex >= grammarPoints.count - 1)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
    }
}

struct GrammarDetailCard: View {
    let grammarPoint: LessonGrammarPoint

    // Computed properties to break down complex expressions
    private var blueGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.blue.opacity(0.1),
                Color.blue.opacity(0.05)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var explanationBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(blueGradient)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.blue.opacity(0.2), lineWidth: 1)
            )
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                explanationSection
                examplesSection
                tipsSection
                mistakesSection
                Spacer()
            }
            .padding(20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var headerSection: some View {
        VStack(spacing: 12) {
            Text(grammarPoint.rule)
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.blue)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)

            if let ruleTamil = grammarPoint.ruleTamil {
                VStack(spacing: 6) {
                    HStack(spacing: 12) {
                        Text(ruleTamil)
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.green)
                            .multilineTextAlignment(.center)

                        // Audio button for Tamil title pronunciation (using TTS)
                        EnhancedAudioButton(
                            text: ruleTamil,
                            audioURL: nil, // Will use TTS
                            context: "grammar_title",
                            size: 32,
                            color: .green
                        )
                    }
                    .padding(.horizontal, 20)

                    if let romanization = grammarPoint.ruleRomanization {
                        Text("[\(romanization)]")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.orange)
                            .italic()
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                }
            }

            difficultyIndicator
        }
    }

    private var difficultyIndicator: some View {
        HStack(spacing: 4) {
            Text("Difficulty Level")
                .font(.caption)
                .foregroundColor(.secondary)

            ForEach(1...5, id: \.self) { level in
                Circle()
                    .fill(level <= grammarPoint.difficultyLevel ? Color.blue : Color.gray.opacity(0.3))
                    .frame(width: 8, height: 8)
            }

            Text("Level \(grammarPoint.difficultyLevel)")
                .font(.caption)
                .foregroundColor(.blue)
                .fontWeight(.medium)
        }
    }

    private var explanationSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "book.fill")
                    .foregroundColor(.blue)
                Text("Grammar Rule")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                    .textCase(.uppercase)
                Spacer()
            }

            Text(grammarPoint.explanation)
                .font(.system(size: 18))
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
        }
        .padding(16)
        .background(explanationBackground)
    }

    @ViewBuilder
    private var examplesSection: some View {
        if !grammarPoint.examples.isEmpty {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.orange)
                    Text("Examples")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                        .textCase(.uppercase)
                    Spacer()
                }

                VStack(spacing: 16) {
                    ForEach(Array(grammarPoint.examples.enumerated()), id: \.offset) { index, example in
                        exampleCard(example: example, index: index)
                    }
                }
            }
            .padding(16)
            .background(orangeGradientBackground)
        }
    }

    private func exampleCard(example: GrammarExample, index: Int) -> some View {
        VStack(spacing: 12) {
            HStack {
                Text("\(index + 1).")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.orange)
                    .frame(width: 25, alignment: .leading)

                VStack(alignment: .leading, spacing: 8) {
                    Text(example.english)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)

                    Text(example.tamil)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.green)

                    Text("[\(example.romanization)]")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.blue)
                        .italic()
                }

                Spacer()

                // Always show audio button - will use TTS if no URL provided
                EnhancedAudioButton(
                    text: example.tamil,
                    audioURL: example.audioURL, // May be nil, will fallback to TTS
                    context: "grammar_example",
                    size: 36,
                    color: .orange
                )
            }
        }
        .padding(12)
        .background(exampleCardBackground)
    }

    // Additional computed properties for backgrounds
    private var orangeGradientBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.orange.opacity(0.1),
                        Color.orange.opacity(0.05)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.orange.opacity(0.2), lineWidth: 1)
            )
    }

    private var exampleCardBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.orange.opacity(0.05))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.orange.opacity(0.2), lineWidth: 1)
            )
    }

    @ViewBuilder
    private var tipsSection: some View {
        if let tips = grammarPoint.tips, !tips.isEmpty {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)
                    Text("💡 Helpful Tips")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                        .textCase(.uppercase)
                    Spacer()
                }

                VStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(tips.enumerated()), id: \.offset) { index, tip in
                        HStack(alignment: .top, spacing: 8) {
                            Text("•")
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(.yellow)

                            Text(tip)
                                .font(.system(size: 16))
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                        }
                    }
                }
            }
            .padding(16)
            .background(yellowGradientBackground)
        }
    }

    @ViewBuilder
    private var mistakesSection: some View {
        if let commonMistakes = grammarPoint.commonMistakes, !commonMistakes.isEmpty {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    Text("⚠️ Common Mistakes")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                        .textCase(.uppercase)
                    Spacer()
                }

                VStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(commonMistakes.enumerated()), id: \.offset) { index, mistake in
                        HStack(alignment: .top, spacing: 8) {
                            Text("⚠️")
                                .font(.system(size: 14))

                            Text(mistake)
                                .font(.system(size: 16))
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                        }
                    }
                }
            }
            .padding(16)
            .background(redGradientBackground)
        }
    }

    private var yellowGradientBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.yellow.opacity(0.1),
                        Color.yellow.opacity(0.05)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
            )
    }

    private var redGradientBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.red.opacity(0.1),
                        Color.red.opacity(0.05)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.red.opacity(0.3), lineWidth: 1)
            )
    }
}

#Preview {
    let sampleGrammar = [
        LessonGrammarPoint(
            rule: "Basic Sentence Structure (Subject-Verb)",
            ruleTamil: "அடிப்படை வாக்கிய அமைப்பு (கருவி-வினை)",
            ruleRomanization: "adippadai vaakkiya amaippu (karuvi-vinai)",
            explanation: "Simple Tamil sentences often follow a Subject-Verb order. The subject is who or what is doing the action, and the verb is the action itself. For greetings, the subject is often implied (hidden).\n\nStructure: [Subject] + Verb\nExample: \"வணக்கம்\" (vaNakkam) - Here the subject \"I\" is implied.",
            examples: [
                GrammarExample(
                    english: "Good morning!",
                    tamil: "காலை வணக்கம்!",
                    romanization: "kaalai vaNakkam!",
                    audioURL: "lesson_01_grammar_01_example_01.mp3"
                ),
                GrammarExample(
                    english: "How are you?",
                    tamil: "எப்படி இருக்கிறீர்கள்?",
                    romanization: "eppadi irukkiRiirgaL?",
                    audioURL: "lesson_01_grammar_01_example_02.mp3"
                ),
                GrammarExample(
                    english: "I am fine, thank you.",
                    tamil: "நலமாக இருக்கிறேன், நன்றி.",
                    romanization: "nalaamaa irukkiReen, nanRi.",
                    audioURL: "lesson_01_grammar_01_example_03.mp3"
                )
            ],
            tips: [
                "Start with simple greetings where the subject is implied",
                "Focus on the main action word (verb) first",
                "Tamil word order can be flexible, but Subject-Verb is most common for beginners",
                "Listen for the verb ending to understand who is speaking"
            ],
            commonMistakes: [
                "Adding unnecessary pronouns like \"நான்\" (I) in simple greetings",
                "Confusing formal and informal verb endings",
                "Overthinking the word order - keep it simple",
                "Forgetting that context often tells us who the subject is"
            ],
            difficultyLevel: 1
        ),
        LessonGrammarPoint(
            rule: "Honorifics (Informal vs. Formal)",
            ruleTamil: "கௌரவப் பதங்கள் (அனாச்சாரம் vs. சம்பிரதாயம்)",
            ruleRomanization: "gauravam (anaacharam vs. sampradaayam)",
            explanation: "Tamil uses different pronouns and verb endings based on formality and respect levels. This is crucial for proper social interaction.",
            examples: [
                GrammarExample(
                    english: "How are you? (Informal)",
                    tamil: "எப்படி இருக்கிறாய்?",
                    romanization: "eppadi irukkiraay?",
                    audioURL: "lesson_01_grammar_03_example_01.mp3"
                ),
                GrammarExample(
                    english: "How are you? (Formal)",
                    tamil: "எப்படி இருக்கிறீர்கள்?",
                    romanization: "eppadi irukkiRiirgaL?",
                    audioURL: "lesson_01_grammar_03_example_02.mp3"
                )
            ],
            tips: [
                "Start with formal versions until you know someone well",
                "Listen to how others address the same person",
                "Age and social status determine formality level"
            ],
            commonMistakes: [
                "Using informal with elders or strangers",
                "Mixing formal and informal in the same conversation",
                "Being too formal with close friends (sounds distant)"
            ],
            difficultyLevel: 2
        )
    ]

    GrammarDetailView(grammarPoints: sampleGrammar)
}
