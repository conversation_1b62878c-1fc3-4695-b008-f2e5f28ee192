import SwiftUI

// MARK: - Enhanced ConversationDetailView with Romanization & Pronunciation
struct ConversationDetailView: View {
    let conversation: TamilSupabaseConversation
    @State private var conversationLines: [TamilSupabaseConversationLine] = []
    @State private var currentLineIndex = 0
    @State private var showPronunciation = false
    @State private var showGrammarNotes = false
    @State private var showCulturalNotes = false
    @State private var isLoading = true

    @StateObject private var audioManager = AudioContentManager.shared
    @StateObject private var supabaseService = SupabaseContentService.shared
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.black.opacity(0.9),
                        Color.green.opacity(0.3),
                        Color.black.opacity(0.9)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                if isLoading {
                    ProgressView("Loading conversation...")
                        .foregroundColor(.white)
                } else if conversationLines.isEmpty {
                    Text("No conversation lines available")
                        .foregroundColor(.white)
                } else {
                    ScrollView {
                        VStack(spacing: 20) {
                            // Header Section
                            conversationHeaderView

                            // Current Line Display
                            currentLineView

                            // Navigation Controls
                            lineNavigationView

                            // Additional Information
                            additionalInfoView
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle("Conversation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.green)
                }
            }
        }
        .task {
            await loadConversationLines()
        }
    }

    // MARK: - Header View
    private var conversationHeaderView: some View {
        VStack(spacing: 12) {
            // Title
            VStack(spacing: 8) {
                Text(conversation.titleEnglish)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text(conversation.titleTamil)
                    .font(.title3)
                    .foregroundColor(.green)
            }

            // Metadata badges
            HStack(spacing: 12) {
                if let participants = conversation.participants {
                    Badge(text: participants, color: .blue)
                }

                if let formality = conversation.formalityLevel {
                    Badge(text: formality.capitalized, color: formalityColor(formality))
                }
            }

            // Context
            if let context = conversation.contextDescription {
                Text(context)
                    .font(.body)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Current Line View
    private var currentLineView: some View {
        VStack(spacing: 16) {
            if currentLineIndex < conversationLines.count {
                let line = conversationLines[currentLineIndex]

                // Speaker Header
                HStack {
                    Text("👤 \(line.speakerName)")
                        .font(.headline)
                        .foregroundColor(.white)

                    Spacer()

                    // Audio Controls
                    HStack(spacing: 12) {
                        Button(action: { playLineAudio(line: line, slowSpeed: false) }) {
                            Image(systemName: "speaker.2.fill")
                                .foregroundColor(.green)
                        }

                        Button(action: { playLineAudio(line: line, slowSpeed: true) }) {
                            Image(systemName: "tortoise.fill")
                                .foregroundColor(.orange)
                        }
                    }
                }

                // Text Content
                VStack(spacing: 12) {
                    // English Text
                    Text(line.textEnglish)
                        .font(.body)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // Tamil Text
                    Text(line.textTamil)
                        .font(.title3)
                        .foregroundColor(.green)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // Romanization
                    Text(line.textRomanized)
                        .font(.body)
                        .foregroundColor(.blue)
                        .italic()
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // Pronunciation Guide (Toggleable)
                    if showPronunciation {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Pronunciation:")
                                .font(.caption)
                                .foregroundColor(.gray)

                            Text(line.pronunciationSimple)
                                .font(.body)
                                .foregroundColor(.yellow)
                                .multilineTextAlignment(.leading)
                                .frame(maxWidth: .infinity, alignment: .leading)

                            if let ipa = line.pronunciationIpa {
                                Text("IPA: \(ipa)")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .multilineTextAlignment(.leading)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                        }
                    }
                }

                // Toggle Pronunciation Button
                Button(action: { showPronunciation.toggle() }) {
                    HStack {
                        Image(systemName: showPronunciation ? "eye.slash" : "eye")
                        Text(showPronunciation ? "Hide Pronunciation" : "Show Pronunciation")
                    }
                    .font(.caption)
                    .foregroundColor(.gray)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Line Navigation
    private var lineNavigationView: some View {
        HStack(spacing: 20) {
            Button(action: previousLine) {
                Image(systemName: "chevron.left.circle.fill")
                    .font(.title2)
                    .foregroundColor(currentLineIndex > 0 ? .green : .gray)
            }
            .disabled(currentLineIndex <= 0)

            Text("\(currentLineIndex + 1) / \(conversationLines.count)")
                .font(.body)
                .foregroundColor(.white)

            Button(action: nextLine) {
                Image(systemName: "chevron.right.circle.fill")
                    .font(.title2)
                    .foregroundColor(currentLineIndex < conversationLines.count - 1 ? .green : .gray)
            }
            .disabled(currentLineIndex >= conversationLines.count - 1)
        }
        .padding()
    }

    // MARK: - Additional Info
    private var additionalInfoView: some View {
        VStack(spacing: 12) {
            if currentLineIndex < conversationLines.count {
                let line = conversationLines[currentLineIndex]

                // Grammar Notes
                if let grammarNotes = line.grammarNotes, !grammarNotes.isEmpty {
                    ExpandableSection(
                        title: "Grammar Notes",
                        content: grammarNotes,
                        isExpanded: $showGrammarNotes,
                        color: .blue
                    )
                }

                // Cultural Notes
                if let culturalNotes = line.culturalNotes, !culturalNotes.isEmpty {
                    ExpandableSection(
                        title: "Cultural Context",
                        content: culturalNotes,
                        isExpanded: $showCulturalNotes,
                        color: .orange
                    )
                }

                // Key Phrases with Romanization & Pronunciation
                if !line.keyPhrases.isEmpty {
                    KeyPhrasesView(keyPhrases: line.keyPhrases)
                } else {
                    // Debug: Show when no key phrases are found
                    Text("No key phrases found for this line")
                        .font(.caption)
                        .foregroundColor(.yellow)
                        .padding()
                        .background(Color.orange.opacity(0.2))
                        .cornerRadius(8)
                }
            }
        }
    }

    // MARK: - Helper Views
    private struct Badge: View {
        let text: String
        let color: Color

        var body: some View {
            Text(text)
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(color.opacity(0.2))
                .foregroundColor(color)
                .cornerRadius(8)
        }
    }

    private struct KeyPhrasesView: View {
        let keyPhrases: [String]

        var body: some View {
            VStack(alignment: .leading, spacing: 12) {
                Text("Key Phrases:")
                    .font(.headline)
                    .foregroundColor(.white)

                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 140))
                ], spacing: 12) {
                    ForEach(Array(keyPhrases.enumerated()), id: \.offset) { index, phrase in
                        KeyPhraseCard(phrase: phrase)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.green.opacity(0.3), lineWidth: 1)
                    )
            )
        }


    }

    private struct KeyPhraseCard: View {
        let phrase: String

        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                // Tamil phrase (no audio button needed)
                Text(phrase)
                    .font(.body)
                    .foregroundColor(.green)
                    .fontWeight(.medium)

                // Always show romanization and pronunciation (not hidden behind clicks)
                VStack(alignment: .leading, spacing: 4) {
                    // Romanization
                    Text(romanizePhrase(phrase))
                        .font(.caption)
                        .foregroundColor(.blue)
                        .italic()

                    // Pronunciation guide
                    Text(pronunciationGuide(phrase))
                        .font(.caption2)
                        .foregroundColor(.yellow)
                }
            }
            .padding(10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.green.opacity(0.15))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.green.opacity(0.5), lineWidth: 1)
                    )
            )
        }

        private func romanizePhrase(_ phrase: String) -> String {
            // Comprehensive romanization mapping for Tamil phrases from conversation data
            let romanizationMap: [String: String] = [
                // Basic greetings
                "வணக்கம்": "vanakkam",
                "நன்றி": "nandri",
                "மன்னிக்கவும்": "mannikkavum",

                // Conversation phrases from database
                "எப்படி இருக்கிறீர்கள்": "eppadi irukkireergal",
                "நலமாக இருக்கிறேன்": "nalamaaga irukkiren",
                "நீங்கள் எப்படி": "neengal eppadi",
                "நானும்": "naanum",
                "எங்கே போகிறீர்கள்": "engae pogireeergal",
                "சந்தைக்குப் போகிறேன்": "sandhaikkup pogireen",
                "பிறகு சந்திப்போம்": "piragu sandhippom",

                // Additional common phrases
                "எப்படி இருக்கீங்க?": "eppadi irukkinga?",
                "நல்லா இருக்கேன்": "nalla irukken",
                "பார்த்ததுக்கு சந்தோஷம்": "paarthatukku sandhosham",
                "என் பேரு": "en peru",
                "உங்க பேரு என்ன?": "unga peru enna?",
                "எங்க இருந்து வர்றீங்க?": "enga irundhu varringa?",
                "நான் இந்தியாவில் இருந்து வர்றேன்": "naan indhiyaavil irundhu varren"
            ]

            return romanizationMap[phrase] ?? "Romanization: \(phrase)"
        }

        private func pronunciationGuide(_ phrase: String) -> String {
            // Comprehensive pronunciation guide for Tamil phrases from conversation data
            let pronunciationMap: [String: String] = [
                // Basic greetings
                "வணக்கம்": "va-nak-kam",
                "நன்றி": "nan-dri",
                "மன்னிக்கவும்": "man-nik-ka-vum",

                // Conversation phrases from database
                "எப்படி இருக்கிறீர்கள்": "ep-pa-di i-ruk-ki-reer-gal",
                "நலமாக இருக்கிறேன்": "na-la-maa-ga i-ruk-ki-ren",
                "நீங்கள் எப்படி": "neen-gal ep-pa-di",
                "நானும்": "naa-num",
                "எங்கே போகிறீர்கள்": "en-gae po-gi-reer-gal",
                "சந்தைக்குப் போகிறேன்": "san-thai-kkup po-gi-reen",
                "பிறகு சந்திப்போம்": "pi-ra-gu san-dhip-pom",

                // Additional common phrases
                "எப்படி இருக்கீங்க?": "ep-pa-di i-ruk-kee-nga?",
                "நல்லா இருக்கேன்": "nal-la i-ruk-ken",
                "பார்த்ததுக்கு சந்தோஷம்": "paar-tha-thuk-ku san-dho-sham",
                "என் பேரு": "en pe-ru",
                "உங்க பேரு என்ன?": "un-ga pe-ru en-na?",
                "எங்க இருந்து வர்றீங்க?": "en-ga i-run-dhu var-ree-nga?",
                "நான் இந்தியாவில் இருந்து வர்றேன்": "naan in-dhi-yaa-vil i-run-dhu var-ren"
            ]

            return pronunciationMap[phrase] ?? "Pronunciation: \(phrase)"
        }
    }

    private struct ExpandableSection: View {
        let title: String
        let content: String
        @Binding var isExpanded: Bool
        let color: Color

        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                Button(action: { isExpanded.toggle() }) {
                    HStack {
                        Text(title)
                            .font(.headline)
                            .foregroundColor(.white)

                        Spacer()

                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .foregroundColor(color)
                    }
                }

                if isExpanded {
                    Text(content)
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }

    // MARK: - Helper Methods
    private func formalityColor(_ formality: String) -> Color {
        switch formality.lowercased() {
        case "formal": return .blue
        case "informal": return .green
        case "semi-formal": return .orange
        default: return .gray
        }
    }

    // MARK: - Actions
    private func loadConversationLines() async {
        let lines = await supabaseService.fetchConversationLines(for: conversation.conversationId)
        await MainActor.run {
            // If no lines found in database, create demo lines for testing
            if lines.isEmpty {
                self.conversationLines = createDemoConversationLines()
                print("🎭 Using demo conversation lines for testing")
            } else {
                self.conversationLines = lines
                print("✅ Loaded \(lines.count) conversation lines from database")

                // Debug: Print key phrases for each line
                for (index, line) in lines.enumerated() {
                    print("🔍 Line \(index + 1) (\(line.speakerName)): \(line.keyPhrases.count) key phrases: \(line.keyPhrases)")
                }
            }
            self.isLoading = false
        }
    }

    private func nextLine() {
        if currentLineIndex < conversationLines.count - 1 {
            currentLineIndex += 1
            showPronunciation = false
            showGrammarNotes = false
            showCulturalNotes = false
        }
    }

    private func previousLine() {
        if currentLineIndex > 0 {
            currentLineIndex -= 1
            showPronunciation = false
            showGrammarNotes = false
            showCulturalNotes = false
        }
    }

    private func playLineAudio(line: TamilSupabaseConversationLine, slowSpeed: Bool) {
        // Use pre-recorded audio files from Supabase (generated with correct voices)
        if let audioUrl = slowSpeed ? line.audioSlowUrl : line.audioUrl,
           !audioUrl.isEmpty {
            audioManager.playConversationLineAudio(line: line, slowSpeed: slowSpeed)
        } else {
            print("⚠️ No pre-recorded audio available for \(line.speakerName). Please run conversation audio generation script.")
            print("📝 Run: python generate_conversation_audio.py --conversation-id \(line.conversationId)")
        }
    }

    private func playFullConversation() {
        audioManager.playFullConversationAudio(conversation: conversation)
    }

    private func createDemoConversationLines() -> [TamilSupabaseConversationLine] {
        return [
            TamilSupabaseConversationLine(
                id: "demo-1",
                conversationId: conversation.conversationId,
                lineNumber: 1,
                speakerName: "Raj",
                speakerRole: "person1",
                textEnglish: "Hello! How are you?",
                textTamil: "வணக்கம்! எப்படி இருக்கிறீர்கள்?",
                textRomanized: "vanakkam! eppadi irukkireergal?",
                pronunciationIpa: "/vəɳəkkəm eppəɖi irukkireerɡəl/",
                pronunciationSimple: "va-nak-kam! ep-pa-di i-ruk-ki-reer-gal?",
                audioUrl: nil,
                audioSlowUrl: nil,
                difficultyLevel: 1,
                keyPhrases: ["வணக்கம்", "எப்படி இருக்கிறீர்கள்"],
                grammarNotes: "வணக்கம் is the standard Tamil greeting used in formal and informal contexts.",
                culturalNotes: "This is a respectful way to greet someone, showing politeness in Tamil culture.",
                createdAt: "2025-06-24T00:00:00Z"
            ),
            TamilSupabaseConversationLine(
                id: "demo-2",
                conversationId: conversation.conversationId,
                lineNumber: 2,
                speakerName: "Priya",
                speakerRole: "person2",
                textEnglish: "I'm fine. How about you?",
                textTamil: "நலமாக இருக்கிறேன். நீங்கள் எப்படி?",
                textRomanized: "nalamaaga irukkiren. neengal eppadi?",
                pronunciationIpa: "/nələmaːɡə irukkireen neeŋɡəl eppəɖi/",
                pronunciationSimple: "na-la-maa-ga i-ruk-ki-ren. neen-gal ep-pa-di?",
                audioUrl: nil,
                audioSlowUrl: nil,
                difficultyLevel: 1,
                keyPhrases: ["நலமாக இருக்கிறேன்", "நீங்கள் எப்படி"],
                grammarNotes: "நலமாக means 'well' or 'fine'. நீங்கள் is the respectful form of 'you'.",
                culturalNotes: "It's polite to ask back about the other person's well-being in Tamil culture.",
                createdAt: "2025-06-24T00:00:00Z"
            ),
            TamilSupabaseConversationLine(
                id: "demo-3",
                conversationId: conversation.conversationId,
                lineNumber: 3,
                speakerName: "Raj",
                speakerRole: "person1",
                textEnglish: "I'm also fine. Where are you going?",
                textTamil: "நானும் நலமாக இருக்கிறேன். எங்கே போகிறீர்கள்?",
                textRomanized: "naanum nalamaaga irukkiren. engae pogireeergal?",
                pronunciationIpa: "/naːnum nələmaːɡə irukkireen enɡeː poɡireeərɡəl/",
                pronunciationSimple: "naa-num na-la-maa-ga i-ruk-ki-ren. en-gae po-gi-reer-gal?",
                audioUrl: nil,
                audioSlowUrl: nil,
                difficultyLevel: 2,
                keyPhrases: ["நானும்", "எங்கே போகிறீர்கள்"],
                grammarNotes: "நானும் means 'I also' or 'me too'. போகிறீர்கள் is the respectful form of 'going'.",
                culturalNotes: "Asking about destination is common in casual conversation between friends.",
                createdAt: "2025-06-24T00:00:00Z"
            )
        ]
    }
}

#Preview {
    ConversationDetailView(conversation: TamilSupabaseConversation(
        id: "preview-id",
        lessonId: "preview-lesson",
        conversationId: "L1C1",
        titleEnglish: "Meeting a Friend",
        titleTamil: "நண்பரைச் சந்தித்தல்",
        contextDescription: "Two friends meet on the street and exchange greetings",
        participants: "Raj & Priya (Friends)",
        formalityLevel: "informal",
        culturalSetting: "casual_street_meeting",
        audioFullUrl: nil,
        createdAt: "2025-06-24T00:00:00Z"
    ))
}
