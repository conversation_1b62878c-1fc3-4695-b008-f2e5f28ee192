import SwiftUI

struct MultiAgentLearningView: View {
    @StateObject private var orchestrationService = AgentOrchestrationService.shared
    @StateObject private var userPreferencesService = UserPreferencesService.shared
    @State private var currentSession: MultiAgentSession?
    @State private var userInput = ""
    @State private var conversationHistory: [MultiAgentResponse] = []
    @State private var selectedScenario: LearningScenario = LearningScenario(
        type: .conversation,
        difficulty: .beginner,
        objectives: ["Practice basic conversation"],
        culturalContext: nil
    )
    @State private var showingScenarioSelector = false
    
    let user: User
    let language: Language
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Agent Status Bar
                agentStatusBar
                
                // Conversation Area
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(conversationHistory.indices, id: \.self) { index in
                                MultiAgentResponseView(response: conversationHistory[index])
                                    .id(index)
                            }
                        }
                        .padding()
                    }
                    .onChange(of: conversationHistory.count) { oldValue, newValue in
                        withAnimation {
                            proxy.scrollTo(conversationHistory.count - 1, anchor: .bottom)
                        }
                    }
                }
                
                // Input Area
                inputArea
            }
            .navigationTitle("AI Learning Team")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Scenario") {
                        showingScenarioSelector = true
                    }
                }
            }
            .sheet(isPresented: $showingScenarioSelector) {
                ScenarioSelectorView(
                    selectedScenario: $selectedScenario,
                    language: language
                )
            }
            .task {
                await startMultiAgentSession()
            }
        }
    }
    
    // MARK: - Agent Status Bar
    
    private var agentStatusBar: some View {
        HStack {
            Text("Active Agents:")
                .font(.caption)
                .foregroundColor(.secondary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(Array(orchestrationService.activeAgents), id: \.self) { agentType in
                        AgentStatusChip(agentType: agentType)
                    }
                }
                .padding(.horizontal)
            }
            
            Spacer()
            
            if orchestrationService.isProcessing {
                ProgressView()
                    .scaleEffect(0.8)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    // MARK: - Input Area
    
    private var inputArea: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                TextField("Ask your AI learning team...", text: $userInput, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)
                
                Button(action: sendMessage) {
                    Image(systemName: "paperplane.fill")
                        .foregroundColor(.white)
                        .frame(width: 36, height: 36)
                        .background(userInput.isEmpty ? Color.gray : Color.blue)
                        .clipShape(Circle())
                }
                .disabled(userInput.isEmpty || orchestrationService.isProcessing)
            }
            
            // Quick Action Buttons
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    QuickActionButton(title: "Grammar Help", icon: "book") {
                        userInput = "Can you help me with grammar?"
                        sendMessage()
                    }
                    
                    QuickActionButton(title: "Practice Conversation", icon: "bubble.left.and.bubble.right") {
                        userInput = "Let's practice conversation"
                        sendMessage()
                    }
                    
                    QuickActionButton(title: "Cultural Context", icon: "globe") {
                        userInput = "Tell me about the culture"
                        sendMessage()
                    }
                    
                    QuickActionButton(title: "Check Progress", icon: "chart.line.uptrend.xyaxis") {
                        userInput = "How am I progressing?"
                        sendMessage()
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: -1)
    }
    
    // MARK: - Actions
    
    private func startMultiAgentSession() async {
        do {
            let session = try await orchestrationService.startMultiAgentSession(
                for: user,
                language: language,
                scenario: selectedScenario
            )
            await MainActor.run {
                currentSession = session
            }
        } catch {
            print("Failed to start multi-agent session: \(error)")
        }
    }
    
    private func sendMessage() {
        guard !userInput.isEmpty, let session = currentSession else { return }
        
        let input = UserInput(
            text: userInput,
            type: .text,
            timestamp: Date(),
            metadata: nil
        )
        
        let currentInput = userInput
        userInput = ""
        
        Task {
            do {
                let response = try await orchestrationService.processUserInput(input, session: session)
                await MainActor.run {
                    conversationHistory.append(response)
                }
            } catch {
                print("Failed to process user input: \(error)")
                await MainActor.run {
                    userInput = currentInput // Restore input on error
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct AgentStatusChip: View {
    let agentType: AgentType
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(Color.green)
                .frame(width: 6, height: 6)
            
            Text(agentType.displayName)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.blue.opacity(0.1))
        .foregroundColor(.blue)
        .clipShape(Capsule())
    }
}

struct MultiAgentResponseView: View {
    let response: MultiAgentResponse
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // User Input
            HStack {
                Spacer()
                VStack(alignment: .trailing) {
                    Text(response.userInput.text)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                    
                    Text(response.userInput.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity * 0.8, alignment: .trailing)
            }
            
            // Agent Responses
            VStack(alignment: .leading, spacing: 8) {
                Text("AI Learning Team Response")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                
                Text(response.synthesizedResponse)
                    .padding()
                    .background(Color(.systemGray6))
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                
                // Individual Agent Contributions
                if response.agentResponses.count > 1 {
                    DisclosureGroup("View Individual Agent Responses") {
                        VStack(alignment: .leading, spacing: 8) {
                            ForEach(Array(response.agentResponses.keys), id: \.self) { agentType in
                                if let agentResponse = response.agentResponses[agentType] {
                                    AgentResponseCard(agentResponse: agentResponse)
                                }
                            }
                        }
                    }
                    .font(.caption)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
}

struct AgentResponseCard: View {
    let agentResponse: AgentResponse
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(agentResponse.agentType.displayName)
                    .font(.caption2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(Int(agentResponse.confidence * 100))%")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Text(agentResponse.content)
                .font(.caption)
        }
        .padding(8)
        .background(agentResponse.agentType.color.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.caption)
                Text(title)
                    .font(.caption)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.blue.opacity(0.1))
            .foregroundColor(.blue)
            .clipShape(Capsule())
        }
    }
}

// MARK: - Extensions

extension AgentType {
    var displayName: String {
        switch self {
        case .tutor: return "Tutor"
        case .conversationPartner: return "Partner"
        case .culturalGuide: return "Culture"
        case .progressCoach: return "Coach"
        case .scenarioDirector: return "Director"
        case .speechCoach: return "Speech"
        case .assessmentAgent: return "Assessment"
        }
    }
    
    var color: Color {
        switch self {
        case .tutor: return .blue
        case .conversationPartner: return .green
        case .culturalGuide: return .purple
        case .progressCoach: return .orange
        case .scenarioDirector: return .red
        case .speechCoach: return .pink
        case .assessmentAgent: return .indigo
        }
    }
}

#Preview {
    MultiAgentLearningView(
        user: User(
            id: UUID(),
            email: "<EMAIL>",
            currentLevel: .beginner,
            targetLanguages: [.spanish],
            createdAt: Date()
        ),
        language: .spanish
    )
}
