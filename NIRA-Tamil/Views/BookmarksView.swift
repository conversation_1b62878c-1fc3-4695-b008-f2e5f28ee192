//
//  BookmarksView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 08/07/2025.
//

import SwiftUI

struct BookmarksView: View {
    @StateObject private var readingProgressService = ReadingProgressService.shared
    @StateObject private var literatureService = LiteratureService.shared
    @State private var searchText = ""
    @State private var sortOption: BookmarkSortOption = .dateAdded
    
    private var filteredBookmarks: [BookmarkData] {
        let bookmarks = readingProgressService.getAllBookmarks()
        
        let filtered = searchText.isEmpty ? bookmarks : bookmarks.filter { bookmark in
            // Get content title for search
            if let content = literatureService.allContent.first(where: { $0.id == bookmark.contentId }) {
                return content.title.localizedCaseInsensitiveContains(searchText) ||
                       content.titleTamil.localizedCaseInsensitiveContains(searchText)
            }
            return false
        }
        
        return filtered.sorted { bookmark1, bookmark2 in
            switch sortOption {
            case .dateAdded:
                return bookmark1.createdAt > bookmark2.createdAt
            case .title:
                let content1 = literatureService.allContent.first(where: { $0.id == bookmark1.contentId })
                let content2 = literatureService.allContent.first(where: { $0.id == bookmark2.contentId })
                return (content1?.title ?? "") < (content2?.title ?? "")
            case .progress:
                let progress1 = readingProgressService.getProgress(contentId: bookmark1.contentId)
                let progress2 = readingProgressService.getProgress(contentId: bookmark2.contentId)
                return progress1 > progress2
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with search and sort
                headerSection
                
                if filteredBookmarks.isEmpty {
                    emptyStateView
                } else {
                    // Bookmarks list
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(filteredBookmarks) { bookmark in
                                bookmarkCard(bookmark)
                            }
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle("Bookmarks")
            .navigationBarTitleDisplayMode(.large)
            .background(
                LinearGradient(
                    colors: [Color.blue.opacity(0.02), Color.purple.opacity(0.02)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        }
        .task {
            await literatureService.loadContent()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search bookmarks...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button("Clear") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
            
            // Sort options
            HStack {
                Text("Sort by:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Picker("Sort", selection: $sortOption) {
                    ForEach(BookmarkSortOption.allCases, id: \.self) { option in
                        Text(option.displayName).tag(option)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    // MARK: - Empty State
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "bookmark.slash")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("No Bookmarks Yet")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Start reading Tamil literature and bookmark your favorite content to see it here.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            NavigationLink(destination: LiteratureExploreView()) {
                HStack {
                    Image(systemName: "book.fill")
                    Text("Explore Literature")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            
            Spacer()
        }
    }
    
    // MARK: - Bookmark Card
    
    private func bookmarkCard(_ bookmark: BookmarkData) -> some View {
        Group {
            if let content = literatureService.allContent.first(where: { $0.id == bookmark.contentId }) {
                NavigationLink(destination: LiteratureDetailView(content: content)) {
                    VStack(alignment: .leading, spacing: 12) {
                        // Header with bookmark info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(content.title)
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                    .lineLimit(2)
                                
                                Text(content.titleTamil)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                            
                            Spacer()
                            
                            // Bookmark date
                            VStack(alignment: .trailing, spacing: 2) {
                                Image(systemName: "bookmark.fill")
                                    .foregroundColor(.blue)
                                
                                Text(bookmark.createdAt, style: .date)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        // Progress and stats
                        HStack(spacing: 16) {
                            // Reading progress
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Progress")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                HStack(spacing: 8) {
                                    ProgressView(value: readingProgressService.getProgress(contentId: bookmark.contentId), total: 1.0)
                                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                                        .frame(height: 4)
                                    
                                    Text("\(Int(readingProgressService.getProgress(contentId: bookmark.contentId) * 100))%")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.primary)
                                }
                            }
                            
                            Spacer()
                            
                            // Reading time
                            VStack(alignment: .trailing, spacing: 4) {
                                Text("Time")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                Text(formatReadingTime(readingProgressService.getReadingTime(contentId: bookmark.contentId)))
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)
                            }
                        }
                        
                        // Bookmark note if available
                        if let note = bookmark.note, !note.isEmpty {
                            Text(note)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color(.systemGray6))
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - Helper Functions
    
    private func formatReadingTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        if minutes < 60 {
            return "\(minutes)m"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            return "\(hours)h \(remainingMinutes)m"
        }
    }
}

// MARK: - Sort Options

enum BookmarkSortOption: CaseIterable {
    case dateAdded
    case title
    case progress
    
    var displayName: String {
        switch self {
        case .dateAdded:
            return "Date"
        case .title:
            return "Title"
        case .progress:
            return "Progress"
        }
    }
}

#Preview {
    BookmarksView()
}
