import SwiftUI

struct ReadingProgressDashboard: View {
    @StateObject private var statsService = ReadingStatisticsService.shared
    @StateObject private var readingService = ReadingContentService.shared
    @State private var showingFullStatistics = false
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                Text("Reading Progress")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button {
                    showingFullStatistics = true
                } label: {
                    Image(systemName: "chart.bar.fill")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            }
            
            // Quick stats
            quickStatsSection
            
            // Today's progress
            todaysProgressSection
            
            // Recent achievements
            if !statsService.achievements.isEmpty {
                recentAchievementsSection
            }
            
            // Current streak
            currentStreakSection
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .sheet(isPresented: $showingFullStatistics) {
            ReadingStatisticsView()
        }
        .onAppear {
            statsService.updateStatistics()
        }
    }
    
    // MARK: - Quick Stats Section
    
    private var quickStatsSection: some View {
        HStack(spacing: 12) {
            QuickStatCard(
                title: "Completed",
                value: "\(statsService.overallStats.totalContentCompleted)",
                icon: "checkmark.circle.fill",
                color: .green
            )
            
            QuickStatCard(
                title: "Reading Time",
                value: formatTime(statsService.overallStats.totalReadingTime),
                icon: "clock.fill",
                color: .blue
            )
            
            QuickStatCard(
                title: "Streak",
                value: "\(statsService.currentStreak)",
                icon: "flame.fill",
                color: .orange
            )
        }
    }
    
    // MARK: - Today's Progress Section
    
    private var todaysProgressSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Today's Progress")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text(statsService.dailyStats.readingTimeDisplay)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(statsService.dailyStats.contentRead)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    
                    Text("Content Read")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(statsService.dailyStats.completedContent)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("Completed")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Progress bar for daily goal (assuming 5 content pieces per day)
            let dailyGoal = 5
            let progress = min(Double(statsService.dailyStats.contentRead) / Double(dailyGoal), 1.0)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("Daily Goal")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(statsService.dailyStats.contentRead)/\(dailyGoal)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: progress)
                    .tint(.blue)
                    .background(Color.secondary.opacity(0.2))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.blue.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Recent Achievements Section
    
    private var recentAchievementsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recent Achievements")
                .font(.headline)
                .foregroundColor(.primary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(Array(statsService.achievements.suffix(3)), id: \.self) { achievement in
                        CompactAchievementCard(achievement: achievement)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
    
    // MARK: - Current Streak Section
    
    private var currentStreakSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: "flame.fill")
                        .foregroundColor(.orange)
                    
                    Text("Current Streak")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                
                Text("\(statsService.currentStreak) days")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Best Streak")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("\(statsService.longestStreak) days")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.orange.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Helper Methods
    
    private func formatTime(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h"
        } else if minutes > 0 {
            return "\(minutes)m"
        } else {
            return "\(seconds)s"
        }
    }
}

// MARK: - Supporting Views

struct QuickStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

struct CompactAchievementCard: View {
    let achievement: ReadingAchievement
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: achievement.icon)
                .font(.title3)
                .foregroundColor(achievement.color)
            
            Text(achievement.title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(width: 80, height: 60)
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(achievement.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(achievement.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    ReadingProgressDashboard()
        .padding()
}
