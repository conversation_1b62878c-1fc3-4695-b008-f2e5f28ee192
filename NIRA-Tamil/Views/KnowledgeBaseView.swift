import SwiftUI
import UniformTypeIdentifiers

struct KnowledgeBaseView: View {
    @StateObject private var enhancedAIService = EnhancedAIService()
    @State private var selectedLanguage = "french"
    @State private var knowledgeBaseItems: [SupabaseKnowledgeBase] = []
    @State private var isLoading = false
    @State private var showingDocumentPicker = false
    @State private var showingAddDocument = false
    @State private var errorMessage: String?
    @State private var showingError = false
    @State private var searchText = ""

    let languages = [
        ("french", "🇫🇷 French"),
        ("spanish", "🇪🇸 Spanish"),
        ("english", "🇺🇸 English"),
        ("japanese", "🇯🇵 Japanese"),
        ("tamil", "🇮🇳 Tamil"),
        ("german", "🇩🇪 German"),
        ("italian", "🇮🇹 Italian"),
        ("portuguese", "🇵🇹 Portuguese"),
        ("korean", "🇰🇷 Korean"),
        ("chinese", "🇨🇳 Chinese"),
        ("arabic", "🇸🇦 Arabic"),
        ("russian", "🇷🇺 Russian"),
        ("hindi", "🇮🇳 Hindi"),
        ("telugu", "🇮🇳 Telugu"),
        ("vietnamese", "🇻🇳 Vietnamese"),
        ("indonesian", "🇮🇩 Indonesian"),
        // New 10 languages
        ("kannada", "🇮🇳 Kannada"),
        ("malayalam", "🇮🇳 Malayalam"),
        ("bengali", "🇧🇩 Bengali"),
        ("marathi", "🇮🇳 Marathi"),
        ("punjabi", "🇮🇳 Punjabi"),
        ("dutch", "🇳🇱 Dutch"),
        ("swedish", "🇸🇪 Swedish"),
        ("thai", "🇹🇭 Thai"),
        ("norwegian", "🇳🇴 Norwegian")
    ]

    var filteredItems: [SupabaseKnowledgeBase] {
        if searchText.isEmpty {
            return knowledgeBaseItems
        } else {
            return knowledgeBaseItems.filter { item in
                item.fileName.localizedCaseInsensitiveContains(searchText) ||
                item.title?.localizedCaseInsensitiveContains(searchText) == true ||
                item.description?.localizedCaseInsensitiveContains(searchText) == true ||
                item.tags.joined().localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Language Selection Header
                languageSelectionHeader

                // Search Bar
                searchBar

                // Content
                if isLoading {
                    loadingView
                } else if filteredItems.isEmpty {
                    emptyStateView
                } else {
                    knowledgeBaseList
                }
            }
            .navigationTitle("Knowledge Base")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingAddDocument = true }) {
                        Image(systemName: "plus")
                            .font(.title2)
                            .foregroundColor(.niraPrimary)
                    }
                }
            }
            .onAppear {
                loadKnowledgeBase()
            }
            .onChange(of: selectedLanguage) { _, _ in
                loadKnowledgeBase()
            }
            .sheet(isPresented: $showingAddDocument) {
                AddDocumentView(
                    language: selectedLanguage,
                    enhancedAIService: enhancedAIService
                ) {
                    loadKnowledgeBase()
                }
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage ?? "An unknown error occurred")
            }
        }
    }

    // MARK: - Language Selection Header

    private var languageSelectionHeader: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(languages, id: \.0) { language in
                    LanguageFilterChip(
                        title: language.1,
                        isSelected: selectedLanguage == language.0,
                        action: {
                            selectedLanguage = language.0
                        }
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - Search Bar

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search documents...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.bottom, 8)
    }

    // MARK: - Loading View

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)

            Text("Loading knowledge base...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - Empty State View

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text")
                .font(.system(size: 60))
                .foregroundColor(.gray)

            VStack(spacing: 8) {
                Text("No Documents Yet")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Upload documents to enhance your AI tutors' knowledge")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }

            Button(action: { showingAddDocument = true }) {
                HStack {
                    Image(systemName: "plus")
                    Text("Add Document")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: Color.primaryGradient,
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(25)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }

    // MARK: - Knowledge Base List

    private var knowledgeBaseList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredItems) { item in
                    KnowledgeBaseItemCard(
                        item: item,
                        onDelete: { deleteItem(item) }
                    )
                }
            }
            .padding(.horizontal)
        }
    }

    // MARK: - Actions

    private func loadKnowledgeBase() {
        isLoading = true

        Task {
            do {
                let items = try await enhancedAIService.getKnowledgeBase(for: selectedLanguage)
                await MainActor.run {
                    knowledgeBaseItems = items
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showingError = true
                    isLoading = false
                }
            }
        }
    }

    private func deleteItem(_ item: SupabaseKnowledgeBase) {
        Task {
            do {
                try await enhancedAIService.deleteKnowledgeBaseItem(item)
                await MainActor.run {
                    knowledgeBaseItems.removeAll { $0.id == item.id }
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

// MARK: - Language Filter Chip

struct LanguageFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Group {
                        if isSelected {
                            LinearGradient(
                                colors: Color.primaryGradient,
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        } else {
                            Color(.systemGray5)
                        }
                    }
                )
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Knowledge Base Item Card

struct KnowledgeBaseItemCard: View {
    let item: SupabaseKnowledgeBase
    let onDelete: () -> Void

    @State private var showingDetails = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                // File Icon
                fileIcon

                VStack(alignment: .leading, spacing: 4) {
                    Text(item.title ?? item.fileName)
                        .font(.headline)
                        .lineLimit(2)

                    Text(item.fileName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Status Badge
                statusBadge

                // Menu
                Menu {
                    Button(action: { showingDetails = true }) {
                        Label("View Details", systemImage: "info.circle")
                    }

                    Button(role: .destructive, action: onDelete) {
                        Label("Delete", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.secondary)
                        .padding(8)
                }
            }

            // Description
            if let description = item.description {
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }

            // Tags
            if !item.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(item.tags, id: \.self) { tag in
                            Text(tag)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 1)
                }
            }

            // Footer
            HStack {
                Text(formatFileSize(item.fileSize))
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Text(item.createdAt.formatted(date: .abbreviated, time: .omitted))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        .sheet(isPresented: $showingDetails) {
            KnowledgeBaseDetailView(item: item)
        }
    }

    private var fileIcon: some View {
        Image(systemName: getFileIcon(for: item.fileType))
            .font(.title2)
            .foregroundColor(getFileColor(for: item.fileType))
            .frame(width: 40, height: 40)
            .background(getFileColor(for: item.fileType).opacity(0.1))
            .cornerRadius(8)
    }

    private var statusBadge: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(item.processingStatus.color)
                .frame(width: 6, height: 6)

            Text(item.processingStatus.displayName)
                .font(.caption)
                .foregroundColor(item.processingStatus.color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(item.processingStatus.color.opacity(0.1))
        .cornerRadius(8)
    }

    private func getFileIcon(for fileType: String) -> String {
        switch fileType.lowercased() {
        case "pdf": return "doc.richtext"
        case "doc", "docx": return "doc.text"
        case "txt", "md": return "doc.plaintext"
        case "jpg", "jpeg", "png": return "photo"
        case "mp3", "wav", "m4a": return "waveform"
        default: return "doc"
        }
    }

    private func getFileColor(for fileType: String) -> Color {
        switch fileType.lowercased() {
        case "pdf": return .red
        case "doc", "docx": return .blue
        case "txt", "md": return .green
        case "jpg", "jpeg", "png": return .purple
        case "mp3", "wav", "m4a": return .orange
        default: return .gray
        }
    }

    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - Add Document View

struct AddDocumentView: View {
    let language: String
    let enhancedAIService: EnhancedAIService
    let onComplete: () -> Void

    @Environment(\.presentationMode) var presentationMode
    @State private var title = ""
    @State private var description = ""
    @State private var showingDocumentPicker = false
    @State private var selectedDocument: DocumentPickerResult?
    @State private var isUploading = false
    @State private var errorMessage: String?
    @State private var showingError = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Upload Area
                uploadArea

                // Form
                if selectedDocument != nil {
                    documentForm
                }

                Spacer()

                // Upload Button
                if selectedDocument != nil {
                    uploadButton
                }
            }
            .padding()
            .navigationTitle("Add Document")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingDocumentPicker) {
                DocumentPicker { result in
                    selectedDocument = result
                }
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage ?? "An unknown error occurred")
            }
        }
    }

    private var uploadArea: some View {
        VStack(spacing: 16) {
            if let document = selectedDocument {
                // Selected Document
                HStack {
                    Image(systemName: "doc.fill")
                        .font(.title)
                        .foregroundColor(.niraPrimary)

                    VStack(alignment: .leading) {
                        Text(document.name)
                            .font(.headline)
                        Text(formatFileSize(document.size))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Button("Change") {
                        showingDocumentPicker = true
                    }
                    .font(.subheadline)
                    .foregroundColor(.niraPrimary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            } else {
                // Upload Prompt
                Button(action: { showingDocumentPicker = true }) {
                    VStack(spacing: 12) {
                        Image(systemName: "icloud.and.arrow.up")
                            .font(.system(size: 40))
                            .foregroundColor(.niraPrimary)

                        Text("Choose Document")
                            .font(.headline)
                            .foregroundColor(.primary)

                        Text("PDF, Word, Text, Images, or Audio files")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 40)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.niraPrimary, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }

    private var documentForm: some View {
        VStack(alignment: .leading, spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Title (Optional)")
                    .font(.headline)

                TextField("Enter a title for this document", text: $title)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }

            VStack(alignment: .leading, spacing: 8) {
                Text("Description (Optional)")
                    .font(.headline)

                TextField("Describe what this document contains", text: $description, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
            }
        }
    }

    private var uploadButton: some View {
        Button(action: uploadDocument) {
            HStack {
                if isUploading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "arrow.up.circle.fill")
                }

                Text(isUploading ? "Uploading..." : "Upload Document")
            }
            .font(.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                LinearGradient(
                    colors: Color.primaryGradient,
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .disabled(isUploading)
    }

    private func uploadDocument() {
        guard let document = selectedDocument else { return }

        isUploading = true

        Task {
            do {
                _ = try await enhancedAIService.uploadDocument(
                    document.data,
                    fileName: document.name,
                    fileType: document.fileExtension,
                    language: language,
                    title: title.isEmpty ? nil : title,
                    description: description.isEmpty ? nil : description
                )

                await MainActor.run {
                    isUploading = false
                    onComplete()
                    presentationMode.wrappedValue.dismiss()
                }
            } catch {
                await MainActor.run {
                    isUploading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }

    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - Document Picker

struct DocumentPicker: UIViewControllerRepresentable {
    let onDocumentPicked: (DocumentPickerResult) -> Void

    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: [
            .pdf, .plainText, .rtf, .image, .audio,
            UTType(filenameExtension: "doc") ?? .data,
            UTType(filenameExtension: "docx") ?? .data,
            UTType(filenameExtension: "md") ?? .data
        ])
        picker.delegate = context.coordinator
        picker.allowsMultipleSelection = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPicker

        init(_ parent: DocumentPicker) {
            self.parent = parent
        }

        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            guard let url = urls.first else { return }

            do {
                let data = try Data(contentsOf: url)
                let result = DocumentPickerResult(
                    name: url.lastPathComponent,
                    data: data,
                    size: Int64(data.count),
                    fileExtension: url.pathExtension
                )
                parent.onDocumentPicked(result)
            } catch {
                print("Error reading document: \(error)")
            }
        }
    }
}

struct DocumentPickerResult {
    let name: String
    let data: Data
    let size: Int64
    let fileExtension: String
}

// MARK: - Knowledge Base Detail View

struct KnowledgeBaseDetailView: View {
    let item: SupabaseKnowledgeBase
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text(item.title ?? item.fileName)
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(item.fileName)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    // Metadata
                    VStack(alignment: .leading, spacing: 12) {
                        KnowledgeDetailRow(title: "Language", value: item.language.capitalized)
                        KnowledgeDetailRow(title: "File Type", value: item.fileType.uppercased())
                        KnowledgeDetailRow(title: "File Size", value: formatFileSize(item.fileSize))
                        KnowledgeDetailRow(title: "Status", value: item.processingStatus.displayName)
                        KnowledgeDetailRow(title: "Created", value: item.createdAt.formatted(date: .abbreviated, time: .shortened))
                    }

                    // Description
                    if let description = item.description {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Description")
                                .font(.headline)

                            Text(description)
                                .font(.body)
                        }
                    }

                    // Tags
                    if !item.tags.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Tags")
                                .font(.headline)

                            FlowLayout(spacing: 8) {
                                ForEach(item.tags, id: \.self) { tag in
                                    Text(tag)
                                        .font(.caption)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color(.systemGray6))
                                        .cornerRadius(12)
                                }
                            }
                        }
                    }

                    // Extracted Text
                    if let extractedText = item.extractedText {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Extracted Content")
                                .font(.headline)

                            Text(extractedText)
                                .font(.body)
                                .padding()
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Document Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }

    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

struct KnowledgeDetailRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Flow Layout for Tags

struct FlowLayout: Layout {
    let spacing: CGFloat

    init(spacing: CGFloat = 8) {
        self.spacing = spacing
    }

    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let result = FlowResult(
            in: proposal.replacingUnspecifiedDimensions().width,
            subviews: subviews,
            spacing: spacing
        )
        return result.size
    }

    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let result = FlowResult(
            in: bounds.width,
            subviews: subviews,
            spacing: spacing
        )

        for (index, subview) in subviews.enumerated() {
            subview.place(at: result.positions[index], proposal: .unspecified)
        }
    }
}

struct FlowResult {
    let size: CGSize
    let positions: [CGPoint]

    init(in maxWidth: CGFloat, subviews: LayoutSubviews, spacing: CGFloat) {
        var positions: [CGPoint] = []
        var size = CGSize.zero
        var currentRowY: CGFloat = 0
        var currentRowX: CGFloat = 0
        var currentRowHeight: CGFloat = 0

        for subview in subviews {
            let subviewSize = subview.sizeThatFits(.unspecified)

            if currentRowX + subviewSize.width > maxWidth && currentRowX > 0 {
                // Start new row
                currentRowY += currentRowHeight + spacing
                currentRowX = 0
                currentRowHeight = 0
            }

            positions.append(CGPoint(x: currentRowX, y: currentRowY))
            currentRowX += subviewSize.width + spacing
            currentRowHeight = max(currentRowHeight, subviewSize.height)
            size.width = max(size.width, currentRowX - spacing)
        }

        size.height = currentRowY + currentRowHeight
        self.size = size
        self.positions = positions
    }
}

#Preview {
    KnowledgeBaseView()
}