//
//  AdaptiveLearningView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct AdaptiveLearningView: View {
    @StateObject private var adaptiveEngine = AdaptiveLearningEngine.shared
    @State private var showingLearningPathDetails = false
    @State private var showingRecommendationDetails = false
    @State private var selectedRecommendation: AdaptiveLearningEngine.AdaptiveRecommendation?
    @State private var isGeneratingPath = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                adaptiveLearningHeader
                
                // Main content
                ScrollView {
                    VStack(spacing: 20) {
                        // Learning state indicator
                        learningStateCard
                        
                        // Current learning path
                        if let learningPath = adaptiveEngine.currentLearningPath {
                            currentLearningPathCard(learningPath)
                        }
                        
                        // Adaptive recommendations
                        if !adaptiveEngine.adaptiveRecommendations.isEmpty {
                            adaptiveRecommendationsSection
                        }
                        
                        // Personalized content
                        if !adaptiveEngine.personalizedContent.isEmpty {
                            personalizedContentSection
                        }
                        
                        // Generate new path button
                        if adaptiveEngine.currentLearningPath == nil {
                            generatePathButton
                        }
                    }
                    .padding()
                }
            }
            .background(
                LinearGradient(
                    colors: [Color.green.opacity(0.03), Color.blue.opacity(0.03)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingLearningPathDetails) {
            if let learningPath = adaptiveEngine.currentLearningPath {
                LearningPathDetailView(learningPath: learningPath)
            }
        }
        .sheet(isPresented: $showingRecommendationDetails) {
            if let recommendation = selectedRecommendation {
                RecommendationDetailView(recommendation: recommendation)
            }
        }
    }
    
    private var adaptiveLearningHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Adaptive Learning")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Personalized Tamil writing journey")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .font(.title2)
                        .foregroundColor(.green)
                    
                    Text("AI Tutor")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var learningStateCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: learningStateIcon)
                    .font(.title2)
                    .foregroundColor(learningStateColor)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Learning Engine Status")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(adaptiveEngine.learningState.displayText)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if case .analyzing = adaptiveEngine.learningState {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(learningStateColor.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(learningStateColor.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private func currentLearningPathCard(_ learningPath: AdaptiveLearningEngine.LearningPath) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Your Learning Path")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View Details") {
                    showingLearningPathDetails = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            // Path type and progress
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(learningPath.pathType.displayName)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                    
                    Spacer()
                    
                    Text("\(learningPath.estimatedDuration) days")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(learningPath.pathType.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Current level and target
            HStack {
                LevelBadge(level: learningPath.currentLevel, label: "Current")
                
                Image(systemName: "arrow.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                LevelBadge(level: learningPath.targetLevel, label: "Target")
                
                Spacer()
            }
            
            // Next milestone
            if let nextMilestone = learningPath.milestones.first(where: { !$0.isCompleted }) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Next Milestone")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                    
                    Text(nextMilestone.title)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private var adaptiveRecommendationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("AI Recommendations")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            ForEach(adaptiveEngine.adaptiveRecommendations.prefix(3), id: \.id) { recommendation in
                LearningAdaptiveRecommendationCard(
                    recommendation: recommendation,
                    onTap: {
                        selectedRecommendation = recommendation
                        showingRecommendationDetails = true
                    }
                )
            }
            
            if adaptiveEngine.adaptiveRecommendations.count > 3 {
                Button("View All Recommendations (\(adaptiveEngine.adaptiveRecommendations.count))") {
                    // Show all recommendations
                }
                .font(.caption)
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity, alignment: .center)
            }
        }
    }
    
    private var personalizedContentSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Personalized Content")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            ForEach(adaptiveEngine.personalizedContent, id: \.id) { content in
                PersonalizedContentCard(content: content)
            }
        }
    }
    
    private var generatePathButton: some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.green.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("Generate Your Learning Path")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Let AI analyze your performance and create a personalized Tamil writing journey")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: generateLearningPath) {
                HStack {
                    if isGeneratingPath {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "sparkles")
                    }
                    
                    Text(isGeneratingPath ? "Generating..." : "Create My Path")
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color.green)
                )
            }
            .disabled(isGeneratingPath)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Computed Properties
    
    private var learningStateIcon: String {
        switch adaptiveEngine.learningState {
        case .analyzing: return "brain.head.profile"
        case .adapting: return "gearshape.2"
        case .ready: return "checkmark.circle.fill"
        case .error: return "exclamationmark.triangle.fill"
        }
    }
    
    private var learningStateColor: Color {
        switch adaptiveEngine.learningState {
        case .analyzing: return .blue
        case .adapting: return .orange
        case .ready: return .green
        case .error: return .red
        }
    }
    
    // MARK: - Actions
    
    private func generateLearningPath() {
        isGeneratingPath = true
        
        Task {
            let _ = await adaptiveEngine.generateAdaptiveLearningPath(for: "current-user")
            isGeneratingPath = false
        }
    }
}

// MARK: - Level Badge

struct LevelBadge: View {
    let level: CEFRLevel
    let label: String
    
    var body: some View {
        VStack(spacing: 2) {
            Text(level.rawValue.uppercased())
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(label)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(level.uiColor)
        )
    }
}

// MARK: - Recommendation Card

struct LearningAdaptiveRecommendationCard: View {
    let recommendation: AdaptiveLearningEngine.AdaptiveRecommendation
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(recommendation.title)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(recommendation.recommendationType.rawValue.replacingOccurrences(of: "_", with: " ").capitalized)
                            .font(.caption)
                            .foregroundColor(recommendation.impact.color)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 2) {
                        Text("\(Int(recommendation.confidence * 100))%")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                        
                        Text("Confidence")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                HStack {
                    Text("\(recommendation.actionItems.count) actions")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(recommendation.priority.rawValue.capitalized)
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(priorityColor(recommendation.priority))
                    
                    Image(systemName: "chevron.right")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(recommendation.impact.color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func priorityColor(_ priority: AdaptiveLearningEngine.AdaptiveRecommendation.Priority) -> Color {
        switch priority {
        case .low: return .gray
        case .medium: return .blue
        case .high: return .orange
        case .urgent: return .red
        }
    }
}

// MARK: - Personalized Content Card

struct PersonalizedContentCard: View {
    let content: AdaptiveLearningEngine.PersonalizedContent
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(content.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(content.contentType.rawValue.replacingOccurrences(of: "_", with: " ").capitalized)
                        .font(.caption)
                        .foregroundColor(.purple)
                }
                
                Spacer()
                
                Text("Valid until \(content.validUntil, style: .time)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Text(content.description)
                .font(.caption)
                .foregroundColor(.secondary)
            
            if !content.targetCharacters.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(content.targetCharacters, id: \.self) { character in
                            Text(character)
                                .font(.title3)
                                .foregroundColor(.primary)
                                .frame(width: 32, height: 32)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color(.tertiarySystemBackground))
                                )
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
            
            Button("Start Practice") {
                // Start personalized practice
            }
            .font(.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.purple)
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.purple.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Learning Path Detail View

struct LearningPathDetailView: View {
    let learningPath: AdaptiveLearningEngine.LearningPath
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Your Personalized Learning Path")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.top)
                    
                    // Path overview
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Path Overview")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text(learningPath.pathType.description)
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Text("Duration: \(learningPath.estimatedDuration) days")
                                .font(.subheadline)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text("Steps: \(learningPath.adaptiveSteps.count)")
                                .font(.subheadline)
                                .foregroundColor(.green)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                    
                    // Milestones
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Milestones")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        ForEach(learningPath.milestones, id: \.id) { milestone in
                            MilestoneRow(milestone: milestone)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                    
                    // Goals
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Personalized Goals")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        ForEach(learningPath.personalizedGoals, id: \.id) { goal in
                            GoalRow(goal: goal)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct MilestoneRow: View {
    let milestone: AdaptiveLearningEngine.LearningPath.Milestone
    
    var body: some View {
        HStack {
            Image(systemName: milestone.isCompleted ? "checkmark.circle.fill" : "circle")
                .foregroundColor(milestone.isCompleted ? .green : .gray)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(milestone.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("Target: \(milestone.targetDate, style: .date)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

struct GoalRow: View {
    let goal: AdaptiveLearningEngine.LearningPath.PersonalizedGoal
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text(goal.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(Int(goal.currentValue))/\(Int(goal.targetValue))")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
            
            ProgressView(value: goal.currentValue, total: goal.targetValue)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
        }
    }
}

// MARK: - Recommendation Detail View

struct RecommendationDetailView: View {
    let recommendation: AdaptiveLearningEngine.AdaptiveRecommendation
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text(recommendation.title)
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.top)
                    
                    Text(recommendation.description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    // Action items
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Action Items")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        ForEach(recommendation.actionItems, id: \.id) { actionItem in
                            ActionItemRow(actionItem: actionItem)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ActionItemRow: View {
    let actionItem: AdaptiveLearningEngine.AdaptiveRecommendation.ActionItem
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(actionItem.action)
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack {
                Text("\(actionItem.estimatedTime) min")
                    .font(.caption)
                    .foregroundColor(.blue)
                
                Text("•")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(actionItem.difficulty)
                    .font(.caption)
                    .foregroundColor(.orange)
                
                Spacer()
            }
            
            Text(actionItem.expectedOutcome)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.tertiarySystemBackground))
        )
    }
}

#Preview {
    AdaptiveLearningView()
}
