//
//  NIRABrandSystem.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

// MARK: - NIRA Brand System

struct NIRABrandSystem {

    // MARK: - Typography

    struct Typography {
        static let displayLarge = Font.system(size: 48, weight: .bold, design: .rounded)
        static let displayMedium = Font.system(size: 36, weight: .bold, design: .rounded)
        static let displaySmall = Font.system(size: 28, weight: .bold, design: .rounded)

        static let headlineLarge = Font.system(size: 24, weight: .semibold, design: .rounded)
        static let headlineMedium = Font.system(size: 20, weight: .semibold, design: .rounded)
        static let headlineSmall = Font.system(size: 18, weight: .semibold, design: .rounded)

        static let bodyLarge = Font.system(size: 16, weight: .regular, design: .default)
        static let bodyMedium = Font.system(size: 14, weight: .regular, design: .default)
        static let bodySmall = Font.system(size: 12, weight: .regular, design: .default)

        static let labelLarge = Font.system(size: 14, weight: .medium, design: .default)
        static let labelMedium = Font.system(size: 12, weight: .medium, design: .default)
        static let labelSmall = Font.system(size: 10, weight: .medium, design: .default)
    }

    // MARK: - Spacing

    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
    }

    // MARK: - Corner Radius

    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
        static let round: CGFloat = 50
    }

    // MARK: - Shadows

    struct Shadows {
        static let light = Shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        static let medium = Shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        static let heavy = Shadow(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
        static let brand = Shadow(color: .niraPrimary.opacity(0.3), radius: 8, x: 0, y: 4)
    }

    struct Shadow {
        let color: Color
        let radius: CGFloat
        let x: CGFloat
        let y: CGFloat
    }
}

// MARK: - Brand Components

struct NIRACard: View {
    let content: AnyView
    let style: CardStyle

    init<Content: View>(style: CardStyle = .default, @ViewBuilder content: () -> Content) {
        self.style = style
        self.content = AnyView(content())
    }

    var body: some View {
        content
            .padding(style.padding)
            .background(style.backgroundColor)
            .cornerRadius(style.cornerRadius)
            .shadow(
                color: style.shadow.color,
                radius: style.shadow.radius,
                x: style.shadow.x,
                y: style.shadow.y
            )
    }
}

enum CardStyle {
    case `default`, elevated, brand, minimal

    var backgroundColor: Color {
        switch self {
        case .default: return Color(.systemBackground)
        case .elevated: return Color(.systemBackground)
        case .brand: return Color.niraPrimary.opacity(0.1)
        case .minimal: return Color.clear
        }
    }

    var cornerRadius: CGFloat {
        switch self {
        case .default: return NIRABrandSystem.CornerRadius.medium
        case .elevated: return NIRABrandSystem.CornerRadius.large
        case .brand: return NIRABrandSystem.CornerRadius.large
        case .minimal: return NIRABrandSystem.CornerRadius.small
        }
    }

    var padding: CGFloat {
        switch self {
        case .default: return NIRABrandSystem.Spacing.md
        case .elevated: return NIRABrandSystem.Spacing.lg
        case .brand: return NIRABrandSystem.Spacing.lg
        case .minimal: return NIRABrandSystem.Spacing.sm
        }
    }

    var shadow: NIRABrandSystem.Shadow {
        switch self {
        case .default: return NIRABrandSystem.Shadows.light
        case .elevated: return NIRABrandSystem.Shadows.medium
        case .brand: return NIRABrandSystem.Shadows.brand
        case .minimal: return NIRABrandSystem.Shadow(color: .clear, radius: 0, x: 0, y: 0)
        }
    }
}

struct NIRAButton: View {
    let title: String
    let style: NIRAButtonStyle
    let size: ButtonSize
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: {
            HapticFeedbackManager.shared.buttonTap()
            action()
        }) {
            HStack(spacing: size.iconSpacing) {
                if let icon = style.icon {
                    Image(systemName: icon)
                        .font(.system(size: size.iconSize, weight: .medium))
                }

                Text(title)
                    .font(size.font)
                    .fontWeight(.semibold)
            }
            .foregroundColor(style.foregroundColor)
            .frame(minHeight: size.height)
            .frame(maxWidth: size.maxWidth)
            .padding(.horizontal, size.horizontalPadding)
            .background(style.backgroundColor)
            .cornerRadius(size.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: size.cornerRadius)
                    .stroke(style.borderColor, lineWidth: style.borderWidth)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(AnimationPresets.buttonPress, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

enum NIRAButtonStyle {
    case primary, secondary, tertiary, destructive, ghost

    var backgroundColor: Color {
        switch self {
        case .primary: return .niraPrimary
        case .secondary: return .niraSecondary
        case .tertiary: return Color(.systemGray6)
        case .destructive: return .red
        case .ghost: return .clear
        }
    }

    var foregroundColor: Color {
        switch self {
        case .primary: return .white
        case .secondary: return .white
        case .tertiary: return .primary
        case .destructive: return .white
        case .ghost: return .niraPrimary
        }
    }

    var borderColor: Color {
        switch self {
        case .ghost: return .niraPrimary
        default: return .clear
        }
    }

    var borderWidth: CGFloat {
        switch self {
        case .ghost: return 1
        default: return 0
        }
    }

    var icon: String? {
        return nil // Override in specific implementations
    }
}

enum ButtonSize {
    case small, medium, large

    var height: CGFloat {
        switch self {
        case .small: return 36
        case .medium: return 44
        case .large: return 52
        }
    }

    var font: Font {
        switch self {
        case .small: return NIRABrandSystem.Typography.labelMedium
        case .medium: return NIRABrandSystem.Typography.bodyMedium
        case .large: return NIRABrandSystem.Typography.bodyLarge
        }
    }

    var horizontalPadding: CGFloat {
        switch self {
        case .small: return NIRABrandSystem.Spacing.md
        case .medium: return NIRABrandSystem.Spacing.lg
        case .large: return NIRABrandSystem.Spacing.xl
        }
    }

    var cornerRadius: CGFloat {
        switch self {
        case .small: return NIRABrandSystem.CornerRadius.small
        case .medium: return NIRABrandSystem.CornerRadius.medium
        case .large: return NIRABrandSystem.CornerRadius.large
        }
    }

    var iconSize: CGFloat {
        switch self {
        case .small: return 14
        case .medium: return 16
        case .large: return 18
        }
    }

    var iconSpacing: CGFloat {
        return NIRABrandSystem.Spacing.sm
    }

    var maxWidth: CGFloat? {
        switch self {
        case .large: return .infinity
        default: return nil
        }
    }
}

#Preview("Brand System") {
    ScrollView {
        VStack(spacing: 30) {
            // Logo variations
            VStack(spacing: 20) {
                Text("NIRA Branding")
                    .font(NIRABrandSystem.Typography.displayMedium)

                HStack(spacing: 20) {
                    NIRALogo(size: .medium, style: .full)
                    NIRALogo(size: .medium, style: .icon)
                    NIRALogo(size: .medium, style: .minimal)
                }
            }

            // Button styles
            VStack(spacing: 16) {
                Text("Button Styles")
                    .font(NIRABrandSystem.Typography.headlineMedium)

                VStack(spacing: 12) {
                    NIRAButton(title: "Primary Button", style: .primary, size: .medium) {}
                    NIRAButton(title: "Secondary Button", style: .secondary, size: .medium) {}
                    NIRAButton(title: "Tertiary Button", style: .tertiary, size: .medium) {}
                    NIRAButton(title: "Ghost Button", style: .ghost, size: .medium) {}
                }
            }

            // Card styles
            VStack(spacing: 16) {
                Text("Card Styles")
                    .font(NIRABrandSystem.Typography.headlineMedium)

                VStack(spacing: 12) {
                    NIRACard(style: .default) {
                        Text("Default Card")
                    }

                    NIRACard(style: .elevated) {
                        Text("Elevated Card")
                    }

                    NIRACard(style: .brand) {
                        Text("Brand Card")
                    }
                }
            }
        }
        .padding()
    }
}
