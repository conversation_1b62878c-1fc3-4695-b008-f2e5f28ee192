//
//  HapticFeedbackManager.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import UIKit

class HapticFeedbackManager {
    static let shared = HapticFeedbackManager()
    
    private init() {}
    
    // MARK: - Impact Feedback
    
    func lightImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    func mediumImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    func heavyImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    // MARK: - Notification Feedback
    
    func success() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    func warning() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.warning)
    }
    
    func error() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.error)
    }
    
    // MARK: - Selection Feedback
    
    func selection() {
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }
    
    // MARK: - Context-Specific Feedback
    
    func buttonTap() {
        lightImpact()
    }
    
    func cardSelection() {
        mediumImpact()
    }
    
    func correctAnswer() {
        success()
    }
    
    func incorrectAnswer() {
        error()
    }
    
    func navigationTransition() {
        selection()
    }
    
    func achievementUnlocked() {
        heavyImpact()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.success()
        }
    }
    
    func simulationComplete() {
        success()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.mediumImpact()
        }
    }
}

// MARK: - SwiftUI View Extension

import SwiftUI

extension View {
    func hapticFeedback(_ type: HapticFeedbackType) -> some View {
        self.onTapGesture {
            switch type {
            case .light:
                HapticFeedbackManager.shared.lightImpact()
            case .medium:
                HapticFeedbackManager.shared.mediumImpact()
            case .heavy:
                HapticFeedbackManager.shared.heavyImpact()
            case .success:
                HapticFeedbackManager.shared.success()
            case .warning:
                HapticFeedbackManager.shared.warning()
            case .error:
                HapticFeedbackManager.shared.error()
            case .selection:
                HapticFeedbackManager.shared.selection()
            }
        }
    }
}

enum HapticFeedbackType {
    case light, medium, heavy
    case success, warning, error
    case selection
}
