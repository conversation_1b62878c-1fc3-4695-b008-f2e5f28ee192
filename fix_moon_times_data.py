#!/usr/bin/env python3
"""
Fix moon_times data to include required phase and illumination fields
"""

import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def fix_moon_times():
    """Fix moon_times data to include required fields"""
    print("🌙 Fixing Moon Times Data")
    print("=" * 40)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }
    
    # Get all records
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "id,date,moon_times",
        "order": "date.asc"
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code != 200:
        print(f"❌ Error fetching records: {response.status_code}")
        return
    
    records = response.json()
    print(f"📊 Found {len(records)} records to check")
    
    fixed_count = 0
    
    for record in records:
        try:
            record_date = record['date']
            record_id = record['id']
            moon_times_raw = record['moon_times']
            
            needs_fixing = False
            
            if moon_times_raw is None or moon_times_raw == "":
                # Create default moon_times
                moon_times = {
                    "moonrise": None,
                    "moonset": None,
                    "phase": "new_moon",
                    "illumination": 0.0
                }
                needs_fixing = True
            else:
                try:
                    if isinstance(moon_times_raw, str):
                        moon_times = json.loads(moon_times_raw)
                    else:
                        moon_times = moon_times_raw
                    
                    # Check if required fields are missing
                    if 'phase' not in moon_times or 'illumination' not in moon_times:
                        moon_times.setdefault('phase', 'new_moon')
                        moon_times.setdefault('illumination', 0.0)
                        needs_fixing = True
                        
                except json.JSONDecodeError:
                    # Invalid JSON, create default
                    moon_times = {
                        "moonrise": None,
                        "moonset": None,
                        "phase": "new_moon",
                        "illumination": 0.0
                    }
                    needs_fixing = True
            
            if needs_fixing:
                print(f"🔧 Fixing {record_date}...")
                
                # Update the record
                update_data = {
                    "moon_times": json.dumps(moon_times),
                    "updated_at": datetime.now().isoformat()
                }
                
                update_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
                update_params = {"id": f"eq.{record_id}"}
                
                update_response = requests.patch(update_url, json=update_data, headers=headers, params=update_params)
                
                if update_response.status_code in [200, 204]:
                    print(f"✅ Fixed {record_date}")
                    fixed_count += 1
                else:
                    print(f"❌ Failed to fix {record_date}: {update_response.status_code}")
            else:
                print(f"✅ {record_date} already has correct moon_times")
                
        except Exception as e:
            print(f"❌ Error processing {record.get('date', 'unknown')}: {e}")
    
    print(f"\n📊 SUMMARY:")
    print(f"✅ Fixed {fixed_count} records")
    print(f"🌙 All moon_times now have required phase and illumination fields!")

def main():
    fix_moon_times()

if __name__ == "__main__":
    main()
