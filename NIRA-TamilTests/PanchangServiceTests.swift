import XCTest
import CoreLocation
@testable import NIRA_Tamil

class PanchangServiceTests: XCTestCase {
    var panchangService: RealTimePanchangService!
    
    override func setUp() {
        super.setUp()
        panchangService = RealTimePanchangService.shared
    }
    
    override func tearDown() {
        panchangService = nil
        super.tearDown()
    }
    
    func testGetPanchangForToday() async throws {
        // Test with Chennai coordinates
        let chennaiLocation = CLLocation(latitude: 13.0827, longitude: 80.2707)
        
        do {
            let panchang = try await panchangService.getPanchangForDate(Date())
            
            XCTAssertNotNil(panchang, "Panchang should not be nil")
            
            if let panchang = panchang {
                // Verify basic structure
                XCTAssertNotNil(panchang.tithi, "Tithi should not be nil")
                XCTAssertNotNil(panchang.nakshatra, "Nakshatra should not be nil")
                XCTAssertNotNil(panchang.yoga, "Yoga should not be nil")
                XCTAssertNotNil(panchang.karana, "Karana should not be nil")
                XCTAssertNotNil(panchang.lunarMonth, "Lunar month should not be nil")
                XCTAssertNotNil(panchang.season, "Season should not be nil")
                
                // Verify tithi data
                XCTAssertGreaterThan(panchang.tithi.number, 0, "Tithi number should be positive")
                XCTAssertLessThanOrEqual(panchang.tithi.number, 30, "Tithi number should be <= 30")
                XCTAssertFalse(panchang.tithi.name.isEmpty, "Tithi name should not be empty")
                XCTAssertFalse(panchang.tithi.tamilName.isEmpty, "Tithi Tamil name should not be empty")
                
                // Verify nakshatra data
                XCTAssertGreaterThan(panchang.nakshatra.number, 0, "Nakshatra number should be positive")
                XCTAssertLessThanOrEqual(panchang.nakshatra.number, 27, "Nakshatra number should be <= 27")
                XCTAssertFalse(panchang.nakshatra.name.isEmpty, "Nakshatra name should not be empty")
                XCTAssertFalse(panchang.nakshatra.tamilName.isEmpty, "Nakshatra Tamil name should not be empty")
                
                // Verify sun times
                XCTAssertNotNil(panchang.sunTimes.sunrise, "Sunrise should not be nil")
                XCTAssertNotNil(panchang.sunTimes.sunset, "Sunset should not be nil")
                
                print("✅ Panchang test passed!")
                print("📅 Date: \(panchang.date)")
                print("🌙 Tithi: \(panchang.tithi.name) (\(panchang.tithi.tamilName))")
                print("⭐ Nakshatra: \(panchang.nakshatra.name) (\(panchang.nakshatra.tamilName))")
                print("🧘 Yoga: \(panchang.yoga.name) (\(panchang.yoga.tamilName))")
                print("⚡ Karana: \(panchang.karana.name) (\(panchang.karana.tamilName))")
                print("🌅 Sunrise: \(panchang.sunTimes.sunrise)")
                print("🌇 Sunset: \(panchang.sunTimes.sunset)")
            }
        } catch {
            XCTFail("Failed to get panchang: \(error)")
        }
    }
    
    func testGetPanchangForSpecificDate() async throws {
        // Test with a specific date (January 15, 2025)
        let calendar = Calendar.current
        let specificDate = calendar.date(from: DateComponents(year: 2025, month: 1, day: 15))!
        
        do {
            let panchang = try await panchangService.getPanchangForDate(specificDate)
            
            XCTAssertNotNil(panchang, "Panchang for specific date should not be nil")
            
            if let panchang = panchang {
                XCTAssertEqual(calendar.component(.day, from: panchang.date), 15, "Date should match")
                XCTAssertEqual(calendar.component(.month, from: panchang.date), 1, "Month should match")
                XCTAssertEqual(calendar.component(.year, from: panchang.date), 2025, "Year should match")
                
                print("✅ Specific date panchang test passed!")
                print("📅 Requested Date: \(specificDate)")
                print("📅 Returned Date: \(panchang.date)")
            }
        } catch {
            XCTFail("Failed to get panchang for specific date: \(error)")
        }
    }
    
    func testGetMonthlyPanchang() async throws {
        // Test getting panchang for current month
        let currentDate = Date()
        
        do {
            let monthlyPanchang = try await panchangService.getMonthlyPanchang(for: currentDate)
            
            XCTAssertFalse(monthlyPanchang.isEmpty, "Monthly panchang should not be empty")
            XCTAssertGreaterThanOrEqual(monthlyPanchang.count, 28, "Should have at least 28 days")
            XCTAssertLessThanOrEqual(monthlyPanchang.count, 31, "Should have at most 31 days")
            
            // Verify all entries have valid data
            for panchang in monthlyPanchang {
                XCTAssertNotNil(panchang.tithi, "Each day should have tithi")
                XCTAssertNotNil(panchang.nakshatra, "Each day should have nakshatra")
                XCTAssertGreaterThan(panchang.tithi.number, 0, "Tithi number should be positive")
                XCTAssertGreaterThan(panchang.nakshatra.number, 0, "Nakshatra number should be positive")
            }
            
            print("✅ Monthly panchang test passed!")
            print("📅 Month: \(Calendar.current.component(.month, from: currentDate))")
            print("📊 Days retrieved: \(monthlyPanchang.count)")
        } catch {
            XCTFail("Failed to get monthly panchang: \(error)")
        }
    }
}
