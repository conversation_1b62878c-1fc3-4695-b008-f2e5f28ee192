import XCTest
@testable import NIRA_Tamil

@MainActor
class TamilScriptServiceTest: XCTestCase {
    
    func testDatabaseConnection() async throws {
        let service = await TamilScriptService()
        
        print("🧪 Testing TamilScriptService database connection...")
        print("🔍 Supabase configured: \(APIKeys.supabaseConfigured)")
        print("🔍 Supabase URL: \(APIKeys.supabaseURL)")
        
        await service.loadAllCharacters()
        
        print("✅ Characters loaded: \(await service.allCharacters.count)")
        print("📝 Error message: \(await service.errorMessage ?? "none")")
        
        // Print first few characters for verification
        let characters = await service.allCharacters
        for (index, char) in characters.prefix(3).enumerated() {
            print("📝 Character \(index + 1): \(char.character) (\(char.romanization)) - \(char.characterType)")
        }
        
        let characterCount = await service.allCharacters.count
        let errorMessage = await service.errorMessage
        
        XCTAssertGreaterThan(characterCount, 0, "Should load characters from database")
        XCTAssertNil(errorMessage, "Should not have error message")
    }
}
