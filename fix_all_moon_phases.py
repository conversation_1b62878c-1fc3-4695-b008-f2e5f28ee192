#!/usr/bin/env python3
"""
Fix all moon phase formats from camelCase to snake_case
"""

import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def fix_all_moon_phases():
    """Fix all moon phase formats"""
    print("🌙 Fixing All Moon Phase Formats")
    print("=" * 50)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }
    
    # Get all records
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "id,date,moon_times",
        "order": "date.asc"
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code != 200:
        print(f"❌ Error: {response.status_code}")
        return
    
    records = response.json()
    print(f"📊 Found {len(records)} records to check")
    
    # Mapping from camelCase to snake_case
    phase_mapping = {
        'newMoon': 'new_moon',
        'waxingCrescent': 'waxing_crescent',
        'firstQuarter': 'first_quarter',
        'waxingGibbous': 'waxing_gibbous',
        'fullMoon': 'full_moon',
        'waningGibbous': 'waning_gibbous',
        'lastQuarter': 'last_quarter',
        'waningCrescent': 'waning_crescent'
    }
    
    fixed_count = 0
    
    for record in records:
        try:
            moon_data = json.loads(record['moon_times'])
            phase = moon_data.get('phase')
            
            if phase in phase_mapping:
                print(f"🔧 Fixing {record['date']}: {phase} -> {phase_mapping[phase]}")
                
                # Fix the phase
                moon_data['phase'] = phase_mapping[phase]
                
                # Update the record
                update_data = {
                    "moon_times": json.dumps(moon_data),
                    "updated_at": datetime.now().isoformat()
                }
                
                update_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
                update_params = {"id": f"eq.{record['id']}"}
                
                update_response = requests.patch(update_url, json=update_data, headers=headers, params=update_params)
                
                if update_response.status_code in [200, 204]:
                    fixed_count += 1
                else:
                    print(f"❌ Failed to fix {record['date']}: {update_response.status_code}")
            else:
                print(f"✅ {record['date']}: Already correct ({phase})")
                
        except Exception as e:
            print(f"❌ Error processing {record['date']}: {e}")
    
    print(f"\n📊 SUMMARY:")
    print(f"✅ Fixed {fixed_count} records")
    print(f"🌙 All moon phases now use correct snake_case format!")

def main():
    fix_all_moon_phases()

if __name__ == "__main__":
    main()
