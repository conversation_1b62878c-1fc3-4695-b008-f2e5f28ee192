#!/usr/bin/env python3
"""
Decode Base64 encoded data from Supabase
"""

import requests
import json
import base64

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def decode_base64_data():
    """Decode Base64 encoded data"""
    print("🔍 Decoding Base64 Data")
    print("=" * 40)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Get today's record
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "*",
        "date": "eq.2025-07-10",
        "limit": 1
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        records = response.json()
        if records:
            record = records[0]
            
            # Fields that might be Base64 encoded
            base64_fields = ['tithi', 'nakshatra', 'yoga', 'karana', 'sun_times', 'moon_times', 'weekday_info', 'location_info', 'tamil_date', 'lunar_month', 'season', 'year_info']
            
            print("🔓 Decoded data:")
            print("-" * 30)
            
            for field in base64_fields:
                if field in record and record[field]:
                    try:
                        # Remove quotes if present
                        encoded_data = record[field].strip("'\"")
                        
                        # Decode Base64
                        decoded_bytes = base64.b64decode(encoded_data)
                        decoded_str = decoded_bytes.decode('utf-8')
                        
                        # Parse JSON
                        parsed_data = json.loads(decoded_str)
                        
                        print(f"✅ {field}:")
                        print(f"   {json.dumps(parsed_data, indent=2)}")
                        print()
                        
                    except Exception as e:
                        print(f"❌ {field}: Decode error - {e}")
                        print(f"   Raw: {record[field][:50]}...")
                        print()
        else:
            print("❌ No record found")
    else:
        print(f"❌ Error: {response.status_code}")

def main():
    decode_base64_data()

if __name__ == "__main__":
    main()
