#!/usr/bin/env python3
"""
Tamil Calendar Test Script
Tests the Free Astrology API and Supabase database integration
"""

import requests
import json
from datetime import datetime, date
import uuid
import os
import sys

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

# Free Astrology API configuration
FREE_ASTROLOGY_API_KEY = "6df0ec16-722b-4acd-a574-bfd546c0c270"
FREE_ASTROLOGY_API_URL = "https://json.freeastrologyapi.com/vedicweekday"

def test_free_astrology_api():
    """Test the Free Astrology API"""
    print("🔍 Testing Free Astrology API...")

    # Try different authentication methods
    auth_methods = [
        {
            "name": "x-api-key header",
            "headers": {
                "Content-Type": "application/json",
                "x-api-key": FREE_ASTROLOGY_API_KEY
            }
        },
        {
            "name": "Authorization Bearer",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {FREE_ASTROLOGY_API_KEY}"
            }
        },
        {
            "name": "No authentication (free tier)",
            "headers": {
                "Content-Type": "application/json"
            }
        }
    ]

    # Prepare the request payload
    payload = {
        "year": 2025,
        "month": 7,
        "date": 10,
        "hours": 6,
        "minutes": 0,
        "seconds": 0,
        "latitude": 13.0827,  # Chennai coordinates
        "longitude": 80.2707,
        "timezone": 5.5,  # IST timezone
        "config": {
            "observation_point": "topocentric",
            "ayanamsha": "lahiri"
        }
    }

    for auth_method in auth_methods:
        print(f"🔍 Trying {auth_method['name']}...")

        try:
            response = requests.post(FREE_ASTROLOGY_API_URL, json=payload, headers=auth_method['headers'])
            print(f"📡 API Response Status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Free Astrology API Success with {auth_method['name']}!")
                print(f"📊 Response Data: {json.dumps(data, indent=2)}")
                return data
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"❌ Error Response: {response.text}")

        except Exception as e:
            print(f"❌ Exception calling API: {e}")

    # If all methods fail, try a simple GET request to check if the API is accessible
    print("🔍 Testing basic API accessibility...")
    try:
        simple_response = requests.get("https://json.freeastrologyapi.com", timeout=10)
        print(f"📡 Basic API Status: {simple_response.status_code}")
        if simple_response.status_code == 200:
            print("✅ API is accessible, but authentication/endpoint might be wrong")
        else:
            print("❌ API is not accessible")
    except Exception as e:
        print(f"❌ API not reachable: {e}")

    return None

def test_prokerala_api():
    """Test the ProKerala API as backup"""
    print("\n🔍 Testing ProKerala API (backup)...")

    # ProKerala API credentials
    client_id = "6df0ec16-722b-4acd-a574-bfd546c0c270"
    client_secret = "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"

    # First get access token
    token_url = "https://api.prokerala.com/token"
    token_data = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret
    }

    try:
        token_response = requests.post(token_url, data=token_data)
        print(f"📡 Token Response Status: {token_response.status_code}")

        if token_response.status_code == 200:
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            print("✅ ProKerala Token Success!")

            # Now test panchang API
            panchang_url = "https://api.prokerala.com/v2/astrology/panchang"
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            params = {
                "ayanamsa": 1,  # Lahiri
                "coordinates": "13.0827,80.2707",  # Chennai
                "datetime": "2025-07-10T06:00:00+05:30",  # Fixed format
                "la": "en"
            }

            panchang_response = requests.get(panchang_url, headers=headers, params=params)
            print(f"📡 Panchang Response Status: {panchang_response.status_code}")

            if panchang_response.status_code == 200:
                data = panchang_response.json()
                print("✅ ProKerala Panchang API Success!")
                print(f"📊 Response Data: {json.dumps(data, indent=2)}")
                return data
            else:
                print(f"❌ Panchang API Error: {panchang_response.status_code}")
                print(f"❌ Error Response: {panchang_response.text}")

        else:
            print(f"❌ Token Error: {token_response.status_code}")
            print(f"❌ Error Response: {token_response.text}")

    except Exception as e:
        print(f"❌ Exception calling ProKerala API: {e}")

    return None

def test_supabase_connection():
    """Test Supabase database connection"""
    print("\n🔍 Testing Supabase Connection...")

    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }

    try:
        # Test basic connection by querying the daily_panchang table
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang?select=id,date&limit=1"
        response = requests.get(url, headers=headers)

        print(f"📡 Supabase Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Supabase Connection Success!")
            print(f"📊 Sample Data: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Supabase Error: {response.status_code}")
            print(f"❌ Error Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Exception connecting to Supabase: {e}")
        return False

def test_database_schema():
    """Test the database schema for daily_panchang table"""
    print("\n🔍 Testing Database Schema...")

    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }

    try:
        # Get table schema information
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang?select=*&limit=0"
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            print("✅ Database Schema Accessible!")

            # Check if we can query with date filter
            today = date.today().strftime("%Y-%m-%d")
            url_with_date = f"{SUPABASE_URL}/rest/v1/daily_panchang?select=*&date=eq.{today}&limit=1"
            date_response = requests.get(url_with_date, headers=headers)

            print(f"📊 Date Query Status: {date_response.status_code}")
            if date_response.status_code == 200:
                data = date_response.json()
                print(f"📊 Date Query Result: {len(data)} records found")
                if data:
                    print(f"📊 Sample Record Keys: {list(data[0].keys())}")

            return True
        else:
            print(f"❌ Schema Error: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Exception testing schema: {e}")
        return False

def test_insert_panchang_data(api_data):
    """Test inserting panchang data into Supabase"""
    print("\n🔍 Testing Panchang Data Insert...")

    if not api_data:
        print("❌ No API data to insert")
        return False

    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }

    # Create test panchang record
    test_record = {
        "id": str(uuid.uuid4()),
        "date": "2025-07-10",
        "location_info": json.dumps({
            "latitude": 13.0827,
            "longitude": 80.2707,
            "city": "Chennai",
            "country": "India"
        }),
        "tamil_date": json.dumps({
            "tamil_year": 2025,
            "tamil_month": "Aadi",
            "tamil_day": 25
        }),
        "sun_times": json.dumps({
            "sunrise": "06:00",
            "sunset": "18:30"
        }),
        "moon_times": json.dumps({
            "moonrise": "Unknown",
            "moonset": "Unknown"
        }),
        "tithi": json.dumps([{
            "id": 1,
            "index": 1,
            "name": "Pratipada",
            "paksha": "Shukla",
            "start": "00:00",
            "end": "23:59"
        }]),
        "nakshatra": json.dumps([{
            "id": 1,
            "name": "Ashwini",
            "lord": {"id": 1, "name": "Ketu", "vedic_name": "Ketu"},
            "start": "00:00",
            "end": "23:59"
        }]),
        "yoga": json.dumps([{
            "id": 1,
            "name": "Vishkumbha",
            "start": "00:00",
            "end": "23:59"
        }]),
        "karana": json.dumps([{
            "id": 1,
            "index": 1,
            "name": "Bava",
            "start": "00:00",
            "end": "23:59"
        }]),
        "weekday_info": json.dumps({
            "weekday_number": 4,
            "weekday_name": "Thursday",
            "vedic_weekday_number": 4,
            "vedic_weekday_name": "Thursday"
        }),
        "lunar_month": json.dumps({
            "name": "Aadi",
            "number": 4
        }),
        "season": json.dumps({
            "name": "Summer",
            "tamil_name": "Kaar"
        }),
        "year_info": json.dumps({
            "gregorian_year": 2025,
            "tamil_year": 3025,
            "year_name": "Vilambi"
        }),
        "significance": "Test panchang data",
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }

    try:
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
        response = requests.post(url, json=test_record, headers=headers)

        print(f"📡 Insert Response Status: {response.status_code}")

        if response.status_code in [200, 201]:
            print("✅ Panchang Data Insert Success!")
            data = response.json()
            print(f"📊 Inserted Record ID: {data[0]['id'] if data else 'Unknown'}")
            return True
        elif response.status_code == 409:
            print("⚠️ Record already exists (conflict) - this is expected")
            return True
        else:
            print(f"❌ Insert Error: {response.status_code}")
            print(f"❌ Error Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Exception inserting data: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Tamil Calendar Integration Tests")
    print("=" * 50)

    # Test 1: Free Astrology API
    api_data = test_free_astrology_api()
    free_api_success = api_data is not None

    # Test 1b: ProKerala API (backup)
    if not free_api_success:
        api_data = test_prokerala_api()
    prokerala_success = api_data is not None

    api_success = free_api_success or prokerala_success

    # Test 2: Supabase Connection
    supabase_success = test_supabase_connection()

    # Test 3: Database Schema
    schema_success = test_database_schema()

    # Test 4: Data Insert
    insert_success = test_insert_panchang_data(api_data)

    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    print(f"🔗 Free Astrology API: {'✅ PASS' if free_api_success else '❌ FAIL'}")
    print(f"🔗 ProKerala API (backup): {'✅ PASS' if prokerala_success else '❌ FAIL'}")
    print(f"🔗 Overall API Status: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"🔗 Supabase Connection: {'✅ PASS' if supabase_success else '❌ FAIL'}")
    print(f"🔗 Database Schema: {'✅ PASS' if schema_success else '❌ FAIL'}")
    print(f"🔗 Data Insert: {'✅ PASS' if insert_success else '❌ FAIL'}")

    overall_success = all([api_success, supabase_success, schema_success, insert_success])
    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")

    if overall_success:
        print("\n🎉 Your Tamil Calendar integration is working perfectly!")
        print("🚀 The iOS app should work without any issues.")
    else:
        print("\n🔧 Some issues need to be fixed before the iOS app will work properly.")

        if api_success and supabase_success and schema_success:
            print("💡 Good news: APIs and database are working! The iOS app should work.")

    return overall_success

if __name__ == "__main__":
    main()