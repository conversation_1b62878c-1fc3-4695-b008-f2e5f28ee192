#!/usr/bin/env python3
"""
Debug Tamil Calendar Data Issues
Check what data exists and what might be missing
"""

import requests
import json
from datetime import datetime, date

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def debug_calendar_data():
    """Debug what data exists in the database"""
    print("🔍 Debugging Tamil Calendar Data")
    print("=" * 50)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Check what data exists
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "date",
        "order": "date.asc"
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code != 200:
        print(f"❌ Error fetching data: {response.status_code}")
        return
    
    records = response.json()
    print(f"✅ Found {len(records)} total records in database")
    
    if records:
        dates = [record['date'] for record in records]
        print(f"📅 Date range: {min(dates)} to {max(dates)}")
        
        # Check specific dates
        today = "2025-07-10"
        june_first = "2025-06-01"
        july_first = "2025-07-01"
        
        print(f"\n🔍 Checking specific dates:")
        print(f"   Today ({today}): {'✅ EXISTS' if today in dates else '❌ MISSING'}")
        print(f"   June 1 ({june_first}): {'✅ EXISTS' if june_first in dates else '❌ MISSING'}")
        print(f"   July 1 ({july_first}): {'✅ EXISTS' if july_first in dates else '❌ MISSING'}")
        
        # Show available months
        months = {}
        for date_str in dates:
            year_month = date_str[:7]  # YYYY-MM
            months[year_month] = months.get(year_month, 0) + 1
        
        print(f"\n📊 Available months:")
        for month, count in sorted(months.items()):
            print(f"   {month}: {count} days")
    
    # Check today's data specifically
    print(f"\n🔍 Checking today's data (2025-07-10):")
    today_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    today_params = {
        "select": "*",
        "date": "eq.2025-07-10"
    }
    
    today_response = requests.get(today_url, headers=headers, params=today_params)
    
    if today_response.status_code == 200:
        today_data = today_response.json()
        if today_data:
            print("✅ Today's data found!")
            record = today_data[0]
            try:
                tithi = json.loads(record['tithi'])
                nakshatra = json.loads(record['nakshatra'])
                print(f"   Tithi: {tithi[0]['name'] if tithi else 'Unknown'}")
                print(f"   Nakshatra: {nakshatra[0]['name'] if nakshatra else 'Unknown'}")
            except Exception as e:
                print(f"   ⚠️ Error parsing data: {e}")
        else:
            print("❌ No data found for today")
    else:
        print(f"❌ Error fetching today's data: {today_response.status_code}")

def main():
    debug_calendar_data()

if __name__ == "__main__":
    main()
