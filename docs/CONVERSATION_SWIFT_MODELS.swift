// Enhanced Swift models for conversation system with romanization and pronunciation

import Foundation

// MARK: - Conversation Line Model
struct TamilSupabaseConversationLine: Codable, Identifiable {
    let id: String
    let conversationId: String
    let lineNumber: Int
    let speakerName: String
    let speakerRole: String // 'person1', 'person2', 'narrator'
    
    // Core Text Content
    let textEnglish: String
    let textTamil: String
    let textRomanized: String
    
    // Pronunciation Guides
    let pronunciationIpa: String?
    let pronunciationSimple: String
    
    // Audio Files
    let audioUrl: String?
    let audioSlowUrl: String?
    
    // Learning Metadata
    let difficultyLevel: Int
    let keyPhrases: [String]
    let grammarNotes: String?
    let culturalNotes: String?
    
    let createdAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case conversationId = "conversation_id"
        case lineNumber = "line_number"
        case speakerName = "speaker_name"
        case speakerRole = "speaker_role"
        case textEnglish = "text_english"
        case textTamil = "text_tamil"
        case textRomanized = "text_romanized"
        case pronunciationIpa = "pronunciation_ipa"
        case pronunciationSimple = "pronunciation_simple"
        case audioUrl = "audio_url"
        case audioSlowUrl = "audio_slow_url"
        case difficultyLevel = "difficulty_level"
        case keyPhrases = "key_phrases"
        case grammarNotes = "grammar_notes"
        case culturalNotes = "cultural_notes"
        case createdAt = "created_at"
    }
}

// MARK: - Enhanced Conversation Model
struct TamilSupabaseConversation: Codable, Identifiable {
    let id: String
    let lessonId: String
    let conversationId: String
    let titleEnglish: String
    let titleTamil: String
    let contextDescription: String?
    let participants: String?
    let formalityLevel: String?
    let culturalSetting: String?
    let audioFullUrl: String?
    let createdAt: String
    
    // Computed properties for UI
    var participantsList: [String] {
        participants?.components(separatedBy: " & ") ?? []
    }
    
    var formalityColor: Color {
        switch formalityLevel?.lowercased() {
        case "formal": return .blue
        case "informal": return .green
        case "semi-formal": return .orange
        default: return .gray
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case lessonId = "lesson_id"
        case conversationId = "conversation_id"
        case titleEnglish = "title_english"
        case titleTamil = "title_tamil"
        case contextDescription = "context_description"
        case participants
        case formalityLevel = "formality_level"
        case culturalSetting = "cultural_setting"
        case audioFullUrl = "audio_full_url"
        case createdAt = "created_at"
    }
}

// MARK: - Conversation with Lines (Combined Model)
struct ConversationWithLines {
    let conversation: TamilSupabaseConversation
    let lines: [TamilSupabaseConversationLine]
    
    var totalLines: Int {
        lines.count
    }
    
    var speakers: [String] {
        Array(Set(lines.map { $0.speakerName })).sorted()
    }
    
    var averageDifficulty: Double {
        guard !lines.isEmpty else { return 0 }
        let total = lines.reduce(0) { $0 + $1.difficultyLevel }
        return Double(total) / Double(lines.count)
    }
}

// MARK: - SupabaseService Extension for Conversation Lines
extension SupabaseService {
    
    // Fetch conversation lines for a specific conversation
    func fetchConversationLines(for conversationId: String) async throws -> [TamilSupabaseConversationLine] {
        let response: [TamilSupabaseConversationLine] = try await supabase
            .from("conversation_lines")
            .select("*")
            .eq("conversation_id", value: conversationId)
            .order("line_number", ascending: true)
            .execute()
            .value
        
        return response
    }
    
    // Fetch conversation with all its lines
    func fetchConversationWithLines(conversationId: String) async throws -> ConversationWithLines? {
        // Fetch conversation metadata
        let conversations: [TamilSupabaseConversation] = try await supabase
            .from("conversations")
            .select("*")
            .eq("conversation_id", value: conversationId)
            .execute()
            .value
        
        guard let conversation = conversations.first else { return nil }
        
        // Fetch conversation lines
        let lines = try await fetchConversationLines(for: conversationId)
        
        return ConversationWithLines(conversation: conversation, lines: lines)
    }
    
    // Fetch all conversations with lines for a lesson
    func fetchConversationsWithLines(for lessonId: String) async throws -> [ConversationWithLines] {
        // First get all conversations for the lesson
        let conversations = try await fetchConversations(for: lessonId)
        
        // Then fetch lines for each conversation
        var conversationsWithLines: [ConversationWithLines] = []
        
        for conversation in conversations {
            let lines = try await fetchConversationLines(for: conversation.conversationId)
            conversationsWithLines.append(ConversationWithLines(conversation: conversation, lines: lines))
        }
        
        return conversationsWithLines
    }
}

// MARK: - Audio Manager Extension for Conversation Lines
extension AudioContentManager {
    
    // Play individual conversation line audio
    func playConversationLineAudio(line: TamilSupabaseConversationLine, slowSpeed: Bool = false) {
        let audioUrl = slowSpeed ? line.audioSlowUrl : line.audioUrl
        
        guard let urlString = audioUrl,
              let url = URL(string: urlString) else {
            print("❌ No audio URL available for conversation line")
            return
        }
        
        let filename = "conv_line_\(line.lineNumber)_\(line.speakerName.lowercased())"
        playAudio(url: url, filename: filename)
    }
    
    // Play full conversation audio
    func playFullConversationAudio(conversation: TamilSupabaseConversation) {
        guard let audioUrl = conversation.audioFullUrl,
              let url = URL(string: audioUrl) else {
            print("❌ No full conversation audio URL available")
            return
        }
        
        let filename = "conv_full_\(conversation.conversationId)"
        playAudio(url: url, filename: filename)
    }
}
