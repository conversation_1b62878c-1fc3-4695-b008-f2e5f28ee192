# Conversation Schema with Romanization & Pronunciation

## 📋 Enhanced Database Schema

### conversation_lines Table Structure
```sql
CREATE TABLE conversation_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id TEXT REFERENCES conversations(conversation_id),
    line_number INTEGER NOT NULL,
    speaker_name TEXT NOT NULL,
    speaker_role TEXT NOT NULL, -- 'person1', 'person2', 'narrator'
    
    -- Core Text Content
    text_english TEXT NOT NULL,
    text_tamil TEXT NOT NULL,
    text_romanized TEXT NOT NULL, -- Tamil in Roman script
    
    -- Pronunciation Guides
    pronunciation_ipa TEXT, -- International Phonetic Alphabet
    pronunciation_simple TEXT NOT NULL, -- Simplified pronunciation guide
    
    -- Audio Files
    audio_url TEXT, -- Individual line audio
    audio_slow_url TEXT, -- Slower pronunciation for learning
    
    -- Learning Metadata
    difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
    key_phrases TEXT[], -- Important phrases in this line
    grammar_notes TEXT, -- Grammar explanations for this line
    cultural_notes TEXT, -- Cultural context for this line
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_difficulty CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    CONSTRAINT valid_line_number CHECK (line_number > 0)
);

-- Indexes for performance
CREATE INDEX idx_conversation_lines_conversation_id ON conversation_lines(conversation_id);
CREATE INDEX idx_conversation_lines_line_number ON conversation_lines(conversation_id, line_number);
```

## 🎭 Complete Example: "Meeting a Friend" Conversation

### Conversation Metadata (conversations table)
```json
{
  "conversation_id": "L1C1",
  "title_english": "Meeting a Friend",
  "title_tamil": "நண்பரைச் சந்தித்தல்",
  "context_description": "Two friends meet on the street and exchange greetings",
  "participants": "Raj & Priya (Friends)",
  "formality_level": "informal",
  "cultural_setting": "casual_street_meeting"
}
```

### Conversation Lines (conversation_lines table)
```json
[
  {
    "line_number": 1,
    "speaker_name": "Raj",
    "speaker_role": "person1",
    "text_english": "Hello! How are you?",
    "text_tamil": "வணக்கம்! எப்படி இருக்கிறீர்கள்?",
    "text_romanized": "Vanakkam! Eppadi irukkireergal?",
    "pronunciation_ipa": "ʋəɳəkːəm ɛpːəɖi iɾukːiɾeːɾɡəɭ",
    "pronunciation_simple": "va-nak-kam! ep-pa-di i-ruk-ki-reer-gal?",
    "difficulty_level": 1,
    "key_phrases": ["வணக்கம்", "எப்படி இருக்கிறீர்கள்"],
    "grammar_notes": "வணக்கம் is the standard Tamil greeting. எப்படி இருக்கிறீர்கள் is formal 'how are you'",
    "cultural_notes": "வணக்கம் can be used any time of day, similar to 'hello' in English"
  },
  {
    "line_number": 2,
    "speaker_name": "Priya",
    "speaker_role": "person2", 
    "text_english": "I'm fine, thank you. How about you?",
    "text_tamil": "நான் நலமாக இருக்கிறேன், நன்றி. நீங்கள் எப்படி?",
    "text_romanized": "Naan nalamaaga irukkiren, nandri. Neengal eppadi?",
    "pronunciation_ipa": "naːn nələmaːɡə iɾukːiɾen nənɖɾi neːŋɡəɭ ɛpːəɖi",
    "pronunciation_simple": "naan na-la-maa-ga i-ruk-ki-ren, nan-dri. neen-gal ep-pa-di?",
    "difficulty_level": 2,
    "key_phrases": ["நலமாக இருக்கிறேன்", "நன்றி", "நீங்கள் எப்படி"],
    "grammar_notes": "நலமாக இருக்கிறேன் = 'I am well'. நன்றி = 'thank you'. நீங்கள் is respectful 'you'",
    "cultural_notes": "Using நீங்கள் shows respect, even among friends in formal contexts"
  },
  {
    "line_number": 3,
    "speaker_name": "Raj",
    "speaker_role": "person1",
    "text_english": "I'm also doing well. Where are you going?",
    "text_tamil": "நானும் நலமாக இருக்கிறேன். நீங்கள் எங்கே போகிறீர்கள்?",
    "text_romanized": "Naanum nalamaaga irukkiren. Neengal engae pogireeergal?",
    "pronunciation_ipa": "naːnum nələmaːɡə iɾukːiɾen neːŋɡəɭ ɛŋɡeː poːɡiɾeːɾɡəɭ",
    "pronunciation_simple": "naa-num na-la-maa-ga i-ruk-ki-ren. neen-gal en-gae po-gi-reer-gal?",
    "difficulty_level": 2,
    "key_phrases": ["நானும்", "எங்கே போகிறீர்கள்"],
    "grammar_notes": "நானும் = 'I also/too'. எங்கே போகிறீர்கள் = 'where are you going' (formal)",
    "cultural_notes": "Asking where someone is going is a common conversation starter in Tamil culture"
  },
  {
    "line_number": 4,
    "speaker_name": "Priya", 
    "speaker_role": "person2",
    "text_english": "I'm going to the market. See you later!",
    "text_tamil": "நான் சந்தைக்குப் போகிறேன். பிறகு சந்திப்போம்!",
    "text_romanized": "Naan sandhaikkup pogireen. Piragu sandhippom!",
    "pronunciation_ipa": "naːn sənðəikːup poːɡiɾeːn piɾəɡu sənðipːoːm",
    "pronunciation_simple": "naan san-thai-kkup po-gi-reen. pi-ra-gu san-dhip-pom!",
    "difficulty_level": 3,
    "key_phrases": ["சந்தைக்குப் போகிறேன்", "பிறகு சந்திப்போம்"],
    "grammar_notes": "சந்தைக்குப் = 'to the market' (dative case). பிறகு சந்திப்போம் = 'see you later'",
    "cultural_notes": "Markets are central to Tamil community life, common destination for daily activities"
  }
]
```

## 🎵 Audio File Naming Convention

### Individual Line Audio Files
```
conv_L1C1_line01_raj.mp3     // Line 1 - Raj speaking
conv_L1C1_line01_raj_slow.mp3 // Line 1 - Raj speaking (slow)
conv_L1C1_line02_priya.mp3   // Line 2 - Priya speaking  
conv_L1C1_line02_priya_slow.mp3 // Line 2 - Priya speaking (slow)
conv_L1C1_line03_raj.mp3     // Line 3 - Raj speaking
conv_L1C1_line03_raj_slow.mp3 // Line 3 - Raj speaking (slow)
conv_L1C1_line04_priya.mp3   // Line 4 - Priya speaking
conv_L1C1_line04_priya_slow.mp3 // Line 4 - Priya speaking (slow)
conv_L1C1_full.mp3           // Complete conversation
```

## 📱 UI Display Pattern

### Conversation Line Component
```
┌─────────────────────────────────────┐
│ 👤 Raj                         🔊 🐌│
│ Hello! How are you?                 │
│ வணக்கம்! எப்படி இருக்கிறீர்கள்?      │
│ Vanakkam! Eppadi irukkireergal?     │
│ 📝 va-nak-kam! ep-pa-di...          │
│ ℹ️ Grammar: வணக்கம் is standard...   │
└─────────────────────────────────────┘
```

### Audio Controls
- 🔊 **Normal Speed**: Regular pronunciation
- 🐌 **Slow Speed**: Slower for learning
- 📝 **Show/Hide Pronunciation**: Toggle pronunciation guide
- ℹ️ **Grammar Notes**: Expandable grammar explanations

## 🎯 Implementation Benefits

1. **Complete Learning Experience**: Text + Audio + Pronunciation
2. **Multiple Learning Styles**: Visual, auditory, and phonetic
3. **Progressive Difficulty**: Difficulty levels for each line
4. **Cultural Context**: Understanding beyond just language
5. **Grammar Integration**: Line-specific grammar explanations
6. **Flexible Audio**: Normal and slow speeds for different learners
