// Enhanced ConversationDetailView with romanization and pronunciation
import SwiftUI

struct EnhancedConversationDetailView: View {
    let conversation: TamilSupabaseConversation
    @State private var conversationLines: [TamilSupabaseConversationLine] = []
    @State private var currentLineIndex = 0
    @State private var showPronunciation = false
    @State private var showGrammarNotes = false
    @State private var showCulturalNotes = false
    @State private var isLoading = true
    
    @StateObject private var audioManager = AudioContentManager.shared
    @StateObject private var supabaseService = SupabaseService.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.black.opacity(0.9),
                        Color.green.opacity(0.3),
                        Color.black.opacity(0.9)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                if isLoading {
                    ProgressView("Loading conversation...")
                        .foregroundColor(.white)
                } else if conversationLines.isEmpty {
                    Text("No conversation lines available")
                        .foregroundColor(.white)
                } else {
                    ScrollView {
                        VStack(spacing: 20) {
                            // Header Section
                            conversationHeaderView
                            
                            // Current Line Display
                            currentLineView
                            
                            // Navigation Controls
                            lineNavigationView
                            
                            // Additional Information
                            additionalInfoView
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle("Conversation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.green)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: playFullConversation) {
                        Image(systemName: "play.circle.fill")
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .task {
            await loadConversationLines()
        }
    }
    
    // MARK: - Header View
    private var conversationHeaderView: some View {
        VStack(spacing: 12) {
            // Title
            VStack(spacing: 8) {
                Text(conversation.titleEnglish)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(conversation.titleTamil)
                    .font(.title3)
                    .foregroundColor(.green)
            }
            
            // Metadata badges
            HStack(spacing: 12) {
                if let participants = conversation.participants {
                    Badge(text: participants, color: .blue)
                }
                
                if let formality = conversation.formalityLevel {
                    Badge(text: formality.capitalized, color: conversation.formalityColor)
                }
            }
            
            // Context
            if let context = conversation.contextDescription {
                Text(context)
                    .font(.body)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Current Line View
    private var currentLineView: some View {
        VStack(spacing: 16) {
            if currentLineIndex < conversationLines.count {
                let line = conversationLines[currentLineIndex]
                
                // Speaker Header
                HStack {
                    Text("👤 \(line.speakerName)")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // Audio Controls
                    HStack(spacing: 12) {
                        Button(action: { playLineAudio(line: line, slowSpeed: false) }) {
                            Image(systemName: "speaker.2.fill")
                                .foregroundColor(.green)
                        }
                        
                        Button(action: { playLineAudio(line: line, slowSpeed: true) }) {
                            Image(systemName: "tortoise.fill")
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                // Text Content
                VStack(spacing: 12) {
                    // English Text
                    Text(line.textEnglish)
                        .font(.body)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // Tamil Text
                    Text(line.textTamil)
                        .font(.title3)
                        .foregroundColor(.green)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // Romanization
                    Text(line.textRomanized)
                        .font(.body)
                        .foregroundColor(.blue)
                        .italic()
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // Pronunciation Guide (Toggleable)
                    if showPronunciation {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Pronunciation:")
                                .font(.caption)
                                .foregroundColor(.gray)
                            
                            Text(line.pronunciationSimple)
                                .font(.body)
                                .foregroundColor(.yellow)
                                .multilineTextAlignment(.leading)
                                .frame(maxWidth: .infinity, alignment: .leading)
                            
                            if let ipa = line.pronunciationIpa {
                                Text("IPA: \(ipa)")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .multilineTextAlignment(.leading)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                        }
                    }
                }
                
                // Toggle Pronunciation Button
                Button(action: { showPronunciation.toggle() }) {
                    HStack {
                        Image(systemName: showPronunciation ? "eye.slash" : "eye")
                        Text(showPronunciation ? "Hide Pronunciation" : "Show Pronunciation")
                    }
                    .font(.caption)
                    .foregroundColor(.gray)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Line Navigation
    private var lineNavigationView: some View {
        HStack(spacing: 20) {
            Button(action: previousLine) {
                Image(systemName: "chevron.left.circle.fill")
                    .font(.title2)
                    .foregroundColor(currentLineIndex > 0 ? .green : .gray)
            }
            .disabled(currentLineIndex <= 0)
            
            Text("\(currentLineIndex + 1) / \(conversationLines.count)")
                .font(.body)
                .foregroundColor(.white)
            
            Button(action: nextLine) {
                Image(systemName: "chevron.right.circle.fill")
                    .font(.title2)
                    .foregroundColor(currentLineIndex < conversationLines.count - 1 ? .green : .gray)
            }
            .disabled(currentLineIndex >= conversationLines.count - 1)
        }
        .padding()
    }
    
    // MARK: - Additional Info
    private var additionalInfoView: some View {
        VStack(spacing: 12) {
            if currentLineIndex < conversationLines.count {
                let line = conversationLines[currentLineIndex]
                
                // Grammar Notes
                if let grammarNotes = line.grammarNotes, !grammarNotes.isEmpty {
                    ExpandableSection(
                        title: "Grammar Notes",
                        content: grammarNotes,
                        isExpanded: $showGrammarNotes,
                        color: .blue
                    )
                }
                
                // Cultural Notes
                if let culturalNotes = line.culturalNotes, !culturalNotes.isEmpty {
                    ExpandableSection(
                        title: "Cultural Context",
                        content: culturalNotes,
                        isExpanded: $showCulturalNotes,
                        color: .orange
                    )
                }
                
                // Key Phrases
                if !line.keyPhrases.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Key Phrases:")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        LazyVGrid(columns: [
                            GridItem(.adaptive(minimum: 120))
                        ], spacing: 8) {
                            ForEach(line.keyPhrases, id: \.self) { phrase in
                                Text(phrase)
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.green.opacity(0.2))
                                    .foregroundColor(.green)
                                    .cornerRadius(8)
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial)
                    )
                }
            }
        }
    }
    
    // MARK: - Helper Views
    private struct Badge: View {
        let text: String
        let color: Color
        
        var body: some View {
            Text(text)
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(color.opacity(0.2))
                .foregroundColor(color)
                .cornerRadius(8)
        }
    }
    
    private struct ExpandableSection: View {
        let title: String
        let content: String
        @Binding var isExpanded: Bool
        let color: Color
        
        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                Button(action: { isExpanded.toggle() }) {
                    HStack {
                        Text(title)
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .foregroundColor(color)
                    }
                }
                
                if isExpanded {
                    Text(content)
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Actions
    private func loadConversationLines() async {
        do {
            let lines = try await supabaseService.fetchConversationLines(for: conversation.conversationId)
            await MainActor.run {
                self.conversationLines = lines
                self.isLoading = false
            }
        } catch {
            print("❌ Error loading conversation lines: \(error)")
            await MainActor.run {
                self.isLoading = false
            }
        }
    }
    
    private func nextLine() {
        if currentLineIndex < conversationLines.count - 1 {
            currentLineIndex += 1
            showPronunciation = false
            showGrammarNotes = false
            showCulturalNotes = false
        }
    }
    
    private func previousLine() {
        if currentLineIndex > 0 {
            currentLineIndex -= 1
            showPronunciation = false
            showGrammarNotes = false
            showCulturalNotes = false
        }
    }
    
    private func playLineAudio(line: TamilSupabaseConversationLine, slowSpeed: Bool) {
        audioManager.playConversationLineAudio(line: line, slowSpeed: slowSpeed)
    }
    
    private func playFullConversation() {
        audioManager.playFullConversationAudio(conversation: conversation)
    }
}
