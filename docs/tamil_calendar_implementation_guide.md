# Tamil Calendar Implementation Guide

## 🎯 **Implementation Priority Matrix**

### **High Priority (Week 1-2)**
1. **Real-time Panchang API Integration**
2. **Daily Data Caching System**
3. **Enhanced Calendar Service**
4. **Monthly Calendar Grid View**

### **Medium Priority (Week 3)**
1. **Daily Detail Modal**
2. **Festival Integration**
3. **Muhurat Timings**
4. **Notification System**

### **Low Priority (Week 4)**
1. **Regional Customization**
2. **Cultural Learning Content**
3. **Advanced Analytics**
4. **Performance Optimization**

## 🔧 **Technical Implementation**

### **1. API Service Layer**

```swift
// NIRA-Tamil/Services/RealTimePanchangService.swift
import Foundation
import Combine

@MainActor
class RealTimePanchangService: ObservableObject {
    static let shared = RealTimePanchangService()
    
    @Published var todayPanchang: DailyPanchang?
    @Published var currentMonth: [DailyPanchang] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let apiKey = "YOUR_API_KEY"
    private let baseURL = "https://json.freeastrologyapi.com"
    private let supabaseClient = NIRASupabaseClient.shared
    
    // MARK: - Public Methods
    
    func loadTodayPanchang() async {
        await loadPanchangForDate(Date())
    }
    
    func loadMonthPanchang(for date: Date) async {
        // Load entire month data
    }
    
    func getPanchangForDate(_ date: Date) async -> DailyPanchang? {
        // Check cache first, then API
    }
    
    // MARK: - Private Methods
    
    private func fetchFromAPI(date: Date) async throws -> DailyPanchang {
        // API call implementation
    }
    
    private func cacheData(_ panchang: DailyPanchang) async {
        // Store in Supabase
    }
    
    private func getCachedData(for date: Date) async -> DailyPanchang? {
        // Retrieve from Supabase
    }
}
```

### **2. Data Models**

```swift
// NIRA-Tamil/Models/PanchangModels.swift
struct DailyPanchang: Codable, Identifiable {
    let id: UUID
    let date: Date
    let tamilDate: TamilDate
    let sunTimes: SunTimes
    let moonTimes: MoonTimes
    let tithi: Tithi
    let nakshatra: Nakshatra
    let yoga: Yoga
    let karana: Karana
    let muhurat: [Muhurat]
    let inauspiciousTimes: [InauspiciousTime]
    let festivals: [TamilFestival]
    let significance: String?
}

struct TamilDate: Codable {
    let day: Int
    let month: TamilMonth
    let year: Int
    let paksha: Paksha
    let season: TamilSeason
}

struct Tithi: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let paksha: Paksha
    let completionTime: Date?
    let percentage: Double
}

struct Nakshatra: Codable {
    let number: Int
    let name: String
    let tamilName: String
    let lord: String
    let startTime: Date
    let endTime: Date
    let percentage: Double
}

struct Muhurat: Codable {
    let type: MuhuratType
    let name: String
    let tamilName: String
    let startTime: Date
    let endTime: Date
    let significance: String
    let isAuspicious: Bool
}

enum MuhuratType: String, Codable, CaseIterable {
    case brahmaMuhurat = "brahma_muhurat"
    case abhijitMuhurat = "abhijit_muhurat"
    case godhuli = "godhuli"
    case rahuKalam = "rahu_kalam"
    case yamaGandam = "yama_gandam"
    case gulikaKalam = "gulika_kalam"
}
```

### **3. Monthly Calendar View**

```swift
// NIRA-Tamil/Views/Calendar/MonthlyCalendarView.swift
struct MonthlyCalendarView: View {
    @StateObject private var panchangService = RealTimePanchangService.shared
    @State private var selectedDate = Date()
    @State private var currentMonth = Date()
    @State private var showingDayDetail = false
    
    private let calendar = Calendar.current
    private let dateFormatter = DateFormatter()
    
    var body: some View {
        VStack(spacing: 0) {
            // Month Header
            monthHeader
            
            // Weekday Headers
            weekdayHeaders
            
            // Calendar Grid
            calendarGrid
            
            // Today's Summary
            todaySummary
        }
        .sheet(isPresented: $showingDayDetail) {
            DayDetailView(date: selectedDate)
        }
        .task {
            await panchangService.loadMonthPanchang(for: currentMonth)
        }
    }
    
    private var monthHeader: some View {
        HStack {
            Button(action: previousMonth) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.niraPrimary)
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Text(monthName)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(tamilMonthName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(action: nextMonth) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.niraPrimary)
            }
        }
        .padding()
    }
    
    private var calendarGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
            ForEach(monthDates, id: \.self) { date in
                CalendarDayCell(
                    date: date,
                    panchang: panchangService.getPanchang(for: date),
                    isSelected: calendar.isDate(date, inSameDayAs: selectedDate),
                    isToday: calendar.isToday(date)
                )
                .onTapGesture {
                    selectedDate = date
                    showingDayDetail = true
                }
            }
        }
        .padding(.horizontal)
    }
}
```

### **4. Day Detail Modal**

```swift
// NIRA-Tamil/Views/Calendar/DayDetailView.swift
struct DayDetailView: View {
    let date: Date
    @StateObject private var panchangService = RealTimePanchangService.shared
    @State private var panchang: DailyPanchang?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Date Header
                    dateHeader
                    
                    // Panchang Details
                    if let panchang = panchang {
                        panchangDetails(panchang)
                    }
                    
                    // Festivals
                    festivalsSection
                    
                    // Muhurat Timings
                    muhuratSection
                    
                    // Cultural Significance
                    significanceSection
                }
                .padding()
            }
            .navigationTitle("Daily Details")
            .navigationBarTitleDisplayMode(.inline)
        }
        .task {
            panchang = await panchangService.getPanchangForDate(date)
        }
    }
    
    private func panchangDetails(_ panchang: DailyPanchang) -> some View {
        VStack(spacing: 16) {
            // Tithi Card
            PanchangCard(
                title: "Tithi",
                tamilTitle: "திதி",
                value: panchang.tithi.name,
                tamilValue: panchang.tithi.tamilName,
                percentage: panchang.tithi.percentage
            )
            
            // Nakshatra Card
            PanchangCard(
                title: "Nakshatra",
                tamilTitle: "நட்சத்திரம்",
                value: panchang.nakshatra.name,
                tamilValue: panchang.nakshatra.tamilName,
                percentage: panchang.nakshatra.percentage
            )
            
            // Yoga Card
            PanchangCard(
                title: "Yoga",
                tamilTitle: "யோகம்",
                value: panchang.yoga.name,
                tamilValue: panchang.yoga.tamilName,
                percentage: nil
            )
        }
    }
}
```

## 📊 **Database Schema Implementation**

```sql
-- Create tables for panchang data
CREATE TABLE daily_panchang (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE UNIQUE NOT NULL,
    tamil_date JSONB NOT NULL,
    sun_times JSONB NOT NULL,
    moon_times JSONB NOT NULL,
    tithi JSONB NOT NULL,
    nakshatra JSONB NOT NULL,
    yoga JSONB NOT NULL,
    karana JSONB NOT NULL,
    muhurat JSONB NOT NULL,
    inauspicious_times JSONB NOT NULL,
    significance TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Index for fast date lookups
CREATE INDEX idx_daily_panchang_date ON daily_panchang(date);

-- Create muhurat timings table
CREATE TABLE muhurat_timings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    muhurat_type VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    tamil_name VARCHAR(100) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    significance TEXT,
    is_auspicious BOOLEAN NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Index for muhurat lookups
CREATE INDEX idx_muhurat_date_type ON muhurat_timings(date, muhurat_type);
```

## 🔄 **API Integration Implementation**

```swift
// NIRA-Tamil/Services/PanchangAPIClient.swift
class PanchangAPIClient {
    private let session = URLSession.shared
    private let apiKey: String
    private let baseURL = "https://json.freeastrologyapi.com"
    
    init(apiKey: String) {
        self.apiKey = apiKey
    }
    
    func fetchPanchang(for date: Date, location: Location) async throws -> PanchangResponse {
        let components = Calendar.current.dateComponents([.year, .month, .day], from: date)
        
        let request = PanchangRequest(
            year: components.year!,
            month: components.month!,
            date: components.day!,
            hours: 6, // Default to 6 AM
            minutes: 0,
            seconds: 0,
            latitude: location.latitude,
            longitude: location.longitude,
            timezone: 5.5, // IST
            config: PanchangConfig(
                observationPoint: "topocentric",
                ayanamsha: "lahiri"
            )
        )
        
        var urlRequest = URLRequest(url: URL(string: "\(baseURL)/complete-panchang")!)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue(apiKey, forHTTPHeaderField: "x-api-key")
        urlRequest.httpBody = try JSONEncoder().encode(request)
        
        let (data, response) = try await session.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw PanchangError.invalidResponse
        }
        
        return try JSONDecoder().decode(PanchangResponse.self, from: data)
    }
}
```

This implementation guide provides the foundation for building a world-class Tamil calendar system that will serve the global Tamil community with accurate, culturally rich calendar information.
