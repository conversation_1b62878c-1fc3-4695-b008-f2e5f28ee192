# NIRA Tamil Lessons Database Schema

## Overview
This document defines the comprehensive database schema for the NIRA Tamil language learning platform. The schema is designed to handle scalable lesson content, user progress tracking, and efficient data loading.

## Core Design Principles

### 1. **Scalability**
- Support for millions of lessons and users
- Efficient pagination and lazy loading
- Optimized for mobile performance

### 2. **Flexibility**
- Support for multiple content types (vocabulary, conversations, grammar, practice)
- Extensible for future lesson types
- Multi-language support (Tamil, English, romanization)

### 3. **Performance**
- Indexed for fast queries
- Minimal data loading for UI responsiveness
- Efficient progress tracking

### 4. **Data Integrity**
- Foreign key constraints
- Validation rules
- Consistent data structure

## Database Tables

### 1. **lessons**
Primary table for lesson metadata and organization.

```sql
CREATE TABLE lessons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_number INTEGER NOT NULL,
    level_code VARCHAR(10) NOT NULL, -- A1, A2, B1, B2, C1, C2
    title_english VARCHAR(200) NOT NULL,
    title_tamil VARCHAR(200) NOT NULL,
    title_romanization VARCHAR(200),
    description_english TEXT,
    description_tamil TEXT,
    focus VARCHAR(500), -- Learning objectives
    duration_minutes INTEGER DEFAULT 15,
    difficulty_score INTEGER DEFAULT 1, -- 1-10 scale
    prerequisites TEXT[], -- Array of prerequisite lesson IDs
    tags TEXT[], -- Searchable tags
    cultural_context TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance
    CONSTRAINT unique_lesson_per_level UNIQUE(level_code, lesson_number)
);

-- Indexes
CREATE INDEX idx_lessons_level_code ON lessons(level_code);
CREATE INDEX idx_lessons_difficulty ON lessons(difficulty_score);
CREATE INDEX idx_lessons_tags ON lessons USING GIN(tags);
CREATE INDEX idx_lessons_active ON lessons(is_active) WHERE is_active = true;
```

### 2. **vocabulary**
Vocabulary items for lessons with rich metadata.

```sql
CREATE TABLE vocabulary (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_id UUID NOT NULL REFERENCES lessons(id) ON DELETE CASCADE,
    vocab_id VARCHAR(50) NOT NULL, -- L1V01, L1V02, etc.
    english_word VARCHAR(100) NOT NULL,
    tamil_translation VARCHAR(100) NOT NULL,
    romanization VARCHAR(100) NOT NULL,
    ipa_pronunciation VARCHAR(100),
    part_of_speech VARCHAR(50),
    difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
    frequency_rank INTEGER, -- How common this word is
    cultural_notes TEXT,
    related_terms TEXT[],
    example_sentence_english TEXT,
    example_sentence_tamil TEXT,
    example_sentence_romanization TEXT,
    audio_word_url VARCHAR(500),
    audio_sentence_url VARCHAR(500),
    image_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_vocab_id_per_lesson UNIQUE(lesson_id, vocab_id)
);

-- Indexes
CREATE INDEX idx_vocabulary_lesson_id ON vocabulary(lesson_id);
CREATE INDEX idx_vocabulary_english_word ON vocabulary(english_word);
CREATE INDEX idx_vocabulary_tamil_word ON vocabulary(tamil_translation);
CREATE INDEX idx_vocabulary_difficulty ON vocabulary(difficulty_level);
CREATE INDEX idx_vocabulary_frequency ON vocabulary(frequency_rank);
```

### 3. **conversations**
Dialogue-based learning content.

```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_id UUID NOT NULL REFERENCES lessons(id) ON DELETE CASCADE,
    conversation_id VARCHAR(50) NOT NULL, -- L1C01, L1C02, etc.
    title_english VARCHAR(200) NOT NULL,
    title_tamil VARCHAR(200) NOT NULL,
    context_description TEXT,
    participants VARCHAR(200), -- "Raj & Priya"
    formality_level VARCHAR(20), -- formal, informal, neutral
    cultural_setting VARCHAR(100), -- home, office, street, etc.
    audio_full_url VARCHAR(500), -- Full conversation audio
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_conversation_id_per_lesson UNIQUE(lesson_id, conversation_id)
);

-- Indexes
CREATE INDEX idx_conversations_lesson_id ON conversations(lesson_id);
CREATE INDEX idx_conversations_formality ON conversations(formality_level);
```

### 4. **dialogue_lines**
Individual lines within conversations.

```sql
CREATE TABLE dialogue_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    line_number INTEGER NOT NULL,
    speaker VARCHAR(100) NOT NULL,
    line_english TEXT NOT NULL,
    line_tamil TEXT NOT NULL,
    line_romanization TEXT,
    emotion VARCHAR(50), -- happy, sad, excited, etc.
    audio_url VARCHAR(500),
    timing_start DECIMAL(5,2), -- Start time in seconds
    timing_end DECIMAL(5,2), -- End time in seconds
    
    CONSTRAINT unique_line_per_conversation UNIQUE(conversation_id, line_number)
);

-- Indexes
CREATE INDEX idx_dialogue_lines_conversation_id ON dialogue_lines(conversation_id);
CREATE INDEX idx_dialogue_lines_speaker ON dialogue_lines(speaker);
```

### 5. **grammar_topics**
Grammar explanations and rules.

```sql
CREATE TABLE grammar_topics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_id UUID NOT NULL REFERENCES lessons(id) ON DELETE CASCADE,
    grammar_id VARCHAR(50) NOT NULL, -- L1G01, L1G02, etc.
    title_english VARCHAR(200) NOT NULL,
    title_tamil VARCHAR(200) NOT NULL,
    rule_english TEXT NOT NULL,
    rule_tamil TEXT NOT NULL,
    explanation TEXT,
    difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
    tips TEXT[],
    common_mistakes TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_grammar_id_per_lesson UNIQUE(lesson_id, grammar_id)
);

-- Indexes
CREATE INDEX idx_grammar_topics_lesson_id ON grammar_topics(lesson_id);
CREATE INDEX idx_grammar_topics_difficulty ON grammar_topics(difficulty_level);
```

### 6. **grammar_examples**
Examples for grammar topics.

```sql
CREATE TABLE grammar_examples (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    grammar_topic_id UUID NOT NULL REFERENCES grammar_topics(id) ON DELETE CASCADE,
    example_number INTEGER NOT NULL,
    example_english TEXT NOT NULL,
    example_tamil TEXT NOT NULL,
    example_romanization TEXT,
    breakdown_explanation TEXT,
    audio_url VARCHAR(500),
    
    CONSTRAINT unique_example_per_grammar UNIQUE(grammar_topic_id, example_number)
);

-- Indexes
CREATE INDEX idx_grammar_examples_grammar_topic_id ON grammar_examples(grammar_topic_id);
```

### 7. **practice_exercises**
Interactive practice activities.

```sql
CREATE TABLE practice_exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_id UUID NOT NULL REFERENCES lessons(id) ON DELETE CASCADE,
    exercise_id VARCHAR(50) NOT NULL, -- L1P01, L1P02, etc.
    exercise_type VARCHAR(50) NOT NULL, -- multiple_choice, fill_blank, matching, etc.
    title_english VARCHAR(200) NOT NULL,
    title_tamil VARCHAR(200) NOT NULL,
    instructions_english TEXT NOT NULL,
    instructions_tamil TEXT NOT NULL,
    difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
    points_value INTEGER DEFAULT 10,
    time_limit_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT unique_exercise_id_per_lesson UNIQUE(lesson_id, exercise_id)
);

-- Indexes
CREATE INDEX idx_practice_exercises_lesson_id ON practice_exercises(lesson_id);
CREATE INDEX idx_practice_exercises_type ON practice_exercises(exercise_type);
CREATE INDEX idx_practice_exercises_difficulty ON practice_exercises(difficulty_level);
```

### 8. **exercise_questions**
Individual questions within practice exercises.

```sql
CREATE TABLE exercise_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exercise_id UUID NOT NULL REFERENCES practice_exercises(id) ON DELETE CASCADE,
    question_number INTEGER NOT NULL,
    question_text_english TEXT NOT NULL,
    question_text_tamil TEXT,
    question_audio_url VARCHAR(500),
    correct_answer TEXT NOT NULL,
    explanation_english TEXT,
    explanation_tamil TEXT,
    points_value INTEGER DEFAULT 1,

    CONSTRAINT unique_question_per_exercise UNIQUE(exercise_id, question_number)
);

-- Indexes
CREATE INDEX idx_exercise_questions_exercise_id ON exercise_questions(exercise_id);
```

### 9. **exercise_options**
Multiple choice options for questions.

```sql
CREATE TABLE exercise_options (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID NOT NULL REFERENCES exercise_questions(id) ON DELETE CASCADE,
    option_letter CHAR(1) NOT NULL, -- A, B, C, D
    option_text_english TEXT NOT NULL,
    option_text_tamil TEXT,
    is_correct BOOLEAN DEFAULT false,

    CONSTRAINT unique_option_per_question UNIQUE(question_id, option_letter)
);

-- Indexes
CREATE INDEX idx_exercise_options_question_id ON exercise_options(question_id);
CREATE INDEX idx_exercise_options_correct ON exercise_options(is_correct) WHERE is_correct = true;
```

## User Progress Tables

### 10. **user_lesson_progress**
Track user progress through lessons.

```sql
CREATE TABLE user_lesson_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL, -- References auth.users
    lesson_id UUID NOT NULL REFERENCES lessons(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'not_started', -- not_started, in_progress, completed
    progress_percentage INTEGER DEFAULT 0, -- 0-100
    vocabulary_mastered INTEGER DEFAULT 0,
    conversations_completed INTEGER DEFAULT 0,
    grammar_understood INTEGER DEFAULT 0,
    exercises_completed INTEGER DEFAULT 0,
    total_score INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT unique_user_lesson UNIQUE(user_id, lesson_id)
);

-- Indexes
CREATE INDEX idx_user_lesson_progress_user_id ON user_lesson_progress(user_id);
CREATE INDEX idx_user_lesson_progress_lesson_id ON user_lesson_progress(lesson_id);
CREATE INDEX idx_user_lesson_progress_status ON user_lesson_progress(status);
CREATE INDEX idx_user_lesson_progress_last_accessed ON user_lesson_progress(last_accessed_at);
```

### 11. **user_vocabulary_mastery**
Track individual vocabulary word mastery.

```sql
CREATE TABLE user_vocabulary_mastery (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL, -- References auth.users
    vocabulary_id UUID NOT NULL REFERENCES vocabulary(id) ON DELETE CASCADE,
    mastery_level INTEGER DEFAULT 0, -- 0-5 scale
    times_practiced INTEGER DEFAULT 0,
    times_correct INTEGER DEFAULT 0,
    last_practiced_at TIMESTAMP WITH TIME ZONE,
    mastered_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT unique_user_vocabulary UNIQUE(user_id, vocabulary_id)
);

-- Indexes
CREATE INDEX idx_user_vocabulary_mastery_user_id ON user_vocabulary_mastery(user_id);
CREATE INDEX idx_user_vocabulary_mastery_vocabulary_id ON user_vocabulary_mastery(vocabulary_id);
CREATE INDEX idx_user_vocabulary_mastery_level ON user_vocabulary_mastery(mastery_level);
```

### 12. **user_exercise_attempts**
Track user attempts at practice exercises.

```sql
CREATE TABLE user_exercise_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL, -- References auth.users
    exercise_id UUID NOT NULL REFERENCES practice_exercises(id) ON DELETE CASCADE,
    attempt_number INTEGER NOT NULL,
    score INTEGER NOT NULL,
    max_score INTEGER NOT NULL,
    time_taken_seconds INTEGER,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT unique_user_exercise_attempt UNIQUE(user_id, exercise_id, attempt_number)
);

-- Indexes
CREATE INDEX idx_user_exercise_attempts_user_id ON user_exercise_attempts(user_id);
CREATE INDEX idx_user_exercise_attempts_exercise_id ON user_exercise_attempts(exercise_id);
CREATE INDEX idx_user_exercise_attempts_completed_at ON user_exercise_attempts(completed_at);
```

## API Design for Efficient Loading

### 1. **Lesson List API**
```
GET /api/lessons?level=A1&page=1&limit=10
```

**Response:**
```json
{
  "lessons": [
    {
      "id": "uuid",
      "lesson_number": 1,
      "level_code": "A1",
      "title_english": "Basic Greetings",
      "title_tamil": "அடிப்படை வாழ்த்துகள்",
      "duration_minutes": 15,
      "difficulty_score": 1,
      "progress": {
        "status": "completed",
        "progress_percentage": 100
      }
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 5,
    "total_lessons": 50
  }
}
```

### 2. **Lesson Detail API**
```
GET /api/lessons/{lesson_id}
```

**Response:**
```json
{
  "lesson": {
    "id": "uuid",
    "lesson_number": 1,
    "level_code": "A1",
    "title_english": "Basic Greetings",
    "title_tamil": "அடிப்படை வாழ்த்துகள்",
    "description_english": "Learn essential Tamil greetings",
    "focus": "Greetings, basic politeness",
    "cultural_context": "Tamil greeting customs"
  },
  "vocabulary_count": 25,
  "conversation_count": 10,
  "grammar_count": 5,
  "exercise_count": 10,
  "user_progress": {
    "status": "in_progress",
    "progress_percentage": 60,
    "vocabulary_mastered": 15,
    "conversations_completed": 6
  }
}
```

### 3. **Lesson Content APIs (Lazy Loading)**
```
GET /api/lessons/{lesson_id}/vocabulary?page=1&limit=5
GET /api/lessons/{lesson_id}/conversations?page=1&limit=2
GET /api/lessons/{lesson_id}/grammar?page=1&limit=2
GET /api/lessons/{lesson_id}/exercises?page=1&limit=2
```

## Implementation Strategy

### Phase 1: Database Setup
1. Create all tables in Supabase
2. Set up Row Level Security (RLS) policies
3. Create database functions for complex queries
4. Add sample data for A1 lessons

### Phase 2: API Layer
1. Create Supabase functions for lesson queries
2. Implement pagination and filtering
3. Add caching for frequently accessed data
4. Create progress tracking functions

### Phase 3: iOS Integration
1. Update TamilContentService to use database
2. Implement lazy loading for lesson content
3. Add offline caching with Core Data
4. Implement progress synchronization

### Phase 4: Performance Optimization
1. Add database indexes for common queries
2. Implement content preloading strategies
3. Add image and audio CDN integration
4. Optimize for mobile data usage

## Benefits of This Schema

### 1. **Scalability**
- Supports unlimited lessons and users
- Efficient pagination prevents memory issues
- Indexed for fast queries

### 2. **Flexibility**
- Easy to add new content types
- Supports multiple languages
- Extensible progress tracking

### 3. **Performance**
- Lazy loading prevents data overload
- Optimized for mobile devices
- Efficient progress queries

### 4. **User Experience**
- Fast lesson loading
- Detailed progress tracking
- Offline capability support

This schema will eliminate the "Index out of range" errors by providing structured, paginated data loading instead of large in-memory arrays.
```
