#!/usr/bin/env python3
"""
🎵 GOOGLE TTS AUDIO GENERATOR FOR NIRA
=====================================
Generates audio files for Tamil lesson content using Google Text-to-Speech

FEATURES:
- Multi-process parallel audio generation
- Google Cloud TTS with Tamil voice support
- Automatic file organization and S3 upload
- Progress tracking and error handling
- Comprehensive content coverage (vocabulary, conversations, grammar, exercises)
"""

import json
import os
import requests
import time
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from google.cloud import texttospeech
from google.oauth2 import service_account
import hashlib
import base64

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.XWPv7bqjwCZ0aYmAIxnWtICe8CU9r1eIw7mESbOns44"
GOOGLE_CREDENTIALS_PATH = "/Users/<USER>/Documents/NIRA/nira-460718-95832b0001f9.json"
AUDIO_OUTPUT_DIR = "/Users/<USER>/Documents/NIRA/temp_audio"

# Global statistics
audio_stats = {
    'total_items': 0,
    'completed_items': 0,
    'vocabulary_audio': 0,
    'conversation_audio': 0,
    'grammar_audio': 0,
    'exercise_audio': 0,
    'errors': []
}
stats_lock = threading.Lock()

def setup_google_tts():
    """Initialize Google Text-to-Speech client"""
    try:
        credentials = service_account.Credentials.from_service_account_file(
            GOOGLE_CREDENTIALS_PATH
        )
        client = texttospeech.TextToSpeechClient(credentials=credentials)
        print("✅ Google TTS client initialized successfully")
        return client
    except Exception as e:
        print(f"❌ Failed to initialize Google TTS: {e}")
        return None

def create_audio_directories():
    """Create directory structure for audio files"""
    directories = [
        f"{AUDIO_OUTPUT_DIR}/tamil/A1/vocabulary",
        f"{AUDIO_OUTPUT_DIR}/tamil/A1/conversations", 
        f"{AUDIO_OUTPUT_DIR}/tamil/A1/grammar",
        f"{AUDIO_OUTPUT_DIR}/tamil/A1/exercises"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print(f"📁 Created audio directory structure in {AUDIO_OUTPUT_DIR}")

def generate_audio_file(client, text: str, output_path: str, voice_name: str = "ta-IN-Standard-A") -> bool:
    """Generate audio file using Google TTS"""
    try:
        # Configure the voice request
        voice = texttospeech.VoiceSelectionParams(
            language_code="ta-IN",
            name=voice_name
        )
        
        # Configure audio format
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3,
            speaking_rate=0.9,  # Slightly slower for learning
            pitch=0.0
        )
        
        # Create synthesis input
        synthesis_input = texttospeech.SynthesisInput(text=text)
        
        # Generate audio
        response = client.synthesize_speech(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config
        )
        
        # Save audio file
        with open(output_path, "wb") as out:
            out.write(response.audio_content)
        
        return True
        
    except Exception as e:
        with stats_lock:
            audio_stats['errors'].append(f"Audio generation failed for {output_path}: {e}")
        return False

def get_lesson_content(lesson_id: str):
    """Fetch all content for a lesson from Supabase"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json'
    }
    
    content = {
        'vocabulary': [],
        'conversations': [],
        'grammar': [],
        'exercises': []
    }
    
    try:
        # Get vocabulary
        vocab_url = f"{SUPABASE_URL}/rest/v1/lesson_vocabulary"
        vocab_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'word_order'}
        vocab_response = requests.get(vocab_url, headers=headers, params=vocab_params)
        vocab_response.raise_for_status()
        content['vocabulary'] = vocab_response.json()
        
        # Get conversations
        conv_url = f"{SUPABASE_URL}/rest/v1/lesson_conversations"
        conv_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'conversation_order'}
        conv_response = requests.get(conv_url, headers=headers, params=conv_params)
        conv_response.raise_for_status()
        content['conversations'] = conv_response.json()
        
        # Get grammar
        grammar_url = f"{SUPABASE_URL}/rest/v1/lesson_grammar"
        grammar_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'grammar_order'}
        grammar_response = requests.get(grammar_url, headers=headers, params=grammar_params)
        grammar_response.raise_for_status()
        content['grammar'] = grammar_response.json()
        
        # Get exercises
        exercise_url = f"{SUPABASE_URL}/rest/v1/lesson_exercises"
        exercise_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'exercise_order'}
        exercise_response = requests.get(exercise_url, headers=headers, params=exercise_params)
        exercise_response.raise_for_status()
        content['exercises'] = exercise_response.json()
        
        return content
        
    except Exception as e:
        print(f"❌ Error fetching lesson content: {e}")
        return None

def generate_vocabulary_audio(client, lesson_num: int, vocabulary_items: List[Dict]):
    """Generate audio for vocabulary items"""
    success_count = 0
    
    for item in vocabulary_items:
        try:
            # Generate audio for Tamil word
            word_filename = f"lesson_{lesson_num:02d}_vocab_{item['word_order']:02d}_word.mp3"
            word_path = f"{AUDIO_OUTPUT_DIR}/tamil/A1/vocabulary/{word_filename}"
            
            if generate_audio_file(client, item['word_target_language'], word_path):
                success_count += 1
            
            # Generate audio for example sentence
            example_filename = f"lesson_{lesson_num:02d}_vocab_{item['word_order']:02d}_example.mp3"
            example_path = f"{AUDIO_OUTPUT_DIR}/tamil/A1/vocabulary/{example_filename}"
            
            if generate_audio_file(client, item['example_sentence_target_language'], example_path):
                success_count += 1
                
        except Exception as e:
            with stats_lock:
                audio_stats['errors'].append(f"Vocabulary audio error for lesson {lesson_num}, item {item['word_order']}: {e}")
    
    with stats_lock:
        audio_stats['vocabulary_audio'] += success_count
    
    print(f"📚 Lesson {lesson_num}: Generated {success_count} vocabulary audio files")
    return success_count

def generate_conversation_audio(client, lesson_num: int, conversations: List[Dict]):
    """Generate audio for conversation content"""
    success_count = 0
    
    for conv in conversations:
        try:
            # Generate audio for each dialogue line
            dialogue_lines = conv.get('conversation_content', [])
            
            for i, line in enumerate(dialogue_lines):
                if 'tamil' in line:
                    line_filename = f"lesson_{lesson_num:02d}_conv_{conv['conversation_order']:02d}_line_{i+1:02d}.mp3"
                    line_path = f"{AUDIO_OUTPUT_DIR}/tamil/A1/conversations/{line_filename}"
                    
                    if generate_audio_file(client, line['tamil'], line_path):
                        success_count += 1
                        
        except Exception as e:
            with stats_lock:
                audio_stats['errors'].append(f"Conversation audio error for lesson {lesson_num}, conv {conv['conversation_order']}: {e}")
    
    with stats_lock:
        audio_stats['conversation_audio'] += success_count
    
    print(f"💬 Lesson {lesson_num}: Generated {success_count} conversation audio files")
    return success_count

def generate_grammar_audio(client, lesson_num: int, grammar_points: List[Dict]):
    """Generate audio for grammar explanations"""
    success_count = 0
    
    for gram in grammar_points:
        try:
            # Generate audio for Tamil explanation
            if gram.get('explanation_target_language'):
                explanation_filename = f"lesson_{lesson_num:02d}_grammar_{gram['grammar_order']:02d}_explanation.mp3"
                explanation_path = f"{AUDIO_OUTPUT_DIR}/tamil/A1/grammar/{explanation_filename}"
                
                if generate_audio_file(client, gram['explanation_target_language'], explanation_path):
                    success_count += 1
            
            # Generate audio for examples
            examples = gram.get('examples', [])
            for i, example in enumerate(examples):
                if isinstance(example, dict) and 'tamil' in example:
                    example_filename = f"lesson_{lesson_num:02d}_grammar_{gram['grammar_order']:02d}_example_{i+1}.mp3"
                    example_path = f"{AUDIO_OUTPUT_DIR}/tamil/A1/grammar/{example_filename}"
                    
                    if generate_audio_file(client, example['tamil'], example_path):
                        success_count += 1
                        
        except Exception as e:
            with stats_lock:
                audio_stats['errors'].append(f"Grammar audio error for lesson {lesson_num}, grammar {gram['grammar_order']}: {e}")
    
    with stats_lock:
        audio_stats['grammar_audio'] += success_count
    
    print(f"📖 Lesson {lesson_num}: Generated {success_count} grammar audio files")
    return success_count

def generate_exercise_audio(client, lesson_num: int, exercises: List[Dict]):
    """Generate audio for exercise content"""
    success_count = 0
    
    for exercise in exercises:
        try:
            # Generate audio for instructions
            if exercise.get('instructions_target_language'):
                instruction_filename = f"lesson_{lesson_num:02d}_exercise_{exercise['exercise_order']:02d}_instructions.mp3"
                instruction_path = f"{AUDIO_OUTPUT_DIR}/tamil/A1/exercises/{instruction_filename}"
                
                if generate_audio_file(client, exercise['instructions_target_language'], instruction_path):
                    success_count += 1
            
            # Generate audio for exercise content (if it contains Tamil text)
            exercise_content = exercise.get('exercise_content', {})
            if isinstance(exercise_content, dict):
                # Look for Tamil text in various fields
                tamil_fields = ['sentence_tamil', 'question_tamil', 'audio_text']
                for field in tamil_fields:
                    if field in exercise_content and exercise_content[field]:
                        content_filename = f"lesson_{lesson_num:02d}_exercise_{exercise['exercise_order']:02d}_{field}.mp3"
                        content_path = f"{AUDIO_OUTPUT_DIR}/tamil/A1/exercises/{content_filename}"
                        
                        if generate_audio_file(client, exercise_content[field], content_path):
                            success_count += 1
                            
        except Exception as e:
            with stats_lock:
                audio_stats['errors'].append(f"Exercise audio error for lesson {lesson_num}, exercise {exercise['exercise_order']}: {e}")
    
    with stats_lock:
        audio_stats['exercise_audio'] += success_count
    
    print(f"🎯 Lesson {lesson_num}: Generated {success_count} exercise audio files")
    return success_count

def process_lesson_audio(client, lesson_id: str, lesson_num: int, lesson_title: str):
    """Process all audio generation for a single lesson"""
    try:
        print(f"\n🚀 Processing Audio for Lesson {lesson_num}: {lesson_title}")

        # Get lesson content
        content = get_lesson_content(lesson_id)
        if not content:
            print(f"❌ Failed to fetch content for lesson {lesson_num}")
            return False

        total_audio_files = 0

        # Generate vocabulary audio
        if content['vocabulary']:
            vocab_count = generate_vocabulary_audio(client, lesson_num, content['vocabulary'])
            total_audio_files += vocab_count

        # Generate conversation audio
        if content['conversations']:
            conv_count = generate_conversation_audio(client, lesson_num, content['conversations'])
            total_audio_files += conv_count

        # Generate grammar audio
        if content['grammar']:
            grammar_count = generate_grammar_audio(client, lesson_num, content['grammar'])
            total_audio_files += grammar_count

        # Generate exercise audio
        if content['exercises']:
            exercise_count = generate_exercise_audio(client, lesson_num, content['exercises'])
            total_audio_files += exercise_count

        with stats_lock:
            audio_stats['completed_items'] += 1

        print(f"✅ Lesson {lesson_num}: Generated {total_audio_files} total audio files")
        return True

    except Exception as e:
        error_msg = f"Lesson {lesson_num} audio processing error: {e}"
        with stats_lock:
            audio_stats['errors'].append(error_msg)
        print(f"❌ {error_msg}")
        return False

def get_uploaded_lessons():
    """Get list of lessons that have content uploaded"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json'
    }

    try:
        # Get lessons that have vocabulary (indicating they have content)
        url = f"{SUPABASE_URL}/rest/v1/lessons"
        params = {
            'select': 'id,lesson_number,title_english',
            'language_code': 'eq.ta',
            'cefr_level': 'eq.A1',
            'lesson_number': 'in.(10,13,14,15,16,17,18,19,20,21,22,23,24,25)',
            'order': 'lesson_number'
        }

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        lessons = response.json()

        print(f"📚 Found {len(lessons)} lessons with uploaded content")
        return lessons

    except Exception as e:
        print(f"❌ Error fetching lessons: {e}")
        return []

def main():
    """Main execution function"""
    print("🎵 GOOGLE TTS AUDIO GENERATOR FOR NIRA")
    print("=" * 60)
    print("🎯 Target: Tamil A1 Lesson Audio Generation")
    print("🔊 Engine: Google Cloud Text-to-Speech")
    print("")

    start_time = time.time()

    # Setup
    print("🔧 Setting up audio generation environment...")

    # Initialize Google TTS
    client = setup_google_tts()
    if not client:
        print("❌ Failed to initialize Google TTS. Exiting.")
        return

    # Create directories
    create_audio_directories()

    # Get lessons to process
    lessons = get_uploaded_lessons()
    if not lessons:
        print("❌ No lessons found to process. Exiting.")
        return

    audio_stats['total_items'] = len(lessons)

    print(f"🚀 Starting parallel audio generation for {len(lessons)} lessons...")
    print(f"🔄 Using {min(3, len(lessons))} workers for optimal performance...")
    print("")

    # Process lessons in parallel (limited to 3 workers to avoid API rate limits)
    successful_lessons = 0
    failed_lessons = 0

    with ThreadPoolExecutor(max_workers=3) as executor:
        # Submit all tasks
        future_to_lesson = {
            executor.submit(
                process_lesson_audio,
                client,
                lesson['id'],
                lesson['lesson_number'],
                lesson['title_english']
            ): lesson
            for lesson in lessons
        }

        # Process completed tasks
        for future in as_completed(future_to_lesson):
            lesson = future_to_lesson[future]
            try:
                success = future.result()
                if success:
                    successful_lessons += 1
                else:
                    failed_lessons += 1

                # Progress update
                completed = successful_lessons + failed_lessons
                print(f"📊 Progress: {completed}/{len(lessons)} lessons processed")

            except Exception as e:
                failed_lessons += 1
                error_msg = f"Future execution error for lesson {lesson['lesson_number']}: {e}"
                with stats_lock:
                    audio_stats['errors'].append(error_msg)
                print(f"❌ {error_msg}")

    # Final statistics
    end_time = time.time()
    duration = end_time - start_time

    print("\n" + "=" * 60)
    print("🎉 AUDIO GENERATION COMPLETE!")
    print("=" * 60)
    print(f"⏱️  Total Time: {duration:.2f} seconds")
    print(f"📚 Lessons Processed: {audio_stats['completed_items']}/{audio_stats['total_items']}")
    print(f"✅ Successful: {successful_lessons}")
    print(f"❌ Failed: {failed_lessons}")
    print("")
    print("🎵 AUDIO FILES GENERATED:")
    print(f"   📝 Vocabulary Audio: {audio_stats['vocabulary_audio']}")
    print(f"   💬 Conversation Audio: {audio_stats['conversation_audio']}")
    print(f"   📖 Grammar Audio: {audio_stats['grammar_audio']}")
    print(f"   🎯 Exercise Audio: {audio_stats['exercise_audio']}")
    total_files = sum([audio_stats['vocabulary_audio'], audio_stats['conversation_audio'], audio_stats['grammar_audio'], audio_stats['exercise_audio']])
    print(f"   🎼 Total Audio Files: {total_files}")

    if audio_stats['errors']:
        print(f"\n⚠️  ERRORS ({len(audio_stats['errors'])}):")
        for error in audio_stats['errors'][:10]:  # Show first 10 errors
            print(f"   • {error}")
        if len(audio_stats['errors']) > 10:
            print(f"   ... and {len(audio_stats['errors']) - 10} more errors")

    print(f"\n📁 Audio files saved to: {AUDIO_OUTPUT_DIR}")
    print("\n🚀 Next Steps:")
    print("   1. Review generated audio files")
    print("   2. Upload audio files to S3/Supabase storage")
    print("   3. Update database with audio URLs")
    print("   4. Test audio playback in NIRA app")

if __name__ == "__main__":
    main()
