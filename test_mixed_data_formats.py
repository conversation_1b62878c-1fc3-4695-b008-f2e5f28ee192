#!/usr/bin/env python3
"""
Test that the app can now handle both JSON and Base64 data formats
"""

import requests
import json
import base64

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def test_data_formats():
    """Test both data formats in the database"""
    print("🔍 Testing Mixed Data Formats")
    print("=" * 50)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Test June data (JSON format from Python script)
    print("📅 JUNE 2025 DATA (JSON format):")
    print("-" * 30)
    
    june_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    june_params = {
        "select": "date,tithi,nakshatra",
        "date": "eq.2025-06-01",
        "limit": 1
    }
    
    june_response = requests.get(june_url, headers=headers, params=june_params)
    
    if june_response.status_code == 200:
        june_records = june_response.json()
        if june_records:
            june_record = june_records[0]
            print(f"Date: {june_record['date']}")
            
            # Parse JSON directly
            try:
                tithi_data = json.loads(june_record['tithi'])
                nakshatra_data = json.loads(june_record['nakshatra'])
                print(f"✅ Tithi: {tithi_data[0]['name'] if tithi_data else 'Empty'}")
                print(f"✅ Nakshatra: {nakshatra_data[0]['name'] if nakshatra_data else 'Empty'}")
                print("✅ iOS app should be able to read this as JSON")
            except Exception as e:
                print(f"❌ Error parsing JSON: {e}")
    
    # Test July data (Base64 format from iOS app)
    print("\n📅 JULY 2025 DATA (Base64 format):")
    print("-" * 30)
    
    july_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    july_params = {
        "select": "date,tithi,nakshatra",
        "date": "eq.2025-07-10",
        "limit": 1
    }
    
    july_response = requests.get(july_url, headers=headers, params=july_params)
    
    if july_response.status_code == 200:
        july_records = july_response.json()
        if july_records:
            july_record = july_records[0]
            print(f"Date: {july_record['date']}")
            
            # Parse Base64
            try:
                tithi_decoded = base64.b64decode(july_record['tithi'].strip("'\"")).decode('utf-8')
                tithi_data = json.loads(tithi_decoded)
                print(f"✅ Tithi: {tithi_data['name']}")
                
                nakshatra_decoded = base64.b64decode(july_record['nakshatra'].strip("'\"")).decode('utf-8')
                nakshatra_data = json.loads(nakshatra_decoded)
                print(f"✅ Nakshatra: {nakshatra_data['name']}")
                print("✅ iOS app should be able to read this as Base64")
            except Exception as e:
                print(f"❌ Error parsing Base64: {e}")
    
    print("\n🎯 WHAT THIS MEANS:")
    print("-" * 20)
    print("✅ Database contains both JSON and Base64 formats")
    print("✅ iOS app has been updated to handle both formats")
    print("✅ Flexible decoding function will try Base64 first, then JSON")
    print("✅ Your app should now display panchang data correctly!")
    
    print("\n🚀 NEXT STEPS:")
    print("-" * 15)
    print("1. Run your iOS app")
    print("2. Navigate to Tamil Calendar")
    print("3. Check both June 2025 (JSON data) and July 2025 (Base64 data)")
    print("4. Both months should display panchang information correctly")
    print("5. Click on dates to see detailed panchang information")

def main():
    test_data_formats()

if __name__ == "__main__":
    main()
