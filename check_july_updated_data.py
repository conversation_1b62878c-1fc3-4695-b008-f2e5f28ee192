#!/usr/bin/env python3
"""
Check the updated July data format
"""

import requests
import json

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def check_july_data():
    """Check the updated July data"""
    print("🔍 Checking Updated July 2025 Data")
    print("=" * 40)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Get July 10 data
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "date,tithi,nakshatra,sun_times",
        "date": "eq.2025-07-10",
        "limit": 1
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        records = response.json()
        if records:
            record = records[0]
            print(f"Date: {record['date']}")
            print(f"Tithi raw: {repr(record['tithi'])}")
            print(f"Nakshatra raw: {repr(record['nakshatra'])}")
            print(f"Sun times raw: {repr(record['sun_times'])}")
            
            # Try to parse as JSON
            try:
                tithi_data = json.loads(record['tithi'])
                print(f"✅ Tithi (JSON): {tithi_data[0]['name'] if tithi_data else 'Empty'}")
            except Exception as e:
                print(f"❌ Tithi JSON error: {e}")
            
            try:
                nakshatra_data = json.loads(record['nakshatra'])
                print(f"✅ Nakshatra (JSON): {nakshatra_data[0]['name'] if nakshatra_data else 'Empty'}")
            except Exception as e:
                print(f"❌ Nakshatra JSON error: {e}")
            
            try:
                sun_data = json.loads(record['sun_times'])
                print(f"✅ Sunrise: {sun_data.get('sunrise', 'Unknown')}")
                print(f"✅ Sunset: {sun_data.get('sunset', 'Unknown')}")
            except Exception as e:
                print(f"❌ Sun times JSON error: {e}")
        else:
            print("❌ No record found")
    else:
        print(f"❌ Error: {response.status_code}")

def main():
    check_july_data()

if __name__ == "__main__":
    main()
