// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "NIRA-Tamil",
    platforms: [
        .iOS(.v16)
    ],
    products: [
        .library(
            name: "NIRA-Tamil",
            targets: ["NIRA-Tamil"]
        ),
    ],
    dependencies: [
        .package(url: "https://github.com/supabase/supabase-swift", from: "2.5.1")
    ],
    targets: [
        .target(
            name: "NIRA-Tamil",
            dependencies: [
                .product(name: "Supabase", package: "supabase-swift")
            ]
        ),
        .testTarget(
            name: "NIRA-TamilTests",
            dependencies: ["NIRA-Tamil"]
        ),
    ]
)
