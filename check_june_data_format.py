#!/usr/bin/env python3
"""
Check June 2025 data format vs July 2025 data format
"""

import requests
import json
import base64

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def check_data_formats():
    """Check data formats for June vs July"""
    print("🔍 Comparing Data Formats")
    print("=" * 50)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Get June data (our Python script)
    print("📅 JUNE 2025 DATA (Python script):")
    print("-" * 30)
    
    june_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    june_params = {
        "select": "date,tithi,nakshatra",
        "date": "eq.2025-06-01",
        "limit": 1
    }
    
    june_response = requests.get(june_url, headers=headers, params=june_params)
    
    if june_response.status_code == 200:
        june_records = june_response.json()
        if june_records:
            june_record = june_records[0]
            print(f"Date: {june_record['date']}")
            print(f"Tithi raw: {repr(june_record['tithi'])}")
            print(f"Nakshatra raw: {repr(june_record['nakshatra'])}")
            
            # Try to parse as JSON
            try:
                tithi_data = json.loads(june_record['tithi'])
                print(f"✅ Tithi (JSON): {tithi_data[0]['name'] if tithi_data else 'Empty'}")
            except:
                print("❌ Tithi: Not JSON format")
            
            try:
                nakshatra_data = json.loads(june_record['nakshatra'])
                print(f"✅ Nakshatra (JSON): {nakshatra_data[0]['name'] if nakshatra_data else 'Empty'}")
            except:
                print("❌ Nakshatra: Not JSON format")
    
    print("\n📅 JULY 2025 DATA (iOS app?):")
    print("-" * 30)
    
    # Get July data (iOS app)
    july_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    july_params = {
        "select": "date,tithi,nakshatra",
        "date": "eq.2025-07-10",
        "limit": 1
    }
    
    july_response = requests.get(july_url, headers=headers, params=july_params)
    
    if july_response.status_code == 200:
        july_records = july_response.json()
        if july_records:
            july_record = july_records[0]
            print(f"Date: {july_record['date']}")
            print(f"Tithi raw: {repr(july_record['tithi'])}")
            print(f"Nakshatra raw: {repr(july_record['nakshatra'])}")
            
            # Try to decode Base64
            try:
                tithi_decoded = base64.b64decode(july_record['tithi'].strip("'\"")).decode('utf-8')
                tithi_data = json.loads(tithi_decoded)
                print(f"✅ Tithi (Base64): {tithi_data['name']}")
            except Exception as e:
                print(f"❌ Tithi decode error: {e}")
            
            try:
                nakshatra_decoded = base64.b64decode(july_record['nakshatra'].strip("'\"")).decode('utf-8')
                nakshatra_data = json.loads(nakshatra_decoded)
                print(f"✅ Nakshatra (Base64): {nakshatra_data['name']}")
            except Exception as e:
                print(f"❌ Nakshatra decode error: {e}")
    
    print("\n🔍 ANALYSIS:")
    print("-" * 20)
    print("• June 2025: Plain JSON format (from Python script)")
    print("• July 2025: Base64 encoded format (from iOS app)")
    print("• iOS app expects one format, but we have mixed formats!")
    print("\n💡 SOLUTION:")
    print("• Need to standardize the data format")
    print("• Either convert iOS app to read JSON, or convert June data to Base64")

def main():
    check_data_formats()

if __name__ == "__main__":
    main()
