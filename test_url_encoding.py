#!/usr/bin/env python3
"""
Test URL encoding for ProKerala API
"""

import requests
import urllib.parse

def test_url_construction():
    """Test different ways of constructing the URL"""
    
    base_url = "https://api.prokerala.com/v2/astrology/panchang"
    coordinates = "13.0827,80.2707"
    datetime_str = "2025-07-21T19:30:00+05:30"
    
    print("🔍 Testing URL Construction Methods")
    print("=" * 50)
    
    # Method 1: Manual string construction (what we're using in iOS)
    manual_url = f"{base_url}?ayanamsa=1&coordinates={coordinates}&datetime={datetime_str}&la=en"
    print(f"📱 iOS Method (Manual): {manual_url}")
    
    # Method 2: Using urllib.parse.urlencode
    params = {
        "ayanamsa": 1,
        "coordinates": coordinates,
        "datetime": datetime_str,
        "la": "en"
    }
    encoded_url = f"{base_url}?{urllib.parse.urlencode(params)}"
    print(f"🐍 Python Method (urlencode): {encoded_url}")
    
    # Method 3: Using requests params (what Python test is using)
    print(f"🔗 Requests params method: {base_url} with params={params}")
    
    print("\n🔍 Testing actual API calls...")
    
    # Get token first
    token_url = "https://api.prokerala.com/token"
    token_data = {
        "grant_type": "client_credentials",
        "client_id": "6df0ec16-722b-4acd-a574-bfd546c0c270",
        "client_secret": "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"
    }
    
    token_response = requests.post(token_url, data=token_data)
    if token_response.status_code != 200:
        print("❌ Failed to get token")
        return
    
    access_token = token_response.json()["access_token"]
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test Method 1: Manual URL (iOS style) - OLD WAY
    print("\n📱 Testing iOS-style manual URL (OLD WAY - should fail)...")
    try:
        response1 = requests.get(manual_url, headers=headers)
        print(f"Status: {response1.status_code}")
        if response1.status_code != 200:
            print(f"Error: {response1.text}")
    except Exception as e:
        print(f"Exception: {e}")

    # Test Method 1b: Manual URL with proper encoding (NEW iOS style)
    print("\n📱 Testing iOS-style manual URL with encoding (NEW WAY - should work)...")
    encoded_datetime = urllib.parse.quote(datetime_str, safe='')
    encoded_manual_url = f"{base_url}?ayanamsa=1&coordinates={coordinates}&datetime={encoded_datetime}&la=en"
    print(f"Encoded URL: {encoded_manual_url}")
    try:
        response1b = requests.get(encoded_manual_url, headers=headers)
        print(f"Status: {response1b.status_code}")
        if response1b.status_code != 200:
            print(f"Error: {response1b.text}")
        else:
            print("✅ Success with URL encoding!")
    except Exception as e:
        print(f"Exception: {e}")
    
    # Test Method 2: Using params (Python style)
    print("\n🐍 Testing Python-style params...")
    try:
        response2 = requests.get(base_url, headers=headers, params=params)
        print(f"Status: {response2.status_code}")
        if response2.status_code != 200:
            print(f"Error: {response2.text}")
        else:
            print("✅ Success!")
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_url_construction()
