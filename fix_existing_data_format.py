#!/usr/bin/env python3
"""
Fix existing data format to match iOS expectations
Convert time strings to ISO format dates
"""

import requests
import json
from datetime import datetime, date

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def fix_data_formats():
    """Fix data formats for iOS compatibility"""
    print("🔧 Fixing Data Formats for iOS Compatibility")
    print("=" * 50)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }
    
    # Get all records that need fixing
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "id,date,sun_times,moon_times",
        "order": "date.asc"
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code != 200:
        print(f"❌ Error fetching records: {response.status_code}")
        return
    
    records = response.json()
    print(f"📊 Found {len(records)} records to check")
    
    fixed_count = 0
    
    for record in records:
        try:
            record_date = record['date']
            record_id = record['id']
            
            # Parse existing sun_times
            sun_times_raw = record['sun_times']
            moon_times_raw = record['moon_times']
            
            if isinstance(sun_times_raw, str):
                sun_times = json.loads(sun_times_raw)
            else:
                sun_times = sun_times_raw
            
            if isinstance(moon_times_raw, str):
                moon_times = json.loads(moon_times_raw)
            else:
                moon_times = moon_times_raw
            
            # Check if we need to fix the format
            needs_fixing = False
            
            # Check sunrise format
            if 'sunrise' in sun_times:
                sunrise = sun_times['sunrise']
                if isinstance(sunrise, str) and ':' in sunrise and len(sunrise) <= 6:
                    # This is a time string like "05:52", needs fixing
                    needs_fixing = True
            
            if needs_fixing:
                print(f"🔧 Fixing {record_date}...")
                
                # Convert time strings to full ISO dates
                fixed_sun_times = fix_time_object(sun_times, record_date)
                fixed_moon_times = fix_time_object(moon_times, record_date)
                
                # Update the record
                update_data = {
                    "sun_times": json.dumps(fixed_sun_times),
                    "moon_times": json.dumps(fixed_moon_times),
                    "updated_at": datetime.now().isoformat()
                }
                
                update_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
                update_params = {"id": f"eq.{record_id}"}
                
                update_response = requests.patch(update_url, json=update_data, headers=headers, params=update_params)
                
                if update_response.status_code in [200, 204]:
                    print(f"✅ Fixed {record_date}")
                    fixed_count += 1
                else:
                    print(f"❌ Failed to fix {record_date}: {update_response.status_code}")
            else:
                print(f"✅ {record_date} already in correct format")
                
        except Exception as e:
            print(f"❌ Error processing {record.get('date', 'unknown')}: {e}")
    
    print(f"\n📊 SUMMARY:")
    print(f"✅ Fixed {fixed_count} records")
    print(f"📱 iOS app should now work without crashes!")

def fix_time_object(time_obj, record_date):
    """Fix time object to have proper ISO dates"""
    if not time_obj:
        return time_obj
    
    fixed_obj = {}
    
    for key, value in time_obj.items():
        if value and isinstance(value, str) and ':' in value and len(value) <= 6:
            # This is a time string like "05:52"
            try:
                # Parse the time
                time_parts = value.split(':')
                hour = int(time_parts[0])
                minute = int(time_parts[1])
                
                # Create a datetime for the given date
                date_obj = datetime.fromisoformat(record_date)
                full_datetime = date_obj.replace(hour=hour, minute=minute, second=0, microsecond=0)
                
                # Convert to ISO format
                fixed_obj[key] = full_datetime.isoformat()
            except:
                fixed_obj[key] = None
        else:
            fixed_obj[key] = value
    
    # Ensure all required fields exist for SunTimes
    if 'sunrise' in time_obj or 'sunset' in time_obj:
        fixed_obj.setdefault('noon', None)
        fixed_obj.setdefault('twilightBegin', None)
        fixed_obj.setdefault('twilightEnd', None)

    # Ensure all required fields exist for MoonTimes
    if 'moonrise' in time_obj or 'moonset' in time_obj:
        fixed_obj.setdefault('phase', 'new_moon')
        fixed_obj.setdefault('illumination', 0.0)

    return fixed_obj

def main():
    fix_data_formats()

if __name__ == "__main__":
    main()
