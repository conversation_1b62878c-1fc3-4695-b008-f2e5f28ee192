#!/usr/bin/env python3
"""
Smart Panchang Populator for June 2025
- Checks existing records in Supabase
- Only fetches missing dates
- Respects API rate limits (5 requests per 60 seconds)
- Can be run multiple times safely
"""

import requests
import json
from datetime import datetime, date, timedelta
import uuid
import time
import sys

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

# ProKerala API credentials
PROKERALA_CLIENT_ID = "6df0ec16-722b-4acd-a574-bfd546c0c270"
PROKERALA_CLIENT_SECRET = "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"

# Chennai coordinates
CHENNAI_LAT = 13.0827
CHENNAI_LON = 80.2707

class SmartPanchangPopulator:
    def __init__(self):
        self.supabase_headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=representation"
        }
        self.prokerala_token = None
        self.token_expires_at = None
        self.request_count = 0
        self.last_request_time = None
        
    def get_existing_dates(self):
        """Get list of dates already in Supabase for June 2025"""
        print("🔍 Checking existing records in Supabase...")
        
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
        params = {
            "select": "date",
            "date": "gte.2025-06-01",
            "date": "lte.2025-06-30"
        }
        
        response = requests.get(url, headers=self.supabase_headers, params=params)
        
        if response.status_code == 200:
            existing_records = response.json()
            existing_dates = [record["date"] for record in existing_records]
            print(f"✅ Found {len(existing_dates)} existing records")
            return set(existing_dates)
        else:
            print(f"❌ Error checking existing records: {response.status_code}")
            return set()
    
    def get_missing_dates(self):
        """Get list of dates that need to be populated"""
        existing_dates = self.get_existing_dates()
        
        # Generate all dates in June 2025
        all_dates = []
        current_date = date(2025, 6, 1)
        end_date = date(2025, 6, 30)
        
        while current_date <= end_date:
            date_str = current_date.strftime("%Y-%m-%d")
            if date_str not in existing_dates:
                all_dates.append(current_date)
            current_date += timedelta(days=1)
        
        print(f"📅 Missing dates to populate: {len(all_dates)}")
        return all_dates
    
    def wait_for_rate_limit(self):
        """Smart rate limiting - 5 requests per 60 seconds"""
        if self.request_count >= 5:
            if self.last_request_time:
                elapsed = time.time() - self.last_request_time
                if elapsed < 60:
                    wait_time = 60 - elapsed + 1  # Add 1 second buffer
                    print(f"⏳ Rate limit reached. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
            
            self.request_count = 0
        
        # Wait 2 seconds between requests for safety
        if self.last_request_time:
            time.sleep(2)
    
    def get_prokerala_token(self):
        """Get or refresh ProKerala API token"""
        if self.prokerala_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.prokerala_token
            
        print("🔑 Getting ProKerala API token...")
        token_url = "https://api.prokerala.com/token"
        token_data = {
            "grant_type": "client_credentials",
            "client_id": PROKERALA_CLIENT_ID,
            "client_secret": PROKERALA_CLIENT_SECRET
        }
        
        response = requests.post(token_url, data=token_data)
        if response.status_code != 200:
            raise Exception(f"Failed to get token: {response.status_code} - {response.text}")
        
        token_info = response.json()
        self.prokerala_token = token_info["access_token"]
        self.token_expires_at = datetime.now() + timedelta(seconds=token_info["expires_in"] - 300)
        
        print("✅ ProKerala token obtained successfully")
        return self.prokerala_token
    
    def fetch_panchang_for_date(self, target_date):
        """Fetch panchang data from ProKerala API for a specific date"""
        self.wait_for_rate_limit()
        
        token = self.get_prokerala_token()
        
        # Format date for ProKerala API (6 AM IST)
        datetime_str = f"{target_date.strftime('%Y-%m-%d')}T06:00:00+05:30"
        
        # Build API URL with proper encoding
        base_url = "https://api.prokerala.com/v2/astrology/panchang"
        params = {
            "ayanamsa": 1,  # Lahiri
            "coordinates": f"{CHENNAI_LAT},{CHENNAI_LON}",
            "datetime": datetime_str,
            "la": "en"
        }
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"📡 Fetching panchang for {target_date.strftime('%Y-%m-%d')}...")
        
        self.request_count += 1
        self.last_request_time = time.time()
        
        response = requests.get(base_url, headers=headers, params=params)
        
        if response.status_code == 429:
            print("⚠️ Rate limit hit, will retry after waiting...")
            self.request_count = 5  # Force wait
            return self.fetch_panchang_for_date(target_date)  # Retry
        elif response.status_code != 200:
            raise Exception(f"API Error {response.status_code}: {response.text}")
        
        return response.json()
    
    def convert_to_supabase_format(self, api_data, target_date):
        """Convert ProKerala API response to Supabase format"""
        data = api_data.get("data", {})
        
        # Extract basic info
        vaara = data.get("vaara", "Unknown")
        
        # Extract and format arrays
        tithi_array = data.get("tithi", [])
        nakshatra_array = data.get("nakshatra", [])
        yoga_array = data.get("yoga", [])
        karana_array = data.get("karana", [])
        
        # Create Supabase record
        record = {
            "id": str(uuid.uuid4()),
            "date": target_date.strftime("%Y-%m-%d"),
            "location_info": json.dumps({
                "latitude": CHENNAI_LAT,
                "longitude": CHENNAI_LON,
                "city": "Chennai",
                "country": "India"
            }),
            "tamil_date": json.dumps({
                "tamil_year": target_date.year + 1000,
                "tamil_month": self.get_tamil_month(target_date.month),
                "tamil_day": target_date.day
            }),
            "sun_times": json.dumps({
                "sunrise": self.extract_time_from_iso(data.get("sunrise")),
                "sunset": self.extract_time_from_iso(data.get("sunset"))
            }),
            "moon_times": json.dumps({
                "moonrise": self.extract_time_from_iso(data.get("moonrise")),
                "moonset": self.extract_time_from_iso(data.get("moonset"))
            }),
            "tithi": json.dumps(tithi_array),
            "nakshatra": json.dumps(nakshatra_array),
            "yoga": json.dumps(yoga_array),
            "karana": json.dumps(karana_array),
            "weekday_info": json.dumps({
                "weekday_number": target_date.weekday() + 1,
                "weekday_name": target_date.strftime("%A"),
                "vedic_weekday_number": target_date.weekday() + 1,
                "vedic_weekday_name": vaara
            }),
            "lunar_month": json.dumps({
                "name": self.get_tamil_month(target_date.month),
                "number": target_date.month
            }),
            "season": json.dumps({
                "name": self.get_season(target_date.month),
                "tamil_name": self.get_tamil_season(target_date.month)
            }),
            "year_info": json.dumps({
                "gregorian_year": target_date.year,
                "tamil_year": target_date.year + 1000,
                "year_name": "Vilambi"
            }),
            "significance": f"Panchang for {target_date.strftime('%B %d, %Y')}",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        return record
    
    def extract_time_from_iso(self, iso_string):
        """Extract time from ISO string"""
        if not iso_string:
            return "Unknown"
        
        try:
            dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
            return dt.strftime("%H:%M")
        except:
            return "Unknown"
    
    def get_tamil_month(self, month):
        """Get Tamil month name"""
        tamil_months = {
            1: "Thai", 2: "Maasi", 3: "Panguni", 4: "Chittirai",
            5: "Vaikasi", 6: "Aani", 7: "Aadi", 8: "Aavani",
            9: "Purattasi", 10: "Aippasi", 11: "Karthikai", 12: "Margazhi"
        }
        return tamil_months.get(month, "Unknown")
    
    def get_season(self, month):
        """Get season name"""
        if month in [12, 1, 2]:
            return "Winter"
        elif month in [3, 4, 5]:
            return "Spring"
        elif month in [6, 7, 8]:
            return "Summer"
        else:
            return "Autumn"
    
    def get_tamil_season(self, month):
        """Get Tamil season name"""
        if month in [12, 1, 2]:
            return "Kaar"
        elif month in [3, 4, 5]:
            return "Koothir"
        elif month in [6, 7, 8]:
            return "Munpani"
        else:
            return "Pinpani"
    
    def save_to_supabase(self, record):
        """Save panchang record to Supabase"""
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
        
        response = requests.post(url, json=record, headers=self.supabase_headers)
        
        if response.status_code in [200, 201]:
            print(f"✅ Saved panchang for {record['date']}")
            return True
        elif response.status_code == 409:
            print(f"⚠️ Record already exists for {record['date']}")
            return True
        else:
            print(f"❌ Failed to save {record['date']}: {response.status_code} - {response.text}")
            return False
    
    def populate_missing_dates(self, batch_size=5):
        """Populate missing dates in batches"""
        missing_dates = self.get_missing_dates()
        
        if not missing_dates:
            print("🎉 All June 2025 dates are already populated!")
            return
        
        print(f"🚀 Starting to populate {len(missing_dates)} missing dates")
        print("=" * 50)
        
        success_count = 0
        
        for i, target_date in enumerate(missing_dates):
            try:
                print(f"📅 Processing {i+1}/{len(missing_dates)}: {target_date.strftime('%Y-%m-%d')}")
                
                # Fetch from API
                api_data = self.fetch_panchang_for_date(target_date)
                
                # Convert to Supabase format
                record = self.convert_to_supabase_format(api_data, target_date)
                
                # Save to Supabase
                if self.save_to_supabase(record):
                    success_count += 1
                
                # Process in batches to avoid overwhelming the API
                if (i + 1) % batch_size == 0 and i + 1 < len(missing_dates):
                    print(f"🔄 Completed batch of {batch_size}. Taking a longer break...")
                    time.sleep(10)
                
            except Exception as e:
                print(f"❌ Error processing {target_date}: {e}")
                print("⏳ Waiting 30 seconds before continuing...")
                time.sleep(30)
        
        print("\n" + "=" * 50)
        print("📊 POPULATION SUMMARY")
        print("=" * 50)
        print(f"📅 Missing Dates Processed: {len(missing_dates)}")
        print(f"✅ Successfully Populated: {success_count}")
        print(f"❌ Failed: {len(missing_dates) - success_count}")
        print(f"📈 Success Rate: {(success_count/len(missing_dates))*100:.1f}%")
        
        # Check final status
        final_missing = self.get_missing_dates()
        if not final_missing:
            print("\n🎉 ALL JUNE 2025 PANCHANG DATA SUCCESSFULLY POPULATED!")
            print("🚀 Your app can now work offline for the entire month!")
        else:
            print(f"\n⚠️ {len(final_missing)} dates still missing. Run the script again to continue.")

def main():
    populator = SmartPanchangPopulator()
    populator.populate_missing_dates()

if __name__ == "__main__":
    main()
