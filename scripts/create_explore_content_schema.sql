-- =====================================================
-- NIRA-Tamil Explore Content Database Schema
-- Enhanced schema for Literature, Calendar, Culture, and Map content
-- =====================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- LITERATURE CONTENT TABLES
-- =====================================================

-- Literature Categories (Thirukkural, Bharathiyar, Classical, Modern)
CREATE TABLE IF NOT EXISTS literature_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    name_tamil VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    description_tamil TEXT,
    emoji VARCHAR(10),
    color_theme VARCHAR(50) DEFAULT 'blue',
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Literature Content (poems, stories, classical texts)
CREATE TABLE IF NOT EXISTS literature_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID REFERENCES literature_categories(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    title_tamil VARCHAR(400) NOT NULL,
    author VARCHAR(100),
    author_tamil VARCHAR(200),
    content_tamil TEXT NOT NULL,
    content_english TEXT,
    romanization TEXT,
    cultural_context TEXT,
    historical_significance TEXT,
    modern_relevance TEXT,
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')) DEFAULT 'beginner',
    reading_time_minutes INTEGER DEFAULT 5,
    audio_url VARCHAR(500),
    audio_male_url VARCHAR(500),
    audio_female_url VARCHAR(500),
    tags TEXT[],
    is_featured BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ENHANCED CALENDAR TABLES
-- =====================================================

-- Tamil Calendar Concepts (Muhurtham, Raahu Kaalam, etc.)
CREATE TABLE IF NOT EXISTS calendar_concepts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    concept_name VARCHAR(100) NOT NULL,
    concept_name_tamil VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    description_tamil TEXT,
    explanation TEXT NOT NULL,
    significance TEXT,
    modern_application TEXT,
    calculation_method TEXT,
    audio_url VARCHAR(500),
    romanization VARCHAR(200),
    category VARCHAR(50) CHECK (category IN ('muhurtham', 'raahu_kaalam', 'yemekandam', 'tithi', 'nakshatra', 'yoga', 'karana')) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enhanced Festivals (Multi-religious)
CREATE TABLE IF NOT EXISTS tamil_festivals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    name_tamil VARCHAR(400) NOT NULL,
    romanization VARCHAR(200),
    religion VARCHAR(50) CHECK (religion IN ('hindu', 'muslim', 'christian', 'secular', 'traditional')) NOT NULL,
    festival_date DATE,
    is_lunar_based BOOLEAN DEFAULT false,
    lunar_calculation_info TEXT,
    duration_days INTEGER DEFAULT 1,
    significance TEXT NOT NULL,
    description TEXT NOT NULL,
    traditions TEXT[],
    modern_celebration TEXT,
    regional_variations TEXT,
    cultural_importance VARCHAR(20) CHECK (cultural_importance IN ('low', 'medium', 'high', 'very_high')) DEFAULT 'medium',
    food_items TEXT[],
    rituals TEXT[],
    audio_url VARCHAR(500),
    image_url VARCHAR(500),
    learning_content JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- CULTURE CONTENT TABLES (Enhanced)
-- =====================================================

-- Cultural Categories (excluding literature, adding cinema & sports)
CREATE TABLE IF NOT EXISTS cultural_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    name_tamil VARCHAR(200) NOT NULL,
    description TEXT,
    emoji VARCHAR(10),
    color_theme VARCHAR(50),
    category_type VARCHAR(50) CHECK (category_type IN ('arts_dance', 'music', 'festivals', 'architecture', 'cuisine', 'cinema', 'sports', 'traditions', 'philosophy')) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cinema Content
CREATE TABLE IF NOT EXISTS cinema_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    title_tamil VARCHAR(400),
    content_type VARCHAR(50) CHECK (content_type IN ('movie', 'personality', 'history', 'genre', 'music', 'technology')) NOT NULL,
    description TEXT NOT NULL,
    description_tamil TEXT,
    cultural_impact TEXT,
    historical_significance TEXT,
    modern_relevance TEXT,
    year_released INTEGER,
    director VARCHAR(100),
    director_tamil VARCHAR(200),
    cast_members TEXT[],
    genre VARCHAR(100),
    awards TEXT[],
    box_office_info TEXT,
    cultural_themes TEXT[],
    audio_url VARCHAR(500),
    image_url VARCHAR(500),
    trailer_url VARCHAR(500),
    romanization TEXT,
    tags TEXT[],
    difficulty_level VARCHAR(20) DEFAULT 'beginner',
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Sports Content
CREATE TABLE IF NOT EXISTS sports_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    name_tamil VARCHAR(400) NOT NULL,
    sport_type VARCHAR(50) CHECK (sport_type IN ('traditional', 'modern', 'personality', 'event', 'history')) NOT NULL,
    description TEXT NOT NULL,
    description_tamil TEXT,
    cultural_significance TEXT,
    historical_background TEXT,
    rules_overview TEXT,
    modern_status TEXT,
    famous_personalities TEXT[],
    major_events TEXT[],
    regional_variations TEXT,
    equipment_needed TEXT[],
    audio_url VARCHAR(500),
    image_url VARCHAR(500),
    video_url VARCHAR(500),
    romanization TEXT,
    tags TEXT[],
    difficulty_level VARCHAR(20) DEFAULT 'beginner',
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enhanced Cultural Insights (excluding literature)
CREATE TABLE IF NOT EXISTS cultural_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID REFERENCES cultural_categories(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    title_tamil VARCHAR(400) NOT NULL,
    description TEXT NOT NULL,
    content TEXT NOT NULL,
    cultural_significance TEXT,
    modern_relevance TEXT,
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')) DEFAULT 'beginner',
    reading_time_minutes INTEGER DEFAULT 5,
    audio_url VARCHAR(500),
    romanization TEXT,
    tags TEXT[],
    is_featured BOOLEAN DEFAULT false,
    is_today_insight BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- MAP & LOCATION TABLES
-- =====================================================

-- Cultural Locations for Map Integration
CREATE TABLE IF NOT EXISTS cultural_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    name_tamil VARCHAR(400) NOT NULL,
    location_type VARCHAR(50) CHECK (location_type IN ('temple', 'historical_site', 'cultural_center', 'museum', 'monument', 'heritage_site', 'natural_site')) NOT NULL,
    description TEXT NOT NULL,
    description_tamil TEXT,
    historical_significance TEXT,
    cultural_importance TEXT,
    architectural_style TEXT,
    best_time_to_visit TEXT,
    visiting_hours TEXT,
    entry_fee TEXT,
    facilities TEXT[],
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100) DEFAULT 'Tamil Nadu',
    country VARCHAR(100) DEFAULT 'India',
    audio_guide_url VARCHAR(500),
    image_urls TEXT[],
    virtual_tour_url VARCHAR(500),
    romanization TEXT,
    tags TEXT[],
    difficulty_level VARCHAR(20) DEFAULT 'beginner',
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- AUDIO CONTENT MANAGEMENT
-- =====================================================

-- Audio Content Registry
CREATE TABLE IF NOT EXISTS audio_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type VARCHAR(50) CHECK (content_type IN ('literature', 'festival', 'cultural_insight', 'cinema', 'sports', 'location', 'calendar_concept')) NOT NULL,
    content_id UUID NOT NULL,
    text_tamil TEXT NOT NULL,
    romanization TEXT,
    voice_type VARCHAR(20) CHECK (voice_type IN ('male', 'female', 'neutral')) DEFAULT 'female',
    voice_model VARCHAR(100) DEFAULT 'ta-IN-Chirp3-HD-Erinome',
    audio_url VARCHAR(500) NOT NULL,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    generation_date TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Literature indexes
CREATE INDEX IF NOT EXISTS idx_literature_content_category ON literature_content(category_id);
CREATE INDEX IF NOT EXISTS idx_literature_content_featured ON literature_content(is_featured);
CREATE INDEX IF NOT EXISTS idx_literature_content_difficulty ON literature_content(difficulty_level);

-- Festival indexes
CREATE INDEX IF NOT EXISTS idx_tamil_festivals_religion ON tamil_festivals(religion);
CREATE INDEX IF NOT EXISTS idx_tamil_festivals_date ON tamil_festivals(festival_date);
CREATE INDEX IF NOT EXISTS idx_tamil_festivals_lunar ON tamil_festivals(is_lunar_based);

-- Cultural content indexes
CREATE INDEX IF NOT EXISTS idx_cultural_insights_category ON cultural_insights(category_id);
CREATE INDEX IF NOT EXISTS idx_cultural_insights_featured ON cultural_insights(is_featured);
CREATE INDEX IF NOT EXISTS idx_cultural_insights_today ON cultural_insights(is_today_insight);

-- Cinema indexes
CREATE INDEX IF NOT EXISTS idx_cinema_content_type ON cinema_content(content_type);
CREATE INDEX IF NOT EXISTS idx_cinema_content_featured ON cinema_content(is_featured);
CREATE INDEX IF NOT EXISTS idx_cinema_content_year ON cinema_content(year_released);

-- Sports indexes
CREATE INDEX IF NOT EXISTS idx_sports_content_type ON sports_content(sport_type);
CREATE INDEX IF NOT EXISTS idx_sports_content_featured ON sports_content(is_featured);

-- Location indexes
CREATE INDEX IF NOT EXISTS idx_cultural_locations_type ON cultural_locations(location_type);
CREATE INDEX IF NOT EXISTS idx_cultural_locations_featured ON cultural_locations(is_featured);
CREATE INDEX IF NOT EXISTS idx_cultural_locations_coords ON cultural_locations(latitude, longitude);

-- Audio indexes
CREATE INDEX IF NOT EXISTS idx_audio_content_type_id ON audio_content(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_audio_content_voice ON audio_content(voice_type);

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE literature_categories IS 'Categories for Tamil literature content (Thirukkural, Bharathiyar, Classical, Modern)';
COMMENT ON TABLE literature_content IS 'Tamil literature content with audio and romanization support';
COMMENT ON TABLE calendar_concepts IS 'Tamil calendar concepts with detailed explanations';
COMMENT ON TABLE tamil_festivals IS 'Multi-religious festivals celebrated in Tamil culture';
COMMENT ON TABLE cultural_categories IS 'Cultural categories excluding literature (arts, music, cinema, sports, etc.)';
COMMENT ON TABLE cinema_content IS 'Tamil cinema history, movies, personalities, and cultural impact';
COMMENT ON TABLE sports_content IS 'Traditional and modern sports in Tamil culture';
COMMENT ON TABLE cultural_insights IS 'Cultural insights and knowledge excluding literature content';
COMMENT ON TABLE cultural_locations IS 'Cultural heritage sites and locations for map integration';
COMMENT ON TABLE audio_content IS 'Pre-generated audio files for all Tamil content';
