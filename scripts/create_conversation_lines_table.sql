-- Enhanced conversation_lines table with romanization and pronunciation
-- Run this in Supabase SQL Editor

CREATE TABLE IF NOT EXISTS conversation_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id TEXT NOT NULL,
    line_number INTEGER NOT NULL,
    speaker_name TEXT NOT NULL,
    speaker_role TEXT NOT NULL, -- 'person1', 'person2', 'narrator'
    
    -- Core Text Content
    text_english TEXT NOT NULL,
    text_tamil TEXT NOT NULL,
    text_romanized TEXT NOT NULL, -- Tamil in Roman script
    
    -- Pronunciation Guides
    pronunciation_ipa TEXT, -- International Phonetic Alphabet
    pronunciation_simple TEXT NOT NULL, -- Simplified pronunciation guide
    
    -- Audio Files
    audio_url TEXT, -- Individual line audio
    audio_slow_url TEXT, -- Slower pronunciation for learning
    
    -- Learning Metadata
    difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
    key_phrases TEXT[], -- Important phrases in this line
    grammar_notes TEXT, -- Grammar explanations for this line
    cultural_notes TEXT, -- Cultural context for this line
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_difficulty CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    CONSTRAINT valid_line_number CHECK (line_number > 0),
    CONSTRAINT fk_conversation_lines_conversation FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_lines_conversation_id ON conversation_lines(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_lines_line_number ON conversation_lines(conversation_id, line_number);

-- Insert sample data for L1C1 "Meeting a Friend" conversation
INSERT INTO conversation_lines (
    conversation_id, line_number, speaker_name, speaker_role,
    text_english, text_tamil, text_romanized, 
    pronunciation_ipa, pronunciation_simple,
    difficulty_level, key_phrases, grammar_notes, cultural_notes
) VALUES 
(
    'L1C1', 1, 'Raj', 'person1',
    'Hello! How are you?',
    'வணக்கம்! எப்படி இருக்கிறீர்கள்?',
    'Vanakkam! Eppadi irukkireergal?',
    'ʋəɳəkːəm ɛpːəɖi iɾukːiɾeːɾɡəɭ',
    'va-nak-kam! ep-pa-di i-ruk-ki-reer-gal?',
    1,
    ARRAY['வணக்கம்', 'எப்படி இருக்கிறீர்கள்'],
    'வணக்கம் is the standard Tamil greeting. எப்படி இருக்கிறீர்கள் is formal "how are you"',
    'வணக்கம் can be used any time of day, similar to "hello" in English'
),
(
    'L1C1', 2, 'Priya', 'person2',
    'I''m fine, thank you. How about you?',
    'நான் நலமாக இருக்கிறேன், நன்றி. நீங்கள் எப்படி?',
    'Naan nalamaaga irukkiren, nandri. Neengal eppadi?',
    'naːn nələmaːɡə iɾukːiɾen nənɖɾi neːŋɡəɭ ɛpːəɖi',
    'naan na-la-maa-ga i-ruk-ki-ren, nan-dri. neen-gal ep-pa-di?',
    2,
    ARRAY['நலமாக இருக்கிறேன்', 'நன்றி', 'நீங்கள் எப்படி'],
    'நலமாக இருக்கிறேன் = "I am well". நன்றி = "thank you". நீங்கள் is respectful "you"',
    'Using நீங்கள் shows respect, even among friends in formal contexts'
),
(
    'L1C1', 3, 'Raj', 'person1',
    'I''m also doing well. Where are you going?',
    'நானும் நலமாக இருக்கிறேன். நீங்கள் எங்கே போகிறீர்கள்?',
    'Naanum nalamaaga irukkiren. Neengal engae pogireeergal?',
    'naːnum nələmaːɡə iɾukːiɾen neːŋɡəɭ ɛŋɡeː poːɡiɾeːɾɡəɭ',
    'naa-num na-la-maa-ga i-ruk-ki-ren. neen-gal en-gae po-gi-reer-gal?',
    2,
    ARRAY['நானும்', 'எங்கே போகிறீர்கள்'],
    'நானும் = "I also/too". எங்கே போகிறீர்கள் = "where are you going" (formal)',
    'Asking where someone is going is a common conversation starter in Tamil culture'
),
(
    'L1C1', 4, 'Priya', 'person2',
    'I''m going to the market. See you later!',
    'நான் சந்தைக்குப் போகிறேன். பிறகு சந்திப்போம்!',
    'Naan sandhaikkup pogireen. Piragu sandhippom!',
    'naːn sənðəikːup poːɡiɾeːn piɾəɡu sənðipːoːm',
    'naan san-thai-kkup po-gi-reen. pi-ra-gu san-dhip-pom!',
    3,
    ARRAY['சந்தைக்குப் போகிறேன்', 'பிறகு சந்திப்போம்'],
    'சந்தைக்குப் = "to the market" (dative case). பிறகு சந்திப்போம் = "see you later"',
    'Markets are central to Tamil community life, common destination for daily activities'
);

-- Verify the data was inserted
SELECT 
    line_number,
    speaker_name,
    text_english,
    text_tamil,
    text_romanized,
    pronunciation_simple
FROM conversation_lines 
WHERE conversation_id = 'L1C1' 
ORDER BY line_number;
