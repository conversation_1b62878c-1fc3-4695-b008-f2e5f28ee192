#!/usr/bin/env python3
"""
A1 Tamil Lesson Upload Script
Uploads A1 lesson content from JSON files to Supabase database
"""

import json
import os
import sys
from supabase import create_client, Client
from typing import Dict, List, Any
import uuid

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def create_supabase_client() -> Client:
    """Create and return Supabase client"""
    return create_client(SUPABASE_URL, SUPABASE_KEY)

def load_lesson_json(lesson_file: str) -> Dict[str, Any]:
    """Load lesson JSON file"""
    if not os.path.exists(lesson_file):
        raise FileNotFoundError(f"Lesson file not found: {lesson_file}")
    
    with open(lesson_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def upload_lesson_metadata(supabase: Client, lesson_data: Dict[str, Any]) -> str:
    """Upload lesson metadata and return lesson ID"""
    lesson_record = {
        "lesson_number": lesson_data["lesson_number"],
        "level_code": lesson_data["level_code"],
        "title_english": lesson_data["title_english"],
        "title_tamil": lesson_data["title_tamil"],
        "duration_minutes": lesson_data.get("duration_minutes", 15),
        "focus": lesson_data.get("focus", ""),
        "description_english": f"A1 Level {lesson_data['lesson_number']}: {lesson_data['title_english']}",
        "description_tamil": f"A1 நிலை {lesson_data['lesson_number']}: {lesson_data['title_tamil']}",
        "is_active": True
    }
    
    # Check if lesson already exists
    existing = supabase.table("lessons").select("id").eq("lesson_number", lesson_data["lesson_number"]).eq("level_code", lesson_data["level_code"]).execute()
    
    if existing.data:
        lesson_id = existing.data[0]["id"]
        print(f"✅ Lesson {lesson_data['level_code']}-L{lesson_data['lesson_number']} already exists with ID: {lesson_id}")
        return lesson_id
    
    result = supabase.table("lessons").insert(lesson_record).execute()
    lesson_id = result.data[0]["id"]
    print(f"✅ Created lesson: {lesson_data['title_english']} (ID: {lesson_id})")
    return lesson_id

def upload_vocabulary(supabase: Client, lesson_id: str, vocabulary_data: List[Dict[str, Any]]) -> int:
    """Upload vocabulary items for the lesson"""
    count = 0
    for vocab in vocabulary_data:
        vocab_record = {
            "lesson_id": lesson_id,
            "vocab_id": vocab["vocab_id"],
            "english_word": vocab["english_word"],
            "tamil_translation": vocab["tamil_translation"],
            "romanization": vocab["romanization"],
            "ipa_pronunciation": vocab.get("ipa", ""),
            "part_of_speech": vocab.get("part_of_speech", ""),
            "cultural_notes": vocab.get("cultural_notes", ""),
            "example_sentence_english": vocab.get("example_sentence_english", ""),
            "example_sentence_tamil": vocab.get("example_sentence_tamil", ""),
            "example_sentence_romanization": vocab.get("example_sentence_romanization", ""),
            "audio_word_url": vocab.get("audio_word_url", ""),
            "audio_sentence_url": vocab.get("audio_sentence_url", ""),
            "related_terms": [vocab.get("related_terms", "")]
        }
        
        # Check if vocabulary already exists
        existing = supabase.table("vocabulary").select("id").eq("vocab_id", vocab["vocab_id"]).eq("lesson_id", lesson_id).execute()
        
        if not existing.data:
            supabase.table("vocabulary").insert(vocab_record).execute()
            count += 1
    
    print(f"✅ Uploaded {count} vocabulary items")
    return count

def upload_conversations(supabase: Client, lesson_id: str, conversations_data: List[Dict[str, Any]]) -> int:
    """Upload conversations and dialogue lines"""
    count = 0
    for conv in conversations_data:
        # Upload conversation metadata
        conv_record = {
            "lesson_id": lesson_id,
            "conversation_id": conv["conversation_id"],
            "title_english": conv["title_english"],
            "title_tamil": conv["title_tamil"],
            "context_description": conv.get("context", ""),
            "participants": conv.get("participants", ""),
            "formality_level": conv.get("formality", "informal")
        }
        
        # Check if conversation already exists
        existing = supabase.table("conversations").select("id").eq("conversation_id", conv["conversation_id"]).eq("lesson_id", lesson_id).execute()
        
        if existing.data:
            conversation_db_id = existing.data[0]["id"]
        else:
            result = supabase.table("conversations").insert(conv_record).execute()
            conversation_db_id = result.data[0]["id"]
            count += 1
        
        # Upload dialogue lines
        for i, line in enumerate(conv["dialogue"]):
            line_record = {
                "conversation_id": conversation_db_id,
                "line_number": i + 1,
                "speaker": line["speaker"],
                "line_english": line["line_english"],
                "line_tamil": line["line_tamil"],
                "line_romanization": line.get("line_romanization", ""),
                "audio_url": line.get("audio_url", "")
            }
            
            # Check if dialogue line already exists
            existing_line = supabase.table("dialogue_lines").select("id").eq("conversation_id", conversation_db_id).eq("line_number", i + 1).execute()
            
            if not existing_line.data:
                supabase.table("dialogue_lines").insert(line_record).execute()
    
    print(f"✅ Uploaded {count} conversations with dialogue lines")
    return count

def upload_grammar_topics(supabase: Client, lesson_id: str, grammar_data: List[Dict[str, Any]]) -> int:
    """Upload grammar topics and examples"""
    count = 0
    for grammar in grammar_data:
        # Upload grammar topic
        grammar_record = {
            "lesson_id": lesson_id,
            "grammar_id": grammar["grammar_id"],
            "title_english": grammar["concept_english"],
            "title_tamil": grammar["concept_tamil"],
            "rule_english": grammar["explanation_english"],
            "rule_tamil": grammar["explanation_tamil"],
            "difficulty_level": 1
        }
        
        # Check if grammar topic already exists
        existing = supabase.table("grammar_topics").select("id").eq("grammar_id", grammar["grammar_id"]).eq("lesson_id", lesson_id).execute()
        
        if existing.data:
            grammar_db_id = existing.data[0]["id"]
        else:
            result = supabase.table("grammar_topics").insert(grammar_record).execute()
            grammar_db_id = result.data[0]["id"]
            count += 1
        
        # Upload grammar examples
        for i, example in enumerate(grammar.get("examples", [])):
            example_record = {
                "grammar_topic_id": grammar_db_id,
                "example_english": example["example_english"],
                "example_tamil": example["example_tamil"],
                "example_romanization": example.get("example_romanization", ""),
                "audio_url": example.get("audio_url", ""),
                "example_order": i + 1
            }
            
            # Check if example already exists
            existing_example = supabase.table("grammar_examples").select("id").eq("grammar_topic_id", grammar_db_id).eq("example_order", i + 1).execute()
            
            if not existing_example.data:
                supabase.table("grammar_examples").insert(example_record).execute()
    
    print(f"✅ Uploaded {count} grammar topics with examples")
    return count

def upload_exercises(supabase: Client, lesson_id: str, exercises_data: List[Dict[str, Any]]) -> int:
    """Upload practice exercises with questions and options"""
    count = 0
    for exercise in exercises_data:
        # Upload exercise metadata
        exercise_record = {
            "lesson_id": lesson_id,
            "exercise_id": exercise["exercise_id"],
            "exercise_type": exercise["type"],
            "title_english": f"Exercise {exercise['exercise_id']}",
            "title_tamil": f"பயிற்சி {exercise['exercise_id']}",
            "instructions_english": exercise["instructions_english"],
            "instructions_tamil": exercise["instructions_tamil"],
            "difficulty_level": 1,
            "points_value": 10
        }
        
        # Check if exercise already exists
        existing = supabase.table("practice_exercises").select("id").eq("exercise_id", exercise["exercise_id"]).eq("lesson_id", lesson_id).execute()
        
        if existing.data:
            exercise_db_id = existing.data[0]["id"]
        else:
            result = supabase.table("practice_exercises").insert(exercise_record).execute()
            exercise_db_id = result.data[0]["id"]
            count += 1
        
        # Upload exercise question
        question_data = exercise.get("question_data", {})
        question_record = {
            "exercise_id": exercise_db_id,
            "question_text_english": question_data.get("question", exercise["instructions_english"]),
            "question_text_tamil": exercise["instructions_tamil"],
            "question_audio_url": exercise.get("audio_instructions_url", "")
        }
        
        # Check if question already exists
        existing_question = supabase.table("exercise_questions").select("id").eq("exercise_id", exercise_db_id).execute()
        
        if existing_question.data:
            question_db_id = existing_question.data[0]["id"]
        else:
            result = supabase.table("exercise_questions").insert(question_record).execute()
            question_db_id = result.data[0]["id"]
        
        # Upload exercise options (for multiple choice)
        options = question_data.get("options", [])
        for i, option in enumerate(options):
            option_record = {
                "question_id": question_db_id,
                "option_text": option["text"],
                "is_correct": option["is_correct"],
                "option_order": i + 1
            }
            
            # Check if option already exists
            existing_option = supabase.table("exercise_options").select("id").eq("question_id", question_db_id).eq("option_order", i + 1).execute()
            
            if not existing_option.data:
                supabase.table("exercise_options").insert(option_record).execute()
        
        # Upload feedback
        feedback = exercise.get("feedback", {})
        if feedback:
            # Correct feedback
            correct_feedback = {
                "exercise_id": exercise_db_id,
                "feedback_type": "correct",
                "feedback_english": feedback.get("correct_feedback_english", "Correct!"),
                "feedback_tamil": feedback.get("correct_feedback_tamil", "சரி!")
            }
            
            # Incorrect feedback
            incorrect_feedback = {
                "exercise_id": exercise_db_id,
                "feedback_type": "incorrect",
                "feedback_english": feedback.get("incorrect_feedback_english", "Incorrect. Try again."),
                "feedback_tamil": feedback.get("incorrect_feedback_tamil", "தவறு. மீண்டும் முயற்சிக்கவும்.")
            }
            
            # Check if feedback already exists
            existing_feedback = supabase.table("exercise_feedback").select("id").eq("exercise_id", exercise_db_id).execute()
            
            if not existing_feedback.data:
                supabase.table("exercise_feedback").insert([correct_feedback, incorrect_feedback]).execute()
    
    print(f"✅ Uploaded {count} exercises with questions and options")
    return count

def main():
    """Main function to upload A1-L1 lesson"""
    if len(sys.argv) != 2:
        print("Usage: python upload_a1_lesson.py <lesson_number>")
        print("Example: python upload_a1_lesson.py 1")
        sys.exit(1)
    
    lesson_number = sys.argv[1]
    lesson_file = f"docs/Lessons/Tamil/Lessons/A1/A1-L{lesson_number}.json"
    
    print(f"🚀 Starting upload for A1-L{lesson_number}")
    print(f"📁 Reading from: {lesson_file}")
    
    try:
        # Load lesson data
        lesson_data = load_lesson_json(lesson_file)
        
        # Create Supabase client
        supabase = create_supabase_client()
        
        # Upload lesson metadata
        lesson_id = upload_lesson_metadata(supabase, lesson_data)
        
        # Upload vocabulary (25 items expected)
        vocab_count = upload_vocabulary(supabase, lesson_id, lesson_data.get("vocabulary", []))
        
        # Upload conversations (10 expected)
        conv_count = upload_conversations(supabase, lesson_id, lesson_data.get("conversations", []))
        
        # Upload grammar topics (5 expected)
        grammar_count = upload_grammar_topics(supabase, lesson_id, lesson_data.get("grammar", []))
        
        # Upload exercises (10 expected)
        exercise_count = upload_exercises(supabase, lesson_id, lesson_data.get("exercises", []))
        
        print(f"\n🎉 Upload Complete for A1-L{lesson_number}!")
        print(f"📊 Summary:")
        print(f"   • Vocabulary: {vocab_count}/25")
        print(f"   • Conversations: {conv_count}/10") 
        print(f"   • Grammar Topics: {grammar_count}/5")
        print(f"   • Exercises: {exercise_count}/10")
        
        # Validate counts
        total_expected = 25 + 10 + 5 + 10  # 50 items total
        total_uploaded = vocab_count + conv_count + grammar_count + exercise_count
        
        if total_uploaded == total_expected:
            print(f"✅ Perfect! All {total_expected} items uploaded successfully")
        else:
            print(f"⚠️  Warning: Expected {total_expected} items, uploaded {total_uploaded}")
            
    except Exception as e:
        print(f"❌ Error uploading lesson: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
