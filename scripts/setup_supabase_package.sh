#!/bin/bash

# Setup Supabase Package for NIRA-Tamil
# This script provides instructions and automated setup for Supabase integration

set -e

echo "🚀 Setting up Supabase Package for NIRA-Tamil"
echo "=============================================="

PROJECT_DIR="/Users/<USER>/Documents/NIRA-Tamil"
PROJECT_FILE="$PROJECT_DIR/NIRA-Tamil.xcodeproj"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📁 Project Directory: $PROJECT_DIR${NC}"
echo -e "${BLUE}📁 Project File: $PROJECT_FILE${NC}"

# Check if project exists
if [ ! -d "$PROJECT_FILE" ]; then
    echo -e "${RED}❌ Xcode project not found at $PROJECT_FILE${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Xcode project found${NC}"

# Check if Supabase is already configured
if [ -f "$PROJECT_DIR/NIRA-Tamil.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved" ]; then
    if grep -q "supabase-swift" "$PROJECT_DIR/NIRA-Tamil.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved"; then
        echo -e "${GREEN}✅ Supabase package already configured${NC}"
    else
        echo -e "${YELLOW}⚠️  Swift Package Manager configured but Supabase not found${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  No Swift Package Manager configuration found${NC}"
fi

echo -e "\n${BLUE}📦 Supabase Package Setup Instructions:${NC}"
echo "1. Open NIRA-Tamil.xcodeproj in Xcode"
echo "2. Select the project in the navigator (top-level NIRA-Tamil)"
echo "3. Select the NIRA-Tamil target"
echo "4. Go to 'Package Dependencies' tab"
echo "5. Click '+' button to add a package"
echo "6. Enter URL: https://github.com/supabase/supabase-swift"
echo "7. Click 'Add Package'"
echo "8. Select 'Supabase' library and click 'Add Package'"

echo -e "\n${BLUE}🔧 After adding the package:${NC}"
echo "1. Build the project to verify Supabase imports work"
echo "2. Test database connection with sample data"
echo "3. Verify local caching functionality"

echo -e "\n${BLUE}🗄️ Database Configuration:${NC}"
echo "- Supabase Project ID: wnsorhbsucjguaoquhvr"
echo "- Database URL: https://wnsorhbsucjguaoquhvr.supabase.co"
echo "- Sample data already inserted in lessons table"

echo -e "\n${BLUE}🧪 Testing Commands:${NC}"
echo "cd $PROJECT_DIR"
echo "xcodebuild -project NIRA-Tamil.xcodeproj -scheme NIRA-Tamil -destination 'platform=iOS Simulator,name=iPhone 16' build"

echo -e "\n${GREEN}🎉 Setup preparation complete!${NC}"
echo -e "${YELLOW}⚠️  Manual step required: Add Supabase package in Xcode${NC}"
