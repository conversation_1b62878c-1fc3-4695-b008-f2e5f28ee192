-- Populate Tamil Script Database with Comprehensive Character Data
-- Run this after creating the database schema

-- Insert Tamil Vowels (உயிரெழுத்துகள்)
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('அ', 'vowel', 'U+0B85', 'a', '/ʌ/', 'Short A', 'அகரம்', 1, 1, 2, 'simple', 'basic_vowel', 1),
('ஆ', 'vowel', 'U+0B86', 'ā', '/aː/', 'Long A', 'ஆகரம்', 1, 2, 3, 'simple', 'long_vowel', 2),
('இ', 'vowel', 'U+0B87', 'i', '/i/', 'Short I', 'இகரம்', 1, 3, 2, 'simple', 'basic_vowel', 3),
('ஈ', 'vowel', 'U+0B88', 'ī', '/iː/', 'Long I', 'ஈகரம்', 1, 4, 3, 'simple', 'long_vowel', 4),
('உ', 'vowel', 'U+0B89', 'u', '/u/', 'Short U', 'உகரம்', 1, 5, 2, 'simple', 'basic_vowel', 5),
('ஊ', 'vowel', 'U+0B8A', 'ū', '/uː/', 'Long U', 'ஊகரம்', 1, 6, 3, 'simple', 'long_vowel', 6),
('எ', 'vowel', 'U+0B8E', 'e', '/e/', 'Short E', 'எகரம்', 2, 7, 3, 'moderate', 'basic_vowel', 7),
('ஏ', 'vowel', 'U+0B8F', 'ē', '/eː/', 'Long E', 'ஏகரம்', 2, 8, 4, 'moderate', 'long_vowel', 8),
('ஐ', 'vowel', 'U+0B90', 'ai', '/ʌɪ/', 'AI', 'ஐகரம்', 2, 9, 4, 'moderate', 'diphthong', 9),
('ஒ', 'vowel', 'U+0B92', 'o', '/o/', 'Short O', 'ஒகரம்', 2, 10, 3, 'moderate', 'basic_vowel', 10),
('ஓ', 'vowel', 'U+0B93', 'ō', '/oː/', 'Long O', 'ஓகரம்', 2, 11, 4, 'moderate', 'long_vowel', 11),
('ஔ', 'vowel', 'U+0B94', 'au', '/ʌʊ/', 'AU', 'ஔகரம்', 3, 12, 5, 'complex', 'diphthong', 12);

-- Insert Tamil Consonants (மெய்யெழுத்துகள்)
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('க்', 'consonant', 'U+0B95+U+0BCD', 'k', '/k/', 'Ka', 'ககரம்', 1, 13, 2, 'simple', 'hard_consonant', 13),
('ங்', 'consonant', 'U+0B99+U+0BCD', 'ṅ', '/ŋ/', 'Nga', 'ஙகரம்', 2, 14, 3, 'moderate', 'nasal_consonant', 14),
('ச்', 'consonant', 'U+0B9A+U+0BCD', 'c', '/t͡ʃ/', 'Cha', 'சகரம்', 1, 15, 2, 'simple', 'hard_consonant', 15),
('ஞ்', 'consonant', 'U+0B9E+U+0BCD', 'ñ', '/ɲ/', 'Nya', 'ஞகரம்', 3, 16, 4, 'complex', 'nasal_consonant', 16),
('ட்', 'consonant', 'U+0B9F+U+0BCD', 'ṭ', '/ʈ/', 'Ta', 'டகரம்', 2, 17, 3, 'moderate', 'hard_consonant', 17),
('ண்', 'consonant', 'U+0BA3+U+0BCD', 'ṇ', '/ɳ/', 'Na', 'ணகரம்', 2, 18, 3, 'moderate', 'nasal_consonant', 18),
('த்', 'consonant', 'U+0BA4+U+0BCD', 't', '/t̪/', 'Tha', 'தகரம்', 1, 19, 2, 'simple', 'soft_consonant', 19),
('ந்', 'consonant', 'U+0BA8+U+0BCD', 'n', '/n̪/', 'Na', 'நகரம்', 1, 20, 2, 'simple', 'nasal_consonant', 20),
('ப்', 'consonant', 'U+0BAA+U+0BCD', 'p', '/p/', 'Pa', 'பகரம்', 1, 21, 2, 'simple', 'hard_consonant', 21),
('ம்', 'consonant', 'U+0BAE+U+0BCD', 'm', '/m/', 'Ma', 'மகரம்', 1, 22, 2, 'simple', 'nasal_consonant', 22),
('ய்', 'consonant', 'U+0BAF+U+0BCD', 'y', '/j/', 'Ya', 'யகரம்', 2, 23, 3, 'moderate', 'semi_vowel', 23),
('ர்', 'consonant', 'U+0BB0+U+0BCD', 'r', '/r/', 'Ra', 'ரகரம்', 1, 24, 2, 'simple', 'liquid_consonant', 24),
('ல்', 'consonant', 'U+0BB2+U+0BCD', 'l', '/l/', 'La', 'லகரம்', 1, 25, 2, 'simple', 'liquid_consonant', 25),
('வ்', 'consonant', 'U+0BB5+U+0BCD', 'v', '/ʋ/', 'Va', 'வகரம்', 2, 26, 3, 'moderate', 'semi_vowel', 26),
('ழ்', 'consonant', 'U+0BB4+U+0BCD', 'ḻ', '/ɭ/', 'Zha', 'ழகரம்', 3, 27, 4, 'complex', 'liquid_consonant', 27),
('ள்', 'consonant', 'U+0BB3+U+0BCD', 'ḷ', '/ɭ/', 'La', 'ளகரம்', 2, 28, 3, 'moderate', 'liquid_consonant', 28),
('ற்', 'consonant', 'U+0BB1+U+0BCD', 'ṟ', '/r/', 'Ra', 'றகரம்', 2, 29, 3, 'moderate', 'hard_consonant', 29),
('ன்', 'consonant', 'U+0BA9+U+0BCD', 'ṉ', '/n/', 'Na', 'னகரம்', 2, 30, 3, 'moderate', 'nasal_consonant', 30);

-- Insert Special Character
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('ஃ', 'special', 'U+0B83', 'ḥ', '/h/', 'Aytham', 'ஆய்தம்', 3, 31, 2, 'complex', 'special_character', 31);

-- Insert Stroke Order Data for Vowels
-- அ (Short A)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'அ'), 1, '{"path_data": "M 10 20 Q 15 10 25 20", "bounding_box": {"x": 10, "y": 10, "width": 15, "height": 10}, "path_length": 20.5}', 'top-to-bottom', 'curve', 1000, '{"x": 10, "y": 20}', '{"x": 25, "y": 20}', 'Start from top left, curve down and right'),
((SELECT id FROM tamil_characters WHERE character = 'அ'), 2, '{"path_data": "M 25 20 L 25 35", "bounding_box": {"x": 25, "y": 20, "width": 0, "height": 15}, "path_length": 15}', 'top-to-bottom', 'vertical', 800, '{"x": 25, "y": 20}', '{"x": 25, "y": 35}', 'Draw vertical line downward');

-- ஆ (Long A)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'ஆ'), 1, '{"path_data": "M 10 20 Q 15 10 25 20", "bounding_box": {"x": 10, "y": 10, "width": 15, "height": 10}, "path_length": 20.5}', 'top-to-bottom', 'curve', 1000, '{"x": 10, "y": 20}', '{"x": 25, "y": 20}', 'Start from top left, curve down and right'),
((SELECT id FROM tamil_characters WHERE character = 'ஆ'), 2, '{"path_data": "M 25 20 L 25 35", "bounding_box": {"x": 25, "y": 20, "width": 0, "height": 15}, "path_length": 15}', 'top-to-bottom', 'vertical', 800, '{"x": 25, "y": 20}', '{"x": 25, "y": 35}', 'Draw vertical line downward'),
((SELECT id FROM tamil_characters WHERE character = 'ஆ'), 3, '{"path_data": "M 30 15 L 40 15", "bounding_box": {"x": 30, "y": 15, "width": 10, "height": 0}, "path_length": 10}', 'left-to-right', 'horizontal', 600, '{"x": 30, "y": 15}', '{"x": 40, "y": 15}', 'Add horizontal extension for long vowel');

-- இ (Short I)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'இ'), 1, '{"path_data": "M 15 10 L 15 30", "bounding_box": {"x": 15, "y": 10, "width": 0, "height": 20}, "path_length": 20}', 'top-to-bottom', 'vertical', 800, '{"x": 15, "y": 10}', '{"x": 15, "y": 30}', 'Draw main vertical stroke'),
((SELECT id FROM tamil_characters WHERE character = 'இ'), 2, '{"path_data": "M 10 15 Q 15 10 20 15", "bounding_box": {"x": 10, "y": 10, "width": 10, "height": 5}, "path_length": 12}', 'left-to-right', 'curve', 700, '{"x": 10, "y": 15}', '{"x": 20, "y": 15}', 'Add curved top portion');

-- க் (Ka consonant)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'க்'), 1, '{"path_data": "M 10 15 Q 20 10 30 15", "bounding_box": {"x": 10, "y": 10, "width": 20, "height": 5}, "path_length": 22}', 'left-to-right', 'curve', 1000, '{"x": 10, "y": 15}', '{"x": 30, "y": 15}', 'Draw curved top stroke'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), 2, '{"path_data": "M 20 15 L 20 30", "bounding_box": {"x": 20, "y": 15, "width": 0, "height": 15}, "path_length": 15}', 'top-to-bottom', 'vertical', 800, '{"x": 20, "y": 15}', '{"x": 20, "y": 30}', 'Draw vertical stem');

-- ம் (Ma consonant)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'ம்'), 1, '{"path_data": "M 10 10 L 30 10", "bounding_box": {"x": 10, "y": 10, "width": 20, "height": 0}, "path_length": 20}', 'left-to-right', 'horizontal', 800, '{"x": 10, "y": 10}', '{"x": 30, "y": 10}', 'Draw horizontal top stroke'),
((SELECT id FROM tamil_characters WHERE character = 'ம்'), 2, '{"path_data": "M 20 10 L 20 30", "bounding_box": {"x": 20, "y": 10, "width": 0, "height": 20}, "path_length": 20}', 'top-to-bottom', 'vertical', 900, '{"x": 20, "y": 10}', '{"x": 20, "y": 30}', 'Draw main vertical stroke');

-- Sample Character Combinations (க + vowels)
INSERT INTO tamil_character_combinations (base_character_id, modifier_character_id, combined_character, combination_rule, visual_transformation, difficulty_level, frequency_rank, learning_order, formation_notes) VALUES
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'அ'), 'க', 'consonant + inherent vowel', 'Remove pulli (்) from consonant', 1, 32, 32, 'Basic consonant-vowel combination'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஆ'), 'கா', 'consonant + ா', 'Add ா after consonant', 1, 33, 33, 'Consonant with long A vowel'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'இ'), 'கி', 'consonant + ி', 'Add ி before consonant', 2, 34, 34, 'Consonant with short I vowel - vowel mark comes before'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஈ'), 'கீ', 'consonant + ீ', 'Add ீ before consonant', 2, 35, 35, 'Consonant with long I vowel'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'உ'), 'கு', 'consonant + ு', 'Add ு below consonant', 2, 36, 36, 'Consonant with short U vowel - vowel mark below'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஊ'), 'கூ', 'consonant + ூ', 'Add ூ below consonant', 2, 37, 37, 'Consonant with long U vowel'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'எ'), 'கெ', 'consonant + ெ', 'Add ெ before consonant', 2, 38, 38, 'Consonant with short E vowel'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஏ'), 'கே', 'consonant + ே', 'Add ே before consonant', 2, 39, 39, 'Consonant with long E vowel'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஐ'), 'கை', 'consonant + ை', 'Add ை before consonant', 3, 40, 40, 'Consonant with AI diphthong'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஒ'), 'கொ', 'consonant + ொ', 'Add ொ before consonant', 2, 41, 41, 'Consonant with short O vowel'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஓ'), 'கோ', 'consonant + ோ', 'Add ோ before consonant', 2, 42, 42, 'Consonant with long O vowel'),
((SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ஔ'), 'கௌ', 'consonant + ௌ', 'Add ௌ before consonant', 3, 43, 43, 'Consonant with AU diphthong');

-- Sample Writing Content for A1 Level
INSERT INTO tamil_writing_content (content_type, cefr_level, writing_mode, practice_text, practice_text_romanized, practice_text_english, target_characters, lesson_id, difficulty_score, estimated_time_minutes, success_criteria, hints, cultural_context, learning_objectives) VALUES
('character', 'A1', 'guided', 'அ', 'a', 'The letter A', ARRAY[(SELECT id FROM tamil_characters WHERE character = 'அ')], 'A1_BASIC_GREETINGS', 1, 5, '{"accuracy_threshold": 70, "completion_required": true}', '[{"id": "' || gen_random_uuid() || '", "hint_type": "stroke_order", "content": "Start from the top and curve down to the right", "priority": 1}]', 'அ is the first letter of Tamil alphabet and represents the sound /a/', ARRAY['Learn basic vowel formation', 'Practice proper stroke order']),

('character', 'A1', 'guided', 'க்', 'k', 'The consonant K', ARRAY[(SELECT id FROM tamil_characters WHERE character = 'க்')], 'A1_BASIC_GREETINGS', 2, 7, '{"accuracy_threshold": 70, "completion_required": true}', '[{"id": "' || gen_random_uuid() || '", "hint_type": "stroke_order", "content": "Draw the curved top first, then the vertical line", "priority": 1}]', 'க் is a basic consonant in Tamil', ARRAY['Learn consonant formation', 'Understand pulli (்) usage']),

('word', 'A1', 'guided', 'அம்மா', 'ammā', 'Mother', ARRAY[(SELECT id FROM tamil_characters WHERE character = 'அ'), (SELECT id FROM tamil_characters WHERE character = 'ம்')], 'A1_FAMILY_MEMBERS', 3, 10, '{"accuracy_threshold": 75, "completion_required": true}', '[{"id": "' || gen_random_uuid() || '", "hint_type": "formation", "content": "Write each character clearly with proper spacing", "priority": 1}]', 'அம்மா is a respectful term for mother in Tamil culture', ARRAY['Practice word formation', 'Learn family vocabulary']),

('phrase', 'A1', 'freeform', 'வணக்கம்', 'vaṇakkam', 'Hello/Greetings', ARRAY[(SELECT id FROM tamil_characters WHERE character = 'வ்'), (SELECT id FROM tamil_characters WHERE character = 'ண்'), (SELECT id FROM tamil_characters WHERE character = 'க்'), (SELECT id FROM tamil_characters WHERE character = 'ம்')], 'A1_BASIC_GREETINGS', 4, 15, '{"accuracy_threshold": 80, "completion_required": true}', '[{"id": "' || gen_random_uuid() || '", "hint_type": "cultural", "content": "வணக்கம் is used throughout the day as a universal greeting", "priority": 1}]', 'வணக்கம் is the most common Tamil greeting, showing respect and politeness', ARRAY['Master essential greeting', 'Practice connected writing']);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tamil_characters_learning_order ON tamil_characters(learning_order);
CREATE INDEX IF NOT EXISTS idx_tamil_combinations_learning_order ON tamil_character_combinations(learning_order);
CREATE INDEX IF NOT EXISTS idx_writing_content_cefr_mode ON tamil_writing_content(cefr_level, writing_mode);

-- Add comments for documentation
COMMENT ON TABLE tamil_characters IS 'Complete Tamil character set with learning metadata';
COMMENT ON TABLE tamil_stroke_orders IS 'Detailed stroke order sequences for proper character formation';
COMMENT ON TABLE tamil_character_combinations IS 'Consonant-vowel combinations forming complete syllables';
COMMENT ON TABLE tamil_writing_content IS 'Structured writing practice content for progressive learning';
