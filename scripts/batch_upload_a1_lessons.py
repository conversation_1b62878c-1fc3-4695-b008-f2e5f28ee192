#!/usr/bin/env python3
"""
Batch A1 Tamil Lessons Upload Script
Uploads all A1 lessons (L2-L25) from JSON files to Supabase database
"""

import json
import os
import sys
import time
from supabase import create_client, Client
from typing import Dict, List, Any
import subprocess

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def create_supabase_client() -> Client:
    """Create and return Supabase client"""
    return create_client(SUPABASE_URL, SUPABASE_KEY)

def validate_lesson_file(lesson_number: int) -> str:
    """Validate lesson file exists and return path"""
    lesson_file = f"docs/Lessons/Tamil/Lessons/A1/A1-L{lesson_number}.json"
    if not os.path.exists(lesson_file):
        raise FileNotFoundError(f"Lesson file not found: {lesson_file}")
    return lesson_file

def upload_single_lesson(lesson_number: int) -> Dict[str, Any]:
    """Upload a single lesson using the upload script"""
    print(f"\n📚 Processing A1-L{lesson_number}...")
    
    try:
        # Run the single lesson upload script
        result = subprocess.run(
            ["python3", "scripts/upload_a1_lesson.py", str(lesson_number)],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout per lesson
        )
        
        if result.returncode == 0:
            # Parse the output to extract counts
            output_lines = result.stdout.strip().split('\n')
            summary = {}
            
            for line in output_lines:
                if "Vocabulary:" in line:
                    summary['vocabulary'] = line.split(':')[1].strip()
                elif "Conversations:" in line:
                    summary['conversations'] = line.split(':')[1].strip()
                elif "Grammar Topics:" in line:
                    summary['grammar'] = line.split(':')[1].strip()
                elif "Exercises:" in line:
                    summary['exercises'] = line.split(':')[1].strip()
            
            return {
                'success': True,
                'lesson_number': lesson_number,
                'summary': summary,
                'output': result.stdout
            }
        else:
            return {
                'success': False,
                'lesson_number': lesson_number,
                'error': result.stderr,
                'output': result.stdout
            }
            
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'lesson_number': lesson_number,
            'error': f"Timeout: Lesson {lesson_number} took longer than 5 minutes to upload"
        }
    except Exception as e:
        return {
            'success': False,
            'lesson_number': lesson_number,
            'error': str(e)
        }

def validate_database_counts(supabase: Client) -> Dict[str, int]:
    """Validate total counts in database for all A1 lessons"""
    counts = {}

    # Count vocabulary
    vocab_result = supabase.table("vocabulary").select("id", count="exact").eq("lessons.level_code", "A1").execute()
    counts['vocabulary'] = vocab_result.count

    # Count conversations
    conv_result = supabase.table("conversations").select("id", count="exact").eq("lessons.level_code", "A1").execute()
    counts['conversations'] = conv_result.count

    # Count grammar topics
    grammar_result = supabase.table("grammar_topics").select("id", count="exact").eq("lessons.level_code", "A1").execute()
    counts['grammar_topics'] = grammar_result.count

    # Count practice exercises
    exercise_result = supabase.table("practice_exercises").select("id", count="exact").eq("lessons.level_code", "A1").execute()
    counts['practice_exercises'] = exercise_result.count

    return counts

def main():
    """Main function to batch upload A1 lessons L2-L25"""
    print("🚀 Starting Batch Upload for A1 Lessons L2-L25")
    print("=" * 60)
    
    # Determine which lessons to upload
    start_lesson = 2
    end_lesson = 25
    
    if len(sys.argv) >= 2:
        start_lesson = int(sys.argv[1])
    if len(sys.argv) >= 3:
        end_lesson = int(sys.argv[2])
    
    print(f"📋 Uploading lessons A1-L{start_lesson} through A1-L{end_lesson}")
    
    # Validate all lesson files exist
    missing_files = []
    for lesson_num in range(start_lesson, end_lesson + 1):
        try:
            validate_lesson_file(lesson_num)
        except FileNotFoundError as e:
            missing_files.append(str(e))
    
    if missing_files:
        print("❌ Missing lesson files:")
        for missing in missing_files:
            print(f"   {missing}")
        sys.exit(1)
    
    # Upload lessons
    results = []
    successful_uploads = 0
    failed_uploads = 0
    
    start_time = time.time()
    
    for lesson_num in range(start_lesson, end_lesson + 1):
        result = upload_single_lesson(lesson_num)
        results.append(result)
        
        if result['success']:
            successful_uploads += 1
            print(f"✅ A1-L{lesson_num} uploaded successfully")
            if 'summary' in result:
                summary = result['summary']
                print(f"   📊 V:{summary.get('vocabulary', '?')} C:{summary.get('conversations', '?')} G:{summary.get('grammar', '?')} E:{summary.get('exercises', '?')}")
        else:
            failed_uploads += 1
            print(f"❌ A1-L{lesson_num} failed: {result.get('error', 'Unknown error')}")
        
        # Small delay between uploads to avoid overwhelming the database
        time.sleep(1)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print("\n" + "=" * 60)
    print("🎉 Batch Upload Complete!")
    print(f"⏱️  Total Time: {total_time:.1f} seconds")
    print(f"✅ Successful: {successful_uploads}")
    print(f"❌ Failed: {failed_uploads}")
    print(f"📊 Total Lessons Processed: {len(results)}")
    
    # Validate final database counts
    print("\n🔍 Validating Database Counts...")
    try:
        supabase = create_supabase_client()
        counts = validate_database_counts(supabase)
        
        expected_counts = {
            'vocabulary': 25 * 25,      # 25 lessons × 25 vocabulary each = 625
            'conversations': 25 * 10,   # 25 lessons × 10 conversations each = 250
            'grammar_topics': 25 * 5,   # 25 lessons × 5 grammar topics each = 125
            'practice_exercises': 25 * 10  # 25 lessons × 10 exercises each = 250
        }
        
        print("📈 Final Database Counts:")
        all_correct = True
        for content_type, actual_count in counts.items():
            expected_count = expected_counts.get(content_type, 0)
            status = "✅" if actual_count == expected_count else "⚠️"
            print(f"   {status} {content_type.title()}: {actual_count}/{expected_count}")
            if actual_count != expected_count:
                all_correct = False
        
        if all_correct:
            print("\n🎯 Perfect! All A1 content uploaded successfully!")
        else:
            print("\n⚠️  Some content counts don't match expected values.")
            
    except Exception as e:
        print(f"❌ Error validating database: {str(e)}")
    
    # Show failed uploads details
    if failed_uploads > 0:
        print(f"\n❌ Failed Uploads Details:")
        for result in results:
            if not result['success']:
                print(f"   A1-L{result['lesson_number']}: {result.get('error', 'Unknown error')}")
    
    # Summary for next steps
    if successful_uploads == len(results):
        print(f"\n🚀 Next Steps:")
        print(f"   1. Test iOS app integration with new lessons")
        print(f"   2. Validate audio URL mappings")
        print(f"   3. Run end-to-end functionality tests")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
