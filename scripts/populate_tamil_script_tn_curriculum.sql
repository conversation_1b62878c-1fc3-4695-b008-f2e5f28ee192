-- Populate Tamil Script Database Following TN Curriculum Progression
-- Based on analysis of Std 1-2 Tamil books
-- Run this after creating the database schema

-- Clear existing data
DELETE FROM tamil_stroke_orders;
DELETE FROM tamil_character_combinations;
DELETE FROM tamil_writing_content;
DELETE FROM tamil_characters;

-- Insert Tamil Characters Following TN Curriculum Learning Order
-- Phase 1: Foundation Characters (Std 1)

-- First Wave: ப, ம and basic combinations (Pages 19-24)
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('ப்', 'consonant', 'U+0BAA+U+0BCD', 'p', '/p/', 'Pa', 'பகரம்', 1, 1, 2, 'simple', 'hard_consonant', 1),
('ம்', 'consonant', 'U+0BAE+U+0BCD', 'm', '/m/', 'Ma', 'மகரம்', 1, 2, 2, 'simple', 'nasal_consonant', 2),
('அ', 'vowel', 'U+0B85', 'a', '/ʌ/', 'Short A', 'அகரம்', 1, 3, 2, 'simple', 'basic_vowel', 3),
('ப', 'combined', 'U+0BAA', 'pa', '/pʌ/', 'Pa with A', 'ப + அ', 1, 4, 2, 'simple', 'basic_combination', 4),
('ம', 'combined', 'U+0BAE', 'ma', '/mʌ/', 'Ma with A', 'ம + அ', 1, 5, 2, 'simple', 'basic_combination', 5),
('ஆ', 'vowel', 'U+0B86', 'ā', '/aː/', 'Long A', 'ஆகரம்', 1, 6, 3, 'simple', 'long_vowel', 6),
('பா', 'combined', 'U+0BAA+U+0BBE', 'pā', '/paː/', 'Pa with Aa', 'ப + ஆ', 1, 7, 3, 'simple', 'basic_combination', 7),
('மா', 'combined', 'U+0BAE+U+0BBE', 'mā', '/maː/', 'Ma with Aa', 'ம + ஆ', 1, 8, 3, 'simple', 'basic_combination', 8);

-- Second Wave: க, ச, த, ந (Pages 30-34)
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('க்', 'consonant', 'U+0B95+U+0BCD', 'k', '/k/', 'Ka', 'ககரம்', 1, 9, 2, 'simple', 'hard_consonant', 9),
('ச்', 'consonant', 'U+0B9A+U+0BCD', 'c', '/t͡ʃ/', 'Cha', 'சகரம்', 1, 10, 2, 'simple', 'hard_consonant', 10),
('த்', 'consonant', 'U+0BA4+U+0BCD', 't', '/t̪/', 'Tha', 'தகரம்', 1, 11, 2, 'simple', 'soft_consonant', 11),
('ந்', 'consonant', 'U+0BA8+U+0BCD', 'n', '/n̪/', 'Na', 'நகரம்', 1, 12, 2, 'simple', 'nasal_consonant', 12),
('க', 'combined', 'U+0B95', 'ka', '/kʌ/', 'Ka with A', 'க + அ', 1, 13, 2, 'simple', 'basic_combination', 13),
('ச', 'combined', 'U+0B9A', 'ca', '/t͡ʃʌ/', 'Cha with A', 'ச + அ', 1, 14, 2, 'simple', 'basic_combination', 14),
('த', 'combined', 'U+0BA4', 'ta', '/t̪ʌ/', 'Tha with A', 'த + அ', 1, 15, 2, 'simple', 'basic_combination', 15),
('ந', 'combined', 'U+0BA8', 'na', '/n̪ʌ/', 'Na with A', 'ந + அ', 1, 16, 2, 'simple', 'basic_combination', 16);

-- Third Wave: வ, ர, ட and vowels இ, ஈ (Pages 39-48)
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('வ்', 'consonant', 'U+0BB5+U+0BCD', 'v', '/ʋ/', 'Va', 'வகரம்', 2, 17, 3, 'moderate', 'semi_vowel', 17),
('ர்', 'consonant', 'U+0BB0+U+0BCD', 'r', '/r/', 'Ra', 'ரகரம்', 1, 18, 2, 'simple', 'liquid_consonant', 18),
('ட்', 'consonant', 'U+0B9F+U+0BCD', 'ṭ', '/ʈ/', 'Ta', 'டகரம்', 2, 19, 3, 'moderate', 'hard_consonant', 19),
('வ', 'combined', 'U+0BB5', 'va', '/ʋʌ/', 'Va with A', 'வ + அ', 2, 20, 3, 'moderate', 'basic_combination', 20),
('வா', 'combined', 'U+0BB5+U+0BBE', 'vā', '/ʋaː/', 'Va with Aa', 'வ + ஆ', 2, 21, 4, 'moderate', 'basic_combination', 21),
('இ', 'vowel', 'U+0B87', 'i', '/i/', 'Short I', 'இகரம்', 1, 22, 2, 'simple', 'basic_vowel', 22),
('ஈ', 'vowel', 'U+0B88', 'ī', '/iː/', 'Long I', 'ஈகரம்', 1, 23, 3, 'simple', 'long_vowel', 23);

-- Fourth Wave: Remaining vowels (Pages 58-70)
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('உ', 'vowel', 'U+0B89', 'u', '/u/', 'Short U', 'உகரம்', 1, 24, 2, 'simple', 'basic_vowel', 24),
('ஊ', 'vowel', 'U+0B8A', 'ū', '/uː/', 'Long U', 'ஊகரம்', 1, 25, 3, 'simple', 'long_vowel', 25),
('எ', 'vowel', 'U+0B8E', 'e', '/e/', 'Short E', 'எகரம்', 2, 26, 3, 'moderate', 'basic_vowel', 26),
('ஏ', 'vowel', 'U+0B8F', 'ē', '/eː/', 'Long E', 'ஏகரம்', 2, 27, 4, 'moderate', 'long_vowel', 27),
('ஐ', 'vowel', 'U+0B90', 'ai', '/ʌɪ/', 'AI', 'ஐகரம்', 2, 28, 4, 'moderate', 'diphthong', 28),
('ஒ', 'vowel', 'U+0B92', 'o', '/o/', 'Short O', 'ஒகரம்', 2, 29, 3, 'moderate', 'basic_vowel', 29),
('ஓ', 'vowel', 'U+0B93', 'ō', '/oː/', 'Long O', 'ஓகரம்', 2, 30, 4, 'moderate', 'long_vowel', 30),
('ஔ', 'vowel', 'U+0B94', 'au', '/ʌʊ/', 'AU', 'ஔகரம்', 3, 31, 5, 'complex', 'diphthong', 31);

-- Phase 2: Extended Consonants (Std 2)
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('ங்', 'consonant', 'U+0B99+U+0BCD', 'ṅ', '/ŋ/', 'Nga', 'ஙகரம்', 2, 32, 3, 'moderate', 'nasal_consonant', 32),
('ஞ்', 'consonant', 'U+0B9E+U+0BCD', 'ñ', '/ɲ/', 'Nya', 'ஞகரம்', 3, 33, 4, 'complex', 'nasal_consonant', 33),
('ண்', 'consonant', 'U+0BA3+U+0BCD', 'ṇ', '/ɳ/', 'Na', 'ணகரம்', 2, 34, 3, 'moderate', 'nasal_consonant', 34),
('ய்', 'consonant', 'U+0BAF+U+0BCD', 'y', '/j/', 'Ya', 'யகரம்', 2, 35, 3, 'moderate', 'semi_vowel', 35),
('ல்', 'consonant', 'U+0BB2+U+0BCD', 'l', '/l/', 'La', 'லகரம்', 1, 36, 2, 'simple', 'liquid_consonant', 36),
('ழ்', 'consonant', 'U+0BB4+U+0BCD', 'ḻ', '/ɭ/', 'Zha', 'ழகரம்', 3, 37, 4, 'complex', 'liquid_consonant', 37),
('ள்', 'consonant', 'U+0BB3+U+0BCD', 'ḷ', '/ɭ/', 'La', 'ளகரம்', 2, 38, 3, 'moderate', 'liquid_consonant', 38),
('ற்', 'consonant', 'U+0BB1+U+0BCD', 'ṟ', '/r/', 'Ra', 'றகரம்', 2, 39, 3, 'moderate', 'hard_consonant', 39),
('ன்', 'consonant', 'U+0BA9+U+0BCD', 'ṉ', '/n/', 'Na', 'னகரம்', 2, 40, 3, 'moderate', 'nasal_consonant', 40);

-- Special Character
INSERT INTO tamil_characters (character, character_type, unicode_value, romanization, ipa_pronunciation, character_name_english, character_name_tamil, difficulty_level, frequency_rank, stroke_count, writing_complexity, character_category, learning_order) VALUES
('ஃ', 'special', 'U+0B83', 'ḥ', '/h/', 'Aytham', 'ஆய்தம்', 3, 41, 2, 'complex', 'special_character', 41);

-- Insert Basic Stroke Orders for Foundation Characters
-- ப் (Pa consonant)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'ப்'), 1, '{"path_data": "M 10 10 L 25 10 L 25 25 L 10 25 Z", "bounding_box": {"x": 10, "y": 10, "width": 15, "height": 15}}', 'clockwise', 'curve', 1200, '{"x": 10, "y": 10}', '{"x": 10, "y": 25}', 'Draw the main body of Pa'),
((SELECT id FROM tamil_characters WHERE character = 'ப்'), 2, '{"path_data": "M 17 25 L 17 30", "bounding_box": {"x": 17, "y": 25, "width": 0, "height": 5}}', 'top-to-bottom', 'vertical', 400, '{"x": 17, "y": 25}', '{"x": 17, "y": 30}', 'Add the pulli (dot) below');

-- ம் (Ma consonant)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'ம்'), 1, '{"path_data": "M 10 15 Q 15 10 20 15 Q 15 20 10 15", "bounding_box": {"x": 10, "y": 10, "width": 10, "height": 10}}', 'clockwise', 'curve', 1000, '{"x": 10, "y": 15}', '{"x": 10, "y": 15}', 'Draw the circular part of Ma'),
((SELECT id FROM tamil_characters WHERE character = 'ம்'), 2, '{"path_data": "M 15 20 L 15 30", "bounding_box": {"x": 15, "y": 20, "width": 0, "height": 10}}', 'top-to-bottom', 'vertical', 600, '{"x": 15, "y": 20}', '{"x": 15, "y": 30}', 'Add the pulli (dot) below');

-- அ (Short A vowel)
INSERT INTO tamil_stroke_orders (character_id, stroke_number, stroke_path, stroke_direction, stroke_type, timing_duration, start_point, end_point, stroke_description) VALUES
((SELECT id FROM tamil_characters WHERE character = 'அ'), 1, '{"path_data": "M 10 20 Q 15 10 25 20", "bounding_box": {"x": 10, "y": 10, "width": 15, "height": 10}}', 'left-to-right', 'curve', 1000, '{"x": 10, "y": 20}', '{"x": 25, "y": 20}', 'Start from top left, curve down and right'),
((SELECT id FROM tamil_characters WHERE character = 'அ'), 2, '{"path_data": "M 25 20 L 25 35", "bounding_box": {"x": 25, "y": 20, "width": 0, "height": 15}}', 'top-to-bottom', 'vertical', 800, '{"x": 25, "y": 20}', '{"x": 25, "y": 35}', 'Draw vertical line downward');

-- Insert Character Combinations for Basic Learning
INSERT INTO tamil_character_combinations (base_character_id, modifier_character_id, combined_character, combination_rule, visual_transformation, difficulty_level, frequency_rank, learning_order, formation_notes) VALUES
-- ப + அ = ப
((SELECT id FROM tamil_characters WHERE character = 'ப்'), (SELECT id FROM tamil_characters WHERE character = 'அ'), 'ப', 'consonant + inherent vowel', 'Remove pulli (்) from consonant', 1, 1, 1, 'Basic consonant-vowel combination - remove the dot'),
-- ம + அ = ம  
((SELECT id FROM tamil_characters WHERE character = 'ம்'), (SELECT id FROM tamil_characters WHERE character = 'அ'), 'ம', 'consonant + inherent vowel', 'Remove pulli (்) from consonant', 1, 2, 2, 'Basic consonant-vowel combination - remove the dot'),
-- ப + ஆ = பா
((SELECT id FROM tamil_characters WHERE character = 'ப்'), (SELECT id FROM tamil_characters WHERE character = 'ஆ'), 'பா', 'consonant + ா', 'Add ா after consonant', 1, 3, 3, 'Consonant with long A vowel sign'),
-- ம + ஆ = மா
((SELECT id FROM tamil_characters WHERE character = 'ம்'), (SELECT id FROM tamil_characters WHERE character = 'ஆ'), 'மா', 'consonant + ா', 'Add ா after consonant', 1, 4, 4, 'Consonant with long A vowel sign');

-- Insert Writing Content for A1 Level Foundation
INSERT INTO tamil_writing_content (content_type, cefr_level, writing_mode, practice_text, practice_text_romanized, practice_text_english, target_characters, lesson_id, difficulty_score, estimated_time_minutes, success_criteria, hints, cultural_context, learning_objectives) VALUES
-- Character practice for ப்
('character', 'A1', 'guided', 'ப்', 'p', 'The consonant Pa', ARRAY[(SELECT id FROM tamil_characters WHERE character = 'ப்')], 'A1_BASIC_GREETINGS', 1, 5, '{"accuracy_threshold": 70, "completion_required": true}', '[{"hint_type": "stroke_order", "content": "Start with the main body, then add the pulli dot below", "priority": 1}]', 'ப் is one of the first consonants taught in Tamil schools', ARRAY['Learn basic consonant formation', 'Practice proper stroke order']),

-- Character practice for ம்
('character', 'A1', 'guided', 'ம்', 'm', 'The consonant Ma', ARRAY[(SELECT id FROM tamil_characters WHERE character = 'ம்')], 'A1_BASIC_GREETINGS', 1, 5, '{"accuracy_threshold": 70, "completion_required": true}', '[{"hint_type": "stroke_order", "content": "Draw the circular part first, then add the pulli dot", "priority": 1}]', 'ம் is a nasal consonant, very common in Tamil', ARRAY['Learn nasal consonant formation', 'Practice circular stroke patterns']),

-- Simple word practice
('word', 'A1', 'guided', 'அம்மா', 'ammā', 'Mother', ARRAY[(SELECT id FROM tamil_characters WHERE character = 'அ'), (SELECT id FROM tamil_characters WHERE character = 'ம்'), (SELECT id FROM tamil_characters WHERE character = 'மா')], 'A1_BASIC_GREETINGS', 2, 10, '{"accuracy_threshold": 75, "completion_required": true}', '[{"hint_type": "word_formation", "content": "அ + ம் + மா = அம்மா (Mother)", "priority": 1}]', 'அம்மா is one of the first words children learn in Tamil', ARRAY['Practice word formation', 'Learn family vocabulary', 'Connect characters to meaning']);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_tamil_characters_learning_order ON tamil_characters(learning_order);
CREATE INDEX IF NOT EXISTS idx_tamil_characters_difficulty ON tamil_characters(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_tamil_characters_type ON tamil_characters(character_type);
CREATE INDEX IF NOT EXISTS idx_tamil_stroke_orders_character ON tamil_stroke_orders(character_id);
CREATE INDEX IF NOT EXISTS idx_tamil_combinations_learning_order ON tamil_character_combinations(learning_order);
CREATE INDEX IF NOT EXISTS idx_tamil_writing_content_cefr ON tamil_writing_content(cefr_level);
CREATE INDEX IF NOT EXISTS idx_tamil_writing_content_type ON tamil_writing_content(content_type);

-- Add grade level mapping for curriculum alignment
ALTER TABLE tamil_characters ADD COLUMN IF NOT EXISTS grade_level INTEGER DEFAULT 1;

-- Update grade levels based on TN curriculum
UPDATE tamil_characters SET grade_level = 1 WHERE learning_order <= 31; -- Std 1 characters
UPDATE tamil_characters SET grade_level = 2 WHERE learning_order > 31;  -- Std 2+ characters

COMMIT;
