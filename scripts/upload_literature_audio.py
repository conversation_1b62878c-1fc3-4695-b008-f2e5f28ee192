#!/usr/bin/env python3
"""
Upload Literature Audio Files to Supabase Storage
This script uploads the generated literature audio files to Supabase storage
and updates the database with the audio URLs.
"""

import os
import sys
import json
import glob
from pathlib import Path
from supabase import create_client, Client
import requests

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDI3NTM5OCwiZXhwIjoyMDY1ODUxMzk4fQ.huEg6PWHSgAaXAtREOLf1aRa3OENbOb437b8lEGyubc"

def main():
    """Main function to upload audio files and update database"""
    
    # Initialize Supabase client
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Get the literature_audio directory
    audio_dir = Path("literature_audio")
    if not audio_dir.exists():
        print("❌ literature_audio directory not found")
        return
    
    # Get all audio files
    audio_files = list(audio_dir.glob("literature_*.mp3"))
    print(f"📁 Found {len(audio_files)} audio files to upload")
    
    uploaded_count = 0
    updated_count = 0
    
    # Group files by content ID
    content_files = {}
    for audio_file in audio_files:
        # Extract content ID and voice type from filename
        # Format: literature_<uuid>_<voice>.mp3
        filename = audio_file.stem
        parts = filename.split('_')
        if len(parts) >= 3:
            content_id = parts[1]
            voice_type = parts[2]
            
            if content_id not in content_files:
                content_files[content_id] = {}
            content_files[content_id][voice_type] = audio_file
    
    print(f"📊 Processing {len(content_files)} content items")
    
    for content_id, files in content_files.items():
        print(f"\n🎵 Processing content: {content_id}")
        
        audio_urls = {}
        
        # Upload each voice type
        for voice_type, file_path in files.items():
            try:
                # Read the audio file
                with open(file_path, 'rb') as f:
                    audio_data = f.read()
                
                # Upload to Supabase storage
                storage_path = f"literature_audio/{file_path.name}"
                
                print(f"  📤 Uploading {voice_type} voice...")
                
                # Upload file to storage
                result = supabase.storage.from_("audio").upload(
                    path=storage_path,
                    file=audio_data,
                    file_options={"content-type": "audio/mpeg"}
                )
                
                if result:
                    # Get public URL
                    public_url = supabase.storage.from_("audio").get_public_url(storage_path)
                    audio_urls[f"audio_{voice_type}_url"] = public_url
                    uploaded_count += 1
                    print(f"  ✅ Uploaded {voice_type} voice: {public_url}")
                else:
                    print(f"  ❌ Failed to upload {voice_type} voice")
                    
            except Exception as e:
                print(f"  ❌ Error uploading {voice_type} voice: {e}")
        
        # Update database with audio URLs
        if audio_urls:
            try:
                result = supabase.table("literature_content").update(audio_urls).eq("id", content_id).execute()
                if result.data:
                    updated_count += 1
                    print(f"  ✅ Updated database for content {content_id}")
                else:
                    print(f"  ❌ Failed to update database for content {content_id}")
            except Exception as e:
                print(f"  ❌ Error updating database: {e}")
    
    print(f"\n🎉 Upload complete!")
    print(f"📤 Uploaded: {uploaded_count} audio files")
    print(f"💾 Updated: {updated_count} content records")

if __name__ == "__main__":
    main()
