-- Tamil Writing App Database Schema
-- Run this in Supabase SQL Editor to create all necessary tables

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tamil Characters Master Table
CREATE TABLE IF NOT EXISTS tamil_characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character VARCHAR(10) NOT NULL UNIQUE,
    character_type VARCHAR(20) NOT NULL CHECK (character_type IN ('vowel', 'consonant', 'combined', 'special')),
    unicode_value VARCHAR(20) NOT NULL,
    romanization VARCHAR(50) NOT NULL,
    ipa_pronunciation VARCHAR(100),
    character_name_english VARCHAR(100) NOT NULL,
    character_name_tamil VARCHAR(100) NOT NULL,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5) DEFAULT 1,
    frequency_rank INTEGER,
    stroke_count INTEGER NOT NULL DEFAULT 1,
    writing_complexity VARCHAR(20) CHECK (writing_complexity IN ('simple', 'moderate', 'complex')) DEFAULT 'simple',
    character_category VARCHAR(30), -- 'basic_vowel', 'long_vowel', 'hard_consonant', etc.
    learning_order INTEGER, -- Order in which characters should be learned
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Stroke Order Sequences
CREATE TABLE IF NOT EXISTS tamil_stroke_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID REFERENCES tamil_characters(id) ON DELETE CASCADE,
    stroke_number INTEGER NOT NULL CHECK (stroke_number > 0),
    stroke_path JSONB NOT NULL, -- SVG path data for stroke
    stroke_direction VARCHAR(50) CHECK (stroke_direction IN ('left-to-right', 'top-to-bottom', 'right-to-left', 'bottom-to-top', 'clockwise', 'counter-clockwise')),
    stroke_type VARCHAR(30) CHECK (stroke_type IN ('horizontal', 'vertical', 'curve', 'dot', 'hook', 'diagonal')),
    timing_duration INTEGER DEFAULT 1000, -- milliseconds for animation
    pressure_variation JSONB, -- pressure points for realistic demonstration
    start_point JSONB NOT NULL, -- {x: number, y: number}
    end_point JSONB NOT NULL, -- {x: number, y: number}
    control_points JSONB, -- For bezier curves
    stroke_description TEXT, -- Human-readable description of the stroke
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(character_id, stroke_number)
);

-- Character Combinations (Vowel + Consonant)
CREATE TABLE IF NOT EXISTS tamil_character_combinations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    base_character_id UUID REFERENCES tamil_characters(id) ON DELETE CASCADE,
    modifier_character_id UUID REFERENCES tamil_characters(id) ON DELETE CASCADE,
    combined_character VARCHAR(10) NOT NULL,
    combination_rule VARCHAR(100),
    visual_transformation TEXT,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5) DEFAULT 1,
    frequency_rank INTEGER,
    learning_order INTEGER,
    formation_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(base_character_id, modifier_character_id)
);

-- Writing Practice Content
CREATE TABLE IF NOT EXISTS tamil_writing_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('character', 'word', 'phrase', 'sentence', 'paragraph')),
    cefr_level VARCHAR(5) NOT NULL CHECK (cefr_level IN ('A1', 'A2', 'B1', 'B2', 'C1', 'C2')),
    writing_mode VARCHAR(20) NOT NULL CHECK (writing_mode IN ('guided', 'freeform', 'assessment')),
    practice_text VARCHAR(500) NOT NULL,
    practice_text_romanized VARCHAR(500),
    practice_text_english VARCHAR(500),
    target_characters UUID[] NOT NULL, -- Array of character IDs
    lesson_id VARCHAR(50), -- Link to existing lessons
    vocabulary_id VARCHAR(50), -- Link to specific vocabulary items
    difficulty_score INTEGER CHECK (difficulty_score BETWEEN 1 AND 10) DEFAULT 1,
    estimated_time_minutes INTEGER DEFAULT 5,
    success_criteria JSONB NOT NULL DEFAULT '{"accuracy_threshold": 80, "completion_required": true}',
    hints JSONB DEFAULT '[]', -- Array of writing tips and guidance
    cultural_context TEXT,
    learning_objectives TEXT[],
    prerequisite_content_ids UUID[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Writing Progress
CREATE TABLE IF NOT EXISTS user_writing_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    character_id UUID REFERENCES tamil_characters(id) ON DELETE CASCADE,
    writing_content_id UUID REFERENCES tamil_writing_content(id) ON DELETE CASCADE,
    practice_session_id UUID,
    accuracy_score DECIMAL(5,2) CHECK (accuracy_score >= 0 AND accuracy_score <= 100),
    completion_time_seconds INTEGER,
    stroke_accuracy JSONB, -- Per-stroke accuracy data
    improvement_areas JSONB DEFAULT '[]', -- Areas needing work
    feedback_provided JSONB DEFAULT '[]', -- AI feedback given
    practice_date TIMESTAMPTZ DEFAULT NOW(),
    device_type VARCHAR(20) CHECK (device_type IN ('iPhone', 'iPad', 'AppleWatch', 'Mac')),
    writing_mode VARCHAR(20) NOT NULL CHECK (writing_mode IN ('guided', 'freeform', 'assessment')),
    attempts_count INTEGER DEFAULT 1,
    is_completed BOOLEAN DEFAULT false,
    mastery_level VARCHAR(20) CHECK (mastery_level IN ('beginner', 'intermediate', 'advanced', 'mastered')) DEFAULT 'beginner'
);

-- Writing Sessions (for tracking complete practice sessions)
CREATE TABLE IF NOT EXISTS writing_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    session_type VARCHAR(30) CHECK (session_type IN ('character_practice', 'word_practice', 'lesson_writing', 'free_practice')),
    lesson_id VARCHAR(50),
    total_characters_practiced INTEGER DEFAULT 0,
    total_accuracy_score DECIMAL(5,2),
    session_duration_seconds INTEGER,
    characters_mastered INTEGER DEFAULT 0,
    improvement_areas JSONB DEFAULT '[]',
    session_notes TEXT,
    device_type VARCHAR(20),
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    is_completed BOOLEAN DEFAULT false
);

-- Writing Achievements and Milestones
CREATE TABLE IF NOT EXISTS writing_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    achievement_type VARCHAR(50) NOT NULL,
    achievement_name VARCHAR(100) NOT NULL,
    achievement_description TEXT,
    criteria_met JSONB NOT NULL,
    points_awarded INTEGER DEFAULT 0,
    badge_icon VARCHAR(100),
    unlocked_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_tamil_characters_type ON tamil_characters(character_type);
CREATE INDEX IF NOT EXISTS idx_tamil_characters_difficulty ON tamil_characters(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_tamil_characters_learning_order ON tamil_characters(learning_order);
CREATE INDEX IF NOT EXISTS idx_stroke_orders_character ON tamil_stroke_orders(character_id);
CREATE INDEX IF NOT EXISTS idx_stroke_orders_number ON tamil_stroke_orders(character_id, stroke_number);
CREATE INDEX IF NOT EXISTS idx_combinations_base ON tamil_character_combinations(base_character_id);
CREATE INDEX IF NOT EXISTS idx_writing_content_level ON tamil_writing_content(cefr_level);
CREATE INDEX IF NOT EXISTS idx_writing_content_type ON tamil_writing_content(content_type);
CREATE INDEX IF NOT EXISTS idx_writing_content_lesson ON tamil_writing_content(lesson_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_user ON user_writing_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_character ON user_writing_progress(character_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_date ON user_writing_progress(practice_date);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user ON writing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_achievements_user ON writing_achievements(user_id);

-- Row Level Security (RLS) policies
ALTER TABLE user_writing_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_achievements ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user data protection
CREATE POLICY "Users can only access their own writing progress" ON user_writing_progress
    FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can only access their own writing sessions" ON writing_sessions
    FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can only access their own achievements" ON writing_achievements
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Public read access for reference tables
CREATE POLICY "Public read access for tamil_characters" ON tamil_characters
    FOR SELECT USING (true);

CREATE POLICY "Public read access for tamil_stroke_orders" ON tamil_stroke_orders
    FOR SELECT USING (true);

CREATE POLICY "Public read access for tamil_character_combinations" ON tamil_character_combinations
    FOR SELECT USING (true);

CREATE POLICY "Public read access for tamil_writing_content" ON tamil_writing_content
    FOR SELECT USING (is_active = true);

-- Enable RLS on reference tables
ALTER TABLE tamil_characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE tamil_stroke_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE tamil_character_combinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE tamil_writing_content ENABLE ROW LEVEL SECURITY;

-- Functions for common operations
CREATE OR REPLACE FUNCTION get_character_stroke_count(character_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM tamil_stroke_orders WHERE character_id = $1);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION calculate_user_writing_accuracy(user_id UUID, days_back INTEGER DEFAULT 7)
RETURNS DECIMAL AS $$
BEGIN
    RETURN (
        SELECT AVG(accuracy_score)
        FROM user_writing_progress 
        WHERE user_id = $1 
        AND practice_date >= NOW() - INTERVAL '1 day' * days_back
        AND accuracy_score IS NOT NULL
    );
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_tamil_characters_updated_at BEFORE UPDATE ON tamil_characters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tamil_writing_content_updated_at BEFORE UPDATE ON tamil_writing_content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE tamil_characters IS 'Master table for all Tamil characters including vowels, consonants, and special characters';
COMMENT ON TABLE tamil_stroke_orders IS 'Detailed stroke order information for each Tamil character';
COMMENT ON TABLE tamil_character_combinations IS 'Combinations of base characters with modifiers (e.g., consonant + vowel)';
COMMENT ON TABLE tamil_writing_content IS 'Practice content for writing exercises at different levels';
COMMENT ON TABLE user_writing_progress IS 'Individual user progress tracking for writing practice';
COMMENT ON TABLE writing_sessions IS 'Complete writing practice session tracking';
COMMENT ON TABLE writing_achievements IS 'User achievements and milestones in writing practice';
