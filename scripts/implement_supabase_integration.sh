#!/bin/bash

# NIRA Tamil - Supabase Integration Implementation Script
# This script automates the implementation of Supabase integration with local caching

set -e  # Exit on any error

echo "🚀 NIRA Tamil - Supabase Integration Implementation"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="/Users/<USER>/Documents/NIRA-Tamil"
SERVICES_DIR="$PROJECT_ROOT/NIRA-Tamil/Services"
MODELS_DIR="$PROJECT_ROOT/NIRA-Tamil/Models"
VIEWS_DIR="$PROJECT_ROOT/NIRA-Tamil/Views"

echo -e "${BLUE}📁 Project Root: $PROJECT_ROOT${NC}"

# Step 1: Update TamilContentService to use Supabase
echo -e "\n${YELLOW}Step 1: Updating TamilContentService to use Supabase...${NC}"

# Create backup of original service
cp "$SERVICES_DIR/TamilContentService.swift" "$SERVICES_DIR/TamilContentService.swift.backup"
echo -e "${GREEN}✅ Backup created: TamilContentService.swift.backup${NC}"

# Step 2: Create Core Data models for local caching
echo -e "\n${YELLOW}Step 2: Creating Core Data models for local caching...${NC}"

cat > "$MODELS_DIR/CoreDataModels.swift" << 'EOF'
//
//  CoreDataModels.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2025-01-19.
//  Copyright © 2025 Securight. All rights reserved.
//

import Foundation
import CoreData

// MARK: - Core Data Entity Extensions

extension CachedLesson {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<CachedLesson> {
        return NSFetchRequest<CachedLesson>(entityName: "CachedLesson")
    }

    @NSManaged public var id: String
    @NSManaged public var lessonNumber: Int32
    @NSManaged public var levelCode: String
    @NSManaged public var titleEnglish: String
    @NSManaged public var titleTamil: String
    @NSManaged public var descriptionEnglish: String?
    @NSManaged public var focus: String?
    @NSManaged public var durationMinutes: Int32
    @NSManaged public var difficultyScore: Int32
    @NSManaged public var culturalContext: String?
    @NSManaged public var isActive: Bool
    @NSManaged public var lastSynced: Date
    @NSManaged public var vocabulary: NSSet?
    @NSManaged public var conversations: NSSet?
    @NSManaged public var grammarTopics: NSSet?
    @NSManaged public var practiceExercises: NSSet?
}

extension CachedVocabulary {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<CachedVocabulary> {
        return NSFetchRequest<CachedVocabulary>(entityName: "CachedVocabulary")
    }

    @NSManaged public var id: String
    @NSManaged public var vocabId: String
    @NSManaged public var englishWord: String
    @NSManaged public var tamilTranslation: String
    @NSManaged public var romanization: String
    @NSManaged public var ipaPronunciation: String?
    @NSManaged public var partOfSpeech: String?
    @NSManaged public var culturalNotes: String?
    @NSManaged public var exampleSentenceEnglish: String?
    @NSManaged public var exampleSentenceTamil: String?
    @NSManaged public var audioWordUrl: String?
    @NSManaged public var audioSentenceUrl: String?
    @NSManaged public var lesson: CachedLesson?
}

// MARK: - Core Data Helper Functions

class CoreDataManager {
    static let shared = CoreDataManager()
    
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "NIRADataModel")
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Core Data error: \(error)")
            }
        }
        return container
    }()
    
    var context: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    func save() {
        if context.hasChanges {
            try? context.save()
        }
    }
}
EOF

echo -e "${GREEN}✅ Created CoreDataModels.swift${NC}"

# Step 3: Create Local Cache Service
echo -e "\n${YELLOW}Step 3: Creating Local Cache Service...${NC}"

cat > "$SERVICES_DIR/LocalCacheService.swift" << 'EOF'
//
//  LocalCacheService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2025-01-19.
//  Copyright © 2025 Securight. All rights reserved.
//

import Foundation
import CoreData
import Combine

@MainActor
class LocalCacheService: ObservableObject {
    static let shared = LocalCacheService()
    
    private let coreDataManager = CoreDataManager.shared
    private let cacheExpiryHours: TimeInterval = 24 // 24 hours
    
    private init() {
        print("🗄️ LocalCacheService initialized")
    }
    
    // MARK: - Cache Management
    
    func isCacheValid(for levelCode: String) -> Bool {
        let request: NSFetchRequest<CachedLesson> = CachedLesson.fetchRequest()
        request.predicate = NSPredicate(format: "levelCode == %@", levelCode)
        request.sortDescriptors = [NSSortDescriptor(key: "lastSynced", ascending: false)]
        request.fetchLimit = 1
        
        do {
            let lessons = try coreDataManager.context.fetch(request)
            if let latestLesson = lessons.first {
                let timeSinceSync = Date().timeIntervalSince(latestLesson.lastSynced)
                return timeSinceSync < (cacheExpiryHours * 3600)
            }
        } catch {
            print("❌ Error checking cache validity: \(error)")
        }
        
        return false
    }
    
    func getCachedLessons(for levelCode: String) -> [TamilSupabaseLesson] {
        let request: NSFetchRequest<CachedLesson> = CachedLesson.fetchRequest()
        request.predicate = NSPredicate(format: "levelCode == %@ AND isActive == YES", levelCode)
        request.sortDescriptors = [NSSortDescriptor(key: "lessonNumber", ascending: true)]
        
        do {
            let cachedLessons = try coreDataManager.context.fetch(request)
            return cachedLessons.compactMap { $0.toTamilSupabaseLesson() }
        } catch {
            print("❌ Error fetching cached lessons: \(error)")
            return []
        }
    }
    
    func cacheLessons(_ lessons: [TamilSupabaseLesson]) {
        for lesson in lessons {
            let cachedLesson = CachedLesson(context: coreDataManager.context)
            cachedLesson.id = lesson.id
            cachedLesson.lessonNumber = Int32(lesson.lessonNumber)
            cachedLesson.levelCode = lesson.levelCode
            cachedLesson.titleEnglish = lesson.titleEnglish
            cachedLesson.titleTamil = lesson.titleTamil
            cachedLesson.descriptionEnglish = lesson.descriptionEnglish
            cachedLesson.focus = lesson.focus
            cachedLesson.durationMinutes = Int32(lesson.durationMinutes)
            cachedLesson.difficultyScore = Int32(lesson.difficultyScore)
            cachedLesson.culturalContext = lesson.culturalContext
            cachedLesson.isActive = lesson.isActive
            cachedLesson.lastSynced = Date()
        }
        
        coreDataManager.save()
        print("✅ Cached \(lessons.count) lessons")
    }
    
    func clearCache(for levelCode: String? = nil) {
        let request: NSFetchRequest<NSFetchRequestResult> = CachedLesson.fetchRequest()
        if let levelCode = levelCode {
            request.predicate = NSPredicate(format: "levelCode == %@", levelCode)
        }
        
        let deleteRequest = NSBatchDeleteRequest(fetchRequest: request)
        
        do {
            try coreDataManager.context.execute(deleteRequest)
            coreDataManager.save()
            print("✅ Cache cleared for level: \(levelCode ?? "all")")
        } catch {
            print("❌ Error clearing cache: \(error)")
        }
    }
}

// MARK: - Core Data Extensions

extension CachedLesson {
    func toTamilSupabaseLesson() -> TamilSupabaseLesson? {
        return TamilSupabaseLesson(
            id: id,
            lessonNumber: Int(lessonNumber),
            levelCode: levelCode,
            titleEnglish: titleEnglish,
            titleTamil: titleTamil,
            titleRomanization: nil,
            descriptionEnglish: descriptionEnglish,
            descriptionTamil: nil,
            focus: focus,
            durationMinutes: Int(durationMinutes),
            difficultyScore: Int(difficultyScore),
            prerequisites: nil,
            tags: nil,
            culturalContext: culturalContext,
            isActive: isActive,
            createdAt: ISO8601DateFormatter().string(from: Date()),
            updatedAt: ISO8601DateFormatter().string(from: Date())
        )
    }
}
EOF

echo -e "${GREEN}✅ Created LocalCacheService.swift${NC}"

# Step 4: Update TamilContentService to use both Supabase and local cache
echo -e "\n${YELLOW}Step 4: Creating new TamilContentService with Supabase integration...${NC}"

cat > "$SERVICES_DIR/TamilContentService_New.swift" << 'EOF'
//
//  TamilContentService.swift
//  NIRA-Tamil
//
//  Updated for Supabase integration with local caching
//

import Foundation
import Combine

@MainActor
class TamilContentService: ObservableObject {
    static let shared = TamilContentService()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var contentMetadata: ContentMetadata?
    
    // Content Storage - now using Supabase models
    @Published var cefrLessons: [CEFRLevel: [TamilSupabaseLesson]] = [:]
    @Published var currentLevel: CEFRLevel = .a1
    @Published var currentTrack: ContentTrack = .conversational
    @Published var currentLesson: TamilSupabaseLesson?
    
    // Services
    private let supabaseService = SupabaseContentService.shared
    private let cacheService = LocalCacheService.shared
    private let audioManager = AudioContentManager.shared
    
    private init() {
        print("🔄 TamilContentService initialized with Supabase integration")
        loadInitialContent()
    }
    
    // MARK: - Content Loading
    
    private func loadInitialContent() {
        Task {
            await loadLessonsForCurrentLevel()
        }
    }
    
    func loadLessonsForCurrentLevel() async {
        await loadLessonsForLevel(currentLevel)
    }
    
    func loadLessonsForLevel(_ level: CEFRLevel) async {
        isLoading = true
        errorMessage = nil
        
        print("🔄 Loading lessons for level: \(level.rawValue)")
        
        // Check local cache first
        if cacheService.isCacheValid(for: level.rawValue) {
            let cachedLessons = cacheService.getCachedLessons(for: level.rawValue)
            if !cachedLessons.isEmpty {
                cefrLessons[level] = cachedLessons
                print("✅ Loaded \(cachedLessons.count) lessons from cache")
                isLoading = false
                return
            }
        }
        
        // Fetch from Supabase if cache is invalid or empty
        await supabaseService.fetchLessons(for: level)
        
        if let error = supabaseService.errorMessage {
            errorMessage = error
            print("❌ Error loading lessons: \(error)")
        } else {
            let lessons = supabaseService.lessons
            cefrLessons[level] = lessons
            
            // Cache the lessons
            cacheService.cacheLessons(lessons)
            
            print("✅ Loaded \(lessons.count) lessons from Supabase")
        }
        
        isLoading = false
    }
    
    // MARK: - Content Access
    
    func getLessonsForLevel(_ level: CEFRLevel) -> [TamilSupabaseLesson] {
        if let lessons = cefrLessons[level], !lessons.isEmpty {
            return lessons
        }
        
        // Load on demand if not available
        Task {
            await loadLessonsForLevel(level)
        }
        
        return cefrLessons[level] ?? []
    }
    
    func getLesson(level: CEFRLevel, lessonNumber: Int) -> TamilSupabaseLesson? {
        return cefrLessons[level]?.first { $0.lessonNumber == lessonNumber }
    }
    
    func setCurrentLesson(_ lesson: TamilSupabaseLesson) {
        currentLesson = lesson
        currentLevel = CEFRLevel(rawValue: lesson.levelCode) ?? .a1
    }
    
    // MARK: - Lesson Content Loading
    
    func loadCompleteLesson(lessonId: String) async -> CompleteTamilLesson? {
        return await supabaseService.fetchCompleteLesson(lessonId: lessonId)
    }
    
    func loadVocabulary(for lessonId: String) async -> [TamilSupabaseVocabulary] {
        return await supabaseService.fetchVocabulary(for: lessonId)
    }
    
    func loadConversations(for lessonId: String) async -> [TamilSupabaseConversation] {
        return await supabaseService.fetchConversations(for: lessonId)
    }
    
    func loadGrammarTopics(for lessonId: String) async -> [TamilSupabaseGrammarTopic] {
        return await supabaseService.fetchGrammarTopics(for: lessonId)
    }
    
    func loadPracticeExercises(for lessonId: String) async -> [TamilSupabasePracticeExercise] {
        return await supabaseService.fetchPracticeExercises(for: lessonId)
    }
    
    // MARK: - Cache Management
    
    func refreshContent(for level: CEFRLevel? = nil) async {
        if let level = level {
            cacheService.clearCache(for: level.rawValue)
            await loadLessonsForLevel(level)
        } else {
            cacheService.clearCache()
            await loadLessonsForCurrentLevel()
        }
    }
    
    // MARK: - Compatibility Methods (for existing UI)
    
    func searchContent(_ query: String) -> [TamilSupabaseLesson] {
        let allLessons = cefrLessons.values.flatMap { $0 }
        return allLessons.filter { lesson in
            lesson.titleEnglish.localizedCaseInsensitiveContains(query) ||
            lesson.titleTamil.localizedCaseInsensitiveContains(query) ||
            (lesson.focus?.localizedCaseInsensitiveContains(query) ?? false)
        }
    }
}

// MARK: - Extensions for Backward Compatibility

extension TamilSupabaseLesson {
    /// Convert to legacy TamilLesson format for existing UI components
    func toLegacyTamilLesson() -> TamilLesson {
        return TamilLesson(
            lessonNumber: lessonNumber,
            levelCode: levelCode,
            titleEnglish: titleEnglish,
            titleTamil: titleTamil,
            durationMinutes: durationMinutes,
            focus: focus ?? "",
            vocabulary: [], // Will be loaded separately
            conversations: [], // Will be loaded separately
            grammar: [], // Will be loaded separately
            practice: [] // Will be loaded separately
        )
    }
}
EOF

echo -e "${GREEN}✅ Created TamilContentService_New.swift${NC}"

echo -e "\n${GREEN}🎉 Supabase Integration Implementation Complete!${NC}"
echo -e "\n${BLUE}Next Steps:${NC}"
echo -e "1. Replace the old TamilContentService.swift with TamilContentService_New.swift"
echo -e "2. Update UI components to handle async loading"
echo -e "3. Add Core Data model file to Xcode project"
echo -e "4. Test the integration"
echo -e "\n${YELLOW}⚠️  Remember to:${NC}"
echo -e "- Add Supabase dependency to your project"
echo -e "- Configure API keys in APIKeys.swift"
echo -e "- Create Core Data model file in Xcode"
echo -e "- Update any UI components that expect synchronous data loading"

echo -e "\n${GREEN}✅ Script completed successfully!${NC}"
