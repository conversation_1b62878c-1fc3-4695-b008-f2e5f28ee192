#!/usr/bin/env python3
"""
Practice Exercise Audio Generator for NIRA-Tamil
Generates audio for Tamil options in practice exercises using Google Cloud TTS
Following A1_BASIC_GREETINGS_IMPLEMENTATION_GUIDE.md approach
"""

import asyncio
import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Optional
import tempfile

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from google.cloud import texttospeech
    from supabase import create_client, Client
    import requests
except ImportError as e:
    print(f"❌ Missing required packages: {e}")
    print("💡 Install with: pip install google-cloud-texttospeech supabase requests")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PracticeExerciseAudioGenerator:
    def __init__(self):
        self.setup_clients()
        
    def setup_clients(self):
        """Initialize Google TTS and Supabase clients"""
        # Google TTS setup
        self.setup_google_tts()
        
        # Supabase setup
        self.setup_supabase()
        
    def setup_google_tts(self):
        """Setup Google Cloud TTS client with service account rotation"""
        google_keys_dir = Path("/Users/<USER>/Documents/NIRA-Tamil/googlettskeys")
        
        if not google_keys_dir.exists():
            raise FileNotFoundError(f"Google TTS keys directory not found: {google_keys_dir}")
            
        key_files = list(google_keys_dir.glob("*.json"))
        if not key_files:
            raise FileNotFoundError("No Google TTS service account keys found")
            
        # Use first available key (could implement rotation)
        key_file = key_files[0]
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(key_file)
        
        self.tts_client = texttospeech.TextToSpeechClient()
        logger.info(f"✅ Google TTS client initialized with key: {key_file.name}")
        
    def setup_supabase(self):
        """Setup Supabase client"""
        supabase_url = "https://wnsorhbsucjguaoquhvr.supabase.co"
        # Use service role key for admin operations
        service_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.huEg6PWHSgAaXAtREOLf1aRa3OENbOb437b8lEGyubc"
        
        self.supabase: Client = create_client(supabase_url, service_key)
        logger.info("✅ Supabase client initialized")
        
    async def generate_audio_file(self, text: str, filename: str, voice_name: str = "ta-IN-Chirp3-HD-Erinome") -> Dict:
        """Generate audio file using Google Cloud TTS"""
        try:
            # Configure voice (using approved Tamil voices)
            voice = texttospeech.VoiceSelectionParams(
                language_code="ta-IN",
                name=voice_name
            )
            
            # Configure audio (slower for learning)
            audio_config = texttospeech.AudioConfig(
                audio_encoding=texttospeech.AudioEncoding.MP3,
                speaking_rate=0.8,  # Slower for basic learners
                pitch=0.0
            )
            
            # Create synthesis input
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            # Generate audio
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=voice,
                audio_config=audio_config
            )
            
            # Save to temporary file
            temp_dir = Path(tempfile.gettempdir()) / "nira_practice_audio"
            temp_dir.mkdir(exist_ok=True)
            
            local_path = temp_dir / filename
            with open(local_path, "wb") as audio_file:
                audio_file.write(response.audio_content)
                
            logger.info(f"✅ Generated audio: {filename} for text: {text}")
            
            return {
                'local_path': str(local_path),
                'filename': filename,
                'text': text,
                'voice': voice_name
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to generate audio for '{text}': {e}")
            raise
            
    async def upload_to_supabase(self, audio_file: Dict) -> str:
        """Upload audio file to Supabase Storage"""
        try:
            bucket_name = "audio"
            file_path = f"practice_exercises/{audio_file['filename']}"

            # Read file content
            with open(audio_file['local_path'], 'rb') as f:
                file_content = f.read()

            # Upload to Supabase Storage
            response = self.supabase.storage.from_(bucket_name).upload(
                file_path,
                file_content,
                file_options={"content-type": "audio/mpeg"}
            )

            # Check if upload was successful (Supabase Python client doesn't use status_code)
            if hasattr(response, 'error') and response.error:
                raise Exception(f"Upload failed: {response.error}")

            # Get public URL
            public_url = self.supabase.storage.from_(bucket_name).get_public_url(file_path)

            logger.info(f"✅ Uploaded to Supabase: {file_path}")
            return public_url

        except Exception as e:
            logger.error(f"❌ Failed to upload {audio_file['filename']}: {e}")
            raise
            
    async def get_practice_exercise_tamil_options(self, lesson_id: str) -> List[Dict]:
        """Get all unique Tamil options from practice exercises for a lesson"""
        try:
            # This would need to be implemented based on your practice exercise data structure
            # For now, return common Tamil words from A1 Basic Greetings
            tamil_options = [
                {"text": "வணக்கம்", "romanization": "Vanakkam", "meaning": "Hello"},
                {"text": "நன்றி", "romanization": "Nanri", "meaning": "Thank you"},
                {"text": "தயவுசெய்து", "romanization": "Thayavu seithu", "meaning": "Please"},
                {"text": "மன்னிக்கவும்", "romanization": "Mannikkavum", "meaning": "Sorry"},
                {"text": "ஆம்", "romanization": "Aam", "meaning": "Yes"},
                {"text": "இல்லை", "romanization": "Illai", "meaning": "No"},
                {"text": "அம்மா", "romanization": "Amma", "meaning": "Mother"},
                {"text": "அப்பா", "romanization": "Appa", "meaning": "Father"},
                {"text": "நண்பன்", "romanization": "Nanban", "meaning": "Friend"},
                {"text": "வீடு", "romanization": "Veedu", "meaning": "House"},
                {"text": "மகிழ்ச்சி", "romanization": "Magizhchi", "meaning": "Happy"},
                {"text": "அன்பு", "romanization": "Anbu", "meaning": "Love"},
                {"text": "நேரம்", "romanization": "Neram", "meaning": "Time"},
                {"text": "வேலை", "romanization": "Velai", "meaning": "Work"}
            ]
            
            logger.info(f"📝 Found {len(tamil_options)} Tamil options for practice exercises")
            return tamil_options
            
        except Exception as e:
            logger.error(f"❌ Failed to get practice exercise options: {e}")
            return []
            
    async def generate_practice_exercise_audio(self, lesson_id: str):
        """Generate audio for all Tamil options in practice exercises"""
        logger.info(f"🎵 Starting practice exercise audio generation for lesson: {lesson_id}")
        
        # Get Tamil options
        tamil_options = await self.get_practice_exercise_tamil_options(lesson_id)
        
        if not tamil_options:
            logger.warning("No Tamil options found for practice exercises")
            return
            
        success_count = 0
        audio_urls = {}
        
        for i, option in enumerate(tamil_options, 1):
            try:
                text = option['text']
                romanization = option['romanization']
                
                # Generate filename
                filename = f"practice_option_{romanization.lower().replace(' ', '_')}.mp3"
                
                logger.info(f"Processing {i}/{len(tamil_options)}: {text} ({romanization})")
                
                # Generate audio
                audio_file = await self.generate_audio_file(text, filename)
                
                # Upload to Supabase
                public_url = await self.upload_to_supabase(audio_file)
                
                # Store URL mapping
                audio_urls[text] = public_url
                
                # Clean up local file
                os.remove(audio_file['local_path'])
                
                success_count += 1
                
                # Small delay to avoid overwhelming services
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Failed to process option {i}: {e}")
                continue
                
        logger.info(f"🎉 Practice exercise audio generation completed!")
        logger.info(f"✅ Successfully generated {success_count}/{len(tamil_options)} audio files")
        
        # Save audio URL mapping for reference
        mapping_file = Path(tempfile.gettempdir()) / "practice_exercise_audio_urls.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(audio_urls, f, ensure_ascii=False, indent=2)
            
        logger.info(f"📄 Audio URL mapping saved to: {mapping_file}")
        
        return audio_urls

async def main():
    """Main function"""
    if len(sys.argv) != 3 or sys.argv[1] != "--lesson-id":
        print("Usage: python generate_practice_exercise_audio.py --lesson-id <lesson-uuid>")
        sys.exit(1)
        
    lesson_id = sys.argv[2]
    
    try:
        generator = PracticeExerciseAudioGenerator()
        await generator.generate_practice_exercise_audio(lesson_id)
        
    except Exception as e:
        logger.error(f"❌ Script failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
