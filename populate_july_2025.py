#!/usr/bin/env python3
"""
Populate July 2025 with real ProKerala data
This will replace the placeholder "Unknown" data with real panchang information
"""

import requests
import json
from datetime import datetime, date, timedelta
import uuid
import time

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

# ProKerala API credentials
PROKERALA_CLIENT_ID = "6df0ec16-722b-4acd-a574-bfd546c0c270"
PROKERALA_CLIENT_SECRET = "0iLz5yY9wRzyavZdk0YRPUtkUaPsYSwSxzQfgeZx"

# Chennai coordinates
CHENNAI_LAT = 13.0827
CHENNAI_LON = 80.2707

class JulyPopulator:
    def __init__(self):
        self.supabase_headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=representation"
        }
        self.prokerala_token = None
        self.token_expires_at = None
        self.request_count = 0
        self.last_request_time = None
        
    def wait_for_rate_limit(self):
        """Smart rate limiting - 5 requests per 60 seconds"""
        if self.request_count >= 5:
            if self.last_request_time:
                elapsed = time.time() - self.last_request_time
                if elapsed < 60:
                    wait_time = 60 - elapsed + 1
                    print(f"⏳ Rate limit reached. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
            
            self.request_count = 0
        
        if self.last_request_time:
            time.sleep(2)
    
    def get_prokerala_token(self):
        """Get or refresh ProKerala API token"""
        if self.prokerala_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.prokerala_token
            
        print("🔑 Getting ProKerala API token...")
        token_url = "https://api.prokerala.com/token"
        token_data = {
            "grant_type": "client_credentials",
            "client_id": PROKERALA_CLIENT_ID,
            "client_secret": PROKERALA_CLIENT_SECRET
        }
        
        response = requests.post(token_url, data=token_data)
        if response.status_code != 200:
            raise Exception(f"Failed to get token: {response.status_code} - {response.text}")
        
        token_info = response.json()
        self.prokerala_token = token_info["access_token"]
        self.token_expires_at = datetime.now() + timedelta(seconds=token_info["expires_in"] - 300)
        
        print("✅ ProKerala token obtained successfully")
        return self.prokerala_token
    
    def fetch_panchang_for_date(self, target_date):
        """Fetch panchang data from ProKerala API for a specific date"""
        self.wait_for_rate_limit()
        
        token = self.get_prokerala_token()
        
        # Format date for ProKerala API (6 AM IST)
        datetime_str = f"{target_date.strftime('%Y-%m-%d')}T06:00:00+05:30"
        
        # Build API URL with proper encoding
        base_url = "https://api.prokerala.com/v2/astrology/panchang"
        params = {
            "ayanamsa": 1,  # Lahiri
            "coordinates": f"{CHENNAI_LAT},{CHENNAI_LON}",
            "datetime": datetime_str,
            "la": "en"
        }
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"📡 Fetching panchang for {target_date.strftime('%Y-%m-%d')}...")
        
        self.request_count += 1
        self.last_request_time = time.time()
        
        response = requests.get(base_url, headers=headers, params=params)
        
        if response.status_code == 429:
            print("⚠️ Rate limit hit, will retry after waiting...")
            self.request_count = 5
            return self.fetch_panchang_for_date(target_date)
        elif response.status_code != 200:
            raise Exception(f"API Error {response.status_code}: {response.text}")
        
        return response.json()
    
    def update_existing_record(self, target_date, api_data):
        """Update existing record with real ProKerala data"""
        data = api_data.get("data", {})
        
        # Extract real panchang data
        tithi_array = data.get("tithi", [])
        nakshatra_array = data.get("nakshatra", [])
        yoga_array = data.get("yoga", [])
        karana_array = data.get("karana", [])
        
        # Create update data with proper iOS-compatible format
        sunrise_iso = self.extract_time_from_iso(data.get("sunrise"))
        sunset_iso = self.extract_time_from_iso(data.get("sunset"))
        moonrise_iso = self.extract_time_from_iso(data.get("moonrise"))
        moonset_iso = self.extract_time_from_iso(data.get("moonset"))

        update_data = {
            "tithi": json.dumps(tithi_array),
            "nakshatra": json.dumps(nakshatra_array),
            "yoga": json.dumps(yoga_array),
            "karana": json.dumps(karana_array),
            "sun_times": json.dumps({
                "sunrise": sunrise_iso,
                "sunset": sunset_iso,
                "noon": None,
                "twilightBegin": None,
                "twilightEnd": None
            }),
            "moon_times": json.dumps({
                "moonrise": moonrise_iso,
                "moonset": moonset_iso,
                "phase": "new_moon",
                "illumination": 0.0
            }),
            "updated_at": datetime.now().isoformat()
        }
        
        # Update the record in Supabase
        date_str = target_date.strftime("%Y-%m-%d")
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
        params = {"date": f"eq.{date_str}"}
        
        response = requests.patch(url, json=update_data, headers=self.supabase_headers, params=params)
        
        if response.status_code in [200, 204]:
            print(f"✅ Updated panchang for {date_str}")
            return True
        else:
            print(f"❌ Failed to update {date_str}: {response.status_code} - {response.text}")
            return False
    
    def extract_time_from_iso(self, iso_string):
        """Extract time from ISO string and return as ISO format for iOS Date parsing"""
        if not iso_string:
            return None

        try:
            # Parse the ISO string and return it in the format iOS expects
            dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
            return dt.isoformat()
        except:
            return None
    
    def populate_july_2025(self):
        """Populate July 2025 with real data"""
        print("🚀 Updating July 2025 with Real ProKerala Data")
        print("=" * 50)
        
        # Focus on first 10 days of July for now
        start_date = date(2025, 7, 1)
        end_date = date(2025, 7, 10)
        
        current_date = start_date
        success_count = 0
        total_days = (end_date - start_date).days + 1
        
        while current_date <= end_date:
            try:
                print(f"📅 Processing {current_date.strftime('%Y-%m-%d')}...")
                
                # Fetch from API
                api_data = self.fetch_panchang_for_date(current_date)
                
                # Update existing record
                if self.update_existing_record(current_date, api_data):
                    success_count += 1
                
            except Exception as e:
                print(f"❌ Error processing {current_date}: {e}")
                print("⏳ Waiting 30 seconds before continuing...")
                time.sleep(30)
            
            current_date += timedelta(days=1)
        
        print("\n" + "=" * 50)
        print("📊 UPDATE SUMMARY")
        print("=" * 50)
        print(f"📅 Total Days: {total_days}")
        print(f"✅ Successfully Updated: {success_count}")
        print(f"❌ Failed: {total_days - success_count}")
        print(f"📈 Success Rate: {(success_count/total_days)*100:.1f}%")
        
        if success_count == total_days:
            print("\n🎉 JULY 2025 DATA SUCCESSFULLY UPDATED WITH REAL PANCHANG!")
            print("🚀 Your app now has real data for both June and July 2025!")
        else:
            print(f"\n⚠️ Some records failed. You may want to retry the failed dates.")

def main():
    populator = JulyPopulator()
    populator.populate_july_2025()

if __name__ == "__main__":
    main()
