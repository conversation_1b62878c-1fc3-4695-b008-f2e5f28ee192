# Scripts Directory Relocation

## Overview
The Python scripts directory has been moved outside the main project structure to resolve Xcode build warnings.

## Location Change
- **Previous Location**: `NIRA-Tamil/Scripts/`
- **New Location**: `../NIRA-Tamil-Scripts/`

## Reason for Change
The Scripts directory contained a Python virtual environment (`venv/`) with 450+ files that were being included in the Xcode build process, causing duplicate file warnings. Moving the directory outside the project structure eliminates these warnings while maintaining all functionality.

## Scripts Available
- `audio_generation.py` - Generates Tamil TTS audio files
- `conversation_audio_generator.py` - Generates conversation audio with proper male/female voices
- `supabase_audio_uploader.py` - Uploads audio files to Supabase storage
- `venv/` - Python virtual environment with required dependencies

## Usage
All scripts should be run from the `../NIRA-Tamil-Scripts/` directory:

```bash
cd ../NIRA-Tamil-Scripts
python conversation_audio_generator.py
```

## Build Status
✅ **Build now succeeds without warnings**
✅ **All functionality preserved**
✅ **Audio generation scripts work correctly**
✅ **App runs successfully with all fixes**
