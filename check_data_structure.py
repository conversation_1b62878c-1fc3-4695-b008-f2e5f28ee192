#!/usr/bin/env python3
"""
Check the actual data structure in Supabase
"""

import requests
import json

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def check_data_structure():
    """Check the actual data structure"""
    print("🔍 Checking Data Structure")
    print("=" * 40)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Get today's record
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "*",
        "date": "eq.2025-07-10",
        "limit": 1
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        records = response.json()
        if records:
            record = records[0]
            print("📊 Raw record structure:")
            print("-" * 30)
            
            for key, value in record.items():
                if key in ['tithi', 'nakshatra', 'yoga', 'karana', 'sun_times', 'moon_times']:
                    print(f"{key}: {repr(value)}")
                else:
                    print(f"{key}: {value}")
            
            print("\n🔍 Parsing JSON fields:")
            print("-" * 30)
            
            json_fields = ['tithi', 'nakshatra', 'yoga', 'karana', 'sun_times', 'moon_times', 'weekday_info']
            
            for field in json_fields:
                if field in record:
                    try:
                        if record[field]:
                            parsed = json.loads(record[field])
                            print(f"✅ {field}: {parsed}")
                        else:
                            print(f"⚠️ {field}: Empty/null")
                    except Exception as e:
                        print(f"❌ {field}: Parse error - {e}")
                        print(f"   Raw value: {repr(record[field])}")
        else:
            print("❌ No record found for 2025-07-10")
    else:
        print(f"❌ Error: {response.status_code}")

def main():
    check_data_structure()

if __name__ == "__main__":
    main()
