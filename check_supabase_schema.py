#!/usr/bin/env python3
"""
Check Supabase schema and data types
"""

import requests
import json

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def check_schema():
    """Check the schema and data types"""
    print("🔍 Checking Supabase Schema")
    print("=" * 40)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Get a sample record to see data types
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "*",
        "limit": 1
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        records = response.json()
        if records:
            record = records[0]
            print("📊 Sample record structure:")
            print("-" * 30)
            
            for key, value in record.items():
                value_type = type(value).__name__
                if isinstance(value, str) and len(value) > 50:
                    preview = value[:50] + "..."
                else:
                    preview = value
                
                print(f"{key}: {value_type} = {repr(preview)}")
                
                # Check if it looks like JSON
                if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                    try:
                        parsed = json.loads(value)
                        print(f"  ✅ Valid JSON: {type(parsed).__name__}")
                    except:
                        print(f"  ❌ Not valid JSON")
                
                # Check if it looks like Base64
                if isinstance(value, str) and len(value) > 10 and value.replace('=', '').isalnum():
                    print(f"  🔍 Might be Base64 encoded")
        else:
            print("❌ No records found")
    else:
        print(f"❌ Error: {response.status_code}")

def main():
    check_schema()

if __name__ == "__main__":
    main()
