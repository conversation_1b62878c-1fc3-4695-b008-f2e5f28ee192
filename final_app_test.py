#!/usr/bin/env python3
"""
Final verification that the app should work correctly
"""

import requests
import json

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def test_app_compatibility():
    """Test that all data is compatible with iOS app"""
    print("🎯 FINAL APP COMPATIBILITY TEST")
    print("=" * 50)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Test a few key dates
    test_dates = ["2025-06-01", "2025-06-15", "2025-07-01", "2025-07-10"]
    
    all_good = True
    
    for test_date in test_dates:
        print(f"\n📅 Testing {test_date}:")
        print("-" * 30)
        
        url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
        params = {
            "select": "date,tithi,nakshatra,sun_times,moon_times",
            "date": f"eq.{test_date}",
            "limit": 1
        }
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ API Error: {response.status_code}")
            all_good = False
            continue
        
        records = response.json()
        if not records:
            print(f"❌ No data found")
            all_good = False
            continue
        
        record = records[0]
        
        # Test Tithi
        try:
            tithi_data = json.loads(record['tithi'])
            if tithi_data and len(tithi_data) > 0:
                print(f"✅ Tithi: {tithi_data[0]['name']}")
            else:
                print(f"⚠️ Tithi: Empty array")
        except Exception as e:
            print(f"❌ Tithi error: {e}")
            all_good = False
        
        # Test Nakshatra
        try:
            nakshatra_data = json.loads(record['nakshatra'])
            if nakshatra_data and len(nakshatra_data) > 0:
                print(f"✅ Nakshatra: {nakshatra_data[0]['name']}")
            else:
                print(f"⚠️ Nakshatra: Empty array")
        except Exception as e:
            print(f"❌ Nakshatra error: {e}")
            all_good = False
        
        # Test Sun Times
        try:
            sun_data = json.loads(record['sun_times'])
            sunrise = sun_data.get('sunrise')
            sunset = sun_data.get('sunset')
            
            if sunrise and sunset:
                print(f"✅ Sun Times: {sunrise[:19]} to {sunset[:19]}")
            else:
                print(f"⚠️ Sun Times: Missing data")
        except Exception as e:
            print(f"❌ Sun Times error: {e}")
            all_good = False
        
        # Test Moon Times (critical for app not crashing)
        try:
            moon_data = json.loads(record['moon_times'])
            phase = moon_data.get('phase', 'missing')
            illumination = moon_data.get('illumination', 'missing')
            
            if phase != 'missing' and illumination != 'missing':
                print(f"✅ Moon Times: Phase={phase}, Illumination={illumination}")
            else:
                print(f"❌ Moon Times: Missing required fields")
                all_good = False
        except Exception as e:
            print(f"❌ Moon Times error: {e}")
            all_good = False
    
    print("\n" + "=" * 50)
    print("🎯 FINAL VERDICT")
    print("=" * 50)
    
    if all_good:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Your iOS app should now work without crashes")
        print("✅ All required fields are present and properly formatted")
        print("✅ Real panchang data is available")
        print("✅ Tamil Calendar is ready for production use!")
        
        print("\n🚀 NEXT STEPS:")
        print("1. Launch your iOS app")
        print("2. Navigate to Tamil Calendar (Explore → Calendar)")
        print("3. Browse June 2025 and July 2025")
        print("4. Click on dates to see panchang details")
        print("5. Enjoy your fully functional Tamil Calendar! 🌟")
        
    else:
        print("⚠️ SOME ISSUES DETECTED")
        print("The app may still have problems. Check the errors above.")
    
    return all_good

def main():
    test_app_compatibility()

if __name__ == "__main__":
    main()
