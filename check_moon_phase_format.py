#!/usr/bin/env python3
"""
Check and fix moon phase format in database
"""

import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

def check_and_fix_moon_phases():
    """Check and fix moon phase format"""
    print("🌙 Checking Moon Phase Format")
    print("=" * 40)
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }
    
    # Get a few sample records
    url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
    params = {
        "select": "id,date,moon_times",
        "limit": 5
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code != 200:
        print(f"❌ Error: {response.status_code}")
        return
    
    records = response.json()
    
    for record in records:
        print(f"\n📅 {record['date']}:")
        try:
            moon_data = json.loads(record['moon_times'])
            phase = moon_data.get('phase', 'missing')
            print(f"   Current phase: {repr(phase)}")
            
            # Check if it needs fixing
            if phase == 'newMoon':
                print("   ❌ Needs fixing: camelCase -> snake_case")
                
                # Fix it
                moon_data['phase'] = 'new_moon'
                
                update_data = {
                    "moon_times": json.dumps(moon_data),
                    "updated_at": datetime.now().isoformat()
                }
                
                update_url = f"{SUPABASE_URL}/rest/v1/daily_panchang"
                update_params = {"id": f"eq.{record['id']}"}
                
                update_response = requests.patch(update_url, json=update_data, headers=headers, params=update_params)
                
                if update_response.status_code in [200, 204]:
                    print("   ✅ Fixed!")
                else:
                    print(f"   ❌ Fix failed: {update_response.status_code}")
            elif phase == 'new_moon':
                print("   ✅ Already correct format")
            else:
                print(f"   ⚠️ Unexpected phase value: {phase}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    check_and_fix_moon_phases()

if __name__ == "__main__":
    main()
